import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/base/select';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/base/tabs';
import { Badge } from '@/components/ui/base/badge';
import { 
  ImageIcon, 
  Upload, 
  Search, 
  Gamepad2,
  Palette,
  Globe
} from 'lucide-react';
import { CustomArtworkUpload } from '@/components/ui/import/CustomArtworkUpload';
import { BoxArtSearch, SearchFilters } from '@/components/ui/import/BoxArtSearch';
import { ArtworkGallery } from '@/components/ui/analytics/ArtworkGallery';
import { UserGameWithDetails } from '@/types/database';
import { cn } from '@/lib/utils';
import { toast } from 'react-hot-toast';
import { artworkSearchService, ArtworkSearchResult } from '@/lib/services/artworkSearchService';
import { customArtworkService } from '@/lib/customArtworkService';

interface ArtworkTabProps {
  libraryGames: UserGameWithDetails[];
  className?: string;
}

export function ArtworkTab({ libraryGames, className }: ArtworkTabProps) {
  const [selectedGameId, setSelectedGameId] = useState<string>('');
  const [activeSection, setActiveSection] = useState<'upload' | 'search' | 'gallery'>('gallery');
  const [isSearching, setIsSearching] = useState(false);
  const [lastSearchResults, setLastSearchResults] = useState<ArtworkSearchResult[]>([]);

  // Get the selected game data
  const selectedGame = useMemo(() => {
    return libraryGames.find(game => game.id === selectedGameId);
  }, [libraryGames, selectedGameId]);

  // Set the first game as selected by default
  React.useEffect(() => {
    if (libraryGames.length > 0 && !selectedGameId) {
      setSelectedGameId(libraryGames[0].id);
    }
  }, [libraryGames, selectedGameId]);

  // Handle artwork upload completion
  const handleUploadComplete = () => {
    toast.success('Artwork uploaded successfully!');
    // Switch to gallery view to see the uploaded artwork
    setActiveSection('gallery');
  };

  // Enhanced box art search implementation with configurable filters
  const handleBoxArtSearch = async (query: string, filters?: SearchFilters) => {
    if (!query.trim()) {
      toast.error('Please provide a search query');
      return [];
    }

    setIsSearching(true);
    try {
      console.log('🔍 Searching for box art:', query, 'with filters:', filters);
      
      // Use our comprehensive artwork search service with enhanced options
      const searchOptions = {
        sources: filters?.sources || ['igdb', 'steamgriddb', 'ai'],
        artworkTypes: ['front', 'back', 'spine'],
        qualityFilter: filters?.qualityFilter || 'all',
        limit: filters?.limit || 100,
        searchMode: filters?.searchMode || 'combined',
        includeScreenshots: false,
        perSourceLimit: {
          igdb: Math.floor((filters?.limit || 100) * 0.3),
          steamgriddb: Math.floor((filters?.limit || 100) * 0.6),
          ai: Math.floor((filters?.limit || 100) * 0.1)
        }
      };
      
      const results = await artworkSearchService.searchArtwork(query, searchOptions);

      // Convert to BoxArtResult format expected by BoxArtSearch component
      const convertedResults = results.map(result => ({
        id: result.id,
        title: result.title,
        url: result.url,
        source: result.source,
        quality: result.quality,
        isOriginal: result.isOriginal
      }));

      setLastSearchResults(results);
      
      if (convertedResults.length === 0) {
        toast.error(`No artwork found for "${query}"`);
      } else {
        const sourceBreakdown = results.reduce((acc, result) => {
          acc[result.source] = (acc[result.source] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        
        const sourceText = Object.entries(sourceBreakdown)
          .map(([source, count]) => `${source.toUpperCase()}: ${count}`)
          .join(', ');
        
        toast.success(`Found ${convertedResults.length} results (${sourceText})`);
      }

      return convertedResults;
    } catch (error) {
      console.error('Error searching for box art:', error);
      toast.error('Failed to search for artwork');
      return [];
    } finally {
      setIsSearching(false);
    }
  };

  // Handle adding box art to collection - real implementation
  const handleAddToCollection = async (url: string, title: string) => {
    if (!selectedGame) {
      toast.error('Please select a game first');
      return;
    }

    try {
      console.log('📥 Adding artwork to collection:', { url, title, gameId: selectedGame.id });
      
      // Download the image and convert to File object
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.statusText}`);
      }

      const blob = await response.blob();
      const filename = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.jpg`;
      const file = new File([blob], filename, { type: 'image/jpeg' });

      // Upload using the custom artwork service
      const uploadResult = await customArtworkService.uploadArtwork(
        selectedGame.id,
        'front', // Default to front cover, could be enhanced to detect type
        file,
        false   // Not primary by default
      );

      if (uploadResult.success) {
        toast.success(`Successfully added "${title}" to your collection!`);
        
        // Switch to gallery view to see the uploaded artwork
        setActiveSection('gallery');
      } else {
        throw new Error(uploadResult.error || 'Upload failed');
      }

    } catch (error) {
      console.error('Error adding artwork to collection:', error);
      toast.error(`Failed to add artwork: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  if (libraryGames.length === 0) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Gamepad2 className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-xl font-semibold mb-2">No Games in Library</h3>
              <p className="text-muted-foreground mb-4">
                Add some games to your library first to manage their artwork
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with Game Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl">
                <ImageIcon className="h-6 w-6 text-primary" />
              </div>
              <div>
                <CardTitle className="text-2xl bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                  Artwork Management
                </CardTitle>
                <p className="text-muted-foreground mt-1">
                  Upload, search, and manage custom artwork for your games
                </p>
              </div>
            </div>
            <Badge variant="secondary" className="text-sm">
              {libraryGames.length} Games
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <label htmlFor="game-select" className="text-sm font-medium text-foreground whitespace-nowrap">
              Select Game:
            </label>
            <Select value={selectedGameId} onValueChange={setSelectedGameId}>
              <SelectTrigger className="max-w-md">
                <SelectValue placeholder="Choose a game..." />
              </SelectTrigger>
              <SelectContent>
                {libraryGames.map((game) => (
                  <SelectItem key={game.id} value={game.id}>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{game.game?.title || 'Unknown Game'}</span>
                      {game.game?.platform && (
                        <Badge variant="outline" className="text-xs">
                          {game.game.platform}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Action Tabs */}
      {selectedGame && (
        <Card>
          <CardContent className="pt-6">
            <Tabs value={activeSection} onValueChange={(value) => setActiveSection(value as typeof activeSection)}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="gallery" className="flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  Current Artwork
                </TabsTrigger>
                <TabsTrigger value="upload" className="flex items-center gap-2">
                  <Upload className="h-4 w-4" />
                  Upload New
                </TabsTrigger>
                <TabsTrigger value="search" className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Web Search
                </TabsTrigger>
              </TabsList>

              {/* Current Artwork Gallery */}
              <TabsContent value="gallery" className="mt-6">
                <ArtworkGallery
                  userGameId={selectedGame.id}
                  className="border-0 shadow-none"
                />
              </TabsContent>

              {/* Upload New Artwork */}
              <TabsContent value="upload" className="mt-6">
                <Card className="border-dashed border-2 border-primary/20">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Upload className="h-5 w-5" />
                      Upload Custom Artwork
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Upload custom box art, covers, and other artwork for <strong>{selectedGame.game?.title}</strong>
                    </p>
                  </CardHeader>
                  <CardContent>
                    <CustomArtworkUpload
                      userGameId={selectedGame.id}
                      gameTitle={selectedGame.game?.title}
                      onUploadComplete={handleUploadComplete}
                      showAdvancedFeatures={true}
                      className="min-h-[400px]"
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Web Search for Artwork */}
              <TabsContent value="search" className="mt-6">
                <Card className="border-dashed border-2 border-secondary/20">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Search className="h-5 w-5" />
                      Find Artwork Online
                    </CardTitle>
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">
                        Search multiple sources for high-quality box art and covers for <strong>{selectedGame.game?.title}</strong>
                      </p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Badge variant="outline" className="text-xs">IGDB Official</Badge>
                        <Badge variant="outline" className="text-xs">SteamGridDB</Badge>
                        <Badge variant="outline" className="text-xs">AI Enhanced</Badge>
                        {lastSearchResults.length > 0 && (
                          <span className="ml-2">• Last search: {lastSearchResults.length} results</span>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <BoxArtSearch
                      onSearch={handleBoxArtSearch}
                      onAddToCollection={handleAddToCollection}
                      initialSearchQuery={selectedGame.game?.title || ''}
                      showAdvancedControls={true}
                      className="min-h-[400px]"
                    />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      {selectedGame && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quick Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {selectedGame.game?.platform || 'N/A'}
                </div>
                <p className="text-xs text-muted-foreground">Platform</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-secondary">
                  {selectedGame.status || 'Unknown'}
                </div>
                <p className="text-xs text-muted-foreground">Game Status</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-accent">
                  {isSearching ? '...' : lastSearchResults.length}
                </div>
                <p className="text-xs text-muted-foreground">Search Results</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-500">
                  {activeSection === 'gallery' ? '📂' : activeSection === 'upload' ? '📤' : '🔍'}
                </div>
                <p className="text-xs text-muted-foreground">Active Mode</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}