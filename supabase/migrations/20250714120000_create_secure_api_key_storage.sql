-- Create table for encrypted user API keys
CREATE TABLE user_api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  platform TEXT NOT NULL, -- 'steam', 'epic', 'xbox', 'playstation', etc.
  key_name TEXT NOT NULL, -- 'api_key', 'client_id', 'client_secret', etc.
  encrypted_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, platform, key_name)
);

-- Create indexes for efficient querying
CREATE INDEX idx_user_api_keys_user_id ON user_api_keys(user_id);
CREATE INDEX idx_user_api_keys_platform ON user_api_keys(platform);
CREATE INDEX idx_user_api_keys_user_platform ON user_api_keys(user_id, platform);

-- Enable RLS (Row Level Security)
ALTER TABLE user_api_keys ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for users to only access their own API keys
CREATE POLICY "Users can only access their own API keys" ON user_api_keys
  FOR ALL USING (auth.uid() = user_id);

-- Create import history table for audit trail
CREATE TABLE import_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  platform TEXT NOT NULL,
  games_imported INTEGER DEFAULT 0,
  games_updated INTEGER DEFAULT 0,
  duplicates_found INTEGER DEFAULT 0,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  error_message TEXT,
  metadata JSONB DEFAULT '{}'
);

-- Create indexes for import history
CREATE INDEX idx_import_history_user_id ON import_history(user_id);
CREATE INDEX idx_import_history_platform ON import_history(platform);
CREATE INDEX idx_import_history_status ON import_history(status);
CREATE INDEX idx_import_history_started_at ON import_history(started_at DESC);

-- Enable RLS for import history
ALTER TABLE import_history ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for import history
CREATE POLICY "Users can only access their own import history" ON import_history
  FOR ALL USING (auth.uid() = user_id);

-- Add platform-specific identifiers to games table
ALTER TABLE games ADD COLUMN IF NOT EXISTS platform_ids JSONB DEFAULT '{}';

-- Add platform data to user_games for cross-platform metadata
ALTER TABLE user_games ADD COLUMN IF NOT EXISTS platform_data JSONB DEFAULT '{}';

-- Create indexes for improved performance
CREATE INDEX IF NOT EXISTS idx_games_platform_ids ON games USING GIN (platform_ids);
CREATE INDEX IF NOT EXISTS idx_user_games_platform_data ON user_games USING GIN (platform_data);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for user_api_keys
CREATE TRIGGER update_user_api_keys_updated_at 
    BEFORE UPDATE ON user_api_keys 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();