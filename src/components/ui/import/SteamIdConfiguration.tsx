/**
 * Steam ID Configuration Component
 * Allows users to configure their Steam ID for library import
 */

import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Input } from '@/components/ui/base/input';
import { Label } from '@/components/ui/base/label';
import { Alert, AlertDescription } from '@/components/ui/base/alert';
import { Badge } from '@/components/ui/base/badge';
import { 
  User, 
  Save, 
  AlertTriangle, 
  CheckCircle, 
  Info,
  ExternalLink,
  Trash2
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/supabase';
import toast from 'react-hot-toast';

interface SteamIdConfigurationProps {
  className?: string;
}

export function SteamIdConfiguration({ className }: SteamIdConfigurationProps) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [steamId, setSteamId] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  // Fetch user preferences to get current steam_id
  const { data: preferences, isLoading } = useQuery({
    queryKey: ['user-preferences', user?.id],
    queryFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const { data, error } = await db.userPreferences.getPreferences(user.id);
      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }
      
      return data;
    },
    enabled: !!user?.id,
  });

  // Update steam_id in user preferences
  const updateSteamId = useMutation({
    mutationFn: async (newSteamId: string) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      // If preferences exist, update them
      if (preferences) {
        const { data, error } = await db.userPreferences.updatePreferences(user.id, {
          ...preferences,
          steam_id: newSteamId || null
        });
        if (error) throw error;
        return data;
      } else {
        // Create new preferences with steam_id
        const { data, error } = await db.userPreferences.createPreferences({
          user_id: user.id,
          theme: 'system',
          notifications: {
            price_alerts: false,
            new_deals: false,
            game_updates: false,
            newsletter: false
          },
          privacy: {
            profile_public: false,
            library_public: false,
            activity_public: false
          },
          display: {
            games_per_page: 20,
            default_sort: 'name',
            show_metacritic: true,
            show_screenshots: true
          },
          steam_id: newSteamId || null
        });
        if (error) throw error;
        return data;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-preferences', user?.id] });
      toast.success('Steam ID updated successfully');
      setIsEditing(false);
    },
    onError: (error) => {
      console.error('Failed to update Steam ID:', error);
      toast.error('Failed to update Steam ID');
    },
  });

  // Set initial steam_id value when preferences load
  useEffect(() => {
    if (preferences?.steam_id) {
      setSteamId(preferences.steam_id);
    }
  }, [preferences?.steam_id]);

  const handleSave = () => {
    const trimmedSteamId = steamId.trim();
    
    // Basic validation
    if (trimmedSteamId && !isValidSteamId(trimmedSteamId)) {
      toast.error('Invalid Steam ID format. Please enter a valid Steam ID or profile URL.');
      return;
    }
    
    updateSteamId.mutate(trimmedSteamId);
  };

  const handleDelete = () => {
    setSteamId('');
    updateSteamId.mutate('');
  };

  const isValidSteamId = (id: string): boolean => {
    // Steam ID can be:
    // - 64-bit Steam ID (17 digits starting with 7656119)
    // - Custom URL (3-32 characters, alphanumeric + underscore)
    // - Full Steam profile URL
    
    if (!id.trim()) return false;
    
    // Extract from URL if provided
    const urlMatch = id.match(/steamcommunity\.com\/(?:id|profiles)\/([^/]+)/);
    if (urlMatch) {
      id = urlMatch[1];
    }
    
    // Check for 64-bit Steam ID
    if (/^7656119\d{10}$/.test(id)) {
      return true;
    }
    
    // Check for custom URL
    if (/^[a-zA-Z0-9_]{3,32}$/.test(id)) {
      return true;
    }
    
    return false;
  };

  const currentSteamId = preferences?.steam_id;
  const hasConfiguration = !!currentSteamId;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Steam ID Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Steam ID Configuration
          {hasConfiguration && (
            <Badge variant="default" className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              Configured
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Configure your Steam ID to enable Steam library import functionality.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Information Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Your Steam ID is required for importing your Steam library. You can find it in your Steam profile URL or use your custom Steam URL.
            <Button
              variant="link"
              className="p-0 h-auto text-sm"
              onClick={() => window.open('https://steamcommunity.com/my/profile', '_blank')}
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Find my Steam ID
            </Button>
          </AlertDescription>
        </Alert>

        {/* Current Configuration */}
        {hasConfiguration && !isEditing && (
          <div className="p-4 border rounded-lg bg-muted/50">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Current Steam ID</Label>
                <p className="text-sm text-muted-foreground mt-1">{currentSteamId}</p>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                >
                  Edit
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleDelete}
                  disabled={updateSteamId.isPending}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Configuration Form */}
        {(!hasConfiguration || isEditing) && (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="steam-id">Steam ID</Label>
              <Input
                id="steam-id"
                type="text"
                placeholder="Enter your Steam ID, custom URL, or profile URL"
                value={steamId}
                onChange={(e) => setSteamId(e.target.value)}
                disabled={updateSteamId.isPending}
              />
              <p className="text-xs text-muted-foreground">
                Examples: 76561198123456789, mycustomurl, or https://steamcommunity.com/id/mycustomurl
              </p>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleSave}
                disabled={updateSteamId.isPending || !steamId.trim()}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {updateSteamId.isPending ? 'Saving...' : 'Save Steam ID'}
              </Button>
              
              {isEditing && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditing(false);
                    setSteamId(currentSteamId || '');
                  }}
                  disabled={updateSteamId.isPending}
                >
                  Cancel
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Validation Error */}
        {steamId.trim() && !isValidSteamId(steamId) && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Invalid Steam ID format. Please enter a valid 17-digit Steam ID, custom URL, or Steam profile URL.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}