/**
 * React hook for API key management
 * Provides secure storage and retrieval of platform API keys
 */

import { useState, useEffect, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiKeyStore, type Platform, type KeyType, type ApiKeyData, type StoredApiKey } from '@/lib/apiKeyStore';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';

interface UseApiKeysResult {
  // Data
  storedKeys: StoredApiKey[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  storeKey: (keyData: ApiKeyData) => Promise<void>;
  getKey: (platform: Platform, keyType: KeyType) => Promise<string | null>;
  getPlatformKeys: (platform: Platform) => Promise<Record<string, string>>;
  deleteKey: (platform: Platform, keyType: KeyType) => Promise<void>;
  deletePlatformKeys: (platform: Platform) => Promise<void>;
  hasPlatformKeys: (platform: Platform) => Promise<boolean>;
  testKeys: () => Promise<void>;
  
  // State
  isStoring: boolean;
  isDeleting: boolean;
  isTesting: boolean;
}

const QUERY_KEY = 'api-keys';

export function useApiKeys(): UseApiKeysResult {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>(null);

  // Query for stored keys metadata
  const { data: storedKeys = [], isLoading } = useQuery({
    queryKey: [QUERY_KEY, user?.id],
    queryFn: () => apiKeyStore.getStoredKeys(),
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Store key mutation
  const storeKeyMutation = useMutation({
    mutationFn: async (keyData: ApiKeyData) => {
      await apiKeyStore.storeKey(keyData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEY] });
      toast.success('API key stored securely');
      setError(null);
    },
    onError: (error: Error) => {
      const message = error.message || 'Failed to store API key';
      setError(message);
      toast.error(message);
    },
  });

  // Delete key mutation
  const deleteKeyMutation = useMutation({
    mutationFn: async ({ platform, keyType }: { platform: Platform; keyType: KeyType }) => {
      await apiKeyStore.deleteKey(platform, keyType);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEY] });
      toast.success('API key deleted');
      setError(null);
    },
    onError: (error: Error) => {
      const message = error.message || 'Failed to delete API key';
      setError(message);
      toast.error(message);
    },
  });

  // Delete platform keys mutation
  const deletePlatformKeysMutation = useMutation({
    mutationFn: async (platform: Platform) => {
      await apiKeyStore.deletePlatformKeys(platform);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEY] });
      toast.success('Platform keys deleted');
      setError(null);
    },
    onError: (error: Error) => {
      const message = error.message || 'Failed to delete platform keys';
      setError(message);
      toast.error(message);
    },
  });

  // Test keys mutation
  const testKeysMutation = useMutation({
    mutationFn: async () => {
      return await apiKeyStore.testStoredKeys();
    },
    onSuccess: (results) => {
      const validKeys = results.filter(r => r.valid).length;
      const totalKeys = results.length;
      
      if (validKeys === totalKeys) {
        toast.success(`All ${totalKeys} stored keys are valid`);
      } else {
        toast.error(`${validKeys}/${totalKeys} keys are valid`);
      }
      setError(null);
    },
    onError: (error: Error) => {
      const message = error.message || 'Failed to test keys';
      setError(message);
      toast.error(message);
    },
  });

  // Clear error when user changes
  useEffect(() => {
    setError(null);
  }, [user?.id]);

  // Wrapper functions
  const storeKey = useCallback(async (keyData: ApiKeyData) => {
    return storeKeyMutation.mutateAsync(keyData);
  }, [storeKeyMutation]);

  const getKey = useCallback(async (platform: Platform, keyType: KeyType) => {
    try {
      return await apiKeyStore.getKeyWithFallback(platform, keyType);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to retrieve key';
      setError(message);
      throw error;
    }
  }, []);

  const getPlatformKeys = useCallback(async (platform: Platform) => {
    try {
      return await apiKeyStore.getPlatformKeys(platform);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to retrieve platform keys';
      setError(message);
      throw error;
    }
  }, []);

  const deleteKey = useCallback(async (platform: Platform, keyType: KeyType) => {
    return deleteKeyMutation.mutateAsync({ platform, keyType });
  }, [deleteKeyMutation]);

  const deletePlatformKeys = useCallback(async (platform: Platform) => {
    return deletePlatformKeysMutation.mutateAsync(platform);
  }, [deletePlatformKeysMutation]);

  const hasPlatformKeys = useCallback(async (platform: Platform) => {
    try {
      return await apiKeyStore.hasPlatformKeys(platform);
    } catch (error) {
      console.error('Failed to check platform keys:', error);
      return false;
    }
  }, []);

  const testKeys = useCallback(async (): Promise<void> => {
    await testKeysMutation.mutateAsync();
  }, [testKeysMutation]);

  return {
    // Data
    storedKeys,
    isLoading,
    error,
    
    // Actions
    storeKey,
    getKey,
    getPlatformKeys,
    deleteKey,
    deletePlatformKeys,
    hasPlatformKeys,
    testKeys,
    
    // State
    isStoring: storeKeyMutation.isPending,
    isDeleting: deleteKeyMutation.isPending || deletePlatformKeysMutation.isPending,
    isTesting: testKeysMutation.isPending,
  };
}

/**
 * Hook for getting platform requirements and metadata
 */
export function usePlatformInfo() {
  const platformRequirements = apiKeyStore.constructor.getPlatformRequirements();
  
  const getPlatformLabel = useCallback((platform: Platform) => {
    return platformRequirements[platform]?.label || platform;
  }, [platformRequirements]);

  const getPlatformKeys = useCallback((platform: Platform) => {
    return platformRequirements[platform]?.keys || [];
  }, [platformRequirements]);

  const getRequiredKeys = useCallback((platform: Platform) => {
    return platformRequirements[platform]?.keys.filter(k => k.required) || [];
  }, [platformRequirements]);

  const isValidPlatform = useCallback((platform: string): platform is Platform => {
    return platform in platformRequirements;
  }, [platformRequirements]);

  return {
    platformRequirements,
    getPlatformLabel,
    getPlatformKeys,
    getRequiredKeys,
    isValidPlatform,
    availablePlatforms: Object.keys(platformRequirements) as Platform[],
  };
}

/**
 * Hook for checking if a platform has been configured
 */
export function usePlatformConfiguration(platform: Platform) {
  const { storedKeys } = useApiKeys();
  const { getRequiredKeys } = usePlatformInfo();
  
  const requiredKeys = getRequiredKeys(platform);
  const configuredKeys = storedKeys.filter(key => key.platform === platform);
  
  const isConfigured = requiredKeys.every(reqKey => 
    configuredKeys.some(confKey => confKey.keyName === reqKey.type)
  );
  
  const missingKeys = requiredKeys.filter(reqKey => 
    !configuredKeys.some(confKey => confKey.keyName === reqKey.type)
  );

  return {
    isConfigured,
    configuredKeys,
    missingKeys,
    requiredKeysCount: requiredKeys.length,
    configuredKeysCount: configuredKeys.length,
  };
}