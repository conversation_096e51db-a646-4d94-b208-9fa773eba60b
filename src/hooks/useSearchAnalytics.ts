import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { searchAnalytics, SearchAnalytics, SearchQuery } from '@/lib/supabase/supabase-search-analytics';

interface SearchAnalyticsState {
  currentSessionId: string | null;
  queryHistory: SearchQuery[];
  isTracking: boolean;
}

/**
 * Hook for managing search analytics tracking and data
 */
export function useSearchAnalytics() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [state, setState] = useState<SearchAnalyticsState>({
    currentSessionId: null,
    queryHistory: [],
    isTracking: true
  });

  // Initialize session when user logs in or component mounts
  useEffect(() => {
    if (user && state.isTracking && !state.currentSessionId) {
      const sessionId = searchAnalytics.generateSessionId();
      setState(prev => ({ ...prev, currentSessionId: sessionId }));
      
      // Create session in database
      searchAnalytics.createSession(user.id, sessionId, {
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        path: window.location.pathname
      }).catch(console.error);
    }
  }, [user, state.isTracking, state.currentSessionId]);

  // Get user analytics data
  const {
    data: analytics,
    isLoading: analyticsLoading,
    error: analyticsError,
    refetch: refetchAnalytics
  } = useQuery({
    queryKey: ['search-analytics', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;
      const { data, error } = await searchAnalytics.getUserAnalytics(user.id, 30);
      if (error) throw error;
      return data;
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });

  // Get query history
  const {
    data: queryHistory,
    isLoading: historyLoading,
    refetch: refetchHistory
  } = useQuery({
    queryKey: ['search-query-history', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const { data, error } = await searchAnalytics.getQueries(user.id, 50);
      if (error) throw error;
      return data || [];
    },
    enabled: !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  });

  // Get popular search terms
  const {
    data: popularTerms,
    isLoading: popularLoading
  } = useQuery({
    queryKey: ['popular-search-terms'],
    queryFn: async () => {
      const { data, error } = await searchAnalytics.getPopularTerms('all_time', 20);
      if (error) throw error;
      return data || [];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  });

  // Get search interactions
  const {
    data: interactions,
    isLoading: interactionsLoading,
    refetch: refetchInteractions
  } = useQuery({
    queryKey: ['search-interactions', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const { data, error } = await searchAnalytics.getInteractions(user.id, 100);
      if (error) throw error;
      return data || [];
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });

  // Record search query mutation
  const recordQueryMutation = useMutation({
    mutationFn: async ({
      queryText,
      filtersApplied = {},
      sortOptions = {},
      searchSource = 'manual'
    }: {
      queryText: string;
      filtersApplied?: Record<string, unknown>;
      sortOptions?: Record<string, unknown>;
      searchSource?: 'manual' | 'voice' | 'ai_assisted' | 'autocomplete';
    }) => {
      const { data, error } = await searchAnalytics.recordQuery(
        user?.id || null,
        queryText,
        filtersApplied,
        sortOptions,
        searchSource,
        state.currentSessionId
      );
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['search-query-history', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['search-analytics', user?.id] });
    }
  });

  // Record search results mutation
  const recordResultsMutation = useMutation({
    mutationFn: async ({
      queryId,
      apiSource,
      totalResults,
      resultsShown,
      responseTimeMs,
      cacheHit = false,
      success = true,
      errorMessage = null
    }: {
      queryId: string;
      apiSource: 'igdb' | 'rawg' | 'thegamesdb' | 'cache';
      totalResults: number;
      resultsShown: number;
      responseTimeMs: number;
      cacheHit?: boolean;
      success?: boolean;
      errorMessage?: string | null;
    }) => {
      const { data, error } = await searchAnalytics.recordResults(
        queryId,
        apiSource,
        totalResults,
        resultsShown,
        responseTimeMs,
        cacheHit,
        success,
        errorMessage
      );
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['search-analytics', user?.id] });
    }
  });

  // Record interaction mutation
  const recordInteractionMutation = useMutation({
    mutationFn: async ({
      queryId,
      gameId,
      interactionType,
      interactionPosition = null,
      interactionData = {}
    }: {
      queryId: string;
      gameId?: string | null;
      interactionType: 'click' | 'add_to_library' | 'add_to_wishlist' | 'view_details' | 'copy_link' | 'hover' | 'filter_applied' | 'sort_applied';
      interactionPosition?: number | null;
      interactionData?: Record<string, unknown>;
    }) => {
      const { data, error } = await searchAnalytics.recordInteraction(
        user?.id || null,
        queryId,
        gameId || null,
        interactionType,
        interactionPosition,
        interactionData
      );
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['search-interactions', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['search-analytics', user?.id] });
    }
  });

  // Convenience methods
  const recordSearch = useCallback(async (
    queryText: string,
    options?: {
      filtersApplied?: Record<string, unknown>;
      sortOptions?: Record<string, unknown>;
      searchSource?: 'manual' | 'voice' | 'ai_assisted' | 'autocomplete';
    }
  ) => {
    if (!state.isTracking) return null;
    
    try {
      const queryId = await recordQueryMutation.mutateAsync({
        queryText,
        ...options
      });
      
      // Update popular search terms
      await searchAnalytics.updatePopularTerms(queryText, true);
      
      return queryId;
    } catch (error) {
      console.error('Error recording search:', error);
      return null;
    }
  }, [state.isTracking, recordQueryMutation]);

  const recordSearchResults = useCallback(async (
    queryId: string,
    results: {
      apiSource: 'igdb' | 'rawg' | 'thegamesdb' | 'cache';
      totalResults: number;
      resultsShown: number;
      responseTimeMs: number;
      cacheHit?: boolean;
      success?: boolean;
      errorMessage?: string | null;
    }
  ) => {
    if (!state.isTracking) return;
    
    try {
      await recordResultsMutation.mutateAsync({
        queryId,
        ...results
      });
    } catch (error) {
      console.error('Error recording search results:', error);
    }
  }, [state.isTracking, recordResultsMutation]);

  const recordInteraction = useCallback(async (
    queryId: string,
    interaction: {
      gameId?: string;
      interactionType: 'click' | 'add_to_library' | 'add_to_wishlist' | 'view_details' | 'copy_link' | 'hover' | 'filter_applied' | 'sort_applied';
      interactionPosition?: number;
      interactionData?: Record<string, unknown>;
    }
  ) => {
    if (!state.isTracking) return;
    
    try {
      await recordInteractionMutation.mutateAsync({
        queryId,
        ...interaction
      });
    } catch (error) {
      console.error('Error recording interaction:', error);
    }
  }, [state.isTracking, recordInteractionMutation]);

  const toggleTracking = useCallback((enabled: boolean) => {
    setState(prev => ({ ...prev, isTracking: enabled }));
  }, []);

  const endSession = useCallback(async (outcome: 'successful' | 'abandoned' | 'partial' = 'successful') => {
    if (state.currentSessionId) {
      try {
        await searchAnalytics.updateSession(state.currentSessionId, {
          end_time: new Date().toISOString(),
          session_outcome: outcome,
          total_queries: queryHistory?.length || 0,
          total_interactions: interactions?.length || 0
        });
        
        setState(prev => ({ ...prev, currentSessionId: null }));
      } catch (error) {
        console.error('Error ending search session:', error);
      }
    }
  }, [state.currentSessionId, queryHistory?.length, interactions?.length]);

  // Auto-end session on page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (state.currentSessionId) {
        endSession('abandoned');
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [state.currentSessionId, endSession]);

  return {
    // Data
    analytics: analytics as SearchAnalytics | null,
    queryHistory: queryHistory || [],
    popularTerms: popularTerms || [],
    interactions: interactions || [],
    
    // Loading states
    isLoading: analyticsLoading || historyLoading || popularLoading || interactionsLoading,
    analyticsLoading,
    historyLoading,
    popularLoading,
    interactionsLoading,
    
    // Mutation states
    isRecordingQuery: recordQueryMutation.isPending,
    isRecordingResults: recordResultsMutation.isPending,
    isRecordingInteraction: recordInteractionMutation.isPending,
    
    // Errors
    error: analyticsError,
    
    // Methods
    recordSearch,
    recordSearchResults,
    recordInteraction,
    toggleTracking,
    endSession,
    refetchAnalytics,
    refetchHistory,
    refetchInteractions,
    
    // State
    isTracking: state.isTracking,
    currentSessionId: state.currentSessionId
  };
}