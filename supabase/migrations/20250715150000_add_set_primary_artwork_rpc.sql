-- Add RPC function for setting primary artwork atomically
-- This ensures proper transaction handling and cache invalidation timing

CREATE OR REPLACE FUNCTION set_primary_artwork(
  artwork_id uuid,
  user_game_id uuid,
  artwork_type text
) RETURNS void AS $$
BEGIN
  -- First, unset all primary artwork for this type and user_game
  UPDATE user_game_artwork 
  SET is_primary = false 
  WHERE user_game_artwork.user_game_id = set_primary_artwork.user_game_id 
    AND user_game_artwork.artwork_type = set_primary_artwork.artwork_type;
  
  -- Then set the specified artwork as primary
  UPDATE user_game_artwork 
  SET is_primary = true 
  WHERE id = artwork_id;
  
  -- Verify the update was successful
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Artwork with ID % not found', artwork_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION set_primary_artwork TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION set_primary_artwork IS 'Atomically sets artwork as primary for its type, ensuring only one primary artwork per type per user game';