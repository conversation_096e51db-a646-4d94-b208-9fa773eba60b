import { GameSearch } from '@/components/ui/filters/GameSearch';
import { Game } from '@/types';

interface SearchInterfaceProps {
  onSearchResults: (games: Game[]) => void;
  onLoading: (loading: boolean) => void;
  onError: (error: string | null) => void;
  platformFilters: string[];
}

export function SearchInterface({ 
  onSearchResults, 
  onLoading, 
  onError, 
  platformFilters 
}: SearchInterfaceProps) {
  return (
    <div className="bg-gradient-to-br from-primary/5 via-background to-blue-500/5 border border-border rounded-lg p-6 overflow-visible">
      <GameSearch
        onSearchResults={onSearchResults}
        onLoading={onLoading}
        onError={onError}
        platformFilters={platformFilters}
      />
    </div>
  );
}