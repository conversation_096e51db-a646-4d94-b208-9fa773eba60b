/**
 * Error Analytics Service
 * Tracks, analyzes, and reports application errors and performance issues
 */

// Error context interface for better typing
interface ErrorContext {
  endpoint?: string;
  query?: string;
  action?: string;
  error?: string | Error;
  [key: string]: unknown;
}

// API Error interface
interface ApiError {
  message?: string;
  status?: number;
  statusText?: string;
  code?: string;
}

// Database Error interface
interface DatabaseError {
  message?: string;
  code?: string;
  detail?: string;
  hint?: string;
}

// Generic Error interface for unknown error types
interface GenericError {
  message?: string;
  name?: string;
  stack?: string;
  code?: string | number;
  [key: string]: unknown;
}

export interface ErrorEntry {
  id: string;
  type: string;
  message: string;
  stack?: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  url?: string;
  userAgent?: string;
  context?: ErrorContext;
  resolved?: boolean;
}

export interface ErrorAnalytics {
  total: number;
  rate: number;
  byType: Record<string, number>;
  bySeverity: Record<string, number>;
  recent: ErrorEntry[];
  trending: {
    type: string;
    count: number;
    change: number;
  }[];
}

class ErrorAnalyticsService {
  private errors: ErrorEntry[] = [];
  private maxErrors = 1000; // Keep last 1000 errors in memory
  private errorCounts: Map<string, number> = new Map();

  constructor() {
    // Set up global error handlers
    this.setupGlobalErrorHandling();
    
    // Load persisted errors from localStorage
    this.loadPersistedErrors();
  }

  private setupGlobalErrorHandling() {
    // Handle unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.logError({
        type: 'javascript_error',
        message: event.message,
        stack: event.error?.stack,
        severity: 'high',
        url: event.filename,
        context: {
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        type: 'unhandled_rejection',
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack,
        severity: 'high',
        context: {
          reason: event.reason
        }
      });
    });
  }

  private loadPersistedErrors() {
    try {
      const stored = localStorage.getItem('codexa_error_analytics');
      if (stored) {
        const data = JSON.parse(stored);
        this.errors = (data.errors || []).slice(-100); // Load last 100 errors
        
        // Rebuild error counts
        this.rebuildErrorCounts();
      }
    } catch (error) {
      console.warn('Could not load persisted error analytics:', error);
    }
  }

  private persistErrors() {
    try {
      const data = {
        errors: this.errors.slice(-100), // Only persist last 100 errors
        lastUpdated: new Date().toISOString()
      };
      
      localStorage.setItem('codexa_error_analytics', JSON.stringify(data));
    } catch (error) {
      console.warn('Could not persist error analytics:', error);
    }
  }

  private rebuildErrorCounts() {
    this.errorCounts.clear();
    this.errors.forEach(error => {
      const count = this.errorCounts.get(error.type) || 0;
      this.errorCounts.set(error.type, count + 1);
    });
  }

  logError(errorData: Partial<ErrorEntry> & { type: string; message: string }) {
    const error: ErrorEntry = {
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      severity: 'medium',
      userId: this.getCurrentUserId(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      resolved: false,
      ...errorData
    };

    // Add to errors array
    this.errors.push(error);
    
    // Maintain size limit
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors);
    }

    // Update error counts
    const count = this.errorCounts.get(error.type) || 0;
    this.errorCounts.set(error.type, count + 1);

    // Persist to localStorage
    this.persistErrors();

    // Send to external service if configured
    this.sendToExternalService(error);

    console.error(`[Error Analytics] ${error.type}: ${error.message}`, error);
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getCurrentUserId(): string | undefined {
    // This would integrate with your auth system
    try {
      const user = JSON.parse(localStorage.getItem('sb-kfzwgkzvlbyxotnbhgqk-auth-token') || '{}');
      return user?.user?.id;
    } catch {
      return undefined;
    }
  }

  private sendToExternalService(error: ErrorEntry) {
    // Send error to external monitoring service (e.g., Sentry, LogRocket)
    // Using beacon API for reliability even on page unload
    if (navigator.sendBeacon && error.severity === 'critical') {
      const errorData = JSON.stringify({
        ...error,
        source: 'codexa_frontend',
        environment: process.env.NODE_ENV
      });

      try {
        navigator.sendBeacon('/api/errors', errorData);
      } catch {
        // Fallback to fetch if beacon fails
        fetch('/api/errors', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: errorData
        }).catch(() => {
          // Silent fail for error reporting
        });
      }
    }
  }

  async getAnalytics(timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<ErrorAnalytics> {
    const now = new Date();
    const cutoff = new Date();
    
    switch (timeframe) {
      case 'hour':
        cutoff.setHours(now.getHours() - 1);
        break;
      case 'day':
        cutoff.setDate(now.getDate() - 1);
        break;
      case 'week':
        cutoff.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoff.setMonth(now.getMonth() - 1);
        break;
    }

    const recentErrors = this.errors.filter(error => 
      new Date(error.timestamp) > cutoff
    );

    // Calculate error types
    const byType: Record<string, number> = {};
    const bySeverity: Record<string, number> = {};
    
    recentErrors.forEach(error => {
      byType[error.type] = (byType[error.type] || 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
    });

    // Calculate trending errors (compare with previous period)
    const previousCutoff = new Date(cutoff);
    switch (timeframe) {
      case 'hour':
        previousCutoff.setHours(previousCutoff.getHours() - 1);
        break;
      case 'day':
        previousCutoff.setDate(previousCutoff.getDate() - 1);
        break;
      case 'week':
        previousCutoff.setDate(previousCutoff.getDate() - 7);
        break;
      case 'month':
        previousCutoff.setMonth(previousCutoff.getMonth() - 1);
        break;
    }

    const previousErrors = this.errors.filter(error => {
      const errorDate = new Date(error.timestamp);
      return errorDate > previousCutoff && errorDate <= cutoff;
    });

    const previousByType: Record<string, number> = {};
    previousErrors.forEach(error => {
      previousByType[error.type] = (previousByType[error.type] || 0) + 1;
    });

    const trending = Object.entries(byType).map(([type, count]) => ({
      type,
      count,
      change: count - (previousByType[type] || 0)
    })).sort((a, b) => b.change - a.change);

    // Calculate error rate (errors per total requests/actions)
    const totalActions = this.estimateTotalActions(timeframe);
    const errorRate = totalActions > 0 ? (recentErrors.length / totalActions) * 100 : 0;

    return {
      total: recentErrors.length,
      rate: errorRate,
      byType,
      bySeverity,
      recent: recentErrors.slice(-20).reverse(), // Last 20 errors, most recent first
      trending: trending.slice(0, 5)
    };
  }

  private estimateTotalActions(timeframe: string): number {
    // This is a rough estimation - in a real app you'd track actual user actions
    const baseActionsPerHour = 100;
    
    switch (timeframe) {
      case 'hour': return baseActionsPerHour;
      case 'day': return baseActionsPerHour * 24;
      case 'week': return baseActionsPerHour * 24 * 7;
      case 'month': return baseActionsPerHour * 24 * 30;
      default: return 100;
    }
  }

  getErrorById(id: string): ErrorEntry | undefined {
    return this.errors.find(error => error.id === id);
  }

  markErrorAsResolved(id: string): boolean {
    const error = this.getErrorById(id);
    if (error) {
      error.resolved = true;
      this.persistErrors();
      return true;
    }
    return false;
  }

  clearErrors(olderThan?: Date) {
    if (olderThan) {
      this.errors = this.errors.filter(error => 
        new Date(error.timestamp) > olderThan
      );
    } else {
      this.errors = [];
    }
    
    this.rebuildErrorCounts();
    this.persistErrors();
  }

  // Convenience methods for common error types
  logApiError(endpoint: string, error: ApiError | Error | string, context?: ErrorContext) {
    this.logError({
      type: 'api_error',
      message: `API call failed: ${endpoint}`,
      severity: 'medium',
      context: {
        endpoint,
        error: typeof error === 'string' ? error : (error as Error).message || String(error),
        ...context
      }
    });
  }

  logDatabaseError(query: string, error: DatabaseError | Error | string) {
    this.logError({
      type: 'database_error',
      message: `Database query failed: ${typeof error === 'string' ? error : (error as Error).message || String(error)}`,
      severity: 'high',
      context: { query }
    });
  }

  logAuthError(message: string, context?: ErrorContext) {
    this.logError({
      type: 'auth_error',
      message,
      severity: 'high',
      context
    });
  }

  logUserActionError(action: string, error: GenericError | Error | string, context?: ErrorContext) {
    this.logError({
      type: 'user_action_error',
      message: `User action failed: ${action}`,
      severity: 'medium',
      context: {
        action,
        error: typeof error === 'string' ? error : (error as Error).message || String(error),
        ...context
      }
    });
  }

  // Get error statistics for monitoring
  getErrorStats() {
    const last24Hours = this.errors.filter(error => 
      new Date(error.timestamp) > new Date(Date.now() - 24 * 60 * 60 * 1000)
    );

    return {
      total: this.errors.length,
      last24Hours: last24Hours.length,
      byType: Object.fromEntries(this.errorCounts),
      averagePerHour: last24Hours.length / 24,
      criticalErrors: this.errors.filter(error => error.severity === 'critical').length
    };
  }
}

export const errorAnalyticsService = new ErrorAnalyticsService();