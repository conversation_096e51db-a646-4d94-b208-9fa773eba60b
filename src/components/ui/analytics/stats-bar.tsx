import { Database, Gamepad2, Star, Calendar, Hash } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Game } from '@/types';

interface StatsBarProps {
  games: Game[];
}

export function StatsBar({ games }: StatsBarProps) {
  if (games.length === 0) return null;

  const stats = {
    totalGames: games.length,
    avgRating: games
      .filter(g => g.metacritic_score)
      .reduce((acc, g) => acc + (g.metacritic_score || 0), 0) / 
      games.filter(g => g.metacritic_score).length || 0,
    platforms: [...new Set(games.flatMap(g => g.platforms || []))].length,
    genres: [...new Set(games.flatMap(g => g.genres || []))].length,
    latestYear: Math.max(...games
      .map(g => g.release_date ? new Date(g.release_date).getFullYear() : 0)
      .filter(year => year > 0)
    ),
    ratedGames: games.filter(g => g.metacritic_score).length
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-6 animate-stagger-fade">
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 via-secondary/5 to-accent/5 shadow-2xl shadow-primary/5 hover:shadow-primary/10 transition-all duration-300">
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {/* Total Games */}
            <div className="text-center group hover:scale-105 transition-all duration-200">
              <div className="flex justify-center mb-2">
                <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-all duration-200 group-hover:shadow-lg">
                  <Gamepad2 className="h-5 w-5 text-primary group-hover:animate-float" />
                </div>
              </div>
              <div className="text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-200">{stats.totalGames}</div>
              <div className="text-sm text-muted-foreground group-hover:text-primary/80 transition-colors duration-200">Total Games</div>
            </div>

            {/* IGDB Source */}
            <div className="text-center group hover:scale-105 transition-all duration-200">
              <div className="flex justify-center mb-2">
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200 group-hover:shadow-lg transition-all duration-200">
                  <Database className="h-3 w-3 mr-1 group-hover:animate-float" />
                  IGDB
                </Badge>
              </div>
              <div className="text-2xl font-bold text-foreground group-hover:text-secondary transition-colors duration-200">{stats.totalGames}</div>
              <div className="text-sm text-muted-foreground group-hover:text-secondary/80 transition-colors duration-200">From IGDB</div>
            </div>

            {/* Average Rating */}
            {stats.avgRating > 0 && (
              <div className="text-center">
                <div className="flex justify-center mb-2">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Star className="h-5 w-5 text-yellow-600" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-foreground">{Math.round(stats.avgRating)}</div>
                <div className="text-sm text-muted-foreground">Avg Rating</div>
              </div>
            )}

            {/* Platforms */}
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Database className="h-5 w-5 text-green-600" />
                </div>
              </div>
              <div className="text-2xl font-bold text-foreground">{stats.platforms}</div>
              <div className="text-sm text-muted-foreground">Platforms</div>
            </div>

            {/* Genres */}
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Hash className="h-5 w-5 text-orange-600" />
                </div>
              </div>
              <div className="text-2xl font-bold text-foreground">{stats.genres}</div>
              <div className="text-sm text-muted-foreground">Genres</div>
            </div>

            {/* Latest Year */}
            {stats.latestYear > 0 && (
              <div className="text-center">
                <div className="flex justify-center mb-2">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Calendar className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-foreground">{stats.latestYear}</div>
                <div className="text-sm text-muted-foreground">Latest Year</div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
