import { useState, useCallback } from 'react';
import { Game } from '@/types';

export interface SearchDataState {
  allGames: Game[];
  loading: boolean;
  error: string | null;
}

export interface SearchDataActions {
  handleSearchResults: (games: Game[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  resetSearchData: () => void;
}

export interface UseSearchDataReturn extends SearchDataState, SearchDataActions {}

const initialState: SearchDataState = {
  allGames: [],
  loading: false,
  error: null,
};

export function useSearchData(): UseSearchDataReturn {
  const [allGames, setAllGames] = useState<Game[]>(initialState.allGames);
  const [loading, setLoading] = useState<boolean>(initialState.loading);
  const [error, setError] = useState<string | null>(initialState.error);

  const handleSearchResults = useCallback((games: Game[]) => {
    setAllGames(games);
    setError(null); // Clear any previous errors when new results come in
  }, []);

  const resetSearchData = useCallback(() => {
    setAllGames(initialState.allGames);
    setLoading(initialState.loading);
    setError(initialState.error);
  }, []);

  return {
    // State
    allGames,
    loading,
    error,
    // Actions
    handleSearchResults,
    setLoading,
    setError,
    resetSearchData,
  };
}