import React from 'react';
import { Avatar } from '@/components/ui/base/avatar';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { 
  Copy, 
  ExternalLink,
  Image as ImageIcon,
  Clock,
  CheckCircle
} from 'lucide-react';
import { ChatMessage } from '@/lib/enhancedAIService';
import { cn } from '@/lib/utils';
import { toast } from 'react-hot-toast';

interface MessageBubbleProps {
  message: ChatMessage;
  isLast?: boolean;
  isLoading?: boolean;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isLast = false,
  isLoading = false
}) => {
  const isUser = message.role === 'user';
  const isSystem = message.role === 'system';

  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content);
    toast.success('Message copied to clipboard');
  };

  const formatTimestamp = (timestamp: Date) => {
    try {
      // Ensure timestamp is a valid Date object
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
      
      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid time';
      }
      
      return new Intl.DateTimeFormat('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }).format(date);
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'Invalid time';
    }
  };

  // Parse URLs and make them clickable
  const formatContent = (content: string) => {
    // Simple URL detection and replacement
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return content.replace(urlRegex, (url) => {
      return `[🔗 ${url}](${url})`;
    });
  };

  // Parse markdown-style links
  const renderContent = (content: string) => {
    const formattedContent = formatContent(content);
    
    // Split by markdown links [text](url)
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = linkRegex.exec(formattedContent)) !== null) {
      // Add text before the link
      if (match.index > lastIndex) {
        parts.push(formattedContent.slice(lastIndex, match.index));
      }
      
      // Add the clickable link
      parts.push(
        <a
          key={match.index}
          href={match[2]}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-500 hover:text-blue-600 underline inline-flex items-center gap-1"
        >
          {match[1]}
          <ExternalLink className="h-3 w-3" />
        </a>
      );
      
      lastIndex = linkRegex.lastIndex;
    }
    
    // Add remaining text
    if (lastIndex < formattedContent.length) {
      parts.push(formattedContent.slice(lastIndex));
    }
    
    return parts.length > 0 ? parts : formattedContent;
  };

  if (isSystem) {
    return (
      <div className="flex justify-center my-2">
        <Badge variant="outline" className="text-xs">
          {message.content}
        </Badge>
      </div>
    );
  }

  return (
    <div className={cn(
      "flex gap-3 max-w-full",
      isUser ? "flex-row-reverse" : "flex-row"
    )}>
      <Avatar 
        fallback={isUser ? "U" : "AI"}
        className={cn(
          "h-8 w-8 flex-shrink-0 flex items-center justify-center rounded-full text-xs font-medium",
          isUser ? "bg-blue-500 text-white" : "bg-green-500 text-white"
        )}
        size="sm"
      />

      <div className={cn(
        "flex flex-col gap-1 min-w-0 flex-1",
        isUser ? "items-end" : "items-start"
      )}>
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>{isUser ? 'You' : 'AI Assistant'}</span>
          <Clock className="h-3 w-3" />
          <span>{formatTimestamp(message.timestamp)}</span>
          {message.metadata?.boxArtSearch && (
            <Badge variant="outline" className="text-xs">
              <ImageIcon className="h-3 w-3 mr-1" />
              Box Art Search
            </Badge>
          )}
          {message.metadata?.gameContext && (
            <Badge variant="outline" className="text-xs">
              🎮 {message.metadata.gameContext}
            </Badge>
          )}
        </div>

        <div className={cn(
          "rounded-lg px-3 py-2 max-w-[85%] break-words",
          isUser 
            ? "bg-blue-500 text-white" 
            : "bg-muted border",
          isLoading && "animate-pulse"
        )}>
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-current rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              </div>
              <span className="text-sm opacity-70">AI is thinking...</span>
            </div>
          ) : (
            <div className="text-sm whitespace-pre-wrap">
              {renderContent(message.content)}
            </div>
          )}
        </div>

        {!isLoading && (
          <div className="flex items-center gap-1 mt-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={copyToClipboard}
              className="h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <Copy className="h-3 w-3" />
            </Button>
            {isLast && !isUser && (
              <CheckCircle className="h-3 w-3 text-green-500" />
            )}
          </div>
        )}
      </div>
    </div>
  );
};