import { exit } from 'node:process';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Initialize Supabase client with environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
  console.error('Missing Supabase URL (VITE_SUPABASE_URL) in environment variables');
  exit(1);
}

if (!supabaseAnonKey) {
  console.error('Missing Supabase Anon Key (VITE_SUPABASE_ANON_KEY) in environment variables');
  exit(1);
}

if (!serviceRoleKey) {
  console.error('Missing Supabase Service Role Key (SUPABASE_SERVICE_ROLE_KEY) in environment variables');
  exit(1);
}

// Create regular client
// We only need the admin client with service role key

// Create admin client with service role key
const adminSupabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function cleanDatabase() {
  try {
    console.log('Starting COMPLETE database cleanup with admin privileges...');

    // Comprehensive list of tables to clean
    const allTables = [
      // Core game data
      'games',
      'game_platforms',
      'game_genres',
      'game_developers',
      'game_publishers',
      'game_tags',
      
      // User data
      'user_profiles',
      'user_preferences',
      'user_games',
      'user_collection_stats',
      'user_recommendations',
      'user_tags',
      'user_game_tags',
      
      // Price tracking
      'price_tracking',
      'price_alerts',
      'price_history',
      
      // Filters and presets
      'filter_presets',
      'search_history',
      
      // Analytics
      'user_activity',
      'search_analytics',
      
      // Import data
      'import_history',
      'platform_connections',
      
      // Artwork and media
      'custom_artwork',
      'media_links',
      
      // Collections
      'collections',
      'collection_games',
      
      // Any other tables that might exist
      'api_keys',
      'notifications',
      'feedback',
      'reports'
    ];

    console.log(`Attempting to clean ${allTables.length} tables with admin privileges`);

    // Try multiple approaches for each table
    for (const tableName of allTables) {
      try {
        console.log(`Cleaning table: ${tableName}`);
        
        // First check if table exists
        const { error: selectError } = await adminSupabase
          .from(tableName)
          .select('*')
          .limit(1);
          
        if (selectError && selectError.code === '42P01') {
          // Table doesn't exist
          console.log(`Table ${tableName} doesn't exist, skipping`);
          continue;
        }
        
        // Try to delete all rows with admin privileges and various strategies
        
        // Strategy 1: Delete with not-null filter on id
        const { error } = await adminSupabase
          .from(tableName)
          .delete()
          .not('id', 'is', null);
        
        if (!error) {
          console.log(`Successfully cleaned table ${tableName} using not-null filter`);
          continue;
        }
        
        // Strategy 2: Delete with not-null filter on user_id
        const { error: error2 } = await adminSupabase
          .from(tableName)
          .delete()
          .not('user_id', 'is', null);
          
        if (!error2) {
          console.log(`Successfully cleaned table ${tableName} using user_id filter`);
          continue;
        }
        
        // Strategy 3: Delete with date filter
        const { error: error3 } = await adminSupabase
          .from(tableName)
          .delete()
          .gte('created_at', '1900-01-01');
          
        if (!error3) {
          console.log(`Successfully cleaned table ${tableName} using date filter`);
          continue;
        }
        
        // Strategy 4: Try with a true condition
        const { error: error4 } = await adminSupabase
          .from(tableName)
          .delete()
          .eq('1', '1');
          
        if (!error4) {
          console.log(`Successfully cleaned table ${tableName} using true condition`);
          continue;
        }
        
        // Strategy 5: Try with RPC call to execute raw SQL (if available)
        try {
          const { error: rpcError } = await adminSupabase.rpc('execute_sql', {
            sql: `DELETE FROM "${tableName}";`
          });
          
          if (!rpcError) {
            console.log(`Successfully cleaned table ${tableName} using raw SQL`);
            continue;
          }
        } catch {
          // RPC might not be available, continue with other strategies
        }
        
        // If all strategies fail, log the error
        console.error(`Failed to clean table ${tableName}: ${error?.message}`);
        
      } catch (tableError) {
        console.error(`Error processing table ${tableName}:`, tableError);
      }
    }

    console.log('COMPLETE database cleanup finished');
  } catch (err) {
    console.error('Error during database cleanup:', err);
    exit(1);
  }
}

cleanDatabase();