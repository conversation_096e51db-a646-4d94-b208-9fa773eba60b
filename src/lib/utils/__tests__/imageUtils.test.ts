import { describe, it, expect } from 'vitest';
import { 
  generatePlaceholderImage, 
  generateGameCoverPlaceholder, 
  generateThumbnailPlaceholder,
  getGameCoverWithFallback 
} from '../imageUtils';

describe('imageUtils', () => {
  describe('generatePlaceholderImage', () => {
    it('should generate a valid SVG data URL', () => {
      const result = generatePlaceholderImage();
      expect(result).toMatch(/^data:image\/svg\+xml;base64,/);
    });

    it('should include the provided text', () => {
      const text = 'Test Game';
      const result = generatePlaceholderImage({ text });
      const decoded = atob(result.split(',')[1]);
      expect(decoded).toContain(text);
    });

    it('should sanitize dangerous characters', () => {
      const text = '<script>alert("xss")</script>';
      const result = generatePlaceholderImage({ text });
      const decoded = atob(result.split(',')[1]);
      expect(decoded).not.toContain('<script>');
      expect(decoded).toContain('&lt;script&gt;');
    });

    it('should handle encoding errors gracefully', () => {
      const text = '🎮 Game Title 🎯';
      const result = generatePlaceholderImage({ text });
      expect(result).toMatch(/^data:image\/svg\+xml/);
    });
  });

  describe('generateGameCoverPlaceholder', () => {
    it('should generate box-art placeholder by default', () => {
      const result = generateGameCoverPlaceholder('Test Game');
      const decoded = atob(result.split(',')[1]);
      expect(decoded).toContain('#4a90e2'); // box-art color
    });

    it('should generate cover placeholder when specified', () => {
      const result = generateGameCoverPlaceholder('Test Game', 'cover');
      const decoded = atob(result.split(',')[1]);
      expect(decoded).toContain('#50c878'); // cover color
    });

    it('should truncate long game names', () => {
      const longName = 'This is a very long game name that should be truncated';
      const result = generateGameCoverPlaceholder(longName);
      const decoded = atob(result.split(',')[1]);
      expect(decoded).toContain('This is a very long...');
    });
  });

  describe('generateThumbnailPlaceholder', () => {
    it('should generate a square thumbnail', () => {
      const result = generateThumbnailPlaceholder('Test');
      const decoded = atob(result.split(',')[1]);
      expect(decoded).toContain('width="300"');
      expect(decoded).toContain('height="300"');
    });
  });

  describe('getGameCoverWithFallback', () => {
    it('should return the original URL if valid', () => {
      const url = 'https://example.com/image.jpg';
      const result = getGameCoverWithFallback(url, 'Test Game');
      expect(result).toBe(url);
    });

    it('should return placeholder if URL is null', () => {
      const result = getGameCoverWithFallback(null, 'Test Game');
      expect(result).toMatch(/^data:image\/svg\+xml;base64,/);
    });

    it('should return placeholder if URL is empty', () => {
      const result = getGameCoverWithFallback('', 'Test Game');
      expect(result).toMatch(/^data:image\/svg\+xml;base64,/);
    });
  });
});