# Codexa - AI-Powered Gaming Library Manager

A sophisticated, AI-enhanced web application for discovering, tracking, and managing your video game collection. Built with React, TypeScript, and Supabase, Codexa provides an intelligent gaming library management experience with advanced features and stunning UI/UX design.

## 🚀 Key Features

### 🎮 Library Management
*   **Personal Collection:** Add games to your library with status tracking (Playing, Completed, Backlog)
*   **Wishlist Management:** Track games you want to play with automated price monitoring
*   **Multi-Platform Import:** Import libraries from Steam, Epic Games, PlayStation, Xbox, GOG, and more
*   **CSV Import/Export:** Full data portability for your gaming collection
*   **Advanced Search & Filtering:** Powerful search with genre, platform, developer, and rating filters

### 🤖 AI-Powered Features
*   **AI Gaming Assistant:** Chat with an intelligent assistant about games, get recommendations, and discover new titles
*   **Smart Recommendations:** AI-powered game suggestions based on your collection and preferences
*   **Collection Insights:** AI analysis of your gaming habits and collection health
*   **Box Art Search:** Find high-quality game artwork and covers using AI-powered web search
*   **Multiple AI Providers:** Support for OpenAI, DeepSeek, and Google Gemini

### 💰 Price Tracking & Deals
*   **Automated Price Monitoring:** Track prices across multiple game stores
*   **Deal Notifications:** Get alerts when wishlist games go on sale
*   **Price History:** View historical pricing data and trends
*   **Best Deals Dashboard:** Discover the hottest deals on your wishlist

### 🔍 Game Discovery
*   **Comprehensive Game Database:** Powered by IGDB with detailed game information
*   **Advanced Filtering:** Filter by genre, platform, release date, rating, and more
*   **Enhanced Search:** Smart search with autocomplete and suggestions
*   **Game Details:** Rich game information including screenshots, trailers, and reviews

### 🛠️ Advanced Features
*   **Secure API Key Management:** Encrypted storage for your API keys
*   **Multi-Platform Support:** Works seamlessly across desktop and mobile
*   **Real-time Updates:** Live data synchronization across devices
*   **Dark/Light Theme:** Customizable appearance settings
*   **Performance Optimized:** Fast loading with virtualized lists and lazy loading

## 🛠️ Tech Stack

### Frontend
*   **[React 18](https://reactjs.org/)** - Modern React with hooks and concurrent features
*   **[TypeScript](https://www.typescriptlang.org/)** - Type-safe development
*   **[Vite](https://vitejs.dev/)** - Fast build tool and dev server
*   **[React Router v7](https://reactrouter.com/)** - Client-side routing
*   **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework
*   **[shadcn/ui](https://ui.shadcn.com/)** - Beautiful, accessible UI components
*   **[Lucide React](https://lucide.dev/)** - Consistent icon library
*   **[React Hot Toast](https://react-hot-toast.com/)** - Toast notifications

### Backend & Database
*   **[Supabase](https://supabase.io/)** - Complete backend solution
    *   **Authentication:** User management with JWT tokens and RLS
    *   **PostgreSQL Database:** Structured data storage with advanced indexing
    *   **Storage:** File uploads and media management
    *   **Edge Functions:** Serverless API proxying and AI integration
    *   **Real-time:** Live data synchronization

### External APIs & Services
*   **[IGDB](https://www.igdb.com/api)** - Primary game database and metadata
*   **[TheGamesDB](https://thegamesdb.net/api/)** - Additional game information and artwork
*   **[SteamGridDB](https://www.steamgriddb.com/api/v2)** - Community game artwork and covers
*   **[Steam API](https://steamcommunity.com/dev)** - Steam library integration
*   **[OpenAI](https://openai.com/api/)** - GPT-4 for AI chat features
*   **[Google Gemini](https://ai.google.dev/)** - Alternative AI provider
*   **[DeepSeek](https://deepseek.com/)** - Cost-effective AI provider

### Development Tools
*   **[TanStack Query](https://tanstack.com/query)** - Server state management
*   **[React Hook Form](https://react-hook-form.com/)** - Form handling
*   **[Zod](https://zod.dev/)** - Schema validation
*   **[ESLint](https://eslint.org/)** - Code linting and quality
*   **[React Window](https://github.com/bvaughn/react-window)** - Virtualized lists for performance

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- [Supabase](https://supabase.io/) account and project
- API keys for external services (optional but recommended)

### Installation

1.  **Clone the repository:**
    ```bash
    git clone <your-repo-url>
    cd codexa
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    Create a `.env` file by copying the example:
    ```bash
    cp .env.example .env
    ```
    
    **Required environment variables:**
    ```env
    # Supabase (Required)
    VITE_SUPABASE_URL=your_supabase_url
    VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
    
    # IGDB (Required for game search)
    VITE_IGDB_CLIENT_ID=your_igdb_client_id
    VITE_IGDB_CLIENT_SECRET=your_igdb_client_secret
    ```
    
    **Optional API keys for enhanced features:**
    ```env
    # AI Features
    VITE_OPENAI_API_KEY=your_openai_api_key
    VITE_DEEPSEEK_API_KEY=your_deepseek_api_key
    VITE_GEMINI_API_KEY=your_gemini_api_key

    
    # Additional Game Data
    VITE_THEGAMESDB_API_KEY=your_thegamesdb_api_key
    
    # Platform Imports
    VITE_STEAM_API_KEY=your_steam_api_key
    ```

4.  **Set up the database:**
    ```bash
    # Install Supabase CLI
    npm install -g supabase
    
    # Link to your project
    supabase link --project-ref your-project-ref
    
    # Run migrations
    supabase db push
    ```

5.  **Start the development server:**
    ```bash
    npm run dev
    ```

6.  **Build for production:**
    ```bash
    npm run build
    ```

### Development Commands
```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run lint     # Run ESLint
npm run preview  # Preview production build
```

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # shadcn/ui components and custom UI
│   ├── layout/          # Layout components (Header, Sidebar, etc.)
│   └── ...
├── pages/               # Page components
│   ├── Dashboard/       # Modular dashboard with sub-components
│   │   ├── index.tsx   # Main dashboard component
│   │   ├── components/ # Dashboard-specific components
│   │   ├── hooks/      # Dashboard-specific hooks
│   │   └── types.ts    # Dashboard TypeScript interfaces
│   ├── Search.tsx       # Game search and discovery
│   ├── Library.tsx      # Personal game collection
│   ├── AIAgent.tsx      # AI chat assistant
│   ├── Deals.tsx        # Price tracking and deals
│   └── Settings.tsx     # User preferences and API keys
├── hooks/               # Custom React hooks
├── lib/                 # Utility functions and services
│   ├── supabase.ts     # Database client
│   ├── api.ts          # API utilities
│   ├── aiService.ts    # AI integration
│   └── ...
├── types/              # TypeScript type definitions
├── contexts/           # React context providers
└── ...

supabase/
├── functions/          # Edge Functions for API proxying
├── migrations/         # Database schema migrations
└── ...
```

## 🛡️ Security & API Keys

### API Key Management
Codexa includes a secure API key management system that encrypts and stores your API keys locally. You can manage your keys through the Settings page:

1. **Navigate to Settings → API Keys**
2. **Add your API keys** for the services you want to use
3. **Keys are encrypted** using AES-256 encryption
4. **Stored securely** in Supabase with Row Level Security

### Required API Keys
- **IGDB (Twitch)**: Required for game search and metadata
- **Supabase**: Required for database and authentication

### Optional API Keys (for enhanced features)
- **OpenAI**: For AI chat and recommendations
- **Google Gemini**: Alternative AI provider
- **DeepSeek**: Cost-effective AI provider

- **Steam**: For Steam library import
- **TheGamesDB**: Additional game metadata

### Getting API Keys
- **[IGDB/Twitch API](https://api.igdb.com/)** - Game database access
- **[OpenAI API](https://openai.com/api/)** - AI chat features
- **[Google Gemini API](https://ai.google.dev/)** - Alternative AI provider

- **[Steam API](https://steamcommunity.com/dev)** - Steam library import
- **[TheGamesDB](https://thegamesdb.net/api/)** - Additional game data

## 🎯 Development Guidelines

### Core Development Rules
- **Always run `npm run lint`** and fix TypeScript issues before committing
- **Always use Supabase migrations** for database schema changes
- **Always test on both desktop and mobile** before deploying
- **Always implement proper error handling** and loading states

### UI/UX Standards
- **Use shadcn/ui components** for consistent design system
- **Follow mobile-first responsive design** principles
- **Implement proper loading states** and error boundaries
- **Use Tailwind CSS** for styling with design tokens
- **Ensure accessibility** with proper ARIA labels and keyboard navigation

### Code Quality Standards
- **Follow TypeScript strict mode** rules
- **Use TanStack Query** for server state management
- **Use React Hook Form + Zod** for form validation
- **Write self-documenting code** with clear variable names
- **Implement proper error handling** for all async operations
- **Validate data before rendering** to prevent display of invalid/incomplete records
- **Use modular component architecture** with focused, single-responsibility components

### Database Best Practices
- **Use Row Level Security (RLS)** for all tables
- **Implement proper indexing** for performance
- **Use Supabase helpers** from `src/lib/supabase.ts`
- **Always test migrations** before deploying

## 🏗️ Architecture Overview

Codexa is built using a modern, scalable architecture:

### Frontend Architecture
- **Component-driven development** with shadcn/ui design system
- **TypeScript-first** approach with strict type checking
- **Custom hooks** for business logic separation
- **Context API** for global state management
- **TanStack Query** for server state and caching
- **Virtualized lists** for performance optimization

### Backend Architecture
- **Supabase** as the primary backend service
- **PostgreSQL** with Row Level Security (RLS)
- **Edge Functions** for API proxying and AI integration
- **Real-time subscriptions** for live data updates
- **Encrypted API key storage** with AES-256 encryption

### Data Flow
1. **User Authentication** → Supabase Auth with JWT tokens
2. **Game Search** → IGDB API via Edge Functions
3. **AI Features** → Multiple AI providers (OpenAI, Gemini, DeepSeek)
4. **Price Tracking** → Automated background jobs
5. **Library Import** → Multi-platform import services
6. **Data Storage** → PostgreSQL with optimized indexing

### Performance Optimizations
- **Lazy loading** for components and images
- **Virtualized lists** for large datasets
- **Debounced search** with caching
- **Optimistic updates** for better UX
- **Prefetching** for predictive loading

## 📊 Features in Detail

### AI Gaming Assistant
- **Multi-Provider Support**: OpenAI, Google Gemini, and DeepSeek
- **Contextual Conversations**: Remembers your gaming preferences
- **Game Recommendations**: Personalized suggestions based on your library
- **Box Art Search**: Find high-quality game artwork
- **Natural Language Processing**: Understand gaming-related queries

### Price Tracking System
- **Automated Monitoring**: Tracks prices across multiple stores
- **Smart Alerts**: Notifies when wishlist games go on sale
- **Historical Data**: View price trends over time
- **Deal Discovery**: Find the best deals automatically
- **Custom Price Targets**: Set your own price alerts

### Multi-Platform Import
- **Steam Integration**: Import your entire Steam library
- **Epic Games Store**: Sync your Epic collection
- **PlayStation Network**: Import PlayStation games
- **Xbox Live**: Sync Xbox achievements and games
- **GOG Galaxy**: Import DRM-free games
- **Batch Processing**: Import multiple platforms simultaneously

### Collection Insights
- **AI Analysis**: Understand your gaming patterns
- **Completion Rates**: Track your backlog progress
- **Genre Distribution**: Visualize your gaming preferences
- **Playtime Estimates**: Smart recommendations for what to play next
- **Collection Health**: Optimize your gaming library

## 🔒 Security & Privacy

### Data Protection
- **End-to-End Encryption**: API keys encrypted with AES-256
- **Row Level Security**: Database access controlled per user
- **HTTPS Only**: All communications encrypted in transit
- **No Data Selling**: Your gaming data stays private
- **GDPR Compliant**: Full data export and deletion capabilities

### API Key Security
- **Local Encryption**: Keys encrypted before storage
- **Secure Transport**: All API calls proxied through Edge Functions
- **Rate Limiting**: Prevents abuse and reduces costs
- **Key Rotation**: Easy to update and rotate keys
- **Audit Logging**: Track API usage and access

### Privacy Features
- **Private by Default**: Collections are private unless shared
- **Data Portability**: Export your data anytime
- **Selective Sharing**: Choose what to share publicly
- **Anonymous Usage**: No tracking or analytics
- **Data Minimization**: Only collect necessary information

⚠️ **Security Issue Reporting**: If you discover a security vulnerability, please report it responsibly by contacting the maintainers directly rather than opening a public issue.

## 🤝 Contributing

We welcome contributions to Codexa! Please follow these guidelines:

### Getting Started
1. **Fork the repository** and create a feature branch
2. **Set up your development environment** following the setup instructions
3. **Make your changes** following the development guidelines
4. **Test thoroughly** on both desktop and mobile
5. **Submit a pull request** with a clear description

### Development Process
- **Follow TypeScript strict mode** rules
- **Use the established code style** and component patterns
- **Write meaningful commit messages**
- **Test your changes** before submitting
- **Update documentation** when needed

### Code Review Process
- **All PRs require review** from maintainers
- **Ensure all tests pass** and linting is clean
- **Address feedback promptly** and professionally
- **Squash commits** when merging

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🌟 Acknowledgments

- **[IGDB](https://www.igdb.com/)** for comprehensive game database
- **[Supabase](https://supabase.io/)** for the amazing backend platform
- **[shadcn/ui](https://ui.shadcn.com/)** for beautiful UI components
- **[OpenAI](https://openai.com/)** for AI capabilities
- **[Tailwind CSS](https://tailwindcss.com/)** for the utility-first CSS framework

---

**Built with ❤️ for gamers, by gamers**
