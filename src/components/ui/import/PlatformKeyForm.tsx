/**
 * Platform Key Form Component
 * Form for adding/editing platform API keys
 */

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Input } from '@/components/ui/base/input';
import { Label } from '@/components/ui/base/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/base/select';
import { Alert, AlertDescription } from '@/components/ui/base/alert';
import { Badge } from '@/components/ui/base/badge';
import { 
  Key, 
  Save, 
  X, 
  Eye, 
  EyeOff, 
  AlertTriangle, 
  Info,
  ExternalLink,
  Copy,
  Check
} from 'lucide-react';
import { useApiKeys, usePlatformInfo } from '@/hooks/useApiKeys';
import { type Platform, type KeyType, type ApiKeyData } from '@/lib/apiKeyStore';
import toast from 'react-hot-toast';

// Form validation schema
const apiKeyFormSchema = z.object({
  platform: z.string().min(1, 'Platform is required'),
  keyType: z.string().min(1, 'Key type is required'),
  value: z.string().min(1, 'API key value is required'),
  label: z.string().optional(),
});

type ApiKeyFormData = z.infer<typeof apiKeyFormSchema>;

interface PlatformKeyFormProps {
  onSuccess: () => void;
  onCancel: () => void;
  initialData?: {
    platform: Platform;
    keyType: KeyType;
    value?: string;
  };
}

// Platform setup instructions
const PLATFORM_INSTRUCTIONS: Record<Platform, { url: string; description: string }> = {
  steam: {
    url: 'https://steamcommunity.com/dev/apikey',
    description: 'Register for a Steam Web API key to access your Steam library data.',
  },
  epic: {
    url: 'https://dev.epicgames.com/portal/',
    description: 'Create an Epic Games developer account and register an application for API access.',
  },
  xbox: {
    url: 'https://docs.microsoft.com/en-us/gaming/xbox-live/get-started/',
    description: 'Register an application in Azure AD to access Xbox Live APIs.',
  },
  playstation: {
    url: 'https://partners.playstation.net/',
    description: 'Apply for PlayStation Network developer access through Sony Partner Portal.',
  },
  gog: {
    url: 'https://www.gog.com/galaxy',
    description: 'Access GOG Galaxy API through their developer program.',
  },
  origin: {
    url: 'https://www.ea.com/developers',
    description: 'Register for EA Origin API access through their developer portal.',
  },
  battlenet: {
    url: 'https://develop.battle.net/',
    description: 'Create a Battle.net API application for Blizzard game data.',
  },
  nintendo: {
    url: 'https://developer.nintendo.com/',
    description: 'Apply for Nintendo Developer Account for limited API access.',
  },
  ubisoft: {
    url: 'https://developers.ubisoft.com/',
    description: 'Register for Ubisoft Connect API access.',
  },
};

export function PlatformKeyForm({ onSuccess, onCancel, initialData }: PlatformKeyFormProps) {
  const { storeKey, isStoring } = useApiKeys();
  const { availablePlatforms, getPlatformLabel, getPlatformKeys } = usePlatformInfo();
  const [showValue, setShowValue] = useState(false);
  const [copied, setCopied] = useState(false);

  const form = useForm<ApiKeyFormData>({
    resolver: zodResolver(apiKeyFormSchema),
    defaultValues: {
      platform: initialData?.platform || '',
      keyType: initialData?.keyType || '',
      value: initialData?.value || '',
      label: '',
    },
  });

  const watchedPlatform = form.watch('platform') as Platform;
  const availableKeys = watchedPlatform ? getPlatformKeys(watchedPlatform) : [];

  const handleSubmit = async (data: ApiKeyFormData) => {
    try {
      const keyData: ApiKeyData = {
        platform: data.platform as Platform,
        keyType: data.keyType as KeyType,
        value: data.value.trim(),
        label: data.label?.trim() || undefined,
      };

      await storeKey(keyData);
      onSuccess();
    } catch {
      // Error handling is done in the hook
      console.error('Failed to store key');
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      toast.success('Copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch {
      toast.error('Failed to copy to clipboard');
    }
  };

  const platformInstructions = watchedPlatform ? PLATFORM_INSTRUCTIONS[watchedPlatform] : null;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          {initialData ? 'Edit API Key' : 'Add API Key'}
        </CardTitle>
        <CardDescription>
          Securely store your platform API keys for library imports and enhanced features.
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Platform Selection */}
          <div className="space-y-2">
            <Label htmlFor="platform">Platform *</Label>
            <Select
              value={form.watch('platform')}
              onValueChange={(value) => {
                form.setValue('platform', value);
                form.setValue('keyType', ''); // Reset key type when platform changes
              }}
              disabled={!!initialData?.platform}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a gaming platform" />
              </SelectTrigger>
              <SelectContent>
                {availablePlatforms.map((platform) => (
                  <SelectItem key={platform} value={platform}>
                    {getPlatformLabel(platform)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.platform && (
              <p className="text-sm text-red-500">{form.formState.errors.platform.message}</p>
            )}
          </div>

          {/* Key Type Selection */}
          {watchedPlatform && (
            <div className="space-y-2">
              <Label htmlFor="keyType">Key Type *</Label>
              <Select
                value={form.watch('keyType')}
                onValueChange={(value) => form.setValue('keyType', value)}
                disabled={!!initialData?.keyType}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select key type" />
                </SelectTrigger>
                <SelectContent>
                  {availableKeys.map((key: { type: string; label: string; required: boolean }) => (
                    <SelectItem key={key.type} value={key.type}>
                      <div className="flex items-center gap-2">
                        {key.label}
                        {key.required && <Badge variant="secondary" className="text-xs">Required</Badge>}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.keyType && (
                <p className="text-sm text-red-500">{form.formState.errors.keyType.message}</p>
              )}
            </div>
          )}

          {/* API Key Value */}
          <div className="space-y-2">
            <Label htmlFor="value">API Key Value *</Label>
            <div className="relative">
              <Input
                {...form.register('value')}
                type={showValue ? 'text' : 'password'}
                placeholder="Enter your API key"
                className="pr-20"
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowValue(!showValue)}
                  className="h-8 w-8 p-0"
                >
                  {showValue ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                {form.watch('value') && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(form.watch('value'))}
                    className="h-8 w-8 p-0"
                  >
                    {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                )}
              </div>
            </div>
            {form.formState.errors.value && (
              <p className="text-sm text-red-500">{form.formState.errors.value.message}</p>
            )}
          </div>

          {/* Optional Label */}
          <div className="space-y-2">
            <Label htmlFor="label">Custom Label (Optional)</Label>
            <Input
              {...form.register('label')}
              placeholder="e.g., Production API Key"
            />
          </div>

          {/* Platform Instructions */}
          {platformInstructions && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription className="space-y-2">
                <p>{platformInstructions.description}</p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(platformInstructions.url, '_blank')}
                  className="flex items-center gap-1"
                >
                  <ExternalLink className="h-3 w-3" />
                  Get API Key
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Security Notice */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Your API key will be encrypted using AES-256-GCM encryption before storage. 
              Only you can decrypt and access this key.
            </AlertDescription>
          </Alert>

          {/* Form Actions */}
          <div className="flex gap-2 pt-4">
            <Button
              type="submit"
              disabled={isStoring || !form.formState.isValid}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isStoring ? 'Storing...' : 'Store Key'}
            </Button>
            
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isStoring}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}