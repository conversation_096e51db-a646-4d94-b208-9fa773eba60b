/**
 * Xbox Live/Game Pass Import Service
 * Handles importing user's Xbox gaming library and achievements
 */

import { BasePlatformService, type PlatformGameData, type AuthenticationResult, type ImportProgress } from './basePlatformService';
import { type KeyType } from '@/lib/apiKeyStore';
import { type Game } from '@/types';

interface XboxAuthResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
}

interface XboxUserToken {
  IssueInstant: string;
  NotAfter: string;
  Token: string;
  DisplayClaims: {
    xui: Array<{
      gtg?: string;
      xid?: string;
      uhs: string;
      agg?: string;
    }>;
  };
}

interface XboxGameData {
  titleId: string;
  name: string;
  description?: string;
  publisher?: string;
  developer?: string;
  type?: string;
  devices?: string[];
  displayImage?: string;
  images?: Array<{
    imageType: string;
    url: string;
    width?: number;
    height?: number;
  }>;
  genres?: string[];
  releaseDate?: string;
  isActive?: boolean;
  achievement?: {
    currentAchievements: number;
    totalAchievements: number;
    currentGamerscore: number;
    totalGamerscore: number;
  };
  gamepass?: {
    isGamePassGame: boolean;
    gamePassCategories?: string[];
  };
  playtime?: {
    lastUnlock?: string;
    totalUnlocks: number;
  };
}

interface XboxTitleHistoryResponse {
  titles: XboxGameData[];
  pagingInfo?: {
    continuationToken?: string;
    totalRecords: number;
  };
}

export class XboxImportService extends BasePlatformService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiry: Date | null = null;
  private userToken: string | null = null;
  private xuid: string | null = null;

  constructor() {
    super('xbox');
  }

  protected getRequiredKeys(): KeyType[] {
    return ['client_id', 'client_secret'];
  }

  /**
   * Authenticate with Xbox Live using OAuth2 and Xbox Live authentication flow
   */
  async authenticate(): Promise<AuthenticationResult> {
    try {
      const clientId = await this.getApiKey('client_id');
      const clientSecret = await this.getApiKey('client_secret');

      if (!clientId || !clientSecret) {
        return {
          success: false,
          error: 'Xbox Live API credentials not found. Please configure your Application ID and Client Secret in Settings.',
        };
      }

      // Step 1: Get OAuth2 token from Microsoft
      const tokenUrl = 'https://login.live.com/oauth20_token.srf';
      
      const response = await this.makeRequest(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: clientId,
          client_secret: clientSecret,
          scope: 'XboxLive.signin XboxLive.offline_access',
        }),
      });

      const authData: XboxAuthResponse = await response.json();

      this.accessToken = authData.access_token;
      this.refreshToken = authData.refresh_token;
      this.tokenExpiry = new Date(Date.now() + authData.expires_in * 1000);

      // Step 2: Get Xbox User Token
      await this.getXboxUserToken();

      return {
        success: true,
        accessToken: this.accessToken,
        refreshToken: this.refreshToken,
        expiresAt: this.tokenExpiry,
      };
    } catch (error) {
      console.error('Xbox Live authentication failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      };
    }
  }

  /**
   * Get Xbox User Token for Xbox Live API calls
   */
  private async getXboxUserToken(): Promise<void> {
    if (!this.accessToken) {
      throw new Error('OAuth token required for Xbox User Token');
    }

    // Authenticate with Xbox Live
    const userAuthUrl = 'https://user.auth.xboxlive.com/user/authenticate';
    
    const userAuthResponse = await this.makeRequest(userAuthUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-xbl-contract-version': '1',
      },
      body: JSON.stringify({
        RelyingParty: 'http://auth.xboxlive.com',
        TokenType: 'JWT',
        Properties: {
          AuthMethod: 'RPS',
          SiteName: 'user.auth.xboxlive.com',
          RpsTicket: `d=${this.accessToken}`,
        },
      }),
    });

    const userTokenData: XboxUserToken = await userAuthResponse.json();
    this.userToken = userTokenData.Token;
    
    // Extract XUID (Xbox User ID)
    if (userTokenData.DisplayClaims?.xui?.[0]?.xid) {
      this.xuid = userTokenData.DisplayClaims.xui[0].xid;
    }

    // Get XSTS Token for final API access
    const xstsUrl = 'https://xsts.auth.xboxlive.com/xsts/authorize';
    
    const xstsResponse = await this.makeRequest(xstsUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-xbl-contract-version': '1',
      },
      body: JSON.stringify({
        RelyingParty: 'https://profile.xboxlive.com/',
        TokenType: 'JWT',
        Properties: {
          SandboxId: 'RETAIL',
          UserTokens: [this.userToken],
        },
      }),
    });

    const xstsTokenData: XboxUserToken = await xstsResponse.json();
    this.userToken = xstsTokenData.Token;
  }

  /**
   * Check if tokens are valid and refresh if needed
   */
  private async ensureValidToken(): Promise<void> {
    if (!this.accessToken || !this.tokenExpiry || this.tokenExpiry <= new Date()) {
      const authResult = await this.authenticate();
      if (!authResult.success) {
        throw new Error(authResult.error || 'Failed to authenticate');
      }
    }
  }

  /**
   * Fetch user's Xbox gaming library and achievements
   */
  async fetchUserLibrary(progressCallback?: (progress: ImportProgress) => void): Promise<PlatformGameData[]> {
    await this.ensureValidToken();

    if (!this.xuid) {
      throw new Error('Xbox User ID not available');
    }

    const games: PlatformGameData[] = [];
    let continuationToken: string | undefined;
    let totalFetched = 0;

    progressCallback?.({
      phase: 'fetching',
      current: 20,
      total: 100,
      message: 'Fetching Xbox gaming history...',
    });

    try {
      do {
        // Get title history (games played)
        const historyUrl = `https://titlehub.xboxlive.com/users/xuid(${this.xuid})/titles/titlehistory/decoration/detail`;
        
        const queryParams = new URLSearchParams({
          maxItems: '100',
        });
        
        if (continuationToken) {
          queryParams.append('continuationToken', continuationToken);
        }

        const response = await this.makeRequest(`${historyUrl}?${queryParams}`, {
          method: 'GET',
          headers: {
            'Authorization': `XBL3.0 x=${this.userToken};${this.userToken}`,
            'x-xbl-contract-version': '2',
            'Accept': 'application/json',
          },
        });

        const historyData: XboxTitleHistoryResponse = await response.json();

        if (historyData.titles && historyData.titles.length > 0) {
          for (const title of historyData.titles) {
            const platformGame = await this.convertXboxGameToPlatformData(title);
            games.push(platformGame);
            totalFetched++;
          }

          // Update progress
          progressCallback?.({
            phase: 'fetching',
            current: 20 + (totalFetched / (historyData.pagingInfo?.totalRecords || totalFetched)) * 40,
            total: 100,
            message: `Fetched ${totalFetched} games from Xbox Live...`,
          });

          continuationToken = historyData.pagingInfo?.continuationToken;
        } else {
          continuationToken = undefined;
        }
      } while (continuationToken);

      return games;
    } catch (error) {
      console.error('Failed to fetch Xbox library:', error);
      throw new Error(`Failed to fetch library: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get detailed achievements for a specific game
   */
  private async getGameAchievements(titleId: string): Promise<unknown> {
    if (!this.xuid || !this.userToken) return null;

    try {
      const achievementsUrl = `https://achievements.xboxlive.com/users/xuid(${this.xuid})/achievements`;
      
      const response = await this.makeRequest(`${achievementsUrl}?titleId=${titleId}`, {
        method: 'GET',
        headers: {
          'Authorization': `XBL3.0 x=${this.userToken};${this.userToken}`,
          'x-xbl-contract-version': '2',
          'Accept': 'application/json',
        },
      });

      return await response.json();
    } catch (error) {
      console.error(`Failed to get achievements for title ${titleId}:`, error);
      return null;
    }
  }

  /**
   * Convert Xbox game data to our platform format
   */
  private async convertXboxGameToPlatformData(xboxGame: XboxGameData): Promise<PlatformGameData> {
    // Find the best image
    let imageUrl = xboxGame.displayImage || '';
    if (xboxGame.images && xboxGame.images.length > 0) {
      const preferredImage = xboxGame.images.find(img => 
        img.imageType === 'BoxArt' || img.imageType === 'Hero' || img.imageType === 'Screenshot'
      );
      if (preferredImage) {
        imageUrl = preferredImage.url;
      }
    }

    // Get detailed achievement data
    const achievementData = await this.getGameAchievements(xboxGame.titleId);

    return {
      platformId: xboxGame.titleId,
      name: xboxGame.name,
      description: xboxGame.description,
      releaseDate: xboxGame.releaseDate,
      developer: xboxGame.developer,
      publisher: xboxGame.publisher,
      genres: xboxGame.genres || [],
      platforms: xboxGame.devices || ['Xbox'],
      imageUrl,
      achievementCount: xboxGame.achievement?.totalAchievements || 0,
      playtime: xboxGame.playtime?.totalUnlocks,
      lastPlayed: xboxGame.playtime?.lastUnlock,
      metadata: {
        xbox_title_id: xboxGame.titleId,
        xbox_type: xboxGame.type,
        xbox_devices: xboxGame.devices,
        xbox_is_active: xboxGame.isActive,
        xbox_achievement_data: xboxGame.achievement,
        xbox_gamepass_info: xboxGame.gamepass,
        xbox_detailed_achievements: achievementData,
        xbox_images: xboxGame.images,
      },
    };
  }

  /**
   * Convert platform data to our internal Game format
   */
  protected convertToGame(platformGame: PlatformGameData): Partial<Game> {
    return {
      name: platformGame.name,
      description: platformGame.description,
      release_date: platformGame.releaseDate,
      developer: platformGame.developer,
      publisher: platformGame.publisher,
      genres: platformGame.genres,
      platforms: platformGame.platforms,
      cover_image: platformGame.imageUrl,
      platform_ids: {
        xbox: platformGame.platformId,
      },
      metadata: {
        import_source: 'xbox',
        achievement_count: platformGame.achievementCount,
        last_played: platformGame.lastPlayed,
        ...platformGame.metadata,
      },
    };
  }

  /**
   * Get user's Game Pass subscription status
   */
  async getGamePassStatus(): Promise<{ hasGamePass: boolean; gamePassGames: number } | null> {
    await this.ensureValidToken();

    if (!this.xuid) return null;

    try {
      // This would require specific Game Pass API endpoints
      // For now, we can infer from games that have Game Pass metadata
      const library = await this.fetchUserLibrary();
      const gamePassGames = library.filter(game => 
        game.metadata?.xbox_gamepass_info?.isGamePassGame
      );

      return {
        hasGamePass: gamePassGames.length > 0,
        gamePassGames: gamePassGames.length,
      };
    } catch (error) {
      console.error('Failed to get Game Pass status:', error);
      return null;
    }
  }

  /**
   * Get user's Xbox gaming statistics
   */
  async getGamingStats(): Promise<{ totalGames: number; totalGamerscore: number; totalAchievements: number } | null> {
    await this.ensureValidToken();

    if (!this.xuid) return null;

    try {
      const profileUrl = `https://profile.xboxlive.com/users/xuid(${this.xuid})/profile/settings`;
      
      const response = await this.makeRequest(`${profileUrl}?settings=Gamerscore,TotalAchievements`, {
        method: 'GET',
        headers: {
          'Authorization': `XBL3.0 x=${this.userToken};${this.userToken}`,
          'x-xbl-contract-version': '2',
          'Accept': 'application/json',
        },
      });

      const profileData = await response.json();
      
      // Extract stats from profile settings
      const gamerscoreSetting = profileData.profileUsers?.[0]?.settings?.find((s: { id: string }) => s.id === 'Gamerscore');
      const achievementsSetting = profileData.profileUsers?.[0]?.settings?.find((s: { id: string }) => s.id === 'TotalAchievements');

      const library = await this.fetchUserLibrary();

      return {
        totalGames: library.length,
        totalGamerscore: parseInt(gamerscoreSetting?.value || '0'),
        totalAchievements: parseInt(achievementsSetting?.value || '0'),
      };
    } catch (error) {
      console.error('Failed to get gaming stats:', error);
      return null;
    }
  }

  /**
   * Search Xbox Game Pass catalog
   */
  async searchGamePass(): Promise<PlatformGameData[]> {
    // This would require access to the Game Pass catalog API
    // For now, this is a placeholder for future implementation
    console.warn('Xbox Game Pass search not yet implemented');
    return [];
  }

  /**
   * Get Xbox Live store URL for a game
   */
  static getStoreUrl(titleId: string): string {
    return `https://www.microsoft.com/en-us/p/game/${titleId}`;
  }

  /**
   * Check if a game is available on Game Pass
   */
  static isGamePassGame(xboxGame: XboxGameData): boolean {
    return xboxGame.gamepass?.isGamePassGame || false;
  }
}

// Export singleton instance
// DISABLED: Only Steam is supported as of July 2025
// export const xboxImportService = new XboxImportService();