import { UserGameWithDetails } from '@/types/database';
import { GameFilters } from '@/types/filters';
import { 
  Collection, 
  SearchState,
  GroupingOption
} from '@/types/library';

/**
 * Enhanced Library Service
 * Provides business logic for library operations including filtering, sorting, grouping, and search
 */
export class LibraryService {
  /**
   * Filter games based on the provided filters
   */
  static filterGames(games: UserGameWithDetails[], filters: GameFilters): UserGameWithDetails[] {
    let filteredGames = [...games];

    // Search filter
    if (filters.search.enabled && filters.search.query.trim()) {
      const query = filters.search.query.toLowerCase();
      filteredGames = filteredGames.filter(game => {
        const title = game.game?.title?.toLowerCase() || '';
        const developer = game.game?.developer?.toLowerCase() || '';
        const publisher = game.game?.publisher?.toLowerCase() || '';
        const genres = game.game?.genres?.join(' ').toLowerCase() || '';
        
        switch (filters.search.searchType) {
          case 'exact':
            return title === query;
          case 'fuzzy':
            return this.fuzzyMatch(title, query) || 
                   this.fuzzyMatch(developer, query) || 
                   this.fuzzyMatch(publisher, query);
          case 'smart':
          default:
            return title.includes(query) || 
                   developer.includes(query) || 
                   publisher.includes(query) || 
                   genres.includes(query);
        }
      });
    }

    // Platform filter
    if (filters.platforms.enabled && filters.platforms.platforms.length > 0) {
      filteredGames = filteredGames.filter(game => {
        const gamePlatforms = game.game?.platforms || [];
        return filters.platforms.platforms.some(platform => 
          gamePlatforms.includes(platform)
        );
      });
    }

    // Genre filter
    if (filters.genres.enabled && filters.genres.genres.length > 0) {
      filteredGames = filteredGames.filter(game => {
        const gameGenres = game.game?.genres || [];
        const hasMatchingGenre = filters.genres.genres.some(genre => 
          gameGenres.includes(genre)
        );
        
        return filters.genres.mode === 'include' ? hasMatchingGenre : !hasMatchingGenre;
      });
    }

    // Year filter
    if (filters.year.enabled) {
      filteredGames = filteredGames.filter(game => {
        const releaseDate = game.game?.release_date;
        if (!releaseDate) return false;
        
        const year = new Date(releaseDate).getFullYear();
        const minYear = filters.year.minYear;
        const maxYear = filters.year.maxYear;
        
        if (minYear && year < minYear) return false;
        if (maxYear && year > maxYear) return false;
        
        return true;
      });
    }

    // Rating filter
    if (filters.rating.enabled) {
      filteredGames = filteredGames.filter(game => {
        const rating = game.game?.metacritic_score || game.personal_rating;
        if (!rating) return false;
        
        const minRating = filters.rating.minRating;
        const maxRating = filters.rating.maxRating;
        
        if (minRating && rating < minRating) return false;
        if (maxRating && rating > maxRating) return false;
        
        return true;
      });
    }

    // Developer filter
    if (filters.developer.enabled && filters.developer.developers.length > 0) {
      filteredGames = filteredGames.filter(game => {
        const developer = game.game?.developer;
        return developer && filters.developer.developers.includes(developer);
      });
    }

    // Publisher filter
    if (filters.publisher.enabled && filters.publisher.publishers.length > 0) {
      filteredGames = filteredGames.filter(game => {
        const publisher = game.game?.publisher;
        return publisher && filters.publisher.publishers.includes(publisher);
      });
    }

    // Status filter
    if (filters.status.enabled && filters.status.statuses.length > 0) {
      filteredGames = filteredGames.filter(game => {
        const hasMatchingStatus = filters.status.statuses.includes(game.status);
        return filters.status.mode === 'include' ? hasMatchingStatus : !hasMatchingStatus;
      });
    }

    // Collection filter
    if (filters.collection.enabled) {
      filteredGames = filteredGames.filter(game => {
        const isInLibrary = game.status !== 'wishlist';
        const isInWishlist = game.status === 'wishlist';
        
        if (filters.collection.inLibrary && !isInLibrary) return false;
        if (filters.collection.inWishlist && !isInWishlist) return false;
        if (filters.collection.notInCollection && (isInLibrary || isInWishlist)) return false;
        
        return true;
      });
    }

    return filteredGames;
  }

  /**
   * Sort games based on the provided sort options
   */
  static sortGames(games: UserGameWithDetails[], sortField: string, sortDirection: 'asc' | 'desc'): UserGameWithDetails[] {
    const sortedGames = [...games];
    
    sortedGames.sort((a, b) => {
      let aValue: string | number | Date;
      let bValue: string | number | Date;
      
      switch (sortField) {
        case 'title':
          aValue = a.game?.title || '';
          bValue = b.game?.title || '';
          break;
        case 'release_date':
          aValue = new Date(a.game?.release_date || 0);
          bValue = new Date(b.game?.release_date || 0);
          break;
        case 'metacritic_score':
          aValue = a.game?.metacritic_score || 0;
          bValue = b.game?.metacritic_score || 0;
          break;
        case 'added_at':
          aValue = new Date(a.date_added);
          bValue = new Date(b.date_added);
          break;
        case 'popularity':
          // Use metacritic score as a proxy for popularity
          aValue = a.game?.metacritic_score || 0;
          bValue = b.game?.metacritic_score || 0;
          break;
        default:
          aValue = a.game?.title || '';
          bValue = b.game?.title || '';
      }
      
      let comparison = 0;
      if (aValue < bValue) comparison = -1;
      if (aValue > bValue) comparison = 1;
      
      return sortDirection === 'desc' ? -comparison : comparison;
    });
    
    return sortedGames;
  }

  /**
   * Group games by the specified grouping option
   */
  static groupGames(games: UserGameWithDetails[], groupBy: GroupingOption): Record<string, UserGameWithDetails[]> {
    if (groupBy === 'none') {
      return { 'All Games': games };
    }

    const grouped: Record<string, UserGameWithDetails[]> = {};
    
    games.forEach(game => {
      let groupKey: string;
      
      switch (groupBy) {
        case 'platform': {
          const platforms = game.game?.platforms || ['Unknown'];
          platforms.forEach(platform => {
            if (!grouped[platform]) grouped[platform] = [];
            grouped[platform].push(game);
          });
          return; // Early return to avoid the default grouping below
        }
        case 'genre': {
          const genres = game.game?.genres || ['Unknown'];
          genres.forEach(genre => {
            if (!grouped[genre]) grouped[genre] = [];
            grouped[genre].push(game);
          });
          return;
        }
        case 'developer':
          groupKey = game.game?.developer || 'Unknown';
          break;
        case 'publisher':
          groupKey = game.game?.publisher || 'Unknown';
          break;
        case 'year': {
          const year = game.game?.release_date ? 
            new Date(game.game.release_date).getFullYear().toString() : 
            'Unknown';
          groupKey = year;
          break;
        }
        case 'status':
          groupKey = game.status.charAt(0).toUpperCase() + game.status.slice(1);
          break;
        case 'rating': {
          const rating = game.personal_rating || game.game?.metacritic_score;
          if (!rating) {
            groupKey = 'Unrated';
          } else if (rating >= 90) {
            groupKey = 'Excellent (90+)';
          } else if (rating >= 80) {
            groupKey = 'Great (80-89)';
          } else if (rating >= 70) {
            groupKey = 'Good (70-79)';
          } else if (rating >= 60) {
            groupKey = 'Average (60-69)';
          } else {
            groupKey = 'Below Average (<60)';
          }
          break;
        }
        case 'tag':
          // This would require tag data which isn't available in the current structure
          groupKey = 'No Tags';
          break;
        default:
          groupKey = 'Other';
      }
      
      if (!grouped[groupKey]) {
        grouped[groupKey] = [];
      }
      grouped[groupKey].push(game);
    });
    
    return grouped;
  }

  /**
   * Search games with advanced search capabilities
   */
  static searchGames(games: UserGameWithDetails[], searchState: SearchState): UserGameWithDetails[] {
    if (!searchState.query.trim()) {
      return games;
    }

    const query = searchState.query.toLowerCase();
    const results = games.filter(game => {
      const title = game.game?.title?.toLowerCase() || '';
      const developer = game.game?.developer?.toLowerCase() || '';
      const publisher = game.game?.publisher?.toLowerCase() || '';
      const genres = game.game?.genres?.join(' ').toLowerCase() || '';
      const description = game.game?.description?.toLowerCase() || '';
      
      return title.includes(query) || 
             developer.includes(query) || 
             publisher.includes(query) || 
             genres.includes(query) ||
             description.includes(query);
    });

    return results;
  }

  /**
   * Generate search suggestions based on the current query and game data
   */
  static generateSearchSuggestions(games: UserGameWithDetails[], query: string): string[] {
    if (!query.trim()) return [];
    
    const suggestions = new Set<string>();
    const lowerQuery = query.toLowerCase();
    
    games.forEach(game => {
      const title = game.game?.title || '';
      const developer = game.game?.developer || '';
      const publisher = game.game?.publisher || '';
      const genres = game.game?.genres || [];
      
      // Add title suggestions
      if (title.toLowerCase().includes(lowerQuery)) {
        suggestions.add(title);
      }
      
      // Add developer suggestions
      if (developer.toLowerCase().includes(lowerQuery)) {
        suggestions.add(developer);
      }
      
      // Add publisher suggestions
      if (publisher.toLowerCase().includes(lowerQuery)) {
        suggestions.add(publisher);
      }
      
      // Add genre suggestions
      genres.forEach(genre => {
        if (genre.toLowerCase().includes(lowerQuery)) {
          suggestions.add(genre);
        }
      });
    });
    
    return Array.from(suggestions).slice(0, 10); // Limit to 10 suggestions
  }

  /**
   * Apply smart collection rules to determine which games belong to a collection
   */
  static applyCollectionRules(games: UserGameWithDetails[], collection: Collection): UserGameWithDetails[] {
    if (!collection.isSmartCollection || !collection.rules) {
      // For manual collections, filter by game IDs
      return games.filter(game => collection.games.includes(game.id));
    }

    return games.filter(game => {
      return collection.rules!.every(rule => {
        const fieldValue = this.getGameFieldValue(game, rule.field);
        return this.evaluateRule(fieldValue, rule.operator, rule.value);
      });
    });
  }

  /**
   * Calculate library statistics
   */
  static calculateLibraryStats(games: UserGameWithDetails[]) {
    const stats = {
      totalGames: games.length,
      totalPlayTime: 0,
      averageRating: 0,
      completionRate: 0,
      genreDistribution: {} as Record<string, number>,
      platformDistribution: {} as Record<string, number>,
      statusDistribution: {} as Record<string, number>,
      yearDistribution: {} as Record<string, number>,
      recentActivity: [] as UserGameWithDetails[]
    };

    if (games.length === 0) return stats;

    // Calculate totals and distributions
    let totalRating = 0;
    let ratedGamesCount = 0;
    let completedGames = 0;

    games.forEach(game => {
      // Play time
      if (game.hours_played) {
        stats.totalPlayTime += game.hours_played;
      }

      // Rating
      const rating = game.personal_rating || game.game?.metacritic_score;
      if (rating) {
        totalRating += rating;
        ratedGamesCount++;
      }

      // Completion
      if (game.status === 'completed') {
        completedGames++;
      }

      // Status distribution
      stats.statusDistribution[game.status] = (stats.statusDistribution[game.status] || 0) + 1;

      // Genre distribution
      const genres = game.game?.genres || [];
      genres.forEach(genre => {
        stats.genreDistribution[genre] = (stats.genreDistribution[genre] || 0) + 1;
      });

      // Platform distribution
      const platforms = game.game?.platforms || [];
      platforms.forEach(platform => {
        stats.platformDistribution[platform] = (stats.platformDistribution[platform] || 0) + 1;
      });

      // Year distribution
      if (game.game?.release_date) {
        const year = new Date(game.game.release_date).getFullYear().toString();
        stats.yearDistribution[year] = (stats.yearDistribution[year] || 0) + 1;
      }
    });

    // Calculate averages and rates
    stats.averageRating = ratedGamesCount > 0 ? totalRating / ratedGamesCount : 0;
    stats.completionRate = games.length > 0 ? (completedGames / games.length) * 100 : 0;

    // Recent activity (last 10 games added)
    stats.recentActivity = [...games]
      .sort((a, b) => new Date(b.date_added).getTime() - new Date(a.date_added).getTime())
      .slice(0, 10);

    return stats;
  }

  /**
   * Export library data in various formats
   */
  static exportLibrary(games: UserGameWithDetails[], format: 'json' | 'csv' | 'xml'): string {
    switch (format) {
      case 'json':
        return JSON.stringify(games, null, 2);
      case 'csv':
        return this.convertToCSV(games);
      case 'xml':
        return this.convertToXML(games);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  // Private helper methods

  private static fuzzyMatch(text: string, query: string): boolean {
    const textChars = text.toLowerCase().split('');
    const queryChars = query.toLowerCase().split('');
    let queryIndex = 0;
    
    for (const char of textChars) {
      if (queryIndex < queryChars.length && char === queryChars[queryIndex]) {
        queryIndex++;
      }
    }
    
    return queryIndex === queryChars.length;
  }

  private static getGameFieldValue(game: UserGameWithDetails, field: string): unknown {
    switch (field) {
      case 'title':
        return game.game?.title;
      case 'developer':
        return game.game?.developer;
      case 'publisher':
        return game.game?.publisher;
      case 'genres':
        return game.game?.genres;
      case 'platforms':
        return game.game?.platforms;
      case 'release_date':
        return game.game?.release_date;
      case 'metacritic_score':
        return game.game?.metacritic_score;
      case 'status':
        return game.status;
      case 'personal_rating':
        return game.personal_rating;
      case 'hours_played':
        return game.hours_played;
      case 'date_added':
        return game.date_added;
      default:
        return null;
    }
  }

  private static evaluateRule(fieldValue: unknown, operator: string, ruleValue: unknown): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === ruleValue;
      case 'contains':
        if (Array.isArray(fieldValue)) {
          return fieldValue.includes(ruleValue);
        }
        return String(fieldValue).toLowerCase().includes(String(ruleValue).toLowerCase());
      case 'greaterThan':
        return Number(fieldValue) > Number(ruleValue);
      case 'lessThan':
        return Number(fieldValue) < Number(ruleValue);
      case 'between':
        if (Array.isArray(ruleValue) && ruleValue.length === 2) {
          const numValue = Number(fieldValue);
          return numValue >= Number(ruleValue[0]) && numValue <= Number(ruleValue[1]);
        }
        return false;
      default:
        return false;
    }
  }

  private static convertToCSV(games: UserGameWithDetails[]): string {
    const headers = [
      'Title', 'Platform', 'Developer', 'Publisher', 'Release Date', 
      'Status', 'Personal Rating', 'Hours Played', 'Date Added'
    ];
    
    const rows = games.map(game => [
      game.game?.title || '',
      game.game?.platforms?.join(';') || '',
      game.game?.developer || '',
      game.game?.publisher || '',
      game.game?.release_date || '',
      game.status,
      game.personal_rating || '',
      game.hours_played || '',
      game.date_added
    ]);
    
    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
      .join('\n');
    
    return csvContent;
  }

  private static convertToXML(games: UserGameWithDetails[]): string {
    const xmlGames = games.map(game => `
    <game>
      <title>${this.escapeXml(game.game?.title || '')}</title>
      <platform>${this.escapeXml(game.game?.platforms?.join(';') || '')}</platform>
      <developer>${this.escapeXml(game.game?.developer || '')}</developer>
      <publisher>${this.escapeXml(game.game?.publisher || '')}</publisher>
      <releaseDate>${this.escapeXml(game.game?.release_date || '')}</releaseDate>
      <status>${this.escapeXml(game.status)}</status>
      <personalRating>${game.personal_rating || ''}</personalRating>
      <hoursPlayed>${game.hours_played || ''}</hoursPlayed>
      <dateAdded>${this.escapeXml(game.date_added)}</dateAdded>
    </game>`).join('');
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<library>
  <games>${xmlGames}
  </games>
</library>`;
  }

  private static escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
}