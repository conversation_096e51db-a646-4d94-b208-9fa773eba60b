# Test Structure Documentation

This document outlines the optimized test structure and organization for the Codexa project.

## Folder Structure

```
src/
├── __tests__/                    # Centralized test directory
│   ├── config/                   # Test configuration and constants
│   │   └── test-config.ts        # Centralized test settings
│   ├── utils/                    # Test utilities and helpers
│   │   └── test-helpers.ts       # Common test helper functions
│   ├── lib/                      # Tests for lib/ directory
│   │   └── utils/                # Tests for utility functions
│   │       └── platformFamilyUtils.test.ts
│   ├── components/               # Tests for components (future)
│   ├── hooks/                    # Tests for custom hooks (future)
│   └── pages/                    # Tests for page components (future)
└── test/                         # Test setup and configuration
    └── setup.ts                  # Global test setup
```

## Key Improvements

### 1. Centralized Test Structure
- All tests are now organized under `src/__tests__/` following the source code structure
- Consistent naming conventions: `*.test.ts` for test files
- Mirror the `src/` directory structure for easy navigation

### 2. Test Utilities and Helpers
- **`test-helpers.ts`**: Common mock data generators and assertion helpers
- **`test-config.ts`**: Centralized configuration and constants
- Reusable mock functions for consistent test data

### 3. Enhanced TypeScript Configuration
- **`tsconfig.test.json`**: Dedicated TypeScript configuration for tests
- Proper type definitions for Vitest and testing libraries
- Strict type checking for test files

### 4. Improved ESLint Configuration
- Test-specific ESLint rules in `eslint.config.js`
- Relaxed rules for test files (e.g., `any` types allowed)
- Global test variables properly configured

### 5. Enhanced Vitest Configuration
- Coverage reporting configured
- Test timeout and retry settings
- Proper include/exclude patterns
- Performance optimizations

### 6. Better Test Setup
- Enhanced `setup.ts` with common mocks (IntersectionObserver, ResizeObserver, matchMedia)
- Global test utilities available
- Consistent cleanup after each test

## Usage Guidelines

### Creating New Tests

1. **Location**: Place tests in `src/__tests__/` mirroring the source structure
   ```
   src/lib/utils/myUtil.ts → src/__tests__/lib/utils/myUtil.test.ts
   ```

2. **Naming**: Use descriptive test names with `.test.ts` extension

3. **Imports**: Use the test helpers for consistent mock data
   ```typescript
   import { createMockUserGameWithDetails, TEST_PLATFORMS } from '@/__tests__/utils/test-helpers';
   ```

### Test Helpers Usage

```typescript
// Create mock game data
const game = createMockUserGameWithDetails('1', 'PC', 'playing', 8.5, 10.5);

// Create multiple games
const games = createMockGameCollection([
  { id: '1', platform: 'PC', status: 'playing' },
  { id: '2', platform: 'Xbox One', status: 'completed' },
]);

// Use test constants
TEST_PLATFORMS.PC.forEach(platform => {
  expect(categorizePlatform(platform)).toBe('PC');
});

// Use assertion helpers
assertPlatformFamilyStats(stats, {
  total: 5,
  playing: 1,
  completed: 1,
});
```

### Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npm test src/__tests__/lib/utils/platformFamilyUtils.test.ts

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm test -- --watch
```

## Best Practices

### 1. Test Organization
- Group related tests using `describe` blocks
- Use descriptive test names that explain the expected behavior
- Follow the AAA pattern: Arrange, Act, Assert

### 2. Mock Data
- Use the centralized test helpers for consistent mock data
- Avoid hardcoded values; use constants from `test-config.ts`
- Create focused, minimal mock data for each test

### 3. Assertions
- Use the custom assertion helpers for complex objects
- Prefer specific assertions over generic ones
- Test both positive and negative cases

### 4. Performance
- Use `it.each()` for parameterized tests to reduce duplication
- Mock external dependencies to keep tests fast
- Avoid unnecessary async operations in tests

### 5. Maintainability
- Keep tests simple and focused on single behaviors
- Use meaningful variable names and comments
- Regularly refactor tests to remove duplication

## Configuration Files

### `vitest.config.ts`
- Test environment configuration
- Coverage settings
- File patterns and exclusions

### `tsconfig.test.json`
- TypeScript configuration for tests
- Type definitions for testing libraries
- Compiler options optimized for testing

### `eslint.config.js`
- Test-specific linting rules
- Global test variables configuration
- Relaxed rules for test files

### `src/test/setup.ts`
- Global test setup and teardown
- Common mocks and utilities
- Environment configuration

This structure provides a solid foundation for maintaining high-quality, well-organized tests as the project grows.