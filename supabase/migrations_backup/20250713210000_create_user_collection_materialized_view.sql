-- User Collection Materialized View Migration
-- Creates optimized materialized view for frequent user collection queries

-- Create materialized view for user collections with game details
CREATE MATERIALIZED VIEW user_games_optimized AS
SELECT 
  ug.id,
  ug.user_id,
  ug.game_id,
  ug.status,
  ug.personal_rating,
  ug.personal_notes,
  ug.hours_played,
  ug.date_added,
  ug.date_completed,
  ug.is_wishlist,
  -- Game details for faster access
  g.title,
  g.platform,
  g.genres,
  g.developer,
  g.publisher,
  g.release_date,
  g.cover_image,
  g.metacritic_score,
  g.description,
  g.screenshots,
  g.youtube_links
FROM user_games ug
JOIN games g ON ug.game_id = g.id;

-- Index the materialized view for optimal performance
CREATE UNIQUE INDEX idx_user_games_optimized_pk ON user_games_optimized(id);
CREATE INDEX idx_user_games_optimized_user_status ON user_games_optimized(user_id, status);
CREATE INDEX idx_user_games_optimized_user_date ON user_games_optimized(user_id, date_added DESC);
CREATE INDEX idx_user_games_optimized_wishlist ON user_games_optimized(user_id, is_wishlist, date_added DESC);
CREATE INDEX idx_user_games_optimized_rating ON user_games_optimized(user_id, personal_rating DESC) WHERE personal_rating IS NOT NULL;

-- Function to refresh the materialized view
CREATE OR REPLACE FUNCTION refresh_user_games_optimized()
RETURNS TRIGGER AS $$
BEGIN
  -- Use CONCURRENTLY to avoid blocking reads during refresh
  REFRESH MATERIALIZED VIEW CONCURRENTLY user_games_optimized;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Triggers to keep materialized view fresh
CREATE TRIGGER refresh_user_games_on_insert
  AFTER INSERT ON user_games
  FOR EACH STATEMENT
  EXECUTE FUNCTION refresh_user_games_optimized();

CREATE TRIGGER refresh_user_games_on_update
  AFTER UPDATE ON user_games
  FOR EACH STATEMENT
  EXECUTE FUNCTION refresh_user_games_optimized();

CREATE TRIGGER refresh_user_games_on_delete
  AFTER DELETE ON user_games
  FOR EACH STATEMENT
  EXECUTE FUNCTION refresh_user_games_optimized();

-- Also refresh when game details change
CREATE TRIGGER refresh_user_games_on_game_update
  AFTER UPDATE ON games
  FOR EACH STATEMENT
  EXECUTE FUNCTION refresh_user_games_optimized();

-- Create optimized functions for common queries using the materialized view
CREATE OR REPLACE FUNCTION get_user_collection_fast(p_user_id UUID)
RETURNS TABLE(
  id UUID,
  game_id UUID,
  status TEXT,
  personal_rating INTEGER,
  hours_played DECIMAL,
  date_added TIMESTAMPTZ,
  title TEXT,
  platform TEXT,
  genres TEXT[],
  cover_image TEXT,
  metacritic_score INTEGER,
  release_date DATE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ugo.id,
    ugo.game_id,
    ugo.status,
    ugo.personal_rating,
    ugo.hours_played,
    ugo.date_added,
    ugo.title,
    ugo.platform,
    ugo.genres,
    ugo.cover_image,
    ugo.metacritic_score,
    ugo.release_date
  FROM user_games_optimized ugo
  WHERE ugo.user_id = p_user_id
  ORDER BY ugo.date_added DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user wishlist optimized
CREATE OR REPLACE FUNCTION get_user_wishlist_fast(p_user_id UUID)
RETURNS TABLE(
  id UUID,
  game_id UUID,
  date_added TIMESTAMPTZ,
  title TEXT,
  platform TEXT,
  cover_image TEXT,
  metacritic_score INTEGER,
  release_date DATE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ugo.id,
    ugo.game_id,
    ugo.date_added,
    ugo.title,
    ugo.platform,
    ugo.cover_image,
    ugo.metacritic_score,
    ugo.release_date
  FROM user_games_optimized ugo
  WHERE ugo.user_id = p_user_id 
    AND ugo.is_wishlist = true
  ORDER BY ugo.date_added DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user collection statistics fast
CREATE OR REPLACE FUNCTION get_user_stats_fast(p_user_id UUID)
RETURNS TABLE(
  total_games INTEGER,
  completed_games INTEGER,
  playing_games INTEGER,
  backlog_games INTEGER,
  wishlist_games INTEGER,
  average_rating DECIMAL,
  total_hours DECIMAL,
  completion_rate DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_games,
    COUNT(*) FILTER (WHERE status = 'completed')::INTEGER as completed_games,
    COUNT(*) FILTER (WHERE status = 'playing')::INTEGER as playing_games,
    COUNT(*) FILTER (WHERE status = 'backlog')::INTEGER as backlog_games,
    COUNT(*) FILTER (WHERE is_wishlist = true)::INTEGER as wishlist_games,
    AVG(personal_rating) as average_rating,
    SUM(COALESCE(hours_played, 0)) as total_hours,
    CASE WHEN COUNT(*) FILTER (WHERE NOT is_wishlist) > 0 
         THEN (COUNT(*) FILTER (WHERE status = 'completed')::DECIMAL / COUNT(*) FILTER (WHERE NOT is_wishlist) * 100)
         ELSE 0 END as completion_rate
  FROM user_games_optimized
  WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant appropriate permissions
GRANT SELECT ON user_games_optimized TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_collection_fast(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_wishlist_fast(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_stats_fast(UUID) TO authenticated;

-- Add comments for documentation
COMMENT ON MATERIALIZED VIEW user_games_optimized IS 'Optimized materialized view combining user_games and games for faster queries';
COMMENT ON FUNCTION get_user_collection_fast(UUID) IS 'Fast function to get user collection using materialized view';
COMMENT ON FUNCTION get_user_wishlist_fast(UUID) IS 'Fast function to get user wishlist using materialized view';
COMMENT ON FUNCTION get_user_stats_fast(UUID) IS 'Fast function to calculate user collection statistics';