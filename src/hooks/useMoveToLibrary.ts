import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/supabase';
import { Game } from '@/types';

/**
 * Hook for moving games from wishlist to library when purchased
 * This removes the game from wishlist and adds it to library
 */
export function useMoveToLibrary() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ userGameId, game }: { userGameId: string; game: Game }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // First, check if game already exists in games table
      const { data: existingGame, error: gameError } = await db.games.getById(game.id);
      
      if (gameError && gameError.code !== 'PGRST116') { // PGRST116 is "not found"
        throw gameError;
      }

      // If game doesn't exist, create it
      if (!existingGame) {
        const { error: createError } = await db.games.create({
          id: game.id,
          title: game.title,
          description: game.description,
          cover_image: game.cover_image,
          screenshots: game.screenshots,
          release_date: game.release_date,
          metacritic_score: game.metacritic_score,
          platforms: game.platforms,
          developer: game.developer,
          publisher: game.publisher,
          genres: game.genres,
          igdb_id: game.igdb_id,
          youtube_links: game.youtube_links,
        });

        if (createError) {
          throw createError;
        }
      }

      // Update the existing wishlist entry to library status
      const { data, error } = await db.userGames.updateStatus(userGameId, {
        status: 'backlog', // Default library status
      });

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (_, variables) => {
      // Invalidate the consolidated user collection query with user ID
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      
      // Show success toast
      toast.success(`"${variables.game.title}" moved to your library!`);
    },
    onError: () => {
      toast.error('Failed to move game to library. Please try again');
    },
  });
}