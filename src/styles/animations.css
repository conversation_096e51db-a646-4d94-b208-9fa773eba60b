/* Animation keyframes and utilities */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  from {
    text-shadow: 0 0 5px hsl(var(--primary) / 0.5);
  }
  to {
    text-shadow: 0 0 20px hsl(var(--primary) / 0.8),
      0 0 30px hsl(var(--primary) / 0.6);
  }
}

@keyframes staggerFade {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes boxArtHover {
  0% {
    transform: scale(1) rotateY(0deg);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  100% {
    transform: scale(1.05) rotateY(2deg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  }
}

@keyframes premiumGlow {
  0%,
  100% {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.1);
  }
  50% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3),
      0 0 30px hsl(var(--primary) / 0.2);
  }
}

@keyframes staggerIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes sparkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes premiumGlowRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes premiumShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes profileDropdownEnter {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}