/**
 * Enhanced error handling for search functionality
 * Provides detailed, user-friendly error messages and suggestions
 */

export interface SearchError {
  type: 'network' | 'api_error' | 'no_results' | 'invalid_query' | 'rate_limit' | 'timeout' | 'unknown';
  message: string;
  suggestions: string[];
  userMessage: string;
  technicalDetails?: string;
  retryable: boolean;
}

interface ErrorPattern {
  pattern: RegExp | string;
  type: SearchError['type'];
  userMessage: string;
  suggestions: string[];
  retryable: boolean;
}

// Error patterns for different types of failures
const ERROR_PATTERNS: ErrorPattern[] = [
  {
    pattern: /network|fetch|connection|NETWORK_ERROR/i,
    type: 'network',
    userMessage: 'Unable to connect to game databases. Please check your internet connection.',
    suggestions: [
      'Check your internet connection',
      'Try refreshing the page',
      'Wait a moment and try again',
      'Check if the site is working in another browser'
    ],
    retryable: true
  },
  {
    pattern: /rate.?limit|too.?many.?requests|429/i,
    type: 'rate_limit',
    userMessage: 'Too many searches too quickly. Please wait a moment before searching again.',
    suggestions: [
      'Wait 30 seconds before searching again',
      'Try a more specific search term',
      'Use fewer search filters'
    ],
    retryable: true
  },
  {
    pattern: /timeout|timed.?out|408/i,
    type: 'timeout',
    userMessage: 'Search request timed out. This might be due to slow servers.',
    suggestions: [
      'Try a shorter, more specific search term',
      'Wait a moment and try again',
      'Check your internet connection speed'
    ],
    retryable: true
  },
  {
    pattern: /401|unauthorized|authentication|token/i,
    type: 'api_error',
    userMessage: 'There\'s an issue with the game database connection.',
    suggestions: [
      'Try refreshing the page',
      'This might be a temporary issue - try again in a few minutes',
      'Contact support if the problem persists'
    ],
    retryable: true
  },
  {
    pattern: /500|502|503|504|server.?error|internal.?error/i,
    type: 'api_error',
    userMessage: 'The game database servers are experiencing issues.',
    suggestions: [
      'Try again in a few minutes',
      'The issue is likely temporary',
      'Try searching for different games'
    ],
    retryable: true
  },
  {
    pattern: /404|not.?found|endpoint/i,
    type: 'api_error',
    userMessage: 'There\'s a technical issue with the search service.',
    suggestions: [
      'Try refreshing the page',
      'Contact support if this keeps happening'
    ],
    retryable: false
  }
];

/**
 * Get search suggestions based on the query that failed
 */
function getSearchSuggestions(failedQuery: string): string[] {
  const query = failedQuery.toLowerCase().trim();
  const suggestions: string[] = [];
  
  // Length-based suggestions
  if (query.length < 2) {
    suggestions.push('Try entering at least 2 characters');
  } else if (query.length > 50) {
    suggestions.push('Try a shorter search term');
  }
  
  // Common typo suggestions (for future enhancement)
  // const commonTypos = {
  //   'call of duty': ['call of duty', 'cod', 'call duty'],
  //   'grand theft auto': ['gta', 'grand theft', 'theft auto'],
  //   'world of warcraft': ['wow', 'warcraft'],
  //   'counter strike': ['cs', 'counter-strike', 'counterstrike'],
  //   'elder scrolls': ['skyrim', 'oblivion', 'morrowind']
  // };
  
  // Special character suggestions
  if (query.includes(' ')) {
    suggestions.push(`Try "${query.replace(/\s+/g, '')}" (without spaces)`);
  }
  
  if (!query.includes(' ') && query.length > 8) {
    suggestions.push('Try adding spaces between words');
  }
  
  // Platform-specific suggestions
  const platforms = ['PC', 'PlayStation', 'Xbox', 'Nintendo Switch', 'mobile'];
  platforms.forEach(platform => {
    if (!query.includes(platform.toLowerCase())) {
      suggestions.push(`Try "${query} ${platform}"`);
    }
  });
  
  // Generic fallback suggestions
  if (suggestions.length === 0) {
    suggestions.push(
      'Try a more specific search term',
      'Check for typos in the game title',
      'Try searching by genre or developer',
      'Use quotes for exact matches: "game title"'
    );
  }
  
  return suggestions.slice(0, 4); // Limit to 4 suggestions
}

/**
 * Process error and return structured error information
 */
export function processSearchError(error: unknown, searchQuery: string = ''): SearchError {
  let errorMessage = '';
  let technicalDetails = '';
  
  // Extract error message
  if (error instanceof Error) {
    errorMessage = error.message;
    technicalDetails = error.stack || error.message;
  } else if (typeof error === 'string') {
    errorMessage = error;
  } else if (error && typeof error === 'object' && 'message' in error) {
    errorMessage = String(error.message);
  } else {
    errorMessage = 'Unknown error occurred';
  }
  
  // Match against known error patterns
  for (const pattern of ERROR_PATTERNS) {
    const isMatch = pattern.pattern instanceof RegExp 
      ? pattern.pattern.test(errorMessage)
      : errorMessage.toLowerCase().includes(pattern.pattern.toLowerCase());
      
    if (isMatch) {
      return {
        type: pattern.type,
        message: errorMessage,
        userMessage: pattern.userMessage,
        suggestions: pattern.suggestions,
        technicalDetails,
        retryable: pattern.retryable
      };
    }
  }
  
  // Handle no results case (when no error but empty results)
  if (errorMessage.includes('no results') || errorMessage.includes('0 games found')) {
    return {
      type: 'no_results',
      message: errorMessage,
      userMessage: `No games found for "${searchQuery}".`,
      suggestions: getSearchSuggestions(searchQuery),
      technicalDetails,
      retryable: false
    };
  }
  
  // Default unknown error
  return {
    type: 'unknown',
    message: errorMessage,
    userMessage: 'Something went wrong with the search. Please try again.',
    suggestions: [
      'Try refreshing the page',
      'Check your internet connection',
      'Try a different search term',
      'Contact support if this keeps happening'
    ],
    technicalDetails,
    retryable: true
  };
}

/**
 * Check if error is likely temporary and should trigger auto-retry
 */
export function shouldAutoRetry(error: SearchError): boolean {
  return error.retryable && (
    error.type === 'network' ||
    error.type === 'timeout' ||
    error.type === 'api_error'
  );
}

/**
 * Format error for logging while keeping user info separate
 */
export function formatErrorForLogging(error: SearchError, searchQuery: string): object {
  return {
    searchQuery,
    errorType: error.type,
    errorMessage: error.message,
    userMessage: error.userMessage,
    retryable: error.retryable,
    timestamp: new Date().toISOString(),
    technicalDetails: error.technicalDetails
  };
}

/**
 * Get user-friendly error message with helpful suggestions
 */
export function getDisplayErrorMessage(error: SearchError): string {
  let message = error.userMessage;
  
  if (error.suggestions.length > 0) {
    message += '\n\nSuggestions:\n' + error.suggestions.map(s => `• ${s}`).join('\n');
  }
  
  return message;
}

/**
 * Create error for empty search results
 */
export function createNoResultsError(searchQuery: string): SearchError {
  return {
    type: 'no_results',
    message: `No games found for search: ${searchQuery}`,
    userMessage: `No games found matching "${searchQuery}".`,
    suggestions: getSearchSuggestions(searchQuery),
    retryable: false
  };
}