import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Input } from '@/components/ui/base/input';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { Skeleton } from '@/components/ui/base/skeleton';
import { 
  Download, 
  ExternalLink, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Gamepad2,
  Info
} from 'lucide-react';
import { useSteamImport, formatImportProgress, getImportPhaseColor } from '@/hooks/useSteamImport';
import { cn } from '@/lib/utils';

interface SteamImportCardProps {
  className?: string;
}

export const SteamImportCard: React.FC<SteamImportCardProps> = ({ 
  className
}) => {
  const [steamIdInput, setSteamIdInput] = useState('');
  const [validationError, setValidationError] = useState<string | null>(null);

  const {
    importProgress,
    importStats,
    isImporting,
    isLoadingStats,
    importError,
    startImport,
    validateSteamIdInput,
    refetchStats
  } = useSteamImport();

  const handleSteamIdChange = (value: string) => {
    setSteamIdInput(value);
    setValidationError(null);
  };

  const handleImport = () => {
    const validation = validateSteamIdInput(steamIdInput);
    
    if (!validation.isValid) {
      setValidationError(validation.error || 'Invalid Steam ID');
      return;
    }

    setValidationError(null);
    startImport(steamIdInput);
  };

  const handleExampleClick = (exampleId: string) => {
    setSteamIdInput(exampleId);
    setValidationError(null);
  };

  if (isLoadingStats) {
    return (
      <Card className={cn("h-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gamepad2 className="h-5 w-5" />
            Steam Library Import
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Gamepad2 className="h-5 w-5 text-[#1b2838]" />
            Steam Library Import
          </div>
          {importStats && importStats.steamGames > 0 && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              {importStats.steamGames} games imported
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Import Statistics */}
          {importStats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-[#1b2838]">
                  {importStats.steamGames}
                </div>
                <p className="text-xs text-muted-foreground">Steam Games</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {importStats.totalImported}
                </div>
                <p className="text-xs text-muted-foreground">Total Imported</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {importStats.epicGames}
                </div>
                <p className="text-xs text-muted-foreground">Epic Games</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {importStats.consoleGames}
                </div>
                <p className="text-xs text-muted-foreground">Console</p>
              </div>
            </div>
          )}

          {/* Import Progress */}
          {importProgress && (
            <div className="space-y-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="flex items-center justify-between">
                <p className={cn("font-medium", getImportPhaseColor(importProgress.phase))}>
                  {formatImportProgress(importProgress)}
                </p>
                <span className="text-sm font-mono">
                  {Math.round(importProgress.progress)}%
                </span>
              </div>
              <Progress value={importProgress.progress} className="h-2" />
              {importProgress.gamesProcessed && importProgress.totalGames && (
                <p className="text-sm text-muted-foreground">
                  {importProgress.gamesProcessed} of {importProgress.totalGames} games processed
                </p>
              )}
            </div>
          )}

          {/* Import Error */}
          {importError && (
            <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <p className="text-sm text-red-600">
                {importError instanceof Error ? importError.message : 'Import failed'}
              </p>
            </div>
          )}

          {/* Steam ID Input */}
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium mb-2 block">
                Steam Profile
              </label>
              <div className="space-y-2">
                <Input
                  value={steamIdInput}
                  onChange={(e) => handleSteamIdChange(e.target.value)}
                  placeholder="Enter Steam ID, custom URL, or profile URL"
                  disabled={isImporting}
                  className={validationError ? 'border-red-500' : ''}
                />
                {validationError && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {validationError}
                  </p>
                )}
              </div>
            </div>

            {/* Examples */}
            <div className="space-y-2">
              <p className="text-xs text-muted-foreground">Examples:</p>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExampleClick('76561198000000000')}
                  disabled={isImporting}
                  className="text-xs"
                >
                  Steam ID: 76561198...
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExampleClick('your_custom_url')}
                  disabled={isImporting}
                  className="text-xs"
                >
                  Custom URL: your_custom_url
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExampleClick('https://steamcommunity.com/id/username')}
                  disabled={isImporting}
                  className="text-xs"
                >
                  Profile URL
                </Button>
              </div>
            </div>

            {/* Import Button */}
            <Button
              onClick={handleImport}
              disabled={isImporting || !steamIdInput.trim()}
              className="w-full"
              size="lg"
            >
              {isImporting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Import Steam Library
                </>
              )}
            </Button>
          </div>

          {/* Information */}
          <div className="space-y-4 pt-4 border-t">
            <div className="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Info className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                  How to find your Steam ID:
                </p>
                <ul className="text-blue-700 dark:text-blue-300 space-y-1 text-xs">
                  <li>1. Open Steam and go to your profile</li>
                  <li>2. Copy the URL or custom URL from your profile</li>
                  <li>3. Your profile must be public for import to work</li>
                </ul>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open('https://steamcommunity.com', '_blank')}
                className="text-xs"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Open Steam Community
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open('https://steamid.io', '_blank')}
                className="text-xs"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Find Your Steam ID
              </Button>
              {importStats && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetchStats()}
                  className="text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Refresh Stats
                </Button>
              )}
            </div>
          </div>

          {/* Last Import Info */}
          {importStats?.lastImport && (
            <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div className="text-sm">
                <p className="text-green-800 dark:text-green-200">
                  Last import: {new Date(importStats.lastImport).toLocaleDateString()}
                </p>
                <p className="text-green-700 dark:text-green-300 text-xs">
                  {importStats.steamGames} Steam games in your collection
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};