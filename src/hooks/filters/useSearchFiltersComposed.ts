import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Game } from '@/types';
import { GameFilters, UseFiltersReturn, FilterResult } from '@/types/filters';
import { applyFilters } from '@/lib/filterUtils';
import { useFilterState } from './useFilterState';
import { useFilterPersistence, parseFiltersFromURL } from './useFilterPersistence';
import { useFilterActions } from './useFilterActions';

interface UseSearchFiltersOptions {
  onFilterChange?: (filters: GameFilters) => void;
  persistToURL?: boolean;
  enableDebounce?: boolean;
  debounceMs?: number;
}

export function useSearchFiltersComposed(
  games: Game[] = [],
  options: UseSearchFiltersOptions = {}
): UseFiltersReturn & {
  filteredGames: Game[];
  filterResult: FilterResult<Game> | null;
  isFiltering: boolean;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  performSearch: (query: string, additionalFilters?: Partial<GameFilters>) => Promise<void>;
  abortController: AbortController | null;
} {
  const {
    onFilterChange,
    persistToURL = false,
    enableDebounce = true,
    debounceMs = 300
  } = options;

  const [searchParams] = useSearchParams();

  // Initialize filters from URL if persistence is enabled
  const initialFilters = useMemo(() => {
    return persistToURL ? parseFiltersFromURL(searchParams) : undefined;
  }, [persistToURL, searchParams]);

  // Initialize search query from URL if persistence is enabled
  const initialSearchQuery = useMemo(() => {
    return persistToURL ? searchParams.get('q') || '' : '';
  }, [persistToURL, searchParams]);

  const {
    filters,
    searchQuery,
    setFilters,
    setSearchQuery,
    updateFilter,
    resetFilters,
    loadPreset,
    hasActiveFilters,
    activeFilterCount,
    validateFilters
  } = useFilterState({ 
    initialFilters 
  });

  // Set initial search query
  useMemo(() => {
    if (initialSearchQuery && searchQuery === '') {
      setSearchQuery(initialSearchQuery);
    }
  }, [initialSearchQuery, searchQuery, setSearchQuery]);

  useFilterPersistence(filters, searchQuery, { persistToURL });

  const {
    isFiltering,
    performSearch,
    abortController
  } = useFilterActions(filters, setFilters, setSearchQuery, {
    enableDebounce,
    debounceMs,
    onFilterChange
  });

  // Compute filtered games
  const filterResult = useMemo(() => {
    if (games.length === 0) return null;
    
    const currentFilters = {
      ...filters,
      search: {
        ...filters.search,
        enabled: searchQuery.trim().length > 0,
        query: searchQuery
      }
    };

    return applyFilters(games, currentFilters);
  }, [games, filters, searchQuery]);

  const filteredGames = filterResult?.data || [];

  return {
    filters,
    updateFilter,
    resetFilters,
    loadPreset,
    hasActiveFilters,
    activeFilterCount,
    validateFilters,
    filteredGames,
    filterResult,
    isFiltering,
    searchQuery,
    setSearchQuery,
    performSearch,
    abortController
  };
}