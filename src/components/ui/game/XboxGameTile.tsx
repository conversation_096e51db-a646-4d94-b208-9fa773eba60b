import { memo, useCallback, useState } from 'react';
import { Card } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Skeleton } from '@/components/ui/base/skeleton';
import { UserGameWithDetails } from '@/types/database';
import { useCustomArtwork } from '@/hooks/useCustomArtwork';
import { getStatusIconElement, getStatusColor } from '@/lib/gameStatusUtils';
import { 
  ImageIcon,
  Star,
  Calendar,
  Upload
} from '@/lib/icons';
import { cn } from '@/lib/utils';

export interface XboxGameTileProps {
  gameData: UserGameWithDetails;
  size?: 'small' | 'medium' | 'large';
  isSelected?: boolean;
  onSelect?: (gameData: UserGameWithDetails) => void;
  className?: string;
}

const tileSizes = {
  small: {
    container: 'w-40 h-[90px]', // 160x90px (16:9)
    artwork: 'aspect-[16/9]',
    title: 'text-xs',
    metadata: 'text-[10px]'
  },
  medium: {
    container: 'w-60 h-[135px]', // 240x135px (16:9)
    artwork: 'aspect-[16/9]',
    title: 'text-sm',
    metadata: 'text-xs'
  },
  large: {
    container: 'w-80 h-[180px]', // 320x180px (16:9)
    artwork: 'aspect-[16/9]',
    title: 'text-base',
    metadata: 'text-sm'
  }
} as const;

export const XboxGameTile = memo<XboxGameTileProps>(({ 
  gameData, 
  size = 'large',
  isSelected = false,
  onSelect,
  className 
}) => {
  const game = gameData.game;
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  
  // Get custom artwork for this user game
  const { getBestCoverImage, hasCustomArtwork } = useCustomArtwork(gameData.id);
  
  const sizeConfig = tileSizes[size];

  const handleTileClick = useCallback(() => {
    onSelect?.(gameData);
  }, [gameData, onSelect]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoaded(true);
  }, []);

  if (!game) return null;

  const bestImage = getBestCoverImage() || game.cover_image;

  return (
    <Card 
      className={cn(
        // Base Xbox tile styling
        'group relative overflow-hidden cursor-pointer border-0 bg-gradient-to-br from-card via-card to-card/90',
        'shadow-lg hover:shadow-2xl hover:shadow-primary/20',
        // Xbox-inspired hover animations
        'transition-all duration-300 ease-out',
        'hover:scale-105 hover:-translate-y-1',
        // Focus and selection states
        isSelected && 'ring-2 ring-primary ring-offset-2 ring-offset-background',
        'focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
        // Size configuration
        sizeConfig.container,
        className
      )}
      onClick={handleTileClick}
      tabIndex={0}
      role="button"
      aria-label={`${game.title} - ${gameData.status}`}
    >
      {/* Main artwork container */}
      <div className={cn(
        'relative w-full h-full overflow-hidden rounded-lg',
        sizeConfig.artwork
      )}>
        {/* Background gradient for Xbox aesthetic */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/80 via-muted/60 to-muted/40" />
        
        {/* Game artwork */}
        {bestImage && !imageError ? (
          <>
            {/* Loading skeleton */}
            {!imageLoaded && (
              <Skeleton 
                className="absolute inset-0 w-full h-full"
                shimmer={true}
              />
            )}
            
            {/* Main artwork image */}
            <img
              src={bestImage}
              alt={game.title}
              className={cn(
                'absolute inset-0 w-full h-full object-cover transition-all duration-500',
                imageLoaded 
                  ? 'opacity-100 group-hover:scale-110 group-hover:brightness-110' 
                  : 'opacity-0'
              )}
              loading="lazy"
              decoding="async"
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          </>
        ) : (
          /* Fallback placeholder with Xbox styling */
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-muted via-muted/90 to-muted/70">
            <div className="relative">
              <ImageIcon className={cn(
                'text-muted-foreground/60',
                size === 'small' ? 'h-8 w-8' : size === 'medium' ? 'h-12 w-12' : 'h-16 w-16'
              )} />
              {/* Xbox-style glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-full animate-pulse opacity-50" />
            </div>
            {size !== 'small' && (
              <p className={cn('mt-2 text-muted-foreground/80 font-medium', sizeConfig.metadata)}>
                No Artwork
              </p>
            )}
          </div>
        )}

        {/* Xbox-style overlay gradients */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Status indicator (top-left) */}
        <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-black/60 backdrop-blur-sm">
            {getStatusIconElement(gameData.status, 'h-4 w-4')}
          </div>
        </div>

        {/* Custom artwork indicator (top-right) */}
        {hasCustomArtwork && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Badge variant="secondary" className="bg-primary/90 text-primary-foreground border-0 text-xs backdrop-blur-sm">
              <Upload className="h-3 w-3 mr-1" />
              Custom
            </Badge>
          </div>
        )}

        {/* Game information overlay (bottom) */}
        <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/80 via-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          {/* Game title */}
          <h3 className={cn(
            'font-semibold text-white truncate mb-1',
            sizeConfig.title
          )}>
            {game.title}
          </h3>
          
          {/* Metadata row */}
          <div className={cn(
            'flex items-center gap-2 text-white/80',
            sizeConfig.metadata
          )}>
            {/* Status badge */}
            <Badge 
              variant="secondary" 
              className={cn(
                'text-xs border-0 backdrop-blur-sm',
                getStatusColor(gameData.status)
              )}
            >
              {gameData.status}
            </Badge>

            {/* Metacritic score */}
            {game.metacritic_score && (
              <Badge variant="secondary" className="bg-yellow-500/90 text-yellow-900 border-0 text-xs backdrop-blur-sm">
                <Star className="h-3 w-3 mr-1" />
                {game.metacritic_score}
              </Badge>
            )}

            {/* Release year */}
            {game.release_date && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{new Date(game.release_date).getFullYear()}</span>
              </div>
            )}
          </div>

          {/* Developer info (for larger tiles) */}
          {size === 'large' && game.developer && (
            <p className="text-white/60 text-xs mt-1 truncate">
              {game.developer}
            </p>
          )}
        </div>

        {/* Xbox-style accent glow */}
        <div className="absolute inset-0 rounded-lg ring-1 ring-white/10 ring-inset opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <div className="absolute top-0 left-0 w-1/3 h-1/3 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        
        {/* Selection indicator */}
        {isSelected && (
          <div className="absolute inset-0 ring-2 ring-primary ring-inset rounded-lg" />
        )}
      </div>
    </Card>
  );
});

XboxGameTile.displayName = 'XboxGameTile';