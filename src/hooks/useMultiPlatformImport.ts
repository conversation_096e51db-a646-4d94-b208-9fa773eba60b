/**
 * Multi-Platform Import Hook
 * Orchestrates imports across multiple gaming platforms
 */

import { useState, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { type Platform } from '@/lib/apiKeyStore';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';

// Import platform services (only Steam is supported as of July 2025)
import { steamPlatformAdapter } from '@/lib/imports/steamPlatformAdapter';
import { type BasePlatformService, type ImportResult, type ImportProgress } from '@/lib/imports/basePlatformService';
import { supabase } from '@/lib/supabase';

// Platform service registry (only Steam is supported as of July 2025)
const PLATFORM_SERVICES: Partial<Record<Platform, BasePlatformService>> = {
  steam: steamPlatformAdapter, // Full Steam integration with adapter
};

export interface PlatformImportStatus {
  platform: Platform;
  status: 'idle' | 'running' | 'completed' | 'failed';
  progress: ImportProgress | null;
  result: ImportResult | null;
  error: string | null;
  startTime: Date | null;
  endTime: Date | null;
}

export interface MultiPlatformImportOptions {
  platforms: Platform[];
  concurrent?: boolean;
  skipDuplicateDetection?: boolean;
  progressCallback?: (platform: Platform, progress: ImportProgress) => void;
}

interface UseMultiPlatformImportResult {
  // Status tracking
  importStatuses: Record<Platform, PlatformImportStatus>;
  isAnyImportRunning: boolean;
  completedImports: number;
  totalImports: number;
  
  // Actions
  startImport: (options: MultiPlatformImportOptions) => Promise<void>;
  stopImport: (platform?: Platform) => void;
  resetStatuses: () => void;
  
  // Platform configuration
  getConfiguredPlatforms: () => Promise<Platform[]>;
  isPlatformConfigured: (platform: Platform) => Promise<boolean>;
  
  // Import history
  importHistory: ImportHistoryRecord[];
  isLoadingHistory: boolean;
  refreshHistory: () => void;
  
  // Overall statistics
  getImportSummary: () => {
    totalGames: number;
    totalUpdated: number;
    totalErrors: number;
    successRate: number;
  };
}

interface ImportHistoryRecord {
  id: string;
  platform: string;
  started_at: string;
  games_imported: number;
  games_updated: number;
  status: string;
}

const QUERY_KEY = 'multi-platform-import';

export function useMultiPlatformImport(): UseMultiPlatformImportResult {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [importStatuses, setImportStatuses] = useState<Record<Platform, PlatformImportStatus>>({} as Record<Platform, PlatformImportStatus>);
  const [abortControllers, setAbortControllers] = useState<Record<Platform, AbortController>>({});

  // Fetch import history
  const { data: importHistory = [], isLoading: isLoadingHistory, refetch: refreshHistory } = useQuery({
    queryKey: [QUERY_KEY, 'history', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      const { data, error } = await supabase
        .from('import_history')
        .select('*')
        .eq('user_id', user.id)
        .order('started_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      return data || [];
    },
    enabled: !!user?.id,
    staleTime: 30 * 1000, // 30 seconds
  });

  // Initialize platform status
  const initializePlatformStatus = useCallback((platform: Platform): PlatformImportStatus => ({
    platform,
    status: 'idle',
    progress: null,
    result: null,
    error: null,
    startTime: null,
    endTime: null,
  }), []);

  // Update platform status
  const updatePlatformStatus = useCallback((platform: Platform, updates: Partial<PlatformImportStatus>) => {
    setImportStatuses(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        ...updates,
      },
    }));
  }, []);

  // Check if platform is configured
  const isPlatformConfigured = useCallback(async (platform: Platform): Promise<boolean> => {
    try {
      const service = PLATFORM_SERVICES[platform];
      if (!service) return false;
      return await service.isConfigured();
    } catch (error) {
      console.error(`Failed to check ${platform} configuration:`, error);
      return false;
    }
  }, []);

  // Get all configured platforms
  const getConfiguredPlatforms = useCallback(async (): Promise<Platform[]> => {
    const platforms = Object.keys(PLATFORM_SERVICES) as Platform[];
    const configuredPlatforms: Platform[] = [];

    for (const platform of platforms) {
      if (await isPlatformConfigured(platform)) {
        configuredPlatforms.push(platform);
      }
    }

    return configuredPlatforms;
  }, [isPlatformConfigured]);

  // Import single platform
  const importSinglePlatform = useCallback(async (
    platform: Platform,
    progressCallback?: (progress: ImportProgress) => void
  ): Promise<ImportResult> => {
    const service = PLATFORM_SERVICES[platform];
    if (!service) {
      throw new Error(`Service not available for platform: ${platform}`);
    }

    // Check if platform is configured
    const isConfigured = await isPlatformConfigured(platform);
    if (!isConfigured) {
      throw new Error(`Platform ${platform} is not configured. Please add API keys in Settings.`);
    }

    // Initialize status
    if (!importStatuses[platform]) {
      setImportStatuses(prev => ({
        ...prev,
        [platform]: initializePlatformStatus(platform),
      }));
    }

    // Update status to running
    updatePlatformStatus(platform, {
      status: 'running',
      startTime: new Date(),
      error: null,
      result: null,
    });

    try {
      // Start import with progress tracking
      const result = await service.importLibrary((progress) => {
        updatePlatformStatus(platform, { progress });
        progressCallback?.(progress);
      });

      // Update status based on result
      updatePlatformStatus(platform, {
        status: result.success ? 'completed' : 'failed',
        result,
        error: result.success ? null : (result.message || result.errors.join(', ')),
        endTime: new Date(),
      });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      updatePlatformStatus(platform, {
        status: 'failed',
        error: errorMessage,
        endTime: new Date(),
      });

      throw error;
    }
  }, [importStatuses, initializePlatformStatus, updatePlatformStatus, isPlatformConfigured]);

  // Start multi-platform import
  const startImport = useCallback(async (options: MultiPlatformImportOptions) => {
    const { platforms, concurrent = false, progressCallback } = options;

    // Validate platforms
    for (const platform of platforms) {
      if (!PLATFORM_SERVICES[platform]) {
        throw new Error(`Service not available for platform: ${platform}`);
      }
    }

    // Initialize statuses for all platforms
    platforms.forEach(platform => {
      if (!importStatuses[platform]) {
        setImportStatuses(prev => ({
          ...prev,
          [platform]: initializePlatformStatus(platform),
        }) as Record<Platform, PlatformImportStatus>);
      }
    });

    try {
      if (concurrent) {
        // Run imports concurrently
        const importPromises = platforms.map(platform => 
          importSinglePlatform(platform, (progress) => {
            progressCallback?.(platform, progress);
          }).catch(error => {
            console.error(`${platform} import failed:`, error);
            return null;
          })
        );

        await Promise.allSettled(importPromises);
      } else {
        // Run imports sequentially
        for (const platform of platforms) {
          try {
            await importSinglePlatform(platform, (progress) => {
              progressCallback?.(platform, progress);
            });
          } catch (error) {
            console.error(`${platform} import failed:`, error);
            // Continue with next platform
          }
        }
      }

      // Refresh history and user library
      refreshHistory();
      queryClient.invalidateQueries({ queryKey: ['user-games'] });
      queryClient.invalidateQueries({ queryKey: ['user-stats'] });

      // Show completion toast with detailed feedback
      const completedCount = platforms.filter(p => 
        importStatuses[p]?.status === 'completed'
      ).length;
      
      if (completedCount > 0) {
        toast.success(`Import completed for ${completedCount}/${platforms.length} platforms`);
      } else {
        // Show more specific error message based on the actual errors
        const failedPlatforms = platforms.filter(p => 
          importStatuses[p]?.status === 'failed'
        );
        
        if (failedPlatforms.length > 0) {
          const firstError = importStatuses[failedPlatforms[0]]?.error;
          if (firstError) {
            toast.error(firstError);
          } else {
            toast.error('All platform imports failed. Check your API keys and try again.');
          }
        } else {
          toast.error('All platform imports failed. Check your API keys and try again.');
        }
      }

    } catch (error) {
      console.error('Multi-platform import failed:', error);
      toast.error('Import failed. Please check your configuration and try again.');
    }
  }, [importStatuses, initializePlatformStatus, importSinglePlatform, refreshHistory, queryClient]);

  // Stop import for specific platform or all
  const stopImport = useCallback((platform?: Platform) => {
    if (platform) {
      // Stop specific platform
      const controller = abortControllers[platform];
      if (controller) {
        controller.abort();
        updatePlatformStatus(platform, {
          status: 'failed',
          error: 'Import cancelled by user',
          endTime: new Date(),
        });
      }
    } else {
      // Stop all imports
      Object.values(abortControllers).forEach(controller => {
        controller.abort();
      });
      
      Object.keys(importStatuses).forEach(p => {
        const platform = p as Platform;
        if (importStatuses[platform]?.status === 'running') {
          updatePlatformStatus(platform, {
            status: 'failed',
            error: 'Import cancelled by user',
            endTime: new Date(),
          });
        }
      });
    }
  }, [abortControllers, importStatuses, updatePlatformStatus]);

  // Reset all statuses
  const resetStatuses = useCallback(() => {
    setImportStatuses({} as Record<Platform, PlatformImportStatus>);
    setAbortControllers({});
  }, []);

  // Calculate derived state
  const isAnyImportRunning = Object.values(importStatuses).some(status => status.status === 'running');
  const completedImports = Object.values(importStatuses).filter(status => status.status === 'completed').length;
  const totalImports = Object.keys(importStatuses).length;

  // Get import summary
  const getImportSummary = useCallback(() => {
    const results = Object.values(importStatuses)
      .map(status => status.result)
      .filter(Boolean) as ImportResult[];

    const totalGames = results.reduce((sum, result) => sum + result.gamesImported, 0);
    const totalUpdated = results.reduce((sum, result) => sum + result.gamesUpdated, 0);
    const totalErrors = results.reduce((sum, result) => sum + result.errors.length, 0);
    const successfulImports = results.filter(result => result.success).length;
    const successRate = results.length > 0 ? (successfulImports / results.length) * 100 : 0;

    return {
      totalGames,
      totalUpdated,
      totalErrors,
      successRate,
    };
  }, [importStatuses]);

  return {
    // Status tracking
    importStatuses,
    isAnyImportRunning,
    completedImports,
    totalImports,
    
    // Actions
    startImport,
    stopImport,
    resetStatuses,
    
    // Platform configuration
    getConfiguredPlatforms,
    isPlatformConfigured,
    
    // Import history
    importHistory,
    isLoadingHistory,
    refreshHistory,
    
    // Overall statistics
    getImportSummary,
  };
}