import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { 
  ArrowLeft, 
  Star, 
  Calendar, 
  User, 
  Building, 
  Tag, 
  Play, 
  Heart,
  Plus,
  ExternalLink,
  CheckCircle,
  Edit3,
  Save,
} from 'lucide-react';
import { Button, Badge, Card, CardContent, CardHeader, CardTitle, Textarea } from '@/components/ui/base';
import { StarRating } from '@/components/ui/game';
import { LoadingSpinner, ImageGallery } from '@/components/ui/utils';
import { ArtworkGallery } from '@/components/ui/analytics';
import { Game } from '@/types';
import { supabase, db } from '@/lib/supabase';
import { useAddToLibrary, useAddToWishlist, useUpdateGameStatus } from '@/hooks/useGameActions';
import { useUserGameStatus } from '@/hooks/useUserLibrary';
import { useCustomArtwork } from '@/hooks/useCustomArtwork';

export default function GameDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isEditingRating, setIsEditingRating] = useState(false);
  const [tempRating, setTempRating] = useState(0);
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [tempNotes, setTempNotes] = useState('');
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const [galleryStartIndex, setGalleryStartIndex] = useState(0);

  const { data: game, isLoading, error } = useQuery({
    queryKey: ['game', id],
    queryFn: async () => {
      if (!id) throw new Error('No game ID provided');
      
      // First try to find the game by ID
      const { data: gameData, error: gameError } = await db.games.getById(id);
      
      if (gameError) {
        // If not found by ID, try to find by igdb_id
        const { data: gameByIgdb, error: igdbError } = await supabase
          .from('games')
          .select('*')
          .eq('igdb_id', id)
          .single();
        
        if (igdbError) {
          throw new Error('Game not found');
        }
        
        return gameByIgdb;
      }
      
      return gameData;
    },
    enabled: !!id,
  });

  // Convert database game to Game type
  const gameData: Game | null = game ? {
    id: game.id,
    title: game.title || 'Unknown Game',
    platforms: game.platforms,
    genres: game.genres || [],
    developer: game.developer,
    publisher: game.publisher,
    release_date: game.release_date,
    description: game.description,
    cover_image: game.cover_image,
    screenshots: game.screenshots || [],
    youtube_links: game.youtube_links || [],
    metacritic_score: game.metacritic_score,
    igdb_id: game.igdb_id,
  } : null;

  const addToLibrary = useAddToLibrary();
  const addToWishlist = useAddToWishlist();
  const updateGameStatus = useUpdateGameStatus();
  const { data: userGame } = useUserGameStatus(gameData?.id || '');
  const { getBestCoverImage } = useCustomArtwork(userGame?.id || '');

  const handleClose = () => {
    navigate(-1); // Go back to previous page
  };

  const handleAddToLibrary = () => {
    if (gameData) {
      addToLibrary.mutate(gameData);
    }
  };

  const handleAddToWishlist = () => {
    if (gameData) {
      addToWishlist.mutate(gameData);
    }
  };

  const handleRatingEdit = () => {
    setTempRating(userGame?.personal_rating || 0);
    setIsEditingRating(true);
  };

  const handleRatingSave = () => {
    if (userGame) {
      updateGameStatus.mutate({
        userGameId: userGame.id,
        status: userGame.status,
        personalRating: tempRating,
        personalNotes: userGame.personal_notes || ''
      });
    }
    setIsEditingRating(false);
  };

  const handleNotesEdit = () => {
    setTempNotes(userGame?.personal_notes || '');
    setIsEditingNotes(true);
  };

  const handleNotesSave = () => {
    if (userGame) {
      updateGameStatus.mutate({
        userGameId: userGame.id,
        status: userGame.status,
        personalRating: userGame.personal_rating,
        personalNotes: tempNotes
      });
    }
    setIsEditingNotes(false);
  };

  const getYouTubeThumbnail = (videoId: string, quality: 'maxres' | 'hq' | 'mq' | 'sd' = 'maxres') => {
    const qualityMap = {
      maxres: 'maxresdefault.jpg',
      hq: 'hqdefault.jpg',
      mq: 'mqdefault.jpg',
      sd: 'sddefault.jpg'
    };
    return `https://img.youtube.com/vi/${videoId}/${qualityMap[quality]}`;
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error || !gameData) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={handleClose} className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <div className="text-center py-8">
          <h1 className="text-2xl font-bold mb-2">Game Not Found</h1>
          <p className="text-muted-foreground">
            The game you're looking for could not be found.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={handleClose} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      </div>
      
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-2xl mb-2">{gameData.title}</CardTitle>
                <div className="flex flex-wrap gap-2">
                  {gameData.developer && (
                    <Badge variant="secondary">
                      <User className="h-3 w-3 mr-1" />
                      {gameData.developer}
                    </Badge>
                  )}
                  {gameData.publisher && (
                    <Badge variant="secondary">
                      <Building className="h-3 w-3 mr-1" />
                      {gameData.publisher}
                    </Badge>
                  )}
                  {gameData.release_date && (
                    <Badge variant="secondary">
                      <Calendar className="h-3 w-3 mr-1" />
                      {new Date(gameData.release_date).getFullYear()}
                    </Badge>
                  )}
                  {gameData.metacritic_score && (
                    <Badge variant="secondary">
                      <Star className="h-3 w-3 mr-1" />
                      {gameData.metacritic_score}
                    </Badge>
                  )}
                </div>
              </div>
              <div className="flex gap-2">
                {userGame ? (
                  <Button variant="outline" disabled>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    In Library
                  </Button>
                ) : (
                  <Button onClick={handleAddToLibrary} disabled={addToLibrary.isPending}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add to Library
                  </Button>
                )}
                <Button 
                  variant="outline" 
                  onClick={handleAddToWishlist}
                  disabled={addToWishlist.isPending}
                >
                  <Heart className="h-4 w-4 mr-2" />
                  Add to Wishlist
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Cover Image */}
              <div className="space-y-4">
                <div className="aspect-[2/3] relative overflow-hidden rounded-lg">
                  {(getBestCoverImage() || gameData.cover_image) ? (
                    <img
                      src={getBestCoverImage() || gameData.cover_image}
                      alt={gameData.title}
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <div className="w-full h-full bg-muted flex items-center justify-center">
                      <span className="text-muted-foreground">No Cover Image</span>
                    </div>
                  )}
                </div>
                
                {/* Custom Artwork */}
                {userGame && <ArtworkGallery userGameId={userGame.id} />}
              </div>

              {/* Game Details */}
              <div className="md:col-span-2 space-y-6">
                {/* Description */}
                {gameData.description && (
                  <div>
                    <h3 className="font-semibold mb-2">Description</h3>
                    <p className="text-muted-foreground">{gameData.description}</p>
                  </div>
                )}

                {/* Genres */}
                {gameData.genres && gameData.genres.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Genres</h3>
                    <div className="flex flex-wrap gap-2">
                      {gameData.genres.map((genre, index) => (
                        <Badge key={index} variant="outline">
                          <Tag className="h-3 w-3 mr-1" />
                          {genre}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Platforms */}
                {gameData.platforms && gameData.platforms.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Platforms</h3>
                    <div className="flex flex-wrap gap-2">
                      {gameData.platforms.map((platform, index) => (
                        <Badge key={index} variant="outline">
                          {platform}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* User Rating */}
                {userGame && (
                  <div>
                    <h3 className="font-semibold mb-2">Your Rating</h3>
                    {isEditingRating ? (
                      <div className="flex items-center gap-2">
                        <StarRating
                          rating={tempRating}
                          onRatingChange={setTempRating}
                          size="lg"
                        />
                        <Button size="sm" onClick={handleRatingSave}>
                          <Save className="h-4 w-4 mr-1" />
                          Save
                        </Button>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <StarRating
                          rating={userGame.personal_rating || 0}
                          readOnly
                          size="lg"
                        />
                        <Button size="sm" variant="outline" onClick={handleRatingEdit}>
                          <Edit3 className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </div>
                    )}
                  </div>
                )}

                {/* User Notes */}
                {userGame && (
                  <div>
                    <h3 className="font-semibold mb-2">Your Notes</h3>
                    {isEditingNotes ? (
                      <div className="space-y-2">
                        <Textarea
                          value={tempNotes}
                          onChange={(e) => setTempNotes(e.target.value)}
                          placeholder="Add your personal notes about this game..."
                          rows={4}
                        />
                        <div className="flex gap-2">
                          <Button size="sm" onClick={handleNotesSave}>
                            <Save className="h-4 w-4 mr-1" />
                            Save
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => setIsEditingNotes(false)}>
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-start gap-2">
                        <div className="flex-1">
                          {userGame.personal_notes ? (
                            <p className="text-muted-foreground whitespace-pre-wrap">{userGame.personal_notes}</p>
                          ) : (
                            <p className="text-muted-foreground/60 italic">No notes yet</p>
                          )}
                        </div>
                        <Button size="sm" variant="outline" onClick={handleNotesEdit}>
                          <Edit3 className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </div>
                    )}
                  </div>
                )}

                {/* Screenshots */}
                {gameData.screenshots && gameData.screenshots.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Screenshots</h3>
                    <div className="grid grid-cols-2 gap-2">
                      {gameData.screenshots.slice(0, 4).map((screenshot, index) => (
                        <div
                          key={index}
                          className="aspect-video relative overflow-hidden rounded-md cursor-pointer"
                          onClick={() => {
                            setGalleryStartIndex(index);
                            setIsGalleryOpen(true);
                          }}
                        >
                          <img
                            src={screenshot}
                            alt={`Screenshot ${index + 1}`}
                            className="object-cover w-full h-full hover:scale-110 transition-transform"
                          />
                        </div>
                      ))}
                    </div>
                    {gameData.screenshots.length > 4 && (
                      <p className="text-sm text-muted-foreground mt-2">
                        +{gameData.screenshots.length - 4} more screenshots
                      </p>
                    )}
                  </div>
                )}

                {/* YouTube Videos */}
                {gameData.youtube_links && gameData.youtube_links.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Videos</h3>
                    <div className="grid grid-cols-2 gap-2">
                      {gameData.youtube_links.slice(0, 2).map((link, index) => {
                        const videoId = link.split('v=')[1]?.split('&')[0];
                        return (
                          <div
                            key={index}
                            className="aspect-video relative overflow-hidden rounded-md cursor-pointer group"
                            onClick={() => window.open(link, '_blank')}
                          >
                            <img
                              src={getYouTubeThumbnail(videoId)}
                              alt={`Video ${index + 1}`}
                              className="object-cover w-full h-full"
                            />
                            <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                              <Play className="h-8 w-8 text-white" />
                            </div>
                            <div className="absolute bottom-2 right-2">
                              <ExternalLink className="h-4 w-4 text-white" />
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Image Gallery Modal */}
      {isGalleryOpen && gameData.screenshots && (
        <ImageGallery
          images={gameData.screenshots}
          startIndex={galleryStartIndex}
          onClose={() => setIsGalleryOpen(false)}
        />
      )}
    </div>
  );
}