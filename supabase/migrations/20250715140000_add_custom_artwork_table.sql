-- Add Custom Artwork Table Migration
-- Allows users to upload custom box art for their games with multiple view types

-- Create user_game_artwork table for custom uploaded artwork
CREATE TABLE IF NOT EXISTS user_game_artwork (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_game_id uuid NOT NULL REFERENCES user_games(id) ON DELETE CASCADE,
  artwork_type text NOT NULL CHECK (artwork_type IN ('front', 'back', 'spine', '3d', 'manual', 'disc')),
  file_url text NOT NULL,
  file_size bigint,
  file_type text,
  is_primary boolean DEFAULT false,
  uploaded_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  
  -- Ensure only one primary artwork per type per user game
  UNIQUE(user_game_id, artwork_type, is_primary) DEFERRABLE INITIALLY DEFERRED
);

-- Create indexes for performance
CREATE INDEX idx_user_game_artwork_user_game_id ON user_game_artwork(user_game_id);
CREATE INDEX idx_user_game_artwork_type ON user_game_artwork(artwork_type);
CREATE INDEX idx_user_game_artwork_primary ON user_game_artwork(user_game_id, artwork_type, is_primary) WHERE is_primary = true;

-- RLS Policies for user_game_artwork
ALTER TABLE user_game_artwork ENABLE ROW LEVEL SECURITY;

-- Users can only see artwork for their own games
DROP POLICY IF EXISTS "Users can view their own game artwork" ON user_game_artwork;
CREATE POLICY "Users can view their own game artwork"
  ON user_game_artwork FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_games ug 
      WHERE ug.id = user_game_artwork.user_game_id 
      AND ug.user_id = auth.uid()
    )
  );

-- Users can insert artwork for their own games
DROP POLICY IF EXISTS "Users can upload artwork for their own games" ON user_game_artwork;
CREATE POLICY "Users can upload artwork for their own games"
  ON user_game_artwork FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_games ug 
      WHERE ug.id = user_game_artwork.user_game_id 
      AND ug.user_id = auth.uid()
    )
  );

-- Users can update their own game artwork
DROP POLICY IF EXISTS "Users can update their own game artwork" ON user_game_artwork;
CREATE POLICY "Users can update their own game artwork"
  ON user_game_artwork FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_games ug 
      WHERE ug.id = user_game_artwork.user_game_id 
      AND ug.user_id = auth.uid()
    )
  );

-- Users can delete their own game artwork
DROP POLICY IF EXISTS "Users can delete their own game artwork" ON user_game_artwork;
CREATE POLICY "Users can delete their own game artwork"
  ON user_game_artwork FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_games ug 
      WHERE ug.id = user_game_artwork.user_game_id 
      AND ug.user_id = auth.uid()
    )
  );

-- Create storage bucket for custom artwork if it doesn't exist
INSERT INTO storage.buckets (id, name, public) 
VALUES ('custom-artwork', 'custom-artwork', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for custom artwork
DROP POLICY IF EXISTS "Custom artwork is publicly accessible" ON storage.objects;
CREATE POLICY "Custom artwork is publicly accessible"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'custom-artwork');

DROP POLICY IF EXISTS "Users can upload their own custom artwork" ON storage.objects;
CREATE POLICY "Users can upload their own custom artwork"
  ON storage.objects FOR INSERT
  TO authenticated
  WITH CHECK (
    bucket_id = 'custom-artwork' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

DROP POLICY IF EXISTS "Users can update their own custom artwork" ON storage.objects;
CREATE POLICY "Users can update their own custom artwork"
  ON storage.objects FOR UPDATE
  TO authenticated
  USING (
    bucket_id = 'custom-artwork' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

DROP POLICY IF EXISTS "Users can delete their own custom artwork" ON storage.objects;
CREATE POLICY "Users can delete their own custom artwork"
  ON storage.objects FOR DELETE
  TO authenticated
  USING (
    bucket_id = 'custom-artwork' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Function to automatically set is_primary to false for other artwork of same type
CREATE OR REPLACE FUNCTION handle_primary_artwork_constraint()
RETURNS TRIGGER AS $$
BEGIN
  -- If setting this artwork as primary, unset primary for other artwork of same type
  IF NEW.is_primary = true THEN
    UPDATE user_game_artwork 
    SET is_primary = false 
    WHERE user_game_id = NEW.user_game_id 
      AND artwork_type = NEW.artwork_type 
      AND id != NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for primary artwork constraint
DROP TRIGGER IF EXISTS trigger_handle_primary_artwork ON user_game_artwork;
CREATE TRIGGER trigger_handle_primary_artwork
  BEFORE INSERT OR UPDATE ON user_game_artwork
  FOR EACH ROW
  EXECUTE FUNCTION handle_primary_artwork_constraint();

-- Add comments for documentation
COMMENT ON TABLE user_game_artwork IS 'Stores custom artwork uploaded by users for their games';
COMMENT ON COLUMN user_game_artwork.artwork_type IS 'Type of artwork: front, back, spine, 3d, manual, disc';
COMMENT ON COLUMN user_game_artwork.is_primary IS 'Whether this is the primary artwork for this type';