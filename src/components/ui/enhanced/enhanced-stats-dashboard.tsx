import React, { memo, useMemo, useCallback, useState } from 'react';
import { UserGameWithDetails } from '@/types/database';
import { StatsDashboardSettings } from '@/types/library';
import { cn } from '@/lib/utils';
import { 
  TrendingUp,
  TrendingDown,
  Trophy,
  Clock,
  Calendar,
  Gamepad2,
  Star,
  BarChart3,
  PieChart,
  Target,
  Award,
  Zap,
  Activity,
  Settings,
  Download,
  Filter
} from '@/lib/icons';
import { Button } from '@/components/ui/base/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/base/tabs';
import { Progress } from '@/components/ui/base/progress';

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'neutral';
    label: string;
  };
  className?: string;
}

interface ChartData {
  name: string;
  value: number;
  color?: string;
  percentage?: number;
}

interface EnhancedStatsDashboardProps {
  games: UserGameWithDetails[];
  settings: StatsDashboardSettings;
  onSettingsChange: (settings: Partial<StatsDashboardSettings>) => void;
  className?: string;
}

// Statistics calculation utilities
const calculateStats = (games: UserGameWithDetails[]) => {
  const now = new Date();
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const thisYear = new Date(now.getFullYear(), 0, 1);
  
  // Basic counts
  const totalGames = games.length;
  const completedGames = games.filter(game => game.status === 'completed').length;
  const playingGames = games.filter(game => game.status === 'playing').length;
  const backlogGames = games.filter(game => game.status === 'backlog').length;
  const wishlistGames = games.filter(game => game.status === 'wishlist').length;
  
  // Time-based stats
  const totalHours = games.reduce((sum, game) => sum + (game.hours_played || 0), 0);
  const avgHoursPerGame = totalGames > 0 ? totalHours / totalGames : 0;
  
  // Monthly stats
  const thisMonthGames = games.filter(game => 
    new Date(game.date_added) >= thisMonth
  ).length;
  
  const thisYearGames = games.filter(game => 
    new Date(game.date_added) >= thisYear
  ).length;
  
  // Completion rate
  const completionRate = totalGames > 0 ? (completedGames / totalGames) * 100 : 0;
  
  // Rating stats
  const ratedGames = games.filter(game => game.personal_rating);
  const avgRating = ratedGames.length > 0 
    ? ratedGames.reduce((sum, game) => sum + (game.personal_rating || 0), 0) / ratedGames.length
    : 0;
  
  // Platform distribution
  const platformStats = games.reduce((acc, game) => {
    const platforms = game.game?.platforms || ['Unknown'];
    platforms.forEach(platform => {
      acc[platform] = (acc[platform] || 0) + 1;
    });
    return acc;
  }, {} as Record<string, number>);
  
  // Genre distribution
  const genreStats = games.reduce((acc, game) => {
    const genres = game.game?.genres || ['Unknown'];
    genres.forEach(genre => {
      acc[genre] = (acc[genre] || 0) + 1;
    });
    return acc;
  }, {} as Record<string, number>);
  
  // Status distribution
  const statusStats = {
    completed: completedGames,
    playing: playingGames,
    backlog: backlogGames,
    wishlist: wishlistGames
  };
  
  // Top developers
  const developerStats = games.reduce((acc, game) => {
    const developer = game.game?.developer || 'Unknown';
    acc[developer] = (acc[developer] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  // Recent activity (last 30 days)
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const recentGames = games.filter(game => 
    new Date(game.date_added) >= thirtyDaysAgo
  ).length;
  
  return {
    overview: {
      totalGames,
      completedGames,
      playingGames,
      backlogGames,
      wishlistGames,
      totalHours,
      avgHoursPerGame,
      completionRate,
      avgRating,
      thisMonthGames,
      thisYearGames,
      recentGames
    },
    distributions: {
      platforms: platformStats,
      genres: genreStats,
      statuses: statusStats,
      developers: developerStats
    }
  };
};

// Stat card component
const StatCard = memo<StatCardProps>(({ 
  title, 
  value, 
  subtitle, 
  icon, 
  trend, 
  className 
}) => {
  return (
    <Card className={cn('transition-all hover:shadow-md', className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <div className="flex items-baseline space-x-2">
              <span className="text-2xl font-bold">{value}</span>
              {subtitle && (
                <span className="text-sm text-muted-foreground">{subtitle}</span>
              )}
            </div>
            {trend && (
              <div className={cn(
                'flex items-center space-x-1 text-xs',
                trend.direction === 'up' && 'text-green-600',
                trend.direction === 'down' && 'text-red-600',
                trend.direction === 'neutral' && 'text-muted-foreground'
              )}>
                {trend.direction === 'up' && <TrendingUp size={12} />}
                {trend.direction === 'down' && <TrendingDown size={12} />}
                <span>{Math.abs(trend.value)}% {trend.label}</span>
              </div>
            )}
          </div>
          <div className="text-muted-foreground">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

StatCard.displayName = 'StatCard';

// Chart components (simplified without external chart library)
interface SimpleBarChartProps {
  data: ChartData[];
  title: string;
  height?: number;
}

const SimpleBarChart = memo<SimpleBarChartProps>(({ data, title, height = 200 }) => {
  const maxValue = Math.max(...data.map(item => item.value));
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 size={18} />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3" style={{ height }}>
          {data.slice(0, 8).map((item) => (
            <div key={item.name} className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="truncate">{item.name}</span>
                <span className="font-medium">{item.value}</span>
              </div>
              <Progress 
                value={(item.value / maxValue) * 100} 
                className="h-2"
              />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
});

SimpleBarChart.displayName = 'SimpleBarChart';

interface SimplePieChartProps {
  data: ChartData[];
  title: string;
}

const SimplePieChart = memo<SimplePieChartProps>(({ data, title }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  const colors = [
    'bg-blue-500',
    'bg-green-500', 
    'bg-yellow-500',
    'bg-purple-500',
    'bg-red-500',
    'bg-indigo-500',
    'bg-pink-500',
    'bg-teal-500'
  ];
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PieChart size={18} />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {data.slice(0, 6).map((item, index) => {
            const percentage = total > 0 ? (item.value / total) * 100 : 0;
            return (
              <div key={item.name} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={cn('w-3 h-3 rounded-full', colors[index % colors.length])} />
                  <span className="text-sm truncate">{item.name}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{item.value}</div>
                  <div className="text-xs text-muted-foreground">
                    {percentage.toFixed(1)}%
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
});

SimplePieChart.displayName = 'SimplePieChart';

// Achievement badges component
interface AchievementBadgesProps {
  stats: ReturnType<typeof calculateStats>;
}

const AchievementBadges = memo<AchievementBadgesProps>(({ stats }) => {
  const achievements = useMemo(() => {
    const badges = [];
    const { overview } = stats;
    
    // Collection size achievements
    if (overview.totalGames >= 100) badges.push({ 
      name: 'Collector', 
      description: '100+ games in collection',
      icon: <Trophy className="text-yellow-500" size={16} />
    });
    
    if (overview.totalGames >= 500) badges.push({ 
      name: 'Library Master', 
      description: '500+ games in collection',
      icon: <Award className="text-purple-500" size={16} />
    });
    
    // Completion achievements
    if (overview.completionRate >= 50) badges.push({
      name: 'Completionist',
      description: '50%+ completion rate',
      icon: <Target className="text-green-500" size={16} />
    });
    
    if (overview.completedGames >= 50) badges.push({
      name: 'Game Finisher',
      description: '50+ completed games',
      icon: <Zap className="text-blue-500" size={16} />
    });
    
    // Time achievements
    if (overview.totalHours >= 1000) badges.push({
      name: 'Time Traveler',
      description: '1000+ hours played',
      icon: <Clock className="text-orange-500" size={16} />
    });
    
    // Activity achievements
    if (overview.thisMonthGames >= 10) badges.push({
      name: 'Active Gamer',
      description: '10+ games this month',
      icon: <Activity className="text-red-500" size={16} />
    });
    
    return badges;
  }, [stats]);
  
  if (achievements.length === 0) return null;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Award size={18} />
          Achievements
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-3">
          {achievements.map((achievement, index) => (
            <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
              {achievement.icon}
              <div>
                <div className="font-medium text-sm">{achievement.name}</div>
                <div className="text-xs text-muted-foreground">{achievement.description}</div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
});

AchievementBadges.displayName = 'AchievementBadges';

export const EnhancedStatsDashboard = memo<EnhancedStatsDashboardProps>(({
  games,
  className
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const stats = useMemo(() => calculateStats(games), [games]);
  
  const handleExport = useCallback(() => {
    const statsData = {
      generated: new Date().toISOString(),
      summary: stats.overview,
      distributions: stats.distributions
    };
    
    const blob = new Blob([JSON.stringify(statsData, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'library-stats.json';
    a.click();
    URL.revokeObjectURL(url);
  }, [stats]);
  
  // Convert distributions to chart data
  const platformChartData: ChartData[] = Object.entries(stats.distributions.platforms)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value);
    
  const genreChartData: ChartData[] = Object.entries(stats.distributions.genres)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value);
    
  const statusChartData: ChartData[] = Object.entries(stats.distributions.statuses)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value);
    
  const developerChartData: ChartData[] = Object.entries(stats.distributions.developers)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value);

  if (games.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-64', className)}>
        <div className="text-center">
          <BarChart3 size={48} className="mx-auto text-muted-foreground mb-4" />
          <p className="text-lg text-muted-foreground">No games to analyze</p>
          <p className="text-sm text-muted-foreground mt-2">
            Add some games to your library to see statistics
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('enhanced-stats-dashboard space-y-6', className)}>
      {/* Dashboard Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 size={24} />
            Library Statistics
          </h2>
          <p className="text-muted-foreground">
            Insights and analytics for your game collection
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter size={16} className="mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download size={16} className="mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Settings size={16} className="mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Games"
          value={stats.overview.totalGames}
          icon={<Gamepad2 size={24} />}
          trend={{
            value: 12,
            direction: 'up',
            label: 'this month'
          }}
        />
        
        <StatCard
          title="Completion Rate"
          value={`${stats.overview.completionRate.toFixed(1)}%`}
          subtitle={`${stats.overview.completedGames} completed`}
          icon={<Trophy size={24} />}
          trend={{
            value: 5,
            direction: 'up',
            label: 'this month'
          }}
        />
        
        <StatCard
          title="Total Hours"
          value={Math.round(stats.overview.totalHours)}
          subtitle="played"
          icon={<Clock size={24} />}
          trend={{
            value: 8,
            direction: 'up',
            label: 'this month'
          }}
        />
        
        <StatCard
          title="Average Rating"
          value={stats.overview.avgRating.toFixed(1)}
          subtitle="/ 10"
          icon={<Star size={24} />}
          trend={{
            value: 2,
            direction: 'neutral',
            label: 'this month'
          }}
        />
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 w-full max-w-md">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="platforms">Platforms</TabsTrigger>
          <TabsTrigger value="genres">Genres</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <SimplePieChart 
              data={statusChartData}
              title="Game Status Distribution"
            />
            <SimpleBarChart 
              data={developerChartData}
              title="Top Developers"
            />
          </div>
          
          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar size={18} />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {stats.overview.recentGames}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Games added (30 days)
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {stats.overview.thisMonthGames}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Games this month
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {stats.overview.thisYearGames}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Games this year
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="platforms" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <SimpleBarChart 
              data={platformChartData}
              title="Games by Platform"
              height={300}
            />
            <SimplePieChart 
              data={platformChartData}
              title="Platform Distribution"
            />
          </div>
        </TabsContent>

        <TabsContent value="genres" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <SimpleBarChart 
              data={genreChartData}
              title="Games by Genre"
              height={300}
            />
            <SimplePieChart 
              data={genreChartData}
              title="Genre Distribution"
            />
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <AchievementBadges stats={stats} />
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target size={18} />
                  Goals & Targets
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Completion Target</span>
                    <span>{stats.overview.completionRate.toFixed(1)}% / 75%</span>
                  </div>
                  <Progress value={Math.min(stats.overview.completionRate / 75 * 100, 100)} />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Monthly Goal</span>
                    <span>{stats.overview.thisMonthGames} / 5 games</span>
                  </div>
                  <Progress value={Math.min(stats.overview.thisMonthGames / 5 * 100, 100)} />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Hours Target</span>
                    <span>{Math.round(stats.overview.totalHours)} / 1000h</span>
                  </div>
                  <Progress value={Math.min(stats.overview.totalHours / 1000 * 100, 100)} />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity size={18} />
                  Quick Facts
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Avg. hours per game</span>
                  <span className="font-medium">
                    {stats.overview.avgHoursPerGame.toFixed(1)}h
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Most played platform</span>
                  <span className="font-medium">
                    {platformChartData[0]?.name || 'N/A'}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Favorite genre</span>
                  <span className="font-medium">
                    {genreChartData[0]?.name || 'N/A'}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Backlog ratio</span>
                  <span className="font-medium">
                    {stats.overview.totalGames > 0 
                      ? ((stats.overview.backlogGames / stats.overview.totalGames) * 100).toFixed(1)
                      : 0}%
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
});

EnhancedStatsDashboard.displayName = 'EnhancedStatsDashboard';