import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/supabase';
import { Game } from '@/types';
import { GameRecord } from '@/types/database';

export interface GameAction {
  gameId: string;
  status: 'library' | 'wishlist' | 'playing' | 'completed' | 'dropped';
  rating?: number;
  notes?: string;
}

export function useAddToLibrary() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (game: Game) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // First, check if game already exists in games table
      const { data: existingGame, error: gameError } = await db.games.getByExternalId(game.id);
      
      if (gameError && gameError.code !== 'PGRST116') { // PGRST116 is "not found"
        throw gameError;
      }

      let gameUUID: string;

      // If game doesn't exist, create it
      if (!existingGame) {
        // Extract external ID fields from the game ID
        const gameRecord: Partial<GameRecord> = {
          title: game.title,
          description: game.description,
          cover_image: game.cover_image,
          screenshots: game.screenshots,
          release_date: game.release_date,
          metacritic_score: game.metacritic_score,
          platform: game.platforms?.[0] || 'PC', // Use first platform as primary
          developer: game.developer,
          publisher: game.publisher,
          genres: game.genres,
          youtube_links: game.youtube_links,
        };

        // Set the correct external ID field based on the game.id prefix
        if (game.id.startsWith('tgdb_')) {
          gameRecord.tgdb_id = game.id.replace('tgdb_', '');
        } else if (game.id.startsWith('igdb_')) {
          gameRecord.igdb_id = game.id.replace('igdb_', '');
        } else if (game.id.startsWith('steam_')) {
          gameRecord.steam_app_id = game.id.replace('steam_', '');
        } else {
          // For backward compatibility, use igdb_id if no prefix
          gameRecord.igdb_id = game.igdb_id || game.id;
        }

        const { data: newGame, error: createError } = await db.games.create(gameRecord);

        if (createError) {
          throw createError;
        }

        gameUUID = newGame.id;
      } else {
        gameUUID = existingGame.id;
      }

      // Add to user's library using the actual UUID
      const { data, error } = await db.userGames.addToCollection({
        user_id: user.id,
        game_id: gameUUID,
        status: 'backlog',
        date_added: new Date().toISOString(),
      });

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (_, variables) => {
      // Invalidate the consolidated user collection query with user ID
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      
      // Show success toast
      toast.success(`"${variables.title}" added to your library`);
    },
    onError: () => {
      toast.error('Failed to add game to library. Please try again');
    },
  });
}

export function useAddToWishlist() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (game: Game) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // First, check if game already exists in games table
      const { data: existingGame, error: gameError } = await db.games.getByExternalId(game.id);
      
      if (gameError && gameError.code !== 'PGRST116') { // PGRST116 is "not found"
        throw gameError;
      }

      let gameUUID: string;

      // If game doesn't exist, create it
      if (!existingGame) {
        // Extract external ID fields from the game ID
        const gameRecord: Partial<GameRecord> = {
          title: game.title,
          description: game.description,
          cover_image: game.cover_image,
          screenshots: game.screenshots,
          release_date: game.release_date,
          metacritic_score: game.metacritic_score,
          platform: game.platforms?.[0] || 'PC', // Use first platform as primary
          developer: game.developer,
          publisher: game.publisher,
          genres: game.genres,
          youtube_links: game.youtube_links,
        };

        // Set the correct external ID field based on the game.id prefix
        if (game.id.startsWith('tgdb_')) {
          gameRecord.tgdb_id = game.id.replace('tgdb_', '');
        } else if (game.id.startsWith('igdb_')) {
          gameRecord.igdb_id = game.id.replace('igdb_', '');
        } else if (game.id.startsWith('steam_')) {
          gameRecord.steam_app_id = game.id.replace('steam_', '');
        } else {
          // For backward compatibility, use igdb_id if no prefix
          gameRecord.igdb_id = game.igdb_id || game.id;
        }

        const { data: newGame, error: createError } = await db.games.create(gameRecord);

        if (createError) {
          throw createError;
        }

        gameUUID = newGame.id;
      } else {
        gameUUID = existingGame.id;
      }

      // Add to user's wishlist using the actual UUID
      const { data, error } = await db.userGames.addToCollection({
        user_id: user.id,
        game_id: gameUUID,
        status: 'wishlist',
        date_added: new Date().toISOString(),
      });

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (_, variables) => {
      // Invalidate the consolidated user collection query with user ID
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      
      // Show success toast
      toast.success(`"${variables.title}" added to your wishlist`);
    },
    onError: () => {
      toast.error('Failed to add game to wishlist. Please try again');
    },
  });
}

export function useUpdateGameStatus() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ userGameId, status, rating, notes }: { 
      userGameId: string; 
      status: 'library' | 'wishlist' | 'playing' | 'completed' | 'dropped' | 'backlog'; 
      rating?: number; 
      notes?: string; 
    }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await db.userGames.updateStatus(userGameId, {
        status,
        personal_rating: rating,
        personal_notes: notes,
        updated_at: new Date().toISOString(),
      });

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (_, variables) => {
      // Invalidate the consolidated user collection query with user ID
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      
      // Show success toast based on what was updated
      if (variables.rating && variables.notes) {
        toast.success('Game rating and notes updated successfully');
      } else if (variables.rating) {
        toast.success('Game rating updated successfully');
      } else if (variables.notes) {
        toast.success('Game notes saved successfully');
      } else {
        toast.success('Game status updated successfully');
      }
    },
    onError: () => {
      toast.error('Failed to update game. Please try again');
    },
  });
}

export function useRemoveFromCollection() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userGameId: string) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const { error } = await db.userGames.removeFromCollection(userGameId);

      if (error) {
        throw error;
      }

      return { success: true };
    },
    onSuccess: () => {
      // Invalidate the consolidated user collection query with user ID
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      
      // Show success toast
      toast.success('Game removed from your collection');
    },
    onError: () => {
      toast.error('Failed to remove game. Please try again');
    },
  });
}