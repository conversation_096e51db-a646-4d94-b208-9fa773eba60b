import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/base/tabs';
import { TagManager } from './TagManager';
import { TagAnalytics } from './TagAnalytics';
import { BulkTagOperations } from './BulkTagOperations';
import { SmartTagSuggestions } from './SmartTagSuggestions';
import { useUserLibrary } from '@/hooks/useUserLibrary';
import { 
  <PERSON>h, 
  BarChart3, 
  FileStack, 
  Sparkles, 
  Settings,
  Target,
  TrendingUp,
  Gamepad2
} from 'lucide-react';

interface TagDashboardProps {
  className?: string;
}

export function TagDashboard({ className }: TagDashboardProps) {
  const [selectedGameIds, setSelectedGameIds] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  
  const { games, isLoading } = useUserLibrary();

  const filteredGames = games || [];
  const untaggedGames = filteredGames.filter(game => !game.tags || game.tags.length === 0);
  const taggedGames = filteredGames.filter(game => game.tags && game.tags.length > 0);

  const handleGameSelection = (gameIds: string[]) => {
    setSelectedGameIds(gameIds);
  };

  const handleSelectUntaggedGames = () => {
    setSelectedGameIds(untaggedGames.map(game => game.id.toString()));
  };

  const handleSelectTaggedGames = () => {
    setSelectedGameIds(taggedGames.map(game => game.id.toString()));
  };

  const handleClearSelection = () => {
    setSelectedGameIds([]);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center text-muted-foreground">
          <Hash className="h-8 w-8 mx-auto mb-2 animate-pulse" />
          <p>Loading tag dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Hash className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="bulk-ops" className="flex items-center gap-2">
            <FileStack className="h-4 w-4" />
            Bulk Operations
          </TabsTrigger>
          <TabsTrigger value="ai-suggestions" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            AI Suggestions
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Gamepad2 className="h-4 w-4 text-primary" />
                  <div>
                    <p className="text-xs text-muted-foreground">Total Games</p>
                    <p className="text-lg font-semibold">{filteredGames.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Hash className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">Tagged</p>
                    <p className="text-lg font-semibold">{taggedGames.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-orange-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">Untagged</p>
                    <p className="text-lg font-semibold">{untaggedGames.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">Tag Coverage</p>
                    <p className="text-lg font-semibold">
                      {filteredGames.length > 0 
                        ? Math.round((taggedGames.length / filteredGames.length) * 100)
                        : 0}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <TagManager>
                  <Button variant="outline" className="w-full gap-2">
                    <Hash className="h-4 w-4" />
                    Manage Tags
                  </Button>
                </TagManager>

                <SmartTagSuggestions games={filteredGames}>
                  <Button variant="outline" className="w-full gap-2">
                    <Sparkles className="h-4 w-4" />
                    AI Suggestions
                  </Button>
                </SmartTagSuggestions>

                <Button
                  variant="outline"
                  className="w-full gap-2"
                  onClick={handleSelectUntaggedGames}
                  disabled={untaggedGames.length === 0}
                >
                  <Target className="h-4 w-4" />
                  Select Untagged ({untaggedGames.length})
                </Button>

                <BulkTagOperations
                  games={filteredGames}
                  selectedGameIds={selectedGameIds}
                  onSelectionChange={handleGameSelection}
                >
                  <Button 
                    variant="outline" 
                    className="w-full gap-2"
                    disabled={selectedGameIds.length === 0}
                  >
                    <FileStack className="h-4 w-4" />
                    Bulk Operations ({selectedGameIds.length})
                  </Button>
                </BulkTagOperations>
              </div>
            </CardContent>
          </Card>

          {/* Selection Controls */}
          {selectedGameIds.length > 0 && (
            <Card className="border-primary/20 bg-primary/5">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4 text-primary" />
                    <span className="font-medium">
                      {selectedGameIds.length} game{selectedGameIds.length !== 1 ? 's' : ''} selected
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSelectTaggedGames}
                    >
                      Select Tagged ({taggedGames.length})
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSelectUntaggedGames}
                    >
                      Select Untagged ({untaggedGames.length})
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleClearSelection}
                    >
                      Clear Selection
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommendations */}
          {(untaggedGames.length > 0 || taggedGames.length < filteredGames.length * 0.5) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {untaggedGames.length > 0 && (
                    <div className="p-3 rounded-lg bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800">
                      <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        {untaggedGames.length} games need tags
                      </p>
                      <p className="text-xs text-yellow-600 dark:text-yellow-300 mb-2">
                        Start by tagging your most frequently played games
                      </p>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleSelectUntaggedGames}
                        className="text-yellow-700 border-yellow-300 hover:bg-yellow-100"
                      >
                        Select All Untagged Games
                      </Button>
                    </div>
                  )}

                  {taggedGames.length < filteredGames.length * 0.5 && (
                    <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                      <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Improve your tag coverage
                      </p>
                      <p className="text-xs text-blue-600 dark:text-blue-300">
                        Use AI suggestions to quickly tag similar games
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <TagAnalytics />
        </TabsContent>

        {/* Bulk Operations Tab */}
        <TabsContent value="bulk-ops">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileStack className="h-5 w-5" />
                  Bulk Tag Operations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Select multiple games and apply tag operations efficiently.
                </p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectUntaggedGames}
                    disabled={untaggedGames.length === 0}
                  >
                    Select Untagged ({untaggedGames.length})
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectTaggedGames}
                    disabled={taggedGames.length === 0}
                  >
                    Select Tagged ({taggedGames.length})
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearSelection}
                    disabled={selectedGameIds.length === 0}
                  >
                    Clear Selection
                  </Button>
                </div>

                <BulkTagOperations
                  games={filteredGames}
                  selectedGameIds={selectedGameIds}
                  onSelectionChange={handleGameSelection}
                >
                  <Button 
                    className="w-full gap-2"
                    disabled={selectedGameIds.length === 0}
                  >
                    <FileStack className="h-4 w-4" />
                    Open Bulk Operations
                    {selectedGameIds.length > 0 && ` (${selectedGameIds.length} selected)`}
                  </Button>
                </BulkTagOperations>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* AI Suggestions Tab */}
        <TabsContent value="ai-suggestions">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  Smart Tag Suggestions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Let AI analyze your games and suggest relevant tags based on genres, platforms, ratings, and more.
                </p>
                
                <SmartTagSuggestions games={filteredGames}>
                  <Button className="w-full gap-2">
                    <Sparkles className="h-4 w-4" />
                    Generate Smart Tag Suggestions
                  </Button>
                </SmartTagSuggestions>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}