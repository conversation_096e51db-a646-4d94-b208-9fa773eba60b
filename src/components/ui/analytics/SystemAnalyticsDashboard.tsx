/**
 * System Analytics Dashboard - Unified Analytics View
 * Combines artwork API analytics, search analytics, collection insights, and system performance
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { <PERSON><PERSON> } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/base/tabs';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/base/alert';

// Import existing analytics components
import ServiceAnalytics from '@/components/ui/settings/ServiceAnalytics';
import { SearchPerformanceDashboard } from './SearchPerformanceDashboard';
import { SystemHealthAlerts } from './SystemHealthAlerts';

// Hooks
import { useSystemAnalytics } from '@/hooks/useSystemAnalytics';

// Icons
import {
  Activity,
  Database,
  Settings,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Cpu,
  HardDrive,
  Network,
  RefreshCw,
  Monitor,
  Users
} from 'lucide-react';

import { cn } from '@/lib/utils';

interface SystemAnalyticsDashboardProps {
  className?: string;
}

export function SystemAnalyticsDashboard({ className }: SystemAnalyticsDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');

  const {
    systemHealth,
    databaseMetrics,
    errorAnalytics,
    artworkAnalytics,
    isLoading,
    error,
    refreshAnalytics
  } = useSystemAnalytics();

  // Calculate system health score
  const systemHealthScore = useMemo(() => {
    if (!systemHealth) return null;

    const weights = {
      uptime: 0.3,
      performance: 0.3,
      errors: 0.2,
      database: 0.2
    };

    const uptimeScore = systemHealth.uptime / 100;
    const performanceScore = Math.max(0, (3000 - systemHealth.avgResponseTime) / 3000);
    const errorScore = Math.max(0, 1 - (systemHealth.errorRate / 10));
    const dbScore = systemHealth.databaseHealth / 100;

    const overallScore = (
      uptimeScore * weights.uptime +
      performanceScore * weights.performance +
      errorScore * weights.errors +
      dbScore * weights.database
    ) * 100;

    return Math.round(overallScore);
  }, [systemHealth]);

  // Get health status color
  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(0);
  };

  const formatBytes = (bytes: number) => {
    if (bytes >= **********) return `${(bytes / **********).toFixed(1)} GB`;
    if (bytes >= 1048576) return `${(bytes / 1048576).toFixed(1)} MB`;
    if (bytes >= 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${bytes} B`;
  };

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-red-500" />
            <p>Error loading system analytics</p>
            <Button variant="outline" onClick={refreshAnalytics} className="mt-2">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Activity className="h-8 w-8 mx-auto mb-2 animate-pulse" />
            <p>Loading system analytics...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <Monitor className="h-6 w-6 text-primary" />
            System Analytics Dashboard
            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
              Preview
            </Badge>
          </h2>
          <p className="text-muted-foreground">
            Monitor system performance, health, and usage across all services
            <span className="text-xs bg-yellow-50 text-yellow-700 px-2 py-1 rounded ml-2">
              Some metrics are simulated - full implementation coming soon
            </span>
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {systemHealthScore && (
            <div className="flex items-center gap-2 px-3 py-1 bg-muted rounded-lg">
              <span className="text-sm text-muted-foreground">Health:</span>
              <span className={cn("font-bold", getHealthColor(systemHealthScore))}>
                {systemHealthScore}%
              </span>
              <Badge variant="secondary" className="text-xs">
                Simulated
              </Badge>
            </div>
          )}
          
          <Button variant="outline" onClick={refreshAnalytics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Health Alert */}
      {systemHealth && systemHealthScore && systemHealthScore < 70 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            System health is below optimal levels ({systemHealthScore}%). 
            Check individual service metrics for potential issues.
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="search">Search</TabsTrigger>
          <TabsTrigger value="errors">Errors</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* System Health Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground flex items-center gap-1">
                      System Uptime
                      <Badge variant="outline" className="text-[10px] px-1 py-0 h-4">MOCK</Badge>
                    </p>
                    <p className="text-2xl font-bold">{systemHealth?.uptime.toFixed(1)}%</p>
                  </div>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <Progress value={systemHealth?.uptime || 0} className="h-1 mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground flex items-center gap-1">
                      Avg Response Time
                      <Badge variant="outline" className="text-[10px] px-1 py-0 h-4">MOCK</Badge>
                    </p>
                    <p className="text-2xl font-bold">{systemHealth?.avgResponseTime}ms</p>
                  </div>
                  <Clock className="h-4 w-4 text-blue-600" />
                </div>
                <div className="mt-1">
                  <span className={cn(
                    "text-xs",
                    (systemHealth?.avgResponseTime || 0) < 1000 ? "text-green-600" :
                    (systemHealth?.avgResponseTime || 0) < 3000 ? "text-yellow-600" : "text-red-600"
                  )}>
                    {(systemHealth?.avgResponseTime || 0) < 1000 ? 'Excellent' :
                     (systemHealth?.avgResponseTime || 0) < 3000 ? 'Good' : 'Needs attention'}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground flex items-center gap-1">
                      Error Rate
                      <Badge variant="outline" className="text-[10px] px-1 py-0 h-4">MOCK</Badge>
                    </p>
                    <p className="text-2xl font-bold">{systemHealth?.errorRate.toFixed(2)}%</p>
                  </div>
                  <AlertTriangle className={cn(
                    "h-4 w-4",
                    (systemHealth?.errorRate || 0) < 1 ? "text-green-600" : 
                    (systemHealth?.errorRate || 0) < 5 ? "text-yellow-600" : "text-red-600"
                  )} />
                </div>
                <Progress 
                  value={Math.min(100, (systemHealth?.errorRate || 0) * 10)} 
                  className="h-1 mt-2" 
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground flex items-center gap-1">
                      Active Users
                      <Badge variant="outline" className="text-[10px] px-1 py-0 h-4">MOCK</Badge>
                    </p>
                    <p className="text-2xl font-bold">{formatNumber(systemHealth?.activeUsers || 0)}</p>
                  </div>
                  <Users className="h-4 w-4 text-purple-600" />
                </div>
                <div className="mt-1">
                  <span className="text-xs text-muted-foreground">
                    Last 24h
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Resource Usage */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Cpu className="h-4 w-4" />
                  CPU Usage
                  <Badge variant="outline" className="text-[10px] px-1 py-0 h-4">SIMULATED</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-2xl font-bold">{systemHealth?.resources.cpu}%</span>
                    <Badge variant={systemHealth?.resources.cpu && systemHealth.resources.cpu > 80 ? "destructive" : "secondary"}>
                      {systemHealth?.resources.cpu && systemHealth.resources.cpu > 80 ? "High" : "Normal"}
                    </Badge>
                  </div>
                  <Progress value={systemHealth?.resources.cpu || 0} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <HardDrive className="h-4 w-4" />
                  Memory Usage
                  <Badge variant="outline" className="text-[10px] px-1 py-0 h-4">SIMULATED</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-2xl font-bold">{systemHealth?.resources.memory}%</span>
                    <Badge variant={systemHealth?.resources.memory && systemHealth.resources.memory > 85 ? "destructive" : "secondary"}>
                      {systemHealth?.resources.memory && systemHealth.resources.memory > 85 ? "High" : "Normal"}
                    </Badge>
                  </div>
                  <Progress value={systemHealth?.resources.memory || 0} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Network className="h-4 w-4" />
                  Network I/O
                  <Badge variant="outline" className="text-[10px] px-1 py-0 h-4">SIMULATED</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-xs text-muted-foreground space-y-1">
                    <div className="flex justify-between">
                      <span>In:</span>
                      <span>{formatBytes(systemHealth?.resources.networkIn || 0)}/s</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Out:</span>
                      <span>{formatBytes(systemHealth?.resources.networkOut || 0)}/s</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* System Health Alerts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <SystemHealthAlerts maxHeight="300px" />
            
            {/* Quick Service Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Service Status Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">API Gateway</p>
                      <p className="text-xs text-muted-foreground">Operational</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">Database</p>
                      <p className="text-xs text-muted-foreground">Healthy</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">Search Service</p>
                      <p className="text-xs text-muted-foreground">Rate Limited</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">Storage</p>
                      <p className="text-xs text-muted-foreground">Available</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Response Times */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Response Time Metrics
                  <Badge variant="secondary" className="text-xs">Coming Soon</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>API Endpoints</span>
                      <span>{systemHealth?.performance.apiResponseTime}ms</span>
                    </div>
                    <Progress value={Math.min(100, (3000 - (systemHealth?.performance.apiResponseTime || 0)) / 30)} className="h-2" />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Database Queries</span>
                      <span>{systemHealth?.performance.dbResponseTime}ms</span>
                    </div>
                    <Progress value={Math.min(100, (1000 - (systemHealth?.performance.dbResponseTime || 0)) / 10)} className="h-2" />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>External APIs</span>
                      <span>{systemHealth?.performance.externalApiResponseTime}ms</span>
                    </div>
                    <Progress value={Math.min(100, (5000 - (systemHealth?.performance.externalApiResponseTime || 0)) / 50)} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Throughput Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Throughput & Load
                  <Badge variant="secondary" className="text-xs">Coming Soon</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Requests per Second</span>
                    <span className="text-lg font-bold">{systemHealth?.performance.requestsPerSecond}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Concurrent Users</span>
                    <span className="text-lg font-bold">{systemHealth?.performance.concurrentUsers}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Queue Length</span>
                    <span className="text-lg font-bold">{systemHealth?.performance.queueLength}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Cache Hit Rate</span>
                    <span className="text-lg font-bold">{systemHealth?.performance.cacheHitRate}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Database Tab */}
        <TabsContent value="database" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Database Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Database Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                {databaseMetrics ? (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Connection Pool</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">{databaseMetrics.connections.active}/{databaseMetrics.connections.max}</span>
                        <Progress 
                          value={(databaseMetrics.connections.active / databaseMetrics.connections.max) * 100} 
                          className="w-20 h-2" 
                        />
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Query Performance</span>
                      <span className="text-sm">{databaseMetrics.avgQueryTime}ms</span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Slow Queries</span>
                      <Badge variant={databaseMetrics.slowQueries > 0 ? "destructive" : "secondary"}>
                        {databaseMetrics.slowQueries}
                      </Badge>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Index Usage</span>
                      <span className="text-sm">{databaseMetrics.indexHitRate}%</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Database className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>Database metrics unavailable</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Storage Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <HardDrive className="h-5 w-5" />
                  Storage Statistics
                </CardTitle>
              </CardHeader>
              <CardContent>
                {databaseMetrics ? (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Database Size</span>
                        <span>{formatBytes(databaseMetrics.storage.size)}</span>
                      </div>
                      <Progress value={databaseMetrics.storage.usage} className="h-2" />
                      <div className="text-xs text-muted-foreground text-right">
                        {databaseMetrics.storage.usage}% used
                      </div>
                    </div>
                    
                    <div className="pt-2 border-t">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Tables</p>
                          <p className="font-medium">{databaseMetrics.tables.count}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Indexes</p>
                          <p className="font-medium">{databaseMetrics.tables.indexes}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Records</p>
                          <p className="font-medium">{formatNumber(databaseMetrics.tables.records)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Avg Size</p>
                          <p className="font-medium">{formatBytes(databaseMetrics.tables.avgSize)}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <HardDrive className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>Storage metrics unavailable</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Services Tab */}
        <TabsContent value="services" className="space-y-6">
          <ServiceAnalytics 
            statistics={artworkAnalytics?.serviceStats || null}
            queueStatus={artworkAnalytics?.queueStatus || null}
          />
        </TabsContent>

        {/* Search Tab */}
        <TabsContent value="search" className="space-y-6">
          <SearchPerformanceDashboard />
        </TabsContent>

        {/* Errors Tab */}
        <TabsContent value="errors" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Error Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Error Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                {errorAnalytics ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-red-50 rounded-lg">
                        <div className="text-2xl font-bold text-red-600">{errorAnalytics.total}</div>
                        <div className="text-sm text-gray-600">Total Errors</div>
                      </div>
                      <div className="text-center p-3 bg-orange-50 rounded-lg">
                        <div className="text-2xl font-bold text-orange-600">{errorAnalytics.rate.toFixed(2)}%</div>
                        <div className="text-sm text-gray-600">Error Rate</div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="font-medium">Error Types</h4>
                      {Object.entries(errorAnalytics.byType).map(([type, count]) => (
                        <div key={type} className="flex justify-between items-center">
                          <span className="text-sm capitalize">{type.replace('_', ' ')}</span>
                          <Badge variant="outline">{count}</Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                    <p>No recent errors</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Errors */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Error Log
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64">
                  {errorAnalytics?.recent && errorAnalytics.recent.length > 0 ? (
                    <div className="space-y-2">
                      {errorAnalytics.recent.slice(0, 10).map((error, index) => (
                        <div key={index} className="p-2 rounded-lg bg-muted/50 text-sm">
                          <div className="flex justify-between items-start">
                            <span className="font-medium text-red-600">{error.type}</span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(error.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1 truncate">
                            {error.message}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <CheckCircle className="h-6 w-6 mx-auto mb-2 text-green-500" />
                      <p className="text-sm">No recent errors</p>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}