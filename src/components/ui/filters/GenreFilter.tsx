import { useState } from 'react';
import { Checkbox } from '@/components/ui/base/checkbox';
import { Input } from '@/components/ui/base/input';
import { But<PERSON> } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { Switch } from '@/components/ui/base/switch';
import { Label } from '@/components/ui/base/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/base/select';
import { Search, X, Plus, Minus } from 'lucide-react';
import { GenreFilter } from '@/types/filters';

interface GenreFilterComponentProps {
  filter: GenreFilter;
  onChange: (update: Partial<GenreFilter>) => void;
  disabled?: boolean;
}

// All available genres - this could be fetched from API in the future
const ALL_GENRES = [
  'Action', 'Adventure', 'RPG', 'Strategy', 'Simulation', 'Sports', 'Racing', 'Fighting',
  'Shooter', 'Platform', 'Puzzle', 'Arcade', 'Horror', 'Stealth', 'Survival',
  'Battle Royale', 'MMORPG', 'MOBA', 'Real-time Strategy', 'Turn-based Strategy',
  'Visual Novel', 'Dating Sim', 'Music', 'Rhythm', 'Educational', 'Casual',
  'Indie', 'Tactical', 'Card Game', 'Board Game', 'Party', 'Family',
  'Sandbox', 'Open World', 'Metroidvania', 'Roguelike', 'Roguelite'
];

// Genre categories for better organization
const GENRE_CATEGORIES = {
  'Action & Adventure': ['Action', 'Adventure', 'Platform', 'Metroidvania'],
  'Role-Playing': ['RPG', 'MMORPG', 'Tactical'],
  'Strategy': ['Strategy', 'Real-time Strategy', 'Turn-based Strategy', 'MOBA'],
  'Simulation & Sports': ['Simulation', 'Sports', 'Racing'],
  'Shooter & Fighting': ['Shooter', 'Fighting', 'Battle Royale'],
  'Puzzle & Casual': ['Puzzle', 'Casual', 'Card Game', 'Board Game', 'Party', 'Family'],
  'Survival & Horror': ['Survival', 'Horror', 'Stealth'],
  'Creative & Sandbox': ['Sandbox', 'Open World', 'Indie'],
  'Specialty': ['Music', 'Rhythm', 'Visual Novel', 'Dating Sim', 'Educational', 'Arcade'],
  'Roguelike': ['Roguelike', 'Roguelite']
};

export function GenreFilterComponent({ 
  filter, 
  onChange, 
  disabled = false 
}: GenreFilterComponentProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const filteredGenres = ALL_GENRES.filter(genre => 
    genre.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const genresByCategory = selectedCategory 
    ? { [selectedCategory]: GENRE_CATEGORIES[selectedCategory as keyof typeof GENRE_CATEGORIES] }
    : GENRE_CATEGORIES;

  const handleToggleEnabled = (enabled: boolean) => {
    onChange({ enabled });
  };

  const handleModeChange = (mode: 'include' | 'exclude') => {
    onChange({ mode });
  };

  const handleGenreToggle = (genre: string) => {
    const isSelected = filter.genres.includes(genre);
    const newGenres = isSelected
      ? filter.genres.filter(g => g !== genre)
      : [...filter.genres, genre];
    
    onChange({ genres: newGenres });
  };

  const handleSelectAll = (genres: string[]) => {
    const allSelected = genres.every(genre => filter.genres.includes(genre));
    
    if (allSelected) {
      // Deselect all genres in this category
      const newGenres = filter.genres.filter(g => !genres.includes(g));
      onChange({ genres: newGenres });
    } else {
      // Select all genres in this category
      const newGenres = [...new Set([...filter.genres, ...genres])];
      onChange({ genres: newGenres });
    }
  };

  const clearAll = () => {
    onChange({ genres: [] });
  };

  const getCategorySelectionState = (genres: string[]) => {
    const selectedCount = genres.filter(g => filter.genres.includes(g)).length;
    if (selectedCount === 0) return 'none';
    if (selectedCount === genres.length) return 'all';
    return 'partial';
  };

  return (
    <div className="space-y-4">
      {/* Enable/Disable Toggle */}
      <div className="flex items-center justify-between">
        <Label htmlFor="genre-enabled" className="text-sm font-medium">
          Filter by Genre
        </Label>
        <Switch
          id="genre-enabled"
          checked={filter.enabled}
          onCheckedChange={handleToggleEnabled}
          disabled={disabled}
        />
      </div>

      {filter.enabled && (
        <>
          {/* Include/Exclude Mode */}
          <div className="flex items-center gap-3">
            <Label className="text-sm text-muted-foreground">Mode:</Label>
            <Select
              value={filter.mode}
              onValueChange={handleModeChange}
              disabled={disabled}
            >
              <SelectTrigger className="w-32 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="include">
                  <div className="flex items-center gap-2">
                    <Plus className="h-3 w-3 text-green-600" />
                    Include
                  </div>
                </SelectItem>
                <SelectItem value="exclude">
                  <div className="flex items-center gap-2">
                    <Minus className="h-3 w-3 text-red-600" />
                    Exclude
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search genres..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              disabled={disabled}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery('')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Selected Genres Summary */}
          {filter.genres.length > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex flex-wrap gap-1">
                {filter.genres.slice(0, 3).map(genre => (
                  <Badge 
                    key={genre} 
                    variant={filter.mode === 'include' ? 'secondary' : 'destructive'} 
                    className="text-xs"
                  >
                    {filter.mode === 'exclude' && <Minus className="h-3 w-3 mr-1" />}
                    {filter.mode === 'include' && <Plus className="h-3 w-3 mr-1" />}
                    {genre}
                  </Badge>
                ))}
                {filter.genres.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{filter.genres.length - 3} more
                  </Badge>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAll}
                className="text-xs h-6"
                disabled={disabled}
              >
                Clear All
              </Button>
            </div>
          )}

          {/* Mode Description */}
          <div className="text-xs text-muted-foreground p-2 bg-muted/50 rounded">
            {filter.mode === 'include' 
              ? 'Show games that have ANY of the selected genres'
              : 'Hide games that have ANY of the selected genres'
            }
          </div>

          {/* Category Filter Buttons */}
          {!searchQuery && (
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === null ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(null)}
                className="text-xs h-7"
              >
                All Categories
              </Button>
              {Object.keys(GENRE_CATEGORIES).map(category => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(selectedCategory === category ? null : category)}
                  className="text-xs h-7"
                >
                  {category}
                </Button>
              ))}
            </div>
          )}

          {/* Genre Selection */}
          <ScrollArea className="h-48">
            <div className="space-y-4">
              {searchQuery ? (
                // Show filtered results when searching
                <div className="space-y-2">
                  {filteredGenres.map(genre => (
                    <div key={genre} className="flex items-center space-x-2">
                      <Checkbox
                        id={`genre-${genre}`}
                        checked={filter.genres.includes(genre)}
                        onCheckedChange={() => handleGenreToggle(genre)}
                        disabled={disabled}
                      />
                      <Label
                        htmlFor={`genre-${genre}`}
                        className="text-sm cursor-pointer flex-1"
                      >
                        {genre}
                      </Label>
                    </div>
                  ))}
                  {filteredGenres.length === 0 && (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      No genres found matching "{searchQuery}"
                    </p>
                  )}
                </div>
              ) : (
                // Show by category when not searching
                Object.entries(genresByCategory).map(([category, genres]) => (
                  <div key={category} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium text-muted-foreground">
                        {category}
                      </Label>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSelectAll(genres)}
                        className="text-xs h-6"
                        disabled={disabled}
                      >
                        {getCategorySelectionState(genres) === 'all' ? 'Deselect All' : 'Select All'}
                      </Button>
                    </div>
                    <div className="space-y-1 pl-4">
                      {genres.map(genre => (
                        <div key={genre} className="flex items-center space-x-2">
                          <Checkbox
                            id={`genre-${genre}`}
                            checked={filter.genres.includes(genre)}
                            onCheckedChange={() => handleGenreToggle(genre)}
                            disabled={disabled}
                          />
                          <Label
                            htmlFor={`genre-${genre}`}
                            className="text-sm cursor-pointer flex-1"
                          >
                            {genre}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </>
      )}
    </div>
  );
}