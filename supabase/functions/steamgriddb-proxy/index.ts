const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, content-type, apikey, x-client-info, x-api-key",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

// Response cache for performance optimization
const responseCache = new Map<string, { data: unknown; expiry: number }>();
const CACHE_TTL = 10 * 60 * 1000; // 10 minutes cache for artwork (longer than search results)

// Cache utility functions
function getCacheKey(endpoint: string, params: unknown, apiKey: string): string {
  const input = `${endpoint}:${JSON.stringify(params)}:${apiKey.substring(0, 8)}`;
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

function getCachedResponse(cacheKey: string): unknown | null {
  const cached = responseCache.get(cacheKey);
  if (!cached) return null;
  
  const now = Date.now();
  if (now > cached.expiry) {
    responseCache.delete(cacheKey);
    return null;
  }
  
  return cached.data;
}

function setCachedResponse(cacheKey: string, data: unknown): void {
  const expiry = Date.now() + CACHE_TTL;
  responseCache.set(cacheKey, { data, expiry });
  
  // Clean up expired entries if cache gets too large
  if (responseCache.size > 500) {
    const now = Date.now();
    for (const [key, value] of responseCache.entries()) {
      if (now > value.expiry) {
        responseCache.delete(key);
      }
    }
  }
}

Deno.serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { endpoint, params } = await req.json();
    console.log("Received request for SteamGridDB endpoint:", endpoint);
    console.log("Request params:", params);

    if (!endpoint) {
      console.error("Endpoint is missing from the request body.");
      return new Response(JSON.stringify({ error: "Endpoint missing from request body" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const apiKey = req.headers.get("X-API-Key");
    console.log("API Key:", apiKey ? "Present" : "Missing");

    if (!apiKey) {
      console.error("Missing SteamGridDB API key");
      return new Response(JSON.stringify({ error: "Missing API key header" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Check cache first for performance
    const cacheKey = getCacheKey(endpoint, params, apiKey);
    const cachedData = getCachedResponse(cacheKey);
    
    if (cachedData) {
      console.log("✅ Cache hit for SteamGridDB query, returning cached response");
      return new Response(JSON.stringify(cachedData), {
        status: 200,
        headers: { 
          ...corsHeaders, 
          "Content-Type": "application/json",
          "X-Cache": "HIT"
        },
      });
    }

    // Build the request URL with parameters
    const url = new URL(`https://www.steamgriddb.com/api/v2${endpoint}`);
    
    // Add query parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            url.searchParams.append(key, value.join(','));
          } else {
            url.searchParams.append(key, value.toString());
          }
        }
      });
    }

    console.log("Making request to SteamGridDB API:", url.toString());
    
    const sgdbResponse = await fetch(url.toString(), {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Accept": "application/json",
        "User-Agent": "Codexa-Game-Library/1.0"
      },
    });

    console.log("SteamGridDB Response status:", sgdbResponse.status);
    const responseText = await sgdbResponse.text();
    console.log("SteamGridDB Response data length:", responseText.length);
    console.log("SteamGridDB Response data sample:", responseText.substring(0, 200));

    if (!sgdbResponse.ok) {
      console.error("SteamGridDB API error:", responseText);
      
      // Handle specific error cases
      if (sgdbResponse.status === 401) {
        throw new Error("SteamGridDB API: Invalid API key");
      } else if (sgdbResponse.status === 403) {
        throw new Error("SteamGridDB API: Access forbidden or rate limit exceeded");
      } else if (sgdbResponse.status === 404) {
        throw new Error("SteamGridDB API: Resource not found");
      } else if (sgdbResponse.status === 400) {
        throw new Error("SteamGridDB API: Bad request - check parameters");
      } else {
        throw new Error(`SteamGridDB API error: ${sgdbResponse.status} - ${responseText}`);
      }
    }

    // Parse the response as JSON to validate it
    let jsonData;
    try {
      jsonData = JSON.parse(responseText);
      console.log("Successfully parsed JSON response");
      console.log("Response success:", jsonData.success);
    } catch (parseError) {
      console.error("Failed to parse SteamGridDB response as JSON:", parseError);
      throw new Error(`Invalid JSON response from SteamGridDB: ${responseText}`);
    }

    // Check if SteamGridDB returned an error in the response
    if (!jsonData.success) {
      console.error("SteamGridDB returned error:", jsonData.errors);
      throw new Error(`SteamGridDB error: ${jsonData.errors?.join(', ') || 'Unknown error'}`);
    }

    // Cache the successful response for future requests
    setCachedResponse(cacheKey, jsonData.data);
    console.log("✅ SteamGridDB response cached for future requests");

    return new Response(JSON.stringify(jsonData.data), {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json",
        "X-Cache": "MISS"
      },
    });
  } catch (error) {
    console.error("Edge function error:", error);
    return new Response(JSON.stringify({ 
      error: error.message,
      details: "Failed to fetch data from SteamGridDB API"
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});