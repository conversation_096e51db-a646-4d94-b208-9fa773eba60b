import React from 'react';
import { Badge } from '@/components/ui/base/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/base/tooltip';
import { Progress } from '@/components/ui/base/progress';
import { 
  Trophy, 
  Star, 
  Crown, 
  Award,
  Target,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SteamAchievementBadgeProps {
  achievementsUnlocked: number;
  achievementsTotal: number;
  rareAchievements?: number;
  lastUnlockedDate?: string;
  className?: string;
  variant?: 'compact' | 'detailed' | 'minimal';
}

export const SteamAchievementBadge: React.FC<SteamAchievementBadgeProps> = ({
  achievementsUnlocked,
  achievementsTotal,
  rareAchievements = 0,
  lastUnlockedDate,
  className,
  variant = 'compact'
}) => {
  const completionPercentage = achievementsTotal > 0 
    ? Math.round((achievementsUnlocked / achievementsTotal) * 100) 
    : 0;

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 90) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    if (percentage >= 70) return 'text-green-600 bg-green-50 border-green-200';
    if (percentage >= 50) return 'text-blue-600 bg-blue-50 border-blue-200';
    if (percentage >= 25) return 'text-orange-600 bg-orange-50 border-orange-200';
    return 'text-gray-600 bg-gray-50 border-gray-200';
  };

  const getCompletionIcon = (percentage: number) => {
    if (percentage >= 90) return <Crown className="h-3 w-3" />;
    if (percentage >= 70) return <Trophy className="h-3 w-3" />;
    if (percentage >= 50) return <Award className="h-3 w-3" />;
    if (percentage >= 25) return <Target className="h-3 w-3" />;
    return <Trophy className="h-3 w-3 opacity-50" />;
  };

  const formatLastUnlocked = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  if (achievementsTotal === 0) {
    return null; // Don't show badge if game has no achievements
  }

  if (variant === 'minimal') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            <Badge 
              variant="outline" 
              className={cn(
                "text-xs px-1.5 py-0.5 gap-1",
                getCompletionColor(completionPercentage),
                className
              )}
            >
              {getCompletionIcon(completionPercentage)}
              {completionPercentage}%
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <p className="font-medium">{achievementsUnlocked}/{achievementsTotal} Achievements</p>
              {rareAchievements > 0 && (
                <p className="text-xs text-purple-400">{rareAchievements} rare</p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (variant === 'compact') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            <div className={cn("flex items-center gap-1.5", className)}>
              <Badge 
                variant="outline" 
                className={cn(
                  "text-xs px-2 py-1 gap-1.5",
                  getCompletionColor(completionPercentage)
                )}
              >
                {getCompletionIcon(completionPercentage)}
                {achievementsUnlocked}/{achievementsTotal}
              </Badge>
              {rareAchievements > 0 && (
                <Badge variant="outline" className="text-xs px-1.5 py-0.5 bg-purple-50 text-purple-600 border-purple-200">
                  <Star className="h-2.5 w-2.5 mr-0.5" />
                  {rareAchievements}
                </Badge>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-2">
              <div className="text-center">
                <p className="font-medium">Achievement Progress</p>
                <p className="text-sm text-muted-foreground">
                  {achievementsUnlocked} of {achievementsTotal} unlocked ({completionPercentage}%)
                </p>
              </div>
              <Progress value={completionPercentage} className="h-1.5 w-32" />
              {rareAchievements > 0 && (
                <p className="text-xs text-purple-400 flex items-center gap-1">
                  <Star className="h-3 w-3" />
                  {rareAchievements} rare achievements
                </p>
              )}
              {lastUnlockedDate && (
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Last: {formatLastUnlocked(lastUnlockedDate)}
                </p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Detailed variant
  return (
    <div className={cn("space-y-2 p-3 bg-muted/30 rounded-lg border", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {getCompletionIcon(completionPercentage)}
          <span className="text-sm font-medium">Achievements</span>
        </div>
        <Badge 
          variant="outline" 
          className={cn(
            "text-xs",
            getCompletionColor(completionPercentage)
          )}
        >
          {completionPercentage}%
        </Badge>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Progress</span>
          <span className="font-medium">{achievementsUnlocked}/{achievementsTotal}</span>
        </div>
        <Progress value={completionPercentage} className="h-2" />
      </div>

      {(rareAchievements > 0 || lastUnlockedDate) && (
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          {rareAchievements > 0 && (
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3 text-purple-500" />
              <span>{rareAchievements} rare</span>
            </div>
          )}
          {lastUnlockedDate && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>Last: {formatLastUnlocked(lastUnlockedDate)}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Achievement rarity indicator component
interface AchievementRarityProps {
  globalPercentage: number;
  className?: string;
}

export const AchievementRarity: React.FC<AchievementRarityProps> = ({
  globalPercentage,
  className
}) => {
  const getRarityInfo = (percentage: number) => {
    if (percentage < 1) return { label: 'Ultra Rare', color: 'text-purple-600 bg-purple-50 border-purple-200', icon: Crown };
    if (percentage < 5) return { label: 'Very Rare', color: 'text-indigo-600 bg-indigo-50 border-indigo-200', icon: Star };
    if (percentage < 15) return { label: 'Rare', color: 'text-blue-600 bg-blue-50 border-blue-200', icon: Award };
    if (percentage < 50) return { label: 'Uncommon', color: 'text-green-600 bg-green-50 border-green-200', icon: Target };
    return { label: 'Common', color: 'text-gray-600 bg-gray-50 border-gray-200', icon: Trophy };
  };

  const rarity = getRarityInfo(globalPercentage);
  const Icon = rarity.icon;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <Badge 
            variant="outline" 
            className={cn(
              "text-xs px-2 py-1 gap-1",
              rarity.color,
              className
            )}
          >
            <Icon className="h-3 w-3" />
            {rarity.label}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-sm">
            {globalPercentage.toFixed(1)}% of players have this achievement
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Steam friends comparison component
interface SteamFriendsComparisonProps {
  commonGames: number;
  friendName: string;
  className?: string;
}

export const SteamFriendsComparison: React.FC<SteamFriendsComparisonProps> = ({
  commonGames,
  friendName,
  className
}) => {
  return (
    <Badge 
      variant="outline" 
      className={cn(
        "text-xs px-2 py-1 gap-1 bg-blue-50 text-blue-600 border-blue-200",
        className
      )}
    >
      <Trophy className="h-3 w-3" />
      {commonGames} with {friendName}
    </Badge>
  );
};
