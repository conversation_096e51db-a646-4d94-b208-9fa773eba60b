import { useState } from 'react';
import { Input } from '@/components/ui/base/input';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Switch } from '@/components/ui/base/switch';
import { Label } from '@/components/ui/base/label';
import { Slider } from '@/components/ui/base/slider';
import { Star, X } from 'lucide-react';
import { RatingFilter } from '@/types/filters';

interface RatingFilterComponentProps {
  filter: RatingFilter;
  onChange: (update: Partial<RatingFilter>) => void;
  disabled?: boolean;
}

const MIN_RATING = 0;
const MAX_RATING = 100;

// Rating presets based on common Metacritic score ranges
const RATING_PRESETS = [
  { label: 'Universal Acclaim (90+)', minRating: 90, maxRating: 100 },
  { label: 'Generally Favorable (75+)', minRating: 75, maxRating: 100 },
  { label: 'Mixed or Average (50-74)', minRating: 50, maxRating: 74 },
  { label: 'Generally Unfavorable (25-49)', minRating: 25, maxRating: 49 },
  { label: 'Overwhelming Dislike (0-24)', minRating: 0, maxRating: 24 },
  { label: 'Highly Rated (85+)', minRating: 85, maxRating: 100 },
  { label: 'Good Games (70+)', minRating: 70, maxRating: 100 },
  { label: 'Decent Games (60+)', minRating: 60, maxRating: 100 }
];

export function RatingFilterComponent({ 
  filter, 
  onChange, 
  disabled = false 
}: RatingFilterComponentProps) {
  const [sliderValues, setSliderValues] = useState<number[]>([
    filter.minRating || MIN_RATING,
    filter.maxRating || MAX_RATING
  ]);

  const handleToggleEnabled = (enabled: boolean) => {
    onChange({ enabled });
  };

  const handleMinRatingChange = (value: string) => {
    const rating = value === '' ? undefined : parseInt(value);
    if (rating === undefined || (rating >= MIN_RATING && rating <= MAX_RATING)) {
      onChange({ minRating: rating });
      if (rating !== undefined) {
        setSliderValues([rating, filter.maxRating || MAX_RATING]);
      }
    }
  };

  const handleMaxRatingChange = (value: string) => {
    const rating = value === '' ? undefined : parseInt(value);
    if (rating === undefined || (rating >= MIN_RATING && rating <= MAX_RATING)) {
      onChange({ maxRating: rating });
      if (rating !== undefined) {
        setSliderValues([filter.minRating || MIN_RATING, rating]);
      }
    }
  };

  const handleSliderChange = (values: number[]) => {
    setSliderValues(values);
    onChange({
      minRating: values[0],
      maxRating: values[1]
    });
  };

  const handlePresetClick = (preset: { minRating: number; maxRating: number }) => {
    onChange({
      minRating: preset.minRating,
      maxRating: preset.maxRating
    });
    setSliderValues([preset.minRating, preset.maxRating]);
  };

  const clearFilter = () => {
    onChange({ minRating: undefined, maxRating: undefined });
    setSliderValues([MIN_RATING, MAX_RATING]);
  };

  const hasActiveRange = filter.minRating !== undefined || filter.maxRating !== undefined;

  const getRatingColor = (rating: number) => {
    if (rating >= 85) return 'text-green-600';
    if (rating >= 70) return 'text-yellow-600';
    if (rating >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  const getRatingLabel = (rating: number) => {
    if (rating >= 90) return 'Universal Acclaim';
    if (rating >= 85) return 'Highly Rated';
    if (rating >= 75) return 'Generally Favorable';
    if (rating >= 60) return 'Mixed Reception';
    if (rating >= 40) return 'Generally Unfavorable';
    return 'Poor Reception';
  };

  return (
    <div className="space-y-4">
      {/* Enable/Disable Toggle */}
      <div className="flex items-center justify-between">
        <Label htmlFor="rating-enabled" className="text-sm font-medium">
          Filter by Rating (Metacritic)
        </Label>
        <Switch
          id="rating-enabled"
          checked={filter.enabled}
          onCheckedChange={handleToggleEnabled}
          disabled={disabled}
        />
      </div>

      {filter.enabled && (
        <>
          {/* Active Range Display */}
          {hasActiveRange && (
            <div className="flex items-center justify-between">
              <Badge variant="secondary" className="text-xs">
                <Star className="h-3 w-3 mr-1" />
                {filter.minRating || MIN_RATING} - {filter.maxRating || MAX_RATING}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilter}
                className="text-xs h-6"
                disabled={disabled}
              >
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
            </div>
          )}

          {/* Rating Range Slider */}
          <div className="space-y-4">
            <div className="px-2">
              <Slider
                value={sliderValues}
                onValueChange={handleSliderChange}
                min={MIN_RATING}
                max={MAX_RATING}
                step={1}
                className="w-full"
                disabled={disabled}
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>{MIN_RATING}</span>
                <span>{MAX_RATING}</span>
              </div>
              
              {/* Rating Quality Indicators */}
              <div className="flex justify-between text-xs mt-2">
                <span className="text-red-600">Poor</span>
                <span className="text-orange-600">Mixed</span>
                <span className="text-yellow-600">Good</span>
                <span className="text-green-600">Excellent</span>
              </div>
            </div>

            {/* Manual Rating Input */}
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Min Rating</Label>
                <Input
                  type="number"
                  placeholder="0"
                  value={filter.minRating?.toString() || ''}
                  onChange={(e) => handleMinRatingChange(e.target.value)}
                  min={MIN_RATING}
                  max={MAX_RATING}
                  className="h-8 text-sm"
                  disabled={disabled}
                />
                {filter.minRating !== undefined && (
                  <p className={`text-xs ${getRatingColor(filter.minRating)}`}>
                    {getRatingLabel(filter.minRating)}
                  </p>
                )}
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Max Rating</Label>
                <Input
                  type="number"
                  placeholder="100"
                  value={filter.maxRating?.toString() || ''}
                  onChange={(e) => handleMaxRatingChange(e.target.value)}
                  min={MIN_RATING}
                  max={MAX_RATING}
                  className="h-8 text-sm"
                  disabled={disabled}
                />
                {filter.maxRating !== undefined && (
                  <p className={`text-xs ${getRatingColor(filter.maxRating)}`}>
                    {getRatingLabel(filter.maxRating)}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Quick Presets */}
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Quick Presets</Label>
            <div className="grid grid-cols-1 gap-1">
              {RATING_PRESETS.map((preset, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handlePresetClick(preset)}
                  className="text-xs h-7 justify-start"
                  disabled={disabled}
                >
                  <span className={getRatingColor((preset.minRating + preset.maxRating) / 2)}>
                    {preset.label}
                  </span>
                </Button>
              ))}
            </div>
          </div>

          {/* Filter Description */}
          <div className="text-xs text-muted-foreground p-2 bg-muted/50 rounded">
            {filter.minRating && filter.maxRating && filter.minRating === filter.maxRating
              ? `Show games with exactly ${filter.minRating} rating`
              : filter.minRating && filter.maxRating
              ? `Show games rated between ${filter.minRating} and ${filter.maxRating}`
              : filter.minRating
              ? `Show games rated ${filter.minRating} or higher`
              : filter.maxRating
              ? `Show games rated ${filter.maxRating} or lower`
              : 'Show games with any rating (including unrated)'
            }
          </div>

          {/* Rating Scale Reference */}
          <div className="text-xs text-muted-foreground p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
            <strong>Metacritic Scale:</strong> 90-100 Universal Acclaim, 75-89 Generally Favorable, 
            50-74 Mixed Reviews, 20-49 Generally Unfavorable, 0-19 Overwhelming Dislike
          </div>
        </>
      )}
    </div>
  );
}