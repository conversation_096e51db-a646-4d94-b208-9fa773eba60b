import { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { customArtworkService, CustomArtwork, ArtworkType } from '@/lib/customArtworkService';

/**
 * Hook to fetch custom artwork for a user game using React Query
 */
export const useCustomArtwork = (userGameId: string) => {
  const queryClient = useQueryClient();

  const { data: artwork = [], isLoading: loading, error, refetch } = useQuery({
    queryKey: ['custom-artwork', userGameId],
    queryFn: () => customArtworkService.getArtworkForUserGame(userGameId),
    enabled: !!userGameId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  // Function to invalidate and refetch artwork
  const invalidateArtwork = () => {
    queryClient.invalidateQueries({ queryKey: ['custom-artwork', userGameId] });
  };

  // Get primary artwork for a specific type
  const getPrimaryArtwork = (artworkType: ArtworkType): CustomArtwork | null => {
    return artwork.find(art => art.artwork_type === artworkType && art.is_primary) || null;
  };

  // Get any artwork for a specific type (primary or first available)
  const getArtworkForType = (artworkType: ArtworkType): CustomArtwork | null => {
    const primary = getPrimaryArtwork(artworkType);
    if (primary) return primary;
    
    // Return first available artwork of this type
    return artwork.find(art => art.artwork_type === artworkType) || null;
  };

  // Get the best available cover image (front > 3d > any other type)
  const getBestCoverImage = (): string | null => {
    // Try front cover first
    const frontCover = getArtworkForType('front');
    if (frontCover) return frontCover.file_url;

    // Try 3D view next
    const threeDView = getArtworkForType('3d');
    if (threeDView) return threeDView.file_url;

    // Try any other artwork type
    const anyArtwork = artwork[0];
    if (anyArtwork) return anyArtwork.file_url;

    return null;
  };

  // Check if user has any custom artwork
  const hasCustomArtwork = artwork.length > 0;

  // Get artwork count by type
  const getArtworkCounts = () => {
    const counts: Record<ArtworkType, number> = {
      front: 0,
      back: 0,
      spine: 0,
      '3d': 0,
      manual: 0,
      disc: 0
    };

    artwork.forEach(art => {
      counts[art.artwork_type]++;
    });

    return counts;
  };

  return {
    artwork,
    loading,
    error,
    hasCustomArtwork,
    getPrimaryArtwork,
    getArtworkForType,
    getBestCoverImage,
    getArtworkCounts,
    refetch,
    invalidateArtwork
  };
};

/**
 * Hook to get primary artwork for a specific type
 */
export const usePrimaryArtwork = (userGameId: string, artworkType: ArtworkType) => {
  const [artwork, setArtwork] = useState<CustomArtwork | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadPrimaryArtwork = async () => {
      if (!userGameId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const primaryArtwork = await customArtworkService.getPrimaryArtwork(userGameId, artworkType);
        setArtwork(primaryArtwork);
      } catch (error) {
        console.error('Error loading primary artwork:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPrimaryArtwork();
  }, [userGameId, artworkType]);

  return { artwork, loading };
};