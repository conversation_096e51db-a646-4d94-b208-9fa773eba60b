/**
 * System Health Alerting Service
 * Monitors system health and triggers alerts for critical issues
 */

import { errorAnalyticsService } from './errorAnalyticsService';
import { databasePerformanceService } from './databasePerformanceService';

export interface Alert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  service: string;
  timestamp: string;
  dismissed: boolean;
  autoResolve: boolean;
  resolved: boolean;
  metadata?: Record<string, any>;
}

export interface AlertThresholds {
  errorRate: number;
  responseTime: number;
  cpuUsage: number;
  memoryUsage: number;
  dbConnections: number;
  slowQueries: number;
}

class SystemHealthAlertingService {
  private alerts: Alert[] = [];
  private thresholds: AlertThresholds = {
    errorRate: 5.0, // 5% error rate
    responseTime: 3000, // 3 seconds
    cpuUsage: 80, // 80% CPU usage
    memoryUsage: 85, // 85% memory usage
    dbConnections: 0.8, // 80% of max connections
    slowQueries: 5 // 5+ slow queries
  };
  
  private alertSubscribers: Array<(alerts: Alert[]) => void> = [];

  constructor() {
    // Start monitoring
    this.startMonitoring();
    
    // Load persisted alerts
    this.loadPersistedAlerts();
  }

  private startMonitoring() {
    // Monitor every 30 seconds
    setInterval(() => {
      this.checkSystemHealth();
    }, 30000);

    // Also check immediately
    setTimeout(() => this.checkSystemHealth(), 1000);
  }

  private async checkSystemHealth() {
    try {
      // Check error rates
      const errorAnalytics = await errorAnalyticsService.getAnalytics('hour');
      if (errorAnalytics && errorAnalytics.rate > this.thresholds.errorRate) {
        this.createAlert({
          type: 'error',
          title: 'High Error Rate',
          message: `Error rate has reached ${errorAnalytics.rate.toFixed(1)}% in the last hour`,
          service: 'application',
          autoResolve: true
        });
      }

      // Check database performance
      const dbMetrics = await databasePerformanceService.getMetrics();
      
      if (dbMetrics.connections.active / dbMetrics.connections.max > this.thresholds.dbConnections) {
        this.createAlert({
          type: 'warning',
          title: 'Database Connection Pool High',
          message: `Database connections at ${((dbMetrics.connections.active / dbMetrics.connections.max) * 100).toFixed(1)}% capacity`,
          service: 'database',
          autoResolve: true
        });
      }

      if (dbMetrics.slowQueries > this.thresholds.slowQueries) {
        this.createAlert({
          type: 'warning',
          title: 'Slow Database Queries',
          message: `${dbMetrics.slowQueries} slow queries detected`,
          service: 'database',
          autoResolve: true
        });
      }

      if (dbMetrics.avgQueryTime > 100) {
        this.createAlert({
          type: 'warning',
          title: 'Database Performance Degraded',
          message: `Average query time is ${dbMetrics.avgQueryTime}ms`,
          service: 'database',
          autoResolve: true
        });
      }

      // Auto-resolve alerts that are no longer relevant
      this.autoResolveAlerts();
      
    } catch (error) {
      console.warn('Error checking system health:', error);
    }
  }

  private createAlert(alertData: Partial<Alert> & { type: Alert['type']; title: string; message: string; service: string }) {
    // Check if similar alert already exists
    const existingAlert = this.alerts.find(alert => 
      !alert.resolved && 
      alert.title === alertData.title && 
      alert.service === alertData.service
    );

    if (existingAlert) {
      // Update existing alert timestamp
      existingAlert.timestamp = new Date().toISOString();
      existingAlert.metadata = { ...existingAlert.metadata, ...alertData.metadata };
      this.notifySubscribers();
      return existingAlert;
    }

    const alert: Alert = {
      id: this.generateAlertId(),
      timestamp: new Date().toISOString(),
      dismissed: false,
      autoResolve: false,
      resolved: false,
      ...alertData
    };

    this.alerts.unshift(alert); // Add to beginning

    // Maintain alerts limit
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(0, 100);
    }

    // Persist alerts
    this.persistAlerts();
    
    // Notify subscribers
    this.notifySubscribers();

    // Log critical alerts to error service
    if (alert.type === 'critical') {
      errorAnalyticsService.logError({
        type: 'system_alert',
        message: `Critical alert: ${alert.title} - ${alert.message}`,
        severity: 'critical',
        context: {
          service: alert.service,
          alertId: alert.id
        }
      });
    }

    return alert;
  }

  private autoResolveAlerts() {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    this.alerts.forEach(alert => {
      if (alert.autoResolve && !alert.resolved && new Date(alert.timestamp) < fiveMinutesAgo) {
        alert.resolved = true;
      }
    });
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private loadPersistedAlerts() {
    try {
      const stored = localStorage.getItem('codexa_system_alerts');
      if (stored) {
        const data = JSON.parse(stored);
        this.alerts = (data.alerts || []).slice(0, 50); // Load last 50 alerts
      }
    } catch (error) {
      console.warn('Could not load persisted alerts:', error);
    }
  }

  private persistAlerts() {
    try {
      const data = {
        alerts: this.alerts.slice(0, 50), // Only persist last 50 alerts
        lastUpdated: new Date().toISOString()
      };
      
      localStorage.setItem('codexa_system_alerts', JSON.stringify(data));
    } catch (error) {
      console.warn('Could not persist alerts:', error);
    }
  }

  private notifySubscribers() {
    this.alertSubscribers.forEach(callback => {
      try {
        callback(this.getActiveAlerts());
      } catch (error) {
        console.warn('Error notifying alert subscriber:', error);
      }
    });
  }

  // Public methods
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.resolved && !alert.dismissed);
  }

  getAllAlerts(): Alert[] {
    return [...this.alerts];
  }

  dismissAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.dismissed = true;
      this.persistAlerts();
      this.notifySubscribers();
      return true;
    }
    return false;
  }

  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      this.persistAlerts();
      this.notifySubscribers();
      return true;
    }
    return false;
  }

  clearResolvedAlerts() {
    this.alerts = this.alerts.filter(alert => !alert.resolved);
    this.persistAlerts();
    this.notifySubscribers();
  }

  updateThresholds(newThresholds: Partial<AlertThresholds>) {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    
    // Persist thresholds
    try {
      localStorage.setItem('codexa_alert_thresholds', JSON.stringify(this.thresholds));
    } catch (error) {
      console.warn('Could not persist alert thresholds:', error);
    }
  }

  getThresholds(): AlertThresholds {
    return { ...this.thresholds };
  }

  subscribeToAlerts(callback: (alerts: Alert[]) => void): () => void {
    this.alertSubscribers.push(callback);
    
    // Immediately notify with current alerts
    callback(this.getActiveAlerts());
    
    // Return unsubscribe function
    return () => {
      const index = this.alertSubscribers.indexOf(callback);
      if (index > -1) {
        this.alertSubscribers.splice(index, 1);
      }
    };
  }

  // Manual alert creation for custom monitoring
  createCustomAlert(
    type: Alert['type'],
    title: string,
    message: string,
    service: string,
    metadata?: Record<string, any>
  ): Alert {
    return this.createAlert({
      type,
      title,
      message,
      service,
      metadata,
      autoResolve: false
    });
  }

  // Get system health summary
  getHealthSummary(): {
    status: 'healthy' | 'warning' | 'critical';
    activeAlertsCount: number;
    criticalAlertsCount: number;
    lastChecked: string;
  } {
    const activeAlerts = this.getActiveAlerts();
    const criticalAlerts = activeAlerts.filter(alert => alert.type === 'critical');
    const errorAlerts = activeAlerts.filter(alert => alert.type === 'error');

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (criticalAlerts.length > 0) {
      status = 'critical';
    } else if (errorAlerts.length > 0 || activeAlerts.length > 5) {
      status = 'warning';
    }

    return {
      status,
      activeAlertsCount: activeAlerts.length,
      criticalAlertsCount: criticalAlerts.length,
      lastChecked: new Date().toISOString()
    };
  }
}

export const systemHealthAlertingService = new SystemHealthAlertingService();