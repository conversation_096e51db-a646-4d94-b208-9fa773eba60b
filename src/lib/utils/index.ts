// Re-export all utility functions from domain-specific files
export * from './baseUtils';
export * from './gameUtils';
export * from './filterUtils';
export * from './searchUtils';
export * from './encryptionUtils';
export * from './performanceUtils';
export * from './errorUtils';
export * from './arrayUtils';
export * from './platformFamilyUtils';

// Export date utils with conflict resolution
export * from './dateUtils';
export { isValidDate as isValidDateUtil } from './dateUtils';

// Export string utils with conflict resolution  
export * from './stringUtils';
export { isNumeric as isNumericUtil } from './stringUtils';

// Export validation utils with conflict resolution
export { 
  isNullOrUndefined,
  isNotNullOrUndefined,
  isEmptyString,
  isNotEmptyString,
  isValidEmail,
  isValidUrl,
  isValidPhoneNumber,
  isValidUUID,
  isValidJSON,
  isValidDate as isValidDateValidator,
  isValidDateString,
  isNumeric as isNumericValidator,
  isInteger,
  isPositiveNumber,
  isNonNegativeNumber,
  isInRange,
  isValidLength,
  isValidPassword,
  isValidHexColor,
  isValidRGBColor,
  isValidCreditCard,
  isValidIPv4,
  isValidMACAddress,
  isValidSlug,
  hasMinLength,
  hasMaxLength,
  hasRequiredProperties,
  validateFormData,
  sanitizeString,
  isSafeForHTML
} from './validationUtils';