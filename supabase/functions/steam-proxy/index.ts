import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Steam API proxy to securely handle Steam Web API calls
// This keeps the Steam API key server-side and avoids CORS issues

interface SteamApiRequest {
  endpoint: 'profile' | 'library' | 'game-details' | 'achievements' | 'recently-played' | 'player-bans' | 'friends' | 'user-stats' | 'global-achievement-percentages';
  steamId?: string;
  appId?: number;
}

const STEAM_API_KEY = Deno.env.get('STEAM_API_KEY');

if (!STEAM_API_KEY) {
  console.error('STEAM_API_KEY environment variable is not set');
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
};

async function handleSteamProfileRequest(steamId: string) {
  const url = `https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v0002/?key=${STEAM_API_KEY}&steamids=${steamId}&format=json`;
  
  console.log(`Fetching Steam profile for ID: ${steamId}`);
  
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
  }
  
  const data = await response.json();
  
  if (!data.response || !data.response.players || data.response.players.length === 0) {
    throw new Error('Steam profile not found');
  }
  
  const profile = data.response.players[0];
  console.log(`✅ Found Steam profile: ${profile.personaname}`);
  
  return {
    success: true,
    data: profile
  };
}

async function handleSteamLibraryRequest(steamId: string) {
  const url = `https://api.steampowered.com/IPlayerService/GetOwnedGames/v0001/?key=${STEAM_API_KEY}&steamid=${steamId}&format=json&include_appinfo=true&include_played_free_games=true`;
  
  console.log(`Fetching Steam library for ID: ${steamId}`);
  
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
  }
  
  const data = await response.json();
  
  if (!data.response || !data.response.games) {
    throw new Error('No games found or Steam profile is private');
  }
  
  console.log(`✅ Found ${data.response.games.length} games in Steam library`);
  
  return {
    success: true,
    data: data.response.games,
    count: data.response.game_count || data.response.games.length
  };
}

async function handleGameDetailsRequest(appId: number) {
  const url = `https://store.steampowered.com/api/appdetails?appids=${appId}&format=json`;
  
  console.log(`Fetching Steam game details for App ID: ${appId}`);
  
  const response = await fetch(url);
  
  if (!response.ok) {
    console.warn(`Steam Store API error for app ${appId}: ${response.status}`);
    return {
      success: false,
      data: null,
      error: `Steam Store API error: ${response.status}`
    };
  }
  
  const data = await response.json();
  const appData = data[appId];
  
  if (!appData || !appData.success || !appData.data) {
    console.warn(`No data found for Steam app ${appId}`);
    return {
      success: false,
      data: null,
      error: 'No game data found'
    };
  }
  
  return {
    success: true,
    data: appData.data
  };
}

async function handlePlayerAchievementsRequest(steamId: string, appId: number) {
  const url = `https://api.steampowered.com/ISteamUserStats/GetPlayerAchievements/v0001/?key=${STEAM_API_KEY}&steamid=${steamId}&appid=${appId}&format=json`;

  console.log(`Fetching achievements for Steam ID: ${steamId}, App ID: ${appId}`);

  const response = await fetch(url);

  if (!response.ok) {
    throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  if (!data.playerstats || !data.playerstats.achievements) {
    console.warn(`No achievements found for Steam ID: ${steamId}, App ID: ${appId}`);
    return {
      success: true,
      data: { achievements: [] }
    };
  }

  console.log(`✅ Found ${data.playerstats.achievements.length} achievements`);

  return {
    success: true,
    data: data.playerstats
  };
}

async function handleRecentlyPlayedRequest(steamId: string) {
  const url = `https://api.steampowered.com/IPlayerService/GetRecentlyPlayedGames/v0001/?key=${STEAM_API_KEY}&steamid=${steamId}&format=json`;

  console.log(`Fetching recently played games for Steam ID: ${steamId}`);

  const response = await fetch(url);

  if (!response.ok) {
    throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  if (!data.response || !data.response.games) {
    console.warn(`No recently played games found for Steam ID: ${steamId}`);
    return {
      success: true,
      data: { games: [] }
    };
  }

  console.log(`✅ Found ${data.response.games.length} recently played games`);

  return {
    success: true,
    data: data.response
  };
}

async function handlePlayerBansRequest(steamId: string) {
  const url = `https://api.steampowered.com/ISteamUser/GetPlayerBans/v1/?key=${STEAM_API_KEY}&steamids=${steamId}&format=json`;

  console.log(`Fetching player bans for Steam ID: ${steamId}`);

  const response = await fetch(url);

  if (!response.ok) {
    throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  if (!data.players || data.players.length === 0) {
    throw new Error('Player ban data not found');
  }

  const playerBans = data.players[0];
  console.log(`✅ Found player ban data for Steam ID: ${steamId}`);

  return {
    success: true,
    data: playerBans
  };
}

async function handleFriendsListRequest(steamId: string) {
  const url = `https://api.steampowered.com/ISteamUser/GetFriendList/v0001/?key=${STEAM_API_KEY}&steamid=${steamId}&relationship=friend&format=json`;

  console.log(`Fetching friends list for Steam ID: ${steamId}`);

  const response = await fetch(url);

  if (!response.ok) {
    if (response.status === 401) {
      throw new Error('Friends list is private');
    }
    throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  if (!data.friendslist || !data.friendslist.friends) {
    console.warn(`No friends found for Steam ID: ${steamId}`);
    return {
      success: true,
      data: { friends: [] }
    };
  }

  console.log(`✅ Found ${data.friendslist.friends.length} friends`);

  return {
    success: true,
    data: data.friendslist
  };
}

async function handleUserStatsRequest(steamId: string, appId: number) {
  const url = `https://api.steampowered.com/ISteamUserStats/GetUserStatsForGame/v0002/?key=${STEAM_API_KEY}&steamid=${steamId}&appid=${appId}&format=json`;

  console.log(`Fetching user stats for Steam ID: ${steamId}, App ID: ${appId}`);

  const response = await fetch(url);

  if (!response.ok) {
    throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  if (!data.playerstats) {
    console.warn(`No user stats found for Steam ID: ${steamId}, App ID: ${appId}`);
    return {
      success: true,
      data: { stats: [] }
    };
  }

  console.log(`✅ Found user stats for game`);

  return {
    success: true,
    data: data.playerstats
  };
}

async function handleGlobalAchievementPercentagesRequest(appId: number) {
  const url = `https://api.steampowered.com/ISteamUserStats/GetGlobalAchievementPercentagesForApp/v0002/?gameid=${appId}&format=json`;

  console.log(`Fetching global achievement percentages for App ID: ${appId}`);

  const response = await fetch(url);

  if (!response.ok) {
    throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  if (!data.achievementpercentages || !data.achievementpercentages.achievements) {
    console.warn(`No global achievement percentages found for App ID: ${appId}`);
    return {
      success: true,
      data: { achievements: [] }
    };
  }

  console.log(`✅ Found global achievement percentages for ${data.achievementpercentages.achievements.length} achievements`);

  return {
    success: true,
    data: data.achievementpercentages
  };
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  try {
    // Validate Steam API key
    if (!STEAM_API_KEY) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Steam API key not configured on server' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Parse request body
    const body: SteamApiRequest = await req.json();
    
    if (!body.endpoint) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing endpoint parameter' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    let result;

    switch (body.endpoint) {
      case 'profile':
        if (!body.steamId) {
          throw new Error('steamId is required for profile endpoint');
        }
        result = await handleSteamProfileRequest(body.steamId);
        break;

      case 'library':
        if (!body.steamId) {
          throw new Error('steamId is required for library endpoint');
        }
        result = await handleSteamLibraryRequest(body.steamId);
        break;

      case 'game-details':
        if (!body.appId) {
          throw new Error('appId is required for game-details endpoint');
        }
        result = await handleGameDetailsRequest(body.appId);
        break;

      case 'achievements':
        if (!body.steamId || !body.appId) {
          throw new Error('steamId and appId are required for achievements endpoint');
        }
        result = await handlePlayerAchievementsRequest(body.steamId, body.appId);
        break;

      case 'recently-played':
        if (!body.steamId) {
          throw new Error('steamId is required for recently-played endpoint');
        }
        result = await handleRecentlyPlayedRequest(body.steamId);
        break;

      case 'player-bans':
        if (!body.steamId) {
          throw new Error('steamId is required for player-bans endpoint');
        }
        result = await handlePlayerBansRequest(body.steamId);
        break;

      case 'friends':
        if (!body.steamId) {
          throw new Error('steamId is required for friends endpoint');
        }
        result = await handleFriendsListRequest(body.steamId);
        break;

      case 'user-stats':
        if (!body.steamId || !body.appId) {
          throw new Error('steamId and appId are required for user-stats endpoint');
        }
        result = await handleUserStatsRequest(body.steamId, body.appId);
        break;

      case 'global-achievement-percentages':
        if (!body.appId) {
          throw new Error('appId is required for global-achievement-percentages endpoint');
        }
        result = await handleGlobalAchievementPercentagesRequest(body.appId);
        break;

      default:
        throw new Error(`Unknown endpoint: ${body.endpoint}`);
    }

    return new Response(
      JSON.stringify(result),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Steam proxy error:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});