import React, { memo, useMemo, useCallback, useState, useRef, useEffect } from 'react';
import { useEnhancedLibrary } from '@/hooks/useEnhancedLibrary';
import { UserGameWithDetails } from '@/types/database';
import { CoverFlowViewSettings, GameCardInteractions } from '@/types/library';
import { cn } from '@/lib/utils';
import { 
  ChevronLeft, 
  ChevronRight, 
  Play, 
  Heart, 
  Star, 
  Calendar,
  RotateCcw,
  Pause
} from 'lucide-react';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent } from '@/components/ui/base/card';
import { Slider } from '@/components/ui/base/slider';

// Cover flow configurations
const COVER_FLOW_CONFIGS = {
  compact: {
    coverWidth: 160,
    coverHeight: 240,
    spacing: 20,
    perspectiveOrigin: '50% 50%',
    rotationAngle: 45
  },
  normal: {
    coverWidth: 200,
    coverHeight: 300,
    spacing: 30,
    perspectiveOrigin: '50% 50%',
    rotationAngle: 50
  },
  large: {
    coverWidth: 260,
    coverHeight: 390,
    spacing: 40,
    perspectiveOrigin: '50% 50%',
    rotationAngle: 55
  }
} as const;

interface EnhancedCoverFlowProps {
  games: UserGameWithDetails[];
  settings: CoverFlowViewSettings;
  onSettingsChange: (settings: Partial<CoverFlowViewSettings>) => void;
  interactions?: GameCardInteractions;
  className?: string;
}

interface CoverItemProps {
  game: UserGameWithDetails;
  index: number;
  centerIndex: number;
  totalVisible: number;
  settings: CoverFlowViewSettings;
  interactions?: GameCardInteractions;
  isSelected: boolean;
  onSelect: (gameId: string) => void;
  onCenterChange: (index: number) => void;
}

// 3D Transform calculations  
const calculateTransform = (
  index: number, 
  centerIndex: number
) => {
  const config = COVER_FLOW_CONFIGS.normal; // Use normal config for calculations
  const position = index - centerIndex;
  const absPosition = Math.abs(position);
  
  // Calculate base positioning
  const baseSpacing = config.spacing + config.coverWidth;
  const translateX = position * baseSpacing;
  
  // 3D rotation based on position
  const rotateY = position === 0 ? 0 : position > 0 ? -config.rotationAngle : config.rotationAngle;
  
  // Z-axis positioning for depth
  const translateZ = position === 0 ? 0 : -absPosition * 50;
  
  // Scale based on distance from center
  const scale = position === 0 ? 1 : Math.max(0.7, 1 - absPosition * 0.1);
  
  // Opacity based on distance
  const opacity = position === 0 ? 1 : Math.max(0.4, 1 - absPosition * 0.2);
  
  return {
    transform: `translateX(${translateX}px) translateZ(${translateZ}px) rotateY(${rotateY}deg) scale(${scale})`,
    opacity,
    zIndex: 100 - absPosition
  };
};

// Individual cover item component
const CoverItem = memo<CoverItemProps>(({
  game,
  index,
  centerIndex,
  settings: _settings, // eslint-disable-line @typescript-eslint/no-unused-vars
  interactions,
  isSelected,
  onSelect,
  onCenterChange
}) => {
  const config = COVER_FLOW_CONFIGS.normal;
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  
  const transforms = calculateTransform(index, centerIndex);
  const isCentered = index === centerIndex;
  
  const handleClick = useCallback(() => {
    if (isCentered) {
      onSelect(game.id);
      interactions?.onQuickPlay?.(game.id);
    } else {
      onCenterChange(index);
    }
  }, [isCentered, game.id, index, onSelect, onCenterChange, interactions]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  }, [handleClick]);

  return (
    <div
      className={cn(
        'absolute top-1/2 left-1/2 cursor-pointer transition-all duration-500 ease-out',
        'transform-gpu', // Use GPU acceleration
        isCentered && 'z-50',
        'hover:brightness-110'
      )}
      style={{
        width: config.coverWidth,
        height: config.coverHeight,
        marginLeft: -config.coverWidth / 2,
        marginTop: -config.coverHeight / 2,
        ...transforms,
        transitionProperty: 'transform, opacity, filter',
        transformStyle: 'preserve-3d'
      }}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`${game.game?.title} - ${isCentered ? 'Play game' : 'Select game'}`}
    >
      {/* Cover Image */}
      <div className={cn(
        'relative w-full h-full rounded-lg overflow-hidden shadow-lg',
        'bg-gradient-to-br from-muted/50 to-muted',
        isCentered && 'ring-2 ring-primary shadow-2xl',
        isSelected && 'ring-2 ring-primary/50'
      )}>
        {/* Loading shimmer */}
        {!imageLoaded && !imageError && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer" />
        )}
        
        {/* Game cover image */}
        {!imageError ? (
          <img
            src={game.game?.cover_image || ''}
            alt={game.game?.title || ''}
            className="w-full h-full object-cover"
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <div className="text-4xl mb-2">🎮</div>
              <div className="text-xs">No Cover</div>
            </div>
          </div>
        )}

        {/* Overlay for centered item */}
        {isCentered && (
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent">
            {/* Quick actions overlay */}
            <div className="absolute bottom-0 left-0 right-0 p-3">
              <div className="flex items-center justify-between text-white">
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-sm truncate">{game.game?.title}</h3>
                  <p className="text-xs opacity-90 truncate">{game.game?.developer}</p>
                </div>
                <div className="flex items-center gap-1 ml-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="h-6 w-6 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      interactions?.onQuickPlay?.(game.id);
                    }}
                  >
                    <Play size={12} />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Status indicator */}
        <div className="absolute top-2 left-2">
          <Badge 
            variant="secondary" 
            className={cn(
              'text-xs px-1 py-0',
              game.status === 'playing' && 'bg-blue-500 text-white',
              game.status === 'completed' && 'bg-green-500 text-white',
              game.status === 'backlog' && 'bg-yellow-500 text-white',
              game.status === 'wishlist' && 'bg-purple-500 text-white'
            )}
          >
            {game.status}
          </Badge>
        </div>

        {/* Rating indicator */}
        {game.personal_rating && (
          <div className="absolute top-2 right-2">
            <div className="flex items-center gap-1 bg-black/50 rounded px-1 py-0.5">
              <Star size={10} className="text-yellow-400 fill-current" />
              <span className="text-xs text-white">{game.personal_rating}</span>
            </div>
          </div>
        )}

        {/* Reflection effect for centered item */}
        {isCentered && (
          <div 
            className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/10 pointer-events-none"
            style={{
              background: 'linear-gradient(135deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%)'
            }}
          />
        )}
      </div>
    </div>
  );
});

CoverItem.displayName = 'CoverItem';

// Hook for cover flow navigation
function useCoverFlowNavigation(
  games: UserGameWithDetails[],
  settings: CoverFlowViewSettings,
  onSettingsChange: (settings: Partial<CoverFlowViewSettings>) => void
) {
  const [isAutoRotating, setIsAutoRotating] = useState(settings.autoRotate);
  const autoRotateTimer = useRef<NodeJS.Timeout | null>(null);

  const centerIndex = Math.max(0, Math.min(settings.centerIndex, games.length - 1));

  const navigateToIndex = useCallback((index: number) => {
    const newIndex = Math.max(0, Math.min(index, games.length - 1));
    onSettingsChange({ centerIndex: newIndex });
  }, [games.length, onSettingsChange]);

  const navigateLeft = useCallback(() => {
    navigateToIndex(centerIndex - 1);
  }, [centerIndex, navigateToIndex]);

  const navigateRight = useCallback(() => {
    navigateToIndex(centerIndex + 1);
  }, [centerIndex, navigateToIndex]);

  const toggleAutoRotate = useCallback(() => {
    const newAutoRotate = !isAutoRotating;
    setIsAutoRotating(newAutoRotate);
    onSettingsChange({ autoRotate: newAutoRotate });
  }, [isAutoRotating, onSettingsChange]);

  // Auto-rotation effect
  useEffect(() => {
    if (isAutoRotating && games.length > 1) {
      autoRotateTimer.current = setInterval(() => {
        navigateToIndex((centerIndex + 1) % games.length);
      }, settings.rotationSpeed);
    } else if (autoRotateTimer.current) {
      clearInterval(autoRotateTimer.current);
      autoRotateTimer.current = null;
    }

    return () => {
      if (autoRotateTimer.current) {
        clearInterval(autoRotateTimer.current);
      }
    };
  }, [isAutoRotating, centerIndex, games.length, settings.rotationSpeed, navigateToIndex]);

  // Keyboard navigation
  useEffect(() => {
    if (!settings.keyboardNavigation) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          navigateLeft();
          break;
        case 'ArrowRight':
          e.preventDefault();
          navigateRight();
          break;
        case ' ':
          e.preventDefault();
          toggleAutoRotate();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [settings.keyboardNavigation, navigateLeft, navigateRight, toggleAutoRotate]);

  return {
    centerIndex,
    isAutoRotating,
    navigateToIndex,
    navigateLeft,
    navigateRight,
    toggleAutoRotate
  };
}

// Hook for touch/mouse gesture handling
function useGestureHandling(
  onNavigateLeft: () => void,
  onNavigateRight: () => void,
  enabled: boolean
) {
  const startX = useRef(0);
  const startY = useRef(0);
  const isDragging = useRef(false);

  const handleStart = useCallback((clientX: number, clientY: number) => {
    if (!enabled) return;
    startX.current = clientX;
    startY.current = clientY;
    isDragging.current = true;
  }, [enabled]);

  const handleEnd = useCallback((clientX: number, clientY: number) => {
    if (!enabled || !isDragging.current) return;
    
    const deltaX = clientX - startX.current;
    const deltaY = Math.abs(clientY - startY.current);
    
    // Only handle horizontal swipes
    if (deltaY < 50 && Math.abs(deltaX) > 50) {
      if (deltaX > 0) {
        onNavigateLeft();
      } else {
        onNavigateRight();
      }
    }
    
    isDragging.current = false;
  }, [enabled, onNavigateLeft, onNavigateRight]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    handleStart(e.clientX, e.clientY);
  }, [handleStart]);

  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    handleEnd(e.clientX, e.clientY);
  }, [handleEnd]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    handleStart(touch.clientX, touch.clientY);
  }, [handleStart]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    const touch = e.changedTouches[0];
    handleEnd(touch.clientX, touch.clientY);
  }, [handleEnd]);

  return {
    handleMouseDown,
    handleMouseUp,
    handleTouchStart,
    handleTouchEnd
  };
}

export const EnhancedCoverFlow = memo<EnhancedCoverFlowProps>(({
  games,
  settings,
  onSettingsChange,
  interactions,
  className
}) => {
  const { selectedGames, toggleGameSelection } = useEnhancedLibrary();
  const {
    centerIndex,
    isAutoRotating,
    navigateToIndex,
    navigateLeft,
    navigateRight,
    toggleAutoRotate
  } = useCoverFlowNavigation(games, settings, onSettingsChange);

  const gestureHandlers = useGestureHandling(
    navigateLeft,
    navigateRight,
    settings.touchEnabled
  );

  // Calculate visible items
  const visibleItems = useMemo(() => {
    const halfVisible = Math.floor(settings.visibleCount / 2);
    const start = Math.max(0, centerIndex - halfVisible);
    const end = Math.min(games.length, centerIndex + halfVisible + 1);
    
    return games.slice(start, end).map((game, index) => ({
      game,
      index: start + index
    }));
  }, [games, centerIndex, settings.visibleCount]);

  const currentGame = games[centerIndex];

  if (games.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-64', className)}>
        <div className="text-center">
          <p className="text-lg text-muted-foreground">No games found</p>
          <p className="text-sm text-muted-foreground mt-2">
            Try adjusting your filters or search criteria
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('enhanced-cover-flow relative w-full h-full overflow-hidden', className)}>
      {/* 3D Container */}
      <div 
        className="relative w-full h-full"
        style={{
          perspective: `${settings.perspective}px`,
          perspectiveOrigin: COVER_FLOW_CONFIGS.normal.perspectiveOrigin
        }}
        {...gestureHandlers}
      >
        {/* Cover items */}
        {visibleItems.map(({ game, index }) => (
          <CoverItem
            key={game.id}
            game={game}
            index={index}
            centerIndex={centerIndex}
            totalVisible={settings.visibleCount}
            settings={settings}
            interactions={interactions}
            isSelected={selectedGames.has(game.id)}
            onSelect={toggleGameSelection}
            onCenterChange={navigateToIndex}
          />
        ))}
      </div>

      {/* Navigation Controls */}
      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
        <Button
          variant="secondary"
          size="sm"
          onClick={navigateLeft}
          disabled={centerIndex === 0}
          className="h-10 w-10 rounded-full shadow-lg"
        >
          <ChevronLeft size={20} />
        </Button>
      </div>

      <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
        <Button
          variant="secondary"
          size="sm"
          onClick={navigateRight}
          disabled={centerIndex === games.length - 1}
          className="h-10 w-10 rounded-full shadow-lg"
        >
          <ChevronRight size={20} />
        </Button>
      </div>

      {/* Auto-rotate control */}
      <div className="absolute top-4 right-4">
        <Button
          variant="secondary"
          size="sm"
          onClick={toggleAutoRotate}
          className={cn(
            'h-8 w-8 rounded-full shadow-lg',
            isAutoRotating && 'bg-primary text-primary-foreground'
          )}
        >
          {isAutoRotating ? <Pause size={16} /> : <RotateCcw size={16} />}
        </Button>
      </div>

      {/* Game Info Panel */}
      {currentGame && (
        <div className="absolute bottom-4 left-4 right-4">
          <Card className="bg-background border-border/50">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h2 className="text-lg font-semibold truncate">{currentGame.game?.title}</h2>
                  <p className="text-sm text-muted-foreground truncate">{currentGame.game?.developer}</p>
                  
                  <div className="flex items-center gap-4 mt-2 text-sm">
                    {currentGame.game?.release_date && (
                      <div className="flex items-center gap-1">
                        <Calendar size={14} />
                        <span>{new Date(currentGame.game.release_date).getFullYear()}</span>
                      </div>
                    )}
                    
                    {currentGame.game?.metacritic_score && (
                      <div className="flex items-center gap-1">
                        <Star size={14} />
                        <span>{currentGame.game.metacritic_score}</span>
                      </div>
                    )}
                    
                    {currentGame.hours_played > 0 && (
                      <span>{currentGame.hours_played}h played</span>
                    )}
                  </div>

                  {currentGame.game?.genres && currentGame.game.genres.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {currentGame.game.genres.slice(0, 3).map((genre) => (
                        <Badge key={genre} variant="secondary" className="text-xs">
                          {genre}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2 ml-4">
                  <Button
                    size="sm"
                    onClick={() => interactions?.onQuickPlay?.(currentGame.id)}
                  >
                    <Play size={16} className="mr-2" />
                    Play
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => toggleGameSelection(currentGame.id)}
                  >
                    <Heart 
                      size={16} 
                      className={selectedGames.has(currentGame.id) ? 'fill-current' : ''} 
                    />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Progress indicator */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
        <div className="flex items-center gap-1">
          {games.map((_, index) => (
            <button
              key={index}
              className={cn(
                'w-2 h-2 rounded-full transition-all',
                index === centerIndex 
                  ? 'bg-primary w-6' 
                  : 'bg-muted-foreground/50 hover:bg-muted-foreground/70'
              )}
              onClick={() => navigateToIndex(index)}
            />
          ))}
        </div>
      </div>
    </div>
  );
});

EnhancedCoverFlow.displayName = 'EnhancedCoverFlow';

// Cover flow customization controls
interface CoverFlowCustomizationControlsProps {
  settings: CoverFlowViewSettings;
  onSettingsChange: (settings: Partial<CoverFlowViewSettings>) => void;
  className?: string;
}

export const CoverFlowCustomizationControls = memo<CoverFlowCustomizationControlsProps>(({
  settings,
  onSettingsChange,
  className
}) => {
  return (
    <Card className={cn('p-4', className)}>
      <CardContent className="flex flex-wrap gap-4 p-0">
        {/* Visible Count */}
        <div className="flex flex-col gap-2 min-w-32">
          <label className="text-sm font-medium">Visible Items</label>
          <Slider
            value={[settings.visibleCount]}
            onValueChange={([value]) => onSettingsChange({ visibleCount: value })}
            min={3}
            max={9}
            step={2}
            className="w-24"
          />
          <span className="text-xs text-muted-foreground">{settings.visibleCount} items</span>
        </div>

        {/* Perspective */}
        <div className="flex flex-col gap-2 min-w-32">
          <label className="text-sm font-medium">Perspective</label>
          <Slider
            value={[settings.perspective]}
            onValueChange={([value]) => onSettingsChange({ perspective: value })}
            min={400}
            max={1200}
            step={100}
            className="w-24"
          />
          <span className="text-xs text-muted-foreground">{settings.perspective}px</span>
        </div>

        {/* Auto-rotate Speed */}
        <div className="flex flex-col gap-2 min-w-32">
          <label className="text-sm font-medium">Auto Speed</label>
          <Slider
            value={[settings.rotationSpeed]}
            onValueChange={([value]) => onSettingsChange({ rotationSpeed: value })}
            min={1000}
            max={10000}
            step={500}
            className="w-24"
          />
          <span className="text-xs text-muted-foreground">{settings.rotationSpeed / 1000}s</span>
        </div>

        {/* Toggle Controls */}
        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium">Options</label>
          <div className="flex gap-2">
            <Button
              variant={settings.touchEnabled ? "default" : "outline"}
              size="sm"
              onClick={() => onSettingsChange({ touchEnabled: !settings.touchEnabled })}
            >
              Touch
            </Button>
            <Button
              variant={settings.keyboardNavigation ? "default" : "outline"}
              size="sm"
              onClick={() => onSettingsChange({ keyboardNavigation: !settings.keyboardNavigation })}
            >
              Keyboard
            </Button>
            <Button
              variant={settings.autoRotate ? "default" : "outline"}
              size="sm"
              onClick={() => onSettingsChange({ autoRotate: !settings.autoRotate })}
            >
              Auto Rotate
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

CoverFlowCustomizationControls.displayName = 'CoverFlowCustomizationControls';