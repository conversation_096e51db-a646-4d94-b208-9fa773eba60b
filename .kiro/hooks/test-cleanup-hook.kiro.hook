{"enabled": true, "name": "Cleanup & TS/ESLint Fixer", "description": "Automatically cleans up test files, optimizes folder structure, and fixes TypeScript and ESLint issues when test files or configuration files are modified", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/components/ui/__tests__/**/*.test.tsx", "src/test/**/*", "*.test.ts", "*.test.tsx", "*.spec.ts", "*.spec.tsx", "eslint.config.js", "tsconfig*.json", "vitest.config.ts"]}, "then": {"type": "askAgent", "prompt": "Task: Optimize the test files and folder structure for better maintainability.\n\nClean and Optimize Test Files:\n\nReview and remove unused imports and variables.\n\nResolve any TypeScript compilation errors and warnings.\n\nAddress ESLint warnings, ensuring code adheres to linting rules.\n\nRefactor Folder Structure:\n\nImprove test folder organization for better scalability and easier navigation.\n\nEnsure proper naming conventions are followed consistently across all test files.\n\nFix TypeScript and ESLint Configuration:\n\nCheck for misconfigurations in tsconfig.json and ensure it is correctly set up for the project.\n\nReview and update ESLint configuration to ensure it is aligned with best practices.\n\nEnsure Consistent Code Quality:\n\nFix any type-related issues or mismatches in test files.\n\nEnsure that test files follow consistent naming conventions, enhancing clarity and maintainability.\n\nOverall Goal: Ensure that the test environment is clean, well-organized, and optimized for future development, reducing potential errors and making it easier to extend or modify tests going forward.\n\n"}}