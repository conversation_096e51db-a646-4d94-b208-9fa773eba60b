/**
 * Global image error handler to prevent external placeholder URL requests
 */

import { generateGameCoverPlaceholder, generateThumbnailPlaceholder } from './imageUtils';

/**
 * Global image error handler that replaces failed images with local SVG placeholders
 */
export function setupGlobalImageErrorHandler(): void {
  // Only run in browser environment
  if (typeof window === 'undefined') return;

  // Override the global Image constructor to handle errors
  const originalImage = window.Image;
  
  window.Image = class extends originalImage {
    constructor(width?: number, height?: number) {
      super(width, height);
      
      // Add error handler that prevents external placeholder requests
      this.addEventListener('error', (event) => {
        const img = event.target as HTMLImageElement;
        
        // Prevent infinite loops by checking if we're already using a placeholder
        if (img.src.startsWith('data:image/svg+xml')) {
          return;
        }
        
        // Generate appropriate placeholder based on alt text or context
        const altText = img.alt || 'Image';
        const isGameCover = img.classList.contains('game-cover') || 
                           img.alt.toLowerCase().includes('game') ||
                           img.alt.toLowerCase().includes('cover');
        
        if (isGameCover) {
          img.src = generateGameCoverPlaceholder(altText, 'box-art');
        } else {
          img.src = generateThumbnailPlaceholder(altText);
        }
      });
    }
  };
}

/**
 * Add error handler to existing images on the page
 */
export function addImageErrorHandlers(): void {
  if (typeof document === 'undefined') return;
  
  const images = document.querySelectorAll('img');
  
  images.forEach((img) => {
    // Skip if already has our error handler
    if (img.dataset.errorHandlerAdded) return;
    
    img.addEventListener('error', (event) => {
      const target = event.target as HTMLImageElement;
      
      // Prevent infinite loops
      if (target.src.startsWith('data:image/svg+xml')) {
        return;
      }
      
      const altText = target.alt || 'Image';
      const isGameCover = target.classList.contains('game-cover') || 
                         target.alt.toLowerCase().includes('game') ||
                         target.alt.toLowerCase().includes('cover');
      
      if (isGameCover) {
        target.src = generateGameCoverPlaceholder(altText, 'box-art');
      } else {
        target.src = generateThumbnailPlaceholder(altText);
      }
    });
    
    img.dataset.errorHandlerAdded = 'true';
  });
}

/**
 * Intercept fetch requests to prevent external placeholder requests
 */
export function interceptPlaceholderRequests(): void {
  if (typeof window === 'undefined' || !window.fetch) return;
  
  const originalFetch = window.fetch;
  
  window.fetch = async function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
    const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
    
    // Block requests to placeholder services
    if (url.includes('placeholder.com') || url.includes('via.placeholder')) {
      console.warn('Blocked external placeholder request:', url);
      
      // Return a mock response with a local placeholder
      const placeholderSvg = generateThumbnailPlaceholder('Blocked External Request');
      const blob = new Blob([placeholderSvg], { type: 'image/svg+xml' });
      
      return new Response(blob, {
        status: 200,
        statusText: 'OK',
        headers: {
          'Content-Type': 'image/svg+xml',
          'Content-Length': blob.size.toString()
        }
      });
    }
    
    return originalFetch.call(this, input, init);
  };
}

/**
 * Initialize all global image handlers
 */
export function initializeGlobalImageHandlers(): void {
  setupGlobalImageErrorHandler();
  interceptPlaceholderRequests();
  
  // Add handlers to existing images
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addImageErrorHandlers);
  } else {
    addImageErrorHandlers();
  }
  
  // Add handlers to dynamically added images
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element;
          
          // Handle img elements
          if (element.tagName === 'IMG') {
            const img = element as HTMLImageElement;
            if (!img.dataset.errorHandlerAdded) {
              img.addEventListener('error', (event) => {
                const target = event.target as HTMLImageElement;
                if (target.src.startsWith('data:image/svg+xml')) return;
                
                const altText = target.alt || 'Image';
                const isGameCover = target.classList.contains('game-cover') || 
                                   target.alt.toLowerCase().includes('game') ||
                                   target.alt.toLowerCase().includes('cover');
                
                if (isGameCover) {
                  target.src = generateGameCoverPlaceholder(altText, 'box-art');
                } else {
                  target.src = generateThumbnailPlaceholder(altText);
                }
              });
              img.dataset.errorHandlerAdded = 'true';
            }
          }
          
          // Handle img elements within added nodes
          const imgs = element.querySelectorAll('img');
          imgs.forEach((img) => {
            if (!img.dataset.errorHandlerAdded) {
              img.addEventListener('error', (event) => {
                const target = event.target as HTMLImageElement;
                if (target.src.startsWith('data:image/svg+xml')) return;
                
                const altText = target.alt || 'Image';
                const isGameCover = target.classList.contains('game-cover') || 
                                   target.alt.toLowerCase().includes('game') ||
                                   target.alt.toLowerCase().includes('cover');
                
                if (isGameCover) {
                  target.src = generateGameCoverPlaceholder(altText, 'box-art');
                } else {
                  target.src = generateThumbnailPlaceholder(altText);
                }
              });
              img.dataset.errorHandlerAdded = 'true';
            }
          });
        }
      });
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}