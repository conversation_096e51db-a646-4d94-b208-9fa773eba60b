import { memo, useCallback, useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/base/dropdown-menu';
import { UserGameWithDetails } from '@/types/database';
import { useCustomArtwork } from '@/hooks/useCustomArtwork';
import { getStatusIconElement, getStatusColor } from '@/lib/gameStatusUtils';
import { cn } from '@/lib/utils';
import { 
  Calendar,
  MoreVertical,
  Trash2,
  ImageIcon,
  Upload,
  Play,
  CheckCircle,
  Pause,
  Star,
  Clock,
  Heart
} from '@/lib/icons';

// Unified card configuration types
export type CardVariant = 'compact' | 'standard' | 'detailed';
export type CardStyle = 'modern' | 'classic' | 'minimal';
export type AnimationLevel = 'subtle' | 'dynamic' | 'premium';

interface UnifiedGameCardProps {
  gameData: UserGameWithDetails;
  variant?: CardVariant;
  style?: CardStyle;
  animationLevel?: AnimationLevel;
  showQuickActions?: boolean;
  enableHoverEffects?: boolean;
  enableSelection?: boolean;
  isSelected?: boolean;
  onGameClick?: (gameData: UserGameWithDetails) => void;
  onStatusUpdate?: (userGameId: string, status: string) => void;
  onRemoveGame?: (userGameId: string) => void;
  onSelect?: (gameId: string, selected: boolean) => void;
  className?: string;
}

const getAnimationConfig = (level: AnimationLevel) => {
  const configs = {
    subtle: {
      hoverScale: 1.01,
      transitionDuration: 200,
      enableParallax: false,
      enableGlow: false,
    },
    dynamic: {
      hoverScale: 1.02,
      transitionDuration: 300,
      enableParallax: false,
      enableGlow: true,
    },
    premium: {
      hoverScale: 1.05,
      transitionDuration: 400,
      enableParallax: true,
      enableGlow: true,
    },
  };
  return configs[level];
};

const getVariantConfig = (variant: CardVariant) => {
  const configs = {
    compact: {
      showHeader: false,
      aspectRatio: 'aspect-[3/4]',
      maxGenres: 2,
      showMetadata: false,
      padding: 'p-2',
    },
    standard: {
      showHeader: true,
      aspectRatio: 'aspect-[2/3]',
      maxGenres: 3,
      showMetadata: true,
      padding: 'p-4',
    },
    detailed: {
      showHeader: true,
      aspectRatio: 'aspect-[2/3]',
      maxGenres: 4,
      showMetadata: true,
      padding: 'p-4',
    },
  };
  return configs[variant];
};

const getStyleConfig = (style: CardStyle) => {
  const configs = {
    modern: {
      cardClasses: 'rounded-xl border-0 shadow-lg bg-gradient-to-br from-card via-card to-card/95',
      imageClasses: 'rounded-lg',
    },
    classic: {
      cardClasses: 'rounded-lg border shadow-md bg-card',
      imageClasses: 'rounded-md',
    },
    minimal: {
      cardClasses: 'rounded-md border-0 shadow-sm bg-card',
      imageClasses: 'rounded',
    },
  };
  return configs[style];
};

export const UnifiedGameCard = memo<UnifiedGameCardProps>(({
  gameData,
  variant = 'standard',
  style = 'modern',
  animationLevel = 'dynamic',
  showQuickActions = true,
  enableHoverEffects = true,
  enableSelection = false,
  isSelected = false,
  onGameClick,
  onStatusUpdate,
  onRemoveGame,
  onSelect,
  className,
}) => {
  const game = gameData.game;
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const cardRef = useRef<HTMLDivElement>(null);
  
  const animConfig = getAnimationConfig(animationLevel);
  const variantConfig = getVariantConfig(variant);
  const styleConfig = getStyleConfig(style);
  
  // Get custom artwork for this user game
  const { getBestCoverImage, hasCustomArtwork } = useCustomArtwork(gameData.id);

  const handleCardClick = useCallback(() => {
    if (enableSelection && onSelect) {
      onSelect(gameData.id, !isSelected);
    } else if (onGameClick) {
      onGameClick(gameData);
    }
  }, [enableSelection, isSelected, onSelect, onGameClick, gameData]);

  const handleStatusUpdate = useCallback((status: string) => {
    onStatusUpdate?.(gameData.id, status);
  }, [gameData.id, onStatusUpdate]);

  const handleRemoveGame = useCallback(() => {
    onRemoveGame?.(gameData.id);
  }, [gameData.id, onRemoveGame]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoaded(true);
  }, []);

  const handleMouseEnter = useCallback(() => {
    if (enableHoverEffects) {
      setIsHovered(true);
    }
  }, [enableHoverEffects]);

  const handleMouseLeave = useCallback(() => {
    if (enableHoverEffects) {
      setIsHovered(false);
      setMousePosition({ x: 0, y: 0 });
    }
  }, [enableHoverEffects]);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!enableHoverEffects || !cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    setMousePosition({ x, y });
  }, [enableHoverEffects]);

  // Parallax effect for premium animations
  useEffect(() => {
    if (!animConfig.enableParallax || !cardRef.current) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isHovered) return;
      
      const rect = cardRef.current!.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const rotateX = (y - centerY) / 10;
      const rotateY = (centerX - x) / 10;
      
      cardRef.current!.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(${animConfig.hoverScale})`;
    };

    const handleMouseLeave = () => {
      if (cardRef.current) {
        cardRef.current.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1)';
      }
    };

    if (isHovered) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [isHovered, animConfig.enableParallax, animConfig.hoverScale]);

  if (!game) return null;

  // Dynamic class construction
  const cardClasses = cn(
    'group cursor-pointer overflow-hidden relative transform-gpu will-change-transform',
    styleConfig.cardClasses,
    enableHoverEffects && [
      'hover:shadow-2xl hover:shadow-primary/10',
      animConfig.enableGlow && 'hover:ring-1 hover:ring-primary/20',
      'hover:-translate-y-1',
    ],
    isSelected && 'ring-2 ring-primary shadow-lg shadow-primary/20',
    'transition-all ease-out',
    className
  );

  const imageClasses = cn(
    'object-cover w-full h-full transition-all ease-out',
    styleConfig.imageClasses,
    imageLoaded 
      ? 'opacity-100 group-hover:scale-110 group-hover:brightness-110 group-hover:saturate-110' 
      : 'opacity-0 scale-105'
  );

  return (
    <Card 
      ref={cardRef}
      className={cardClasses}
      onClick={handleCardClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
      style={{
        transitionDuration: `${animConfig.transitionDuration}ms`,
        '--mouse-x': `${mousePosition.x}%`,
        '--mouse-y': `${mousePosition.y}%`,
      } as React.CSSProperties}
    >
      {/* Premium animated background gradient */}
      {enableHoverEffects && style === 'modern' && (
        <div 
          className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
          style={{
            background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
              hsl(var(--primary) / 0.05) 0%, 
              hsl(var(--secondary) / 0.03) 50%, 
              transparent 100%)`
          }}
        />
      )}
      
      {/* Premium glow effect */}
      {enableHoverEffects && animConfig.enableGlow && (
        <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10" />
      )}

      {/* Selection indicator */}
      {enableSelection && isSelected && (
        <div className="absolute top-2 left-2 z-10">
          <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
            <CheckCircle className="h-4 w-4 text-primary-foreground" />
          </div>
        </div>
      )}

      {/* Card Header - conditional based on variant */}
      {variantConfig.showHeader && (
        <CardHeader className="pb-2">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className={cn(
                'truncate group-hover:text-primary transition-colors',
                variant === 'detailed' ? 'text-lg' : 'text-base'
              )}>
                {game.title}
              </CardTitle>
              <CardDescription className="flex items-center gap-2 mt-1">
                {getStatusIconElement(gameData.status)}
                <span className="capitalize text-xs">{gameData.status}</span>
                {variant === 'detailed' && game.developer && (
                  <>
                    <span className="text-muted-foreground">•</span>
                    <span className="truncate text-xs">{game.developer}</span>
                  </>
                )}
              </CardDescription>
            </div>
            
            {showQuickActions && (
              <DropdownMenu
                trigger={
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                }
                align="end"
              >
                <DropdownMenuItem onClick={() => handleStatusUpdate('playing')}>
                  <Play className="h-4 w-4 mr-2" />
                  Mark as Playing
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusUpdate('completed')}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Mark as Completed
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusUpdate('backlog')}>
                  <Pause className="h-4 w-4 mr-2" />
                  Move to Backlog
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusUpdate('wishlist')}>
                  <Heart className="h-4 w-4 mr-2" />
                  Add to Wishlist
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={handleRemoveGame}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remove from Library
                </DropdownMenuItem>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>
      )}

      <CardContent className={variantConfig.padding}>
        <div className="space-y-3">
          {/* Enhanced Image Container */}
          <div className={cn(
            'relative overflow-hidden bg-gradient-to-br from-muted via-muted to-muted/80',
            'group-hover:shadow-2xl group-hover:shadow-primary/20 transition-premium box-art-glow',
            variantConfig.aspectRatio,
            styleConfig.imageClasses
          )}>
            <div className="relative w-full h-full">
              {(getBestCoverImage() || game.cover_image) && !imageError ? (
                <>
                  {/* Loading shimmer effect */}
                  {!imageLoaded && (
                    <div className={cn("absolute inset-0 box-art-shimmer", styleConfig.imageClasses)}>
                      <div className="w-full h-full bg-gradient-to-br from-muted/80 via-muted/60 to-muted/40 animate-pulse" />
                    </div>
                  )}
                  
                  {/* Main image */}
                  <img
                    src={getBestCoverImage() || game.cover_image}
                    alt={game.title}
                    className={imageClasses}
                    loading="lazy"
                    decoding="async"
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                    style={{ imageRendering: 'crisp-edges' }}
                  />
                  
                  {/* Enhanced overlay effects */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/10 opacity-0 group-hover:opacity-100 transition-premium glass-morphism" />
                  <div className="absolute inset-0 bg-gradient-to-br from-white/15 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-premium" />
                  <div className="absolute top-0 left-0 w-1/3 h-1/3 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-premium" />
                  
                  {/* Premium glow border */}
                  <div className={cn("absolute inset-0 ring-1 ring-white/20 ring-inset opacity-0 group-hover:opacity-100 transition-premium", styleConfig.imageClasses)} />
                </>
              ) : (
                /* Enhanced fallback placeholder */
                <div className={cn(
                  "w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-muted via-muted/90 to-muted/70 group-hover:from-muted/80 group-hover:to-muted/60 transition-premium",
                  styleConfig.imageClasses
                )}>
                  <div className="relative mb-3">
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-full animate-glow" />
                    <ImageIcon className={cn(
                      'text-muted-foreground/60 animate-pulse relative z-10',
                      variant === 'compact' ? 'h-8 w-8' : 'h-16 w-16'
                    )} />
                  </div>
                  <p className="text-xs text-muted-foreground font-medium tracking-wide">No Cover Art</p>
                </div>
              )}
            </div>
            
            {/* Status indicator */}
            <div className="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-premium">
              <div className="w-8 h-8 rounded-full bg-black/40 flex items-center justify-center">
                {getStatusIconElement(gameData.status)}
              </div>
            </div>

            {/* Custom artwork indicator */}
            {hasCustomArtwork && (
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-premium">
                <Badge variant="secondary" className="bg-primary text-primary-foreground border-0 text-xs">
                  <Upload className="h-3 w-3 mr-1" />
                  Custom
                </Badge>
              </div>
            )}
          </div>

          {/* Compact variant - minimal info */}
          {variant === 'compact' && (
            <div className="space-y-1">
              <h3 className="text-sm font-medium truncate group-hover:text-primary transition-colors">
                {game.title}
              </h3>
              <Badge className={cn('text-xs', getStatusColor(gameData.status))}>
                {gameData.status}
              </Badge>
            </div>
          )}

          {/* Standard and detailed variants - full info */}
          {variantConfig.showMetadata && (
            <>
              <div className="flex flex-wrap gap-2">
                <Badge className={getStatusColor(gameData.status)}>
                  {gameData.status}
                </Badge>

                {game.metacritic_score && (
                  <Badge variant="secondary">
                    <Star className="h-3 w-3 mr-1" />
                    {game.metacritic_score}
                  </Badge>
                )}

                {variant === 'detailed' && gameData.personal_rating && (
                  <Badge variant="outline" className="text-yellow-600 border-yellow-200">
                    <Star className="h-3 w-3 mr-1 fill-current" />
                    {gameData.personal_rating}/10
                  </Badge>
                )}
              </div>

              {game.genres && game.genres.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {game.genres.slice(0, variantConfig.maxGenres).map((genre, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {genre}
                    </Badge>
                  ))}
                  {game.genres.length > variantConfig.maxGenres && (
                    <Badge variant="outline" className="text-xs">
                      +{game.genres.length - variantConfig.maxGenres} more
                    </Badge>
                  )}
                </div>
              )}

              {/* Additional metadata for detailed variant */}
              {variant === 'detailed' && (
                <div className="space-y-2 text-sm text-muted-foreground">
                  {gameData.personal_rating && (
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-current text-yellow-500" />
                      <span>Your rating: {gameData.personal_rating}/10</span>
                    </div>
                  )}

                  {game.release_date && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(game.release_date).getFullYear()}</span>
                    </div>
                  )}
                  
                  {gameData.hours_played && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{Math.round(gameData.hours_played)}h played</span>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
});

UnifiedGameCard.displayName = 'UnifiedGameCard';