import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { userGameTags } from '@/lib/supabase';

/**
 * Enhanced game tags hook with bulk operations for tag management
 */
export function useEnhancedGameTags() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Add tags to a specific game
  const addTagsToGameMutation = useMutation({
    mutationFn: async ({ gameId, tagIds }: { gameId: string; tagIds: string[] }) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const results = [];
      for (const tagId of tagIds) {
        try {
          const { data, error } = await userGameTags.addTagToGame(user.id, gameId, tagId);
          if (error) throw error;
          results.push(data);
        } catch (error) {
          console.error(`Error adding tag ${tagId} to game ${gameId}:`, error);
          // Continue with other tags even if one fails
        }
      }
      return results;
    },
    onSuccess: (_, { gameId }) => {
      queryClient.invalidateQueries({ queryKey: ['game-tags', gameId] });
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-games'] });
    }
  });

  // Remove tags from a specific game
  const removeTagsFromGameMutation = useMutation({
    mutationFn: async ({ gameId, tagIds }: { gameId: string; tagIds: string[] }) => {
      for (const tagId of tagIds) {
        try {
          const { error } = await userGameTags.removeTagFromGame(gameId, tagId);
          if (error) throw error;
        } catch (error) {
          console.error(`Error removing tag ${tagId} from game ${gameId}:`, error);
          // Continue with other tags even if one fails
        }
      }
    },
    onSuccess: (_, { gameId }) => {
      queryClient.invalidateQueries({ queryKey: ['game-tags', gameId] });
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-games'] });
    }
  });

  // Replace all tags on a specific game
  const replaceGameTagsMutation = useMutation({
    mutationFn: async ({ gameId, tagIds }: { gameId: string; tagIds: string[] }) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      // First remove all existing tags
      try {
        const { error: removeError } = await userGameTags.removeAllTagsFromGame(gameId);
        if (removeError) throw removeError;
      } catch (error) {
        console.error(`Error removing existing tags from game ${gameId}:`, error);
      }
      
      // Then add new tags
      const results = [];
      for (const tagId of tagIds) {
        try {
          const { data, error } = await userGameTags.addTagToGame(user.id, gameId, tagId);
          if (error) throw error;
          results.push(data);
        } catch (error) {
          console.error(`Error adding tag ${tagId} to game ${gameId}:`, error);
        }
      }
      
      return results;
    },
    onSuccess: (_, { gameId }) => {
      queryClient.invalidateQueries({ queryKey: ['game-tags', gameId] });
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-games'] });
    }
  });

  // Bulk add tags to multiple games
  const bulkAddTagsToGamesMutation = useMutation({
    mutationFn: async ({ gameIds, tagIds }: { gameIds: string[]; tagIds: string[] }) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const results = [];
      for (const gameId of gameIds) {
        for (const tagId of tagIds) {
          try {
            const { data, error } = await userGameTags.addTagToGame(user.id, gameId, tagId);
            if (error) throw error;
            results.push({ gameId, tagId, data });
          } catch (error) {
            console.error(`Error adding tag ${tagId} to game ${gameId}:`, error);
          }
        }
      }
      return results;
    },
    onSuccess: (_, { gameIds }) => {
      // Invalidate queries for all affected games
      gameIds.forEach(gameId => {
        queryClient.invalidateQueries({ queryKey: ['game-tags', gameId] });
      });
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-games'] });
    }
  });

  // Bulk remove tags from multiple games
  const bulkRemoveTagsFromGamesMutation = useMutation({
    mutationFn: async ({ gameIds, tagIds }: { gameIds: string[]; tagIds: string[] }) => {
      for (const gameId of gameIds) {
        for (const tagId of tagIds) {
          try {
            const { error } = await userGameTags.removeTagFromGame(gameId, tagId);
            if (error) throw error;
          } catch (error) {
            console.error(`Error removing tag ${tagId} from game ${gameId}:`, error);
          }
        }
      }
    },
    onSuccess: (_, { gameIds }) => {
      // Invalidate queries for all affected games
      gameIds.forEach(gameId => {
        queryClient.invalidateQueries({ queryKey: ['game-tags', gameId] });
      });
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-games'] });
    }
  });

  // Bulk replace tags on multiple games
  const bulkReplaceTagsOnGamesMutation = useMutation({
    mutationFn: async ({ gameIds, tagIds }: { gameIds: string[]; tagIds: string[] }) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const results = [];
      for (const gameId of gameIds) {
        try {
          // Remove all existing tags
          const { error: removeError } = await userGameTags.removeAllTagsFromGame(gameId);
          if (removeError) throw removeError;
          
          // Add new tags
          for (const tagId of tagIds) {
            const { data, error } = await userGameTags.addTagToGame(user.id, gameId, tagId);
            if (error) throw error;
            results.push({ gameId, tagId, data });
          }
        } catch (error) {
          console.error(`Error replacing tags on game ${gameId}:`, error);
        }
      }
      return results;
    },
    onSuccess: (_, { gameIds }) => {
      // Invalidate queries for all affected games
      gameIds.forEach(gameId => {
        queryClient.invalidateQueries({ queryKey: ['game-tags', gameId] });
      });
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-games'] });
    }
  });

  return {
    // Single game operations
    addTagsToGame: (gameId: string, tagIds: string[]) => 
      addTagsToGameMutation.mutate({ gameId, tagIds }),
    removeTagsFromGame: (gameId: string, tagIds: string[]) => 
      removeTagsFromGameMutation.mutate({ gameId, tagIds }),
    replaceGameTags: (gameId: string, tagIds: string[]) => 
      replaceGameTagsMutation.mutate({ gameId, tagIds }),

    // Bulk operations
    bulkAddTagsToGames: (gameIds: string[], tagIds: string[]) => 
      bulkAddTagsToGamesMutation.mutate({ gameIds, tagIds }),
    bulkRemoveTagsFromGames: (gameIds: string[], tagIds: string[]) => 
      bulkRemoveTagsFromGamesMutation.mutate({ gameIds, tagIds }),
    bulkReplaceTagsOnGames: (gameIds: string[], tagIds: string[]) => 
      bulkReplaceTagsOnGamesMutation.mutate({ gameIds, tagIds }),

    // Status indicators
    isAddingTags: addTagsToGameMutation.isPending,
    isRemovingTags: removeTagsFromGameMutation.isPending,
    isReplacingTags: replaceGameTagsMutation.isPending,
    isBulkAdding: bulkAddTagsToGamesMutation.isPending,
    isBulkRemoving: bulkRemoveTagsFromGamesMutation.isPending,
    isBulkReplacing: bulkReplaceTagsOnGamesMutation.isPending,

    // Error states
    addError: addTagsToGameMutation.error,
    removeError: removeTagsFromGameMutation.error,
    replaceError: replaceGameTagsMutation.error,
    bulkAddError: bulkAddTagsToGamesMutation.error,
    bulkRemoveError: bulkRemoveTagsFromGamesMutation.error,
    bulkReplaceError: bulkReplaceTagsOnGamesMutation.error
  };
}