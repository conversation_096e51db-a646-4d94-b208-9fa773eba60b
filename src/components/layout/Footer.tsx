import { Heart, Github, Settings, User, ExternalLink, Shield, FileText } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/base/tooltip';

export function Footer() {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { 
      icon: Settings, 
      label: 'Settings', 
      to: '/settings',
      description: 'App settings and preferences' 
    },
    { 
      icon: User, 
      label: 'Profile', 
      to: '/profile',
      description: 'Manage your profile' 
    },
    { 
      icon: Shield, 
      label: 'Privacy', 
      href: '/privacy',
      external: true 
    },
    { 
      icon: FileText, 
      label: 'Terms', 
      href: '/terms',
      external: true 
    },
    { 
      icon: Github, 
      label: 'GitHub', 
      href: 'https://github.com/stijnus/Codexa',
      external: true 
    },
  ];

  return (
    <footer className="bg-background border-t border-border mt-8">
      <div className="max-w-7xl mx-auto px-4 py-4">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          {/* Left side - Copyright and version */}
          <div className="flex items-center gap-4">
            <p className="text-sm text-muted-foreground flex items-center gap-2">
              Made with <Heart className="h-4 w-4 text-red-500" /> for gamers
            </p>
            <Badge variant="outline" className="text-xs">
              v2.1.0
            </Badge>
          </div>

          {/* Center - Quick Links */}
          <div className="flex items-center gap-1">
            {quickLinks.map((link) => {
              const LinkComponent = link.external || link.href ? 'a' : Link;
              const linkProps = link.external || link.href 
                ? { href: link.href, target: link.external ? '_blank' : undefined, rel: link.external ? 'noopener noreferrer' : undefined }
                : { to: link.to };

              return (
                <Tooltip key={link.label}>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 text-xs text-muted-foreground hover:text-foreground transition-colors"
                      asChild
                    >
                      <LinkComponent {...linkProps}>
                        <link.icon className="h-3 w-3 mr-1" />
                        {link.label}
                        {link.external && <ExternalLink className="h-2 w-2 ml-1 opacity-60" />}
                      </LinkComponent>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{link.description || link.label}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </div>

          {/* Right side - Copyright */}
          <p className="text-xs text-muted-foreground">
            © {currentYear} Game Library
          </p>
        </div>
      </div>
    </footer>
  );
}
