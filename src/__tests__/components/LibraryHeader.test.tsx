import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { LibraryHeader } from '@/pages/Library/components/LibraryHeader';
import { ViewMode } from '@/pages/Library/types';

describe('LibraryHeader', () => {
  const defaultProps = {
    gameCount: 42,
    viewMode: 'status' as ViewMode,
    onViewModeChange: vi.fn(),
  };

  it('renders library title and game count', () => {
    render(<LibraryHeader {...defaultProps} />);
    
    expect(screen.getByText('My Library')).toBeInTheDocument();
    expect(screen.getByText('Track your gaming collection')).toBeInTheDocument();
    expect(screen.getByText('42 games')).toBeInTheDocument();
  });

  it('renders all view mode buttons', () => {
    render(<LibraryHeader {...defaultProps} />);
    
    expect(screen.getByRole('button', { name: /^status$/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /^platform$/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /platform family|family/i })).toBeInTheDocument();
  });

  it('highlights the active view mode', () => {
    render(<LibraryHeader {...defaultProps} viewMode="platform-family" />);
    
    const statusButton = screen.getByRole('button', { name: /^status$/i });
    const platformButton = screen.getByRole('button', { name: /^platform$/i });
    const familyButton = screen.getByRole('button', { name: /platform family|family/i });
    
    // The active button should have different styling (default variant)
    expect(familyButton).toHaveClass('bg-primary');
    expect(statusButton).not.toHaveClass('bg-primary');
    expect(platformButton).not.toHaveClass('bg-primary');
  });

  it('calls onViewModeChange when view mode buttons are clicked', () => {
    const onViewModeChange = vi.fn();
    render(<LibraryHeader {...defaultProps} onViewModeChange={onViewModeChange} />);
    
    fireEvent.click(screen.getByRole('button', { name: /platform family|family/i }));
    expect(onViewModeChange).toHaveBeenCalledWith('platform-family');
    
    fireEvent.click(screen.getByRole('button', { name: /^platform$/i }));
    expect(onViewModeChange).toHaveBeenCalledWith('platform');
    
    fireEvent.click(screen.getByRole('button', { name: /^status$/i }));
    expect(onViewModeChange).toHaveBeenCalledWith('status');
  });

  it('displays correct icons for each view mode', () => {
    render(<LibraryHeader {...defaultProps} />);
    
    // Check that icons are present (they should be rendered as SVG elements)
    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button.querySelector('svg')).toBeInTheDocument();
    });
  });

  it('handles zero games correctly', () => {
    render(<LibraryHeader {...defaultProps} gameCount={0} />);
    expect(screen.getByText('0 games')).toBeInTheDocument();
  });

  it('handles large game counts correctly', () => {
    render(<LibraryHeader {...defaultProps} gameCount={1337} />);
    expect(screen.getByText('1337 games')).toBeInTheDocument();
  });
});