import { memo } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/base/button';
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/base/dropdown-menu';
import { Badge } from '@/components/ui/base/badge';
import { Separator } from '@/components/ui/base/separator';
import { cn } from '@/lib/utils';
import {
  CheckCircle,
  Trash2,
  Play,
  Pause,
  Heart,
  Star,
  MoreHorizontal,
  X
} from '@/lib/icons';

interface BulkOperationsToolbarProps {
  selectedCount: number;
  totalCount: number;
  onSelectAll: () => void;
  onSelectNone: () => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkRemove: () => void;
  onBulkRate: (rating: number) => void;
  className?: string;
}

const statusOptions = [
  { value: 'playing', label: 'Mark as Playing', icon: Play, color: 'text-green-600' },
  { value: 'completed', label: 'Mark as Completed', icon: CheckCircle, color: 'text-blue-600' },
  { value: 'backlog', label: 'Move to Backlog', icon: Pause, color: 'text-orange-600' },
  { value: 'wishlist', label: 'Add to Wishlist', icon: Heart, color: 'text-pink-600' },
] as const;

const ratingOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

export const BulkOperationsToolbar = memo<BulkOperationsToolbarProps>(({
  selectedCount,
  totalCount,
  onSelectAll,
  onSelectNone,
  onBulkStatusUpdate,
  onBulkRemove,
  onBulkRate,
  className
}) => {

  const isAllSelected = selectedCount === totalCount && totalCount > 0;
  const isSomeSelected = selectedCount > 0 && selectedCount < totalCount;

  if (selectedCount === 0) {
    return null;
  }

  return (
    <div className={cn(
      'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50',
      'bg-card border rounded-lg shadow-2xl',
      'flex items-center gap-2 p-3',
      'min-w-96 max-w-4xl',
      'transition-all duration-300 ease-out',
      'animate-slide-up',
      className
    )}>
      {/* Selection info */}
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={isAllSelected ? onSelectNone : onSelectAll}
          className="h-8 w-8 p-0"
          title={isAllSelected ? 'Deselect all' : 'Select all'}
        >
          {isAllSelected ? (
            <CheckCircle className="h-4 w-4" />
          ) : isSomeSelected ? (
            <div className="h-4 w-4 border border-primary bg-primary/20 rounded-sm flex items-center justify-center">
              <div className="w-2 h-2 bg-primary rounded-sm" />
            </div>
          ) : (
            <div className="h-4 w-4 border border-muted-foreground rounded-sm" />
          )}
        </Button>
        
        <Badge variant="secondary" className="font-medium">
          {selectedCount} selected
        </Badge>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Quick actions - always visible */}
      <div className="flex items-center gap-1">
        {statusOptions.slice(0, 2).map((option) => {
          const Icon = option.icon;
          return (
            <Button
              key={option.value}
              variant="ghost"
              size="sm"
              onClick={() => onBulkStatusUpdate(option.value)}
              className={cn("h-8 gap-2", option.color)}
              title={option.label}
            >
              <Icon className="h-4 w-4" />
              <span className="hidden sm:inline text-sm">{option.label.replace('Mark as ', '').replace('Move to ', '')}</span>
            </Button>
          );
        })}
        
        {/* More actions dropdown */}
        <DropdownMenu
          trigger={
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          }
          align="end"
        >
          {/* Additional status options */}
          {statusOptions.slice(2).map((option) => {
            const Icon = option.icon;
            return (
              <DropdownMenuItem
                key={option.value}
                onClick={() => onBulkStatusUpdate(option.value)}
                className={option.color}
              >
                <Icon className="h-4 w-4 mr-2" />
                {option.label}
              </DropdownMenuItem>
            );
          })}
          
          <Separator className="my-2" />
          
          {/* Rating submenu */}
          <div className="px-2 py-1">
            <div className="text-sm font-medium mb-2 flex items-center gap-2">
              <Star className="h-4 w-4" />
              Rate Games
            </div>
            <div className="grid grid-cols-5 gap-1">
              {ratingOptions.map((rating) => (
                <Button
                  key={rating}
                  variant="ghost"
                  size="sm"
                  onClick={() => onBulkRate(rating)}
                  className="h-8 w-8 p-0 text-xs"
                  title={`Rate ${rating}/10`}
                >
                  {rating}
                </Button>
              ))}
            </div>
          </div>
          
          <Separator className="my-2" />
          
          {/* Destructive actions */}
          <DropdownMenuItem
            onClick={onBulkRemove}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Remove from Library
          </DropdownMenuItem>
        </DropdownMenu>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Clear selection */}
      <Button
        variant="ghost"
        size="sm"
        onClick={onSelectNone}
        className="h-8 w-8 p-0"
        title="Clear selection"
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  );
});

BulkOperationsToolbar.displayName = 'BulkOperationsToolbar';

// Keyboard shortcuts hook for bulk operations
export const useBulkOperationShortcuts = (
  selectedCount: number,
  onSelectAll: () => void,
  onSelectNone: () => void,
  onBulkStatusUpdate: (status: string) => void,
  onBulkRemove: () => void
) => {
  return {
    onKeyDown: (e: KeyboardEvent) => {
      // Only handle shortcuts when games are selected
      if (selectedCount === 0) return;

      // Ctrl/Cmd + A - Select all
      if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
        e.preventDefault();
        onSelectAll();
      }
      
      // Escape - Clear selection
      if (e.key === 'Escape') {
        e.preventDefault();
        onSelectNone();
      }
      
      // P - Mark as playing
      if (e.key === 'p' || e.key === 'P') {
        e.preventDefault();
        onBulkStatusUpdate('playing');
      }
      
      // C - Mark as completed
      if (e.key === 'c' || e.key === 'C') {
        e.preventDefault();
        onBulkStatusUpdate('completed');
      }
      
      // B - Move to backlog
      if (e.key === 'b' || e.key === 'B') {
        e.preventDefault();
        onBulkStatusUpdate('backlog');
      }
      
      // Delete - Remove from library
      if (e.key === 'Delete' || e.key === 'Backspace') {
        e.preventDefault();
        if (confirm(`Remove ${selectedCount} games from library?`)) {
          onBulkRemove();
        }
      }
    }
  };
};