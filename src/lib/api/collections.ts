import { supabase } from '../supabase';
import {
  UserGameRecord,
  UserGameUpdateData,
  PriceTrackingRecord,
  PriceTrackingUpdateData,
  PriceAlertRecord,
  PriceAlertUpdateData,
} from '../../types/database';

/**
 * User Games Collection API
 * Handles user's game library, wishlist, and collection management
 */
export class UserGamesAPI {
  /**
   * Get user's complete game collection
   */
  async getUserCollection(userId: string) {
    try {
      // Ensure user records exist before querying
      await this.ensureUserRecords(userId);
      
      return supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .order('date_added', { ascending: false });
    } catch (error) {
      console.error('Error getting user collection:', error);
      throw error;
    }
  }

  /**
   * Get user's games by status (playing, completed, wishlist, etc.)
   */
  async getUserGamesByStatus(userId: string, status: string) {
    try {
      await this.ensureUserRecords(userId);
      
      return supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .eq('status', status)
        .order('date_added', { ascending: false });
    } catch (error) {
      console.error(`Error getting user games by status ${status}:`, error);
      throw error;
    }
  }

  /**
   * Get user's wishlist
   */
  async getUserWishlist(userId: string) {
    return this.getUserGamesByStatus(userId, 'wishlist');
  }

  /**
   * Get user's currently playing games
   */
  async getCurrentlyPlaying(userId: string) {
    return this.getUserGamesByStatus(userId, 'playing');
  }

  /**
   * Get user's completed games
   */
  async getCompletedGames(userId: string) {
    return this.getUserGamesByStatus(userId, 'completed');
  }

  /**
   * Get user's backlog
   */
  async getBacklog(userId: string) {
    return this.getUserGamesByStatus(userId, 'backlog');
  }

  /**
   * Add a game to user's collection
   */
  async addToCollection(userGame: Omit<UserGameRecord, 'id' | 'created_at' | 'updated_at'>) {
    return supabase.from('user_games').insert(userGame).select().single();
  }

  /**
   * Add or update a game in user's collection (upsert)
   */
  async upsertToCollection(userGame: Omit<UserGameRecord, 'id' | 'created_at' | 'updated_at'>) {
    return supabase.from('user_games').upsert(userGame, { onConflict: 'user_id,game_id' }).select().single();
  }

  /**
   * Get a specific user game record
   */
  async getByUserAndGame(userId: string, gameId: string) {
    return supabase.from('user_games').select('*').eq('user_id', userId).eq('game_id', gameId).single();
  }

  /**
   * Update game status or other properties
   */
  async updateStatus(id: string, updates: UserGameUpdateData) {
    return supabase.from('user_games').update(updates).eq('id', id).select().single();
  }

  /**
   * Update game status by user and game ID
   */
  async updateGameStatus(userId: string, gameId: string, updates: UserGameUpdateData) {
    return supabase
      .from('user_games')
      .update(updates)
      .eq('user_id', userId)
      .eq('game_id', gameId)
      .select()
      .single();
  }

  /**
   * Remove a game from user's collection
   */
  async removeFromCollection(id: string) {
    return supabase.from('user_games').delete().eq('id', id);
  }

  /**
   * Remove a game from collection by user and game ID
   */
  async removeGameFromCollection(userId: string, gameId: string) {
    return supabase
      .from('user_games')
      .delete()
      .eq('user_id', userId)
      .eq('game_id', gameId);
  }

  /**
   * Move game to wishlist
   */
  async moveToWishlist(userId: string, gameId: string) {
    return this.updateGameStatus(userId, gameId, { status: 'wishlist' });
  }

  /**
   * Move game to library (owned)
   */
  async moveToLibrary(userId: string, gameId: string) {
    return this.updateGameStatus(userId, gameId, { status: 'owned' });
  }

  /**
   * Mark game as currently playing
   */
  async markAsPlaying(userId: string, gameId: string) {
    return this.updateGameStatus(userId, gameId, { status: 'playing' });
  }

  /**
   * Mark game as completed
   */
  async markAsCompleted(userId: string, gameId: string) {
    return this.updateGameStatus(userId, gameId, { status: 'completed' });
  }

  /**
   * Add game to backlog
   */
  async addToBacklog(userId: string, gameId: string) {
    return this.updateGameStatus(userId, gameId, { status: 'backlog' });
  }

  /**
   * Get user's collection statistics
   */
  async getCollectionStats(userId: string) {
    try {
      await this.ensureUserRecords(userId);
      
      return supabase.from('user_collection_stats').select('*').eq('user_id', userId).single();
    } catch (error) {
      console.error('Error getting collection stats:', error);
      throw error;
    }
  }

  /**
   * Helper function to ensure user records exist
   */
  private async ensureUserRecords(userId: string) {
    try {
      // Check if user profile exists
      const { error: profileError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        const { error: createProfileError } = await supabase
          .from('user_profiles')
          .insert({ id: userId });
        
        if (createProfileError) {
          console.warn('Failed to create user profile:', createProfileError);
        }
      }

      // Check if user preferences exist
      const { error: preferencesError } = await supabase
        .from('user_preferences')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (preferencesError && preferencesError.code === 'PGRST116') {
        // Preferences don't exist, create them
        const { error: createPreferencesError } = await supabase
          .from('user_preferences')
          .insert({ user_id: userId });
        
        if (createPreferencesError) {
          console.warn('Failed to create user preferences:', createPreferencesError);
        }
      }
    } catch (error) {
      console.warn('Error ensuring user records:', error);
    }
  }
}

/**
 * Price Tracking API
 * Handles game price tracking, alerts, and wishlist price monitoring
 */
export class PriceTrackingAPI {
  /**
   * Get current prices for a game
   */
  async getGamePrices(gameId: string) {
    return supabase.from('price_tracking').select('*').eq('game_id', gameId).order('last_updated', { ascending: false });
  }

  /**
   * Get price history for a game
   */
  async getGamePriceHistory(gameId: string, days: number = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    return supabase
      .from('price_tracking')
      .select('*')
      .eq('game_id', gameId)
      .gte('last_updated', cutoffDate.toISOString())
      .order('last_updated', { ascending: true });
  }

  /**
   * Get lowest recorded price for a game
   */
  async getLowestPrice(gameId: string) {
    return supabase
      .from('price_tracking')
      .select('*')
      .eq('game_id', gameId)
      .order('price', { ascending: true })
      .limit(1)
      .single();
  }

  /**
   * Get current prices across all platforms for a game
   */
  async getCurrentPrices(gameId: string) {
    return supabase
      .from('price_tracking')
      .select('*')
      .eq('game_id', gameId)
      .order('last_updated', { ascending: false })
      .limit(10);
  }

  /**
   * Add a new price entry
   */
  async addPriceEntry(priceData: Omit<PriceTrackingRecord, 'id' | 'created_at'>) {
    return supabase.from('price_tracking').insert(priceData).select().single();
  }

  /**
   * Update an existing price entry
   */
  async updatePriceEntry(id: string, updates: PriceTrackingUpdateData) {
    return supabase.from('price_tracking').update(updates).eq('id', id).select().single();
  }

  /**
   * Get user's price alerts
   */
  async getPriceAlerts(userId: string) {
    return supabase
      .from('price_alerts')
      .select(`
        *,
        game:games(*)
      `)
      .eq('user_id', userId)
      .eq('is_active', true);
  }

  /**
   * Create a new price alert
   */
  async createPriceAlert(alertData: Omit<PriceAlertRecord, 'id' | 'created_at' | 'updated_at'>) {
    return supabase.from('price_alerts').insert(alertData).select().single();
  }

  /**
   * Update a price alert
   */
  async updatePriceAlert(id: string, updates: PriceAlertUpdateData) {
    return supabase.from('price_alerts').update(updates).eq('id', id).select().single();
  }

  /**
   * Delete a price alert
   */
  async deletePriceAlert(id: string) {
    return supabase.from('price_alerts').delete().eq('id', id);
  }

  /**
   * Get wishlist with current prices
   */
  async getWishlistWithPrices(userId: string) {
    try {
      // Ensure user records exist before querying
      await this.ensureUserRecords(userId);
      
      return supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .eq('status', 'wishlist')
        .order('date_added', { ascending: false });
    } catch (error) {
      console.error('Error getting wishlist with prices:', error);
      throw error;
    }
  }

  /**
   * Get price alerts that should be triggered
   */
  async getTriggeredAlerts(userId: string) {
    // This would typically involve a more complex query joining price_alerts with current prices
    // For now, return active alerts and let the calling code check prices
    return this.getPriceAlerts(userId);
  }

  /**
   * Helper function to ensure user records exist
   */
  private async ensureUserRecords(userId: string) {
    try {
      // Check if user profile exists
      const { error: profileError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        const { error: createProfileError } = await supabase
          .from('user_profiles')
          .insert({ id: userId });
        
        if (createProfileError) {
          console.warn('Failed to create user profile:', createProfileError);
        }
      }

      // Check if user preferences exist
      const { error: preferencesError } = await supabase
        .from('user_preferences')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (preferencesError && preferencesError.code === 'PGRST116') {
        // Preferences don't exist, create them
        const { error: createPreferencesError } = await supabase
          .from('user_preferences')
          .insert({ user_id: userId });
        
        if (createPreferencesError) {
          console.warn('Failed to create user preferences:', createPreferencesError);
        }
      }
    } catch (error) {
      console.warn('Error ensuring user records:', error);
    }
  }
}

/**
 * Smart Collections API
 * Handles dynamic collections based on user behavior and preferences
 */
export class SmartCollectionsAPI {
  /**
   * Get recently added games
   */
  async getRecentlyAdded(userId: string, limit: number = 10) {
    try {
      return supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .order('date_added', { ascending: false })
        .limit(limit);
    } catch (error) {
      console.error('Error getting recently added games:', error);
      throw error;
    }
  }

  /**
   * Get highly rated games in user's collection
   */
  async getHighlyRated(userId: string, minRating: number = 8, limit: number = 10) {
    try {
      return supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .gte('user_rating', minRating)
        .order('user_rating', { ascending: false })
        .limit(limit);
    } catch (error) {
      console.error('Error getting highly rated games:', error);
      throw error;
    }
  }

  /**
   * Get games by genre from user's collection
   */
  async getGamesByGenre(userId: string, genre: string, limit: number = 20) {
    try {
      // This would require a more complex query with JSON operations
      // For now, return all games and let the calling code filter
      return supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .limit(limit);
    } catch (error) {
      console.error(`Error getting games by genre ${genre}:`, error);
      throw error;
    }
  }

  /**
   * Get games by platform from user's collection
   */
  async getGamesByPlatform(userId: string, platform: string, limit: number = 20) {
    try {
      return supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .eq('platform', platform)
        .limit(limit);
    } catch (error) {
      console.error(`Error getting games by platform ${platform}:`, error);
      throw error;
    }
  }

  /**
   * Get unfinished games (started but not completed)
   */
  async getUnfinishedGames(userId: string, limit: number = 20) {
    try {
      return supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .in('status', ['playing', 'paused'])
        .order('last_played', { ascending: false })
        .limit(limit);
    } catch (error) {
      console.error('Error getting unfinished games:', error);
      throw error;
    }
  }

  /**
   * Get games to replay (completed games with high ratings)
   */
  async getGamesToReplay(userId: string, minRating: number = 8, limit: number = 10) {
    try {
      return supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .eq('status', 'completed')
        .gte('user_rating', minRating)
        .order('user_rating', { ascending: false })
        .limit(limit);
    } catch (error) {
      console.error('Error getting games to replay:', error);
      throw error;
    }
  }
}

// Export API service instances
export const userGamesAPI = new UserGamesAPI();
export const priceTrackingAPI = new PriceTrackingAPI();
export const smartCollectionsAPI = new SmartCollectionsAPI();

// Keep backward compatibility
export const userGames = userGamesAPI;
export const priceTracking = priceTrackingAPI;