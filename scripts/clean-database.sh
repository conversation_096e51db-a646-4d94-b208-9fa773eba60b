#!/bin/bash

# Connect to Supabase and clean database using Supabase CLI

echo "Starting database cleanup..."

# List all tables
TABLES=$(supabase db list-tables --project-ref kfzwgkzvlbyxotnbhgqk)

if [ -z "$TABLES" ]; then
  echo "No tables found in database"
  exit 0
fi

echo "Found tables:"
echo "$TABLES"

# Drop each table
for TABLE in $TABLES; do
  echo "Dropping table: $TABLE"
  supabase db drop-table --project-ref kfzwgkzvlbyxotnbhgqk --table $TABLE
done

echo "Database cleanup completed successfully"