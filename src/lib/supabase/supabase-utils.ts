export interface ErrorDetails {
  message: string;
  status?: string | number;
  name?: string;
  timestamp: string;
  action: string;
  context: string;
  [key: string]: any;
}

/**
 * Enhanced error logging with external service integration
 */
export const logError = (action: string, error: any, additionalContext: Record<string, any> = {}) => {
  const errorDetails: ErrorDetails = {
    message: error?.message || 'Unknown error',
    status: error?.status || 'unknown',
    name: error?.name || 'Error',
    timestamp: new Date().toISOString(),
    action,
    context: 'supabase',
    ...additionalContext
  };
  
  console.error(`❌ ${action} Error:`, errorDetails);
  
  // Log to external service if available
  if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
    try {
      window.navigator.sendBeacon('/api/log-error', JSON.stringify(errorDetails));
    } catch (logError) {
      console.warn('Failed to send error log:', logError);
    }
  }
};

/**
 * Retry logic for database operations
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Exponential backoff
      const waitTime = delay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw lastError;
};

/**
 * Type guard for checking if an error is a Supabase error
 */
export const isSupabaseError = (error: any): boolean => {
  return error && (error.code || error.status || error.message);
};

/**
 * Extract meaningful error message from Supabase error
 */
export const getErrorMessage = (error: any): string => {
  if (!error) return 'Unknown error occurred';
  
  if (typeof error === 'string') return error;
  
  if (error.message) return error.message;
  
  if (error.code) {
    switch (error.code) {
      case 'PGRST116':
        return 'Record not found';
      case '23505':
        return 'Record already exists';
      case '23503':
        return 'Referenced record does not exist';
      default:
        return `Database error: ${error.code}`;
    }
  }
  
  return 'An unexpected error occurred';
};

/**
 * Validate required environment variables
 */
export const validateEnvironment = () => {
  const requiredVars = ['VITE_SUPABASE_URL', 'VITE_SUPABASE_ANON_KEY'];
  const missing = requiredVars.filter(varName => !import.meta.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};