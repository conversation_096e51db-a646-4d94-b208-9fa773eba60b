-- Fix games table ID to support custom API IDs (like 'igdb_123', 'rawg_456')
-- This changes the ID from UUID to text to support external API identifiers

-- First, drop the existing foreign key constraints
ALTER TABLE user_games DROP CONSTRAINT IF EXISTS user_games_game_id_fkey;
ALTER TABLE price_tracking DROP CONSTRAINT IF EXISTS price_tracking_game_id_fkey;

-- Drop the existing games table (it should be empty for new setups)
DROP TABLE IF EXISTS games CASCADE;

-- Recreate games table with text ID
CREATE TABLE games (
  id text PRIMARY KEY,
  title text NOT NULL,
  platform text NOT NULL,
  genres text[] DEFAULT '{}',
  developer text,
  publisher text,
  release_date date,
  description text,
  cover_image text,
  screenshots text[] DEFAULT '{}',
  youtube_links text[] DEFAULT '{}',
  metacritic_score integer CHECK (metacritic_score >= 0 AND metacritic_score <= 100),
  igdb_id text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Clear dependent tables before changing schema, as the `games` table is being recreated.
-- This will remove existing user library/wishlist data.
DELETE FROM user_games;
DELETE FROM price_tracking;

-- Update user_games table to use text for game_id
ALTER TABLE user_games DROP COLUMN IF EXISTS game_id;
ALTER TABLE user_games ADD COLUMN game_id text REFERENCES games(id) ON DELETE CASCADE NOT NULL;

-- Update price_tracking table to use text for game_id  
ALTER TABLE price_tracking DROP COLUMN IF EXISTS game_id;
ALTER TABLE price_tracking ADD COLUMN game_id text REFERENCES games(id) ON DELETE CASCADE NOT NULL;

-- Recreate indexes
CREATE INDEX IF NOT EXISTS idx_games_title ON games(title);
CREATE INDEX IF NOT EXISTS idx_games_platform ON games(platform);
CREATE INDEX IF NOT EXISTS idx_games_genres ON games USING GIN(genres);
CREATE INDEX IF NOT EXISTS idx_user_games_game_id ON user_games(game_id);
CREATE INDEX IF NOT EXISTS idx_price_tracking_game_id ON price_tracking(game_id);

-- Update updated_at timestamp trigger
DROP TRIGGER IF EXISTS update_games_updated_at ON games;
CREATE TRIGGER update_games_updated_at
  BEFORE UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE games ENABLE ROW LEVEL SECURITY;

-- Recreate policies for games table
DROP POLICY IF EXISTS "Games are readable by authenticated users" ON games;
DROP POLICY IF EXISTS "Authenticated users can create games" ON games;
DROP POLICY IF EXISTS "Authenticated users can update games" ON games;

CREATE POLICY "Games are readable by authenticated users"
  ON games
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can create games"
  ON games
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update games"
  ON games
  FOR UPDATE
  TO authenticated
  USING (true);

-- Remove platform restriction - allow all platforms
ALTER TABLE games DROP CONSTRAINT IF EXISTS games_platform_check;