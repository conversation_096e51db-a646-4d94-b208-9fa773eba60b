import { useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';
import { userGamesAPI } from '@/lib/api';

export function useLibraryActions() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  // Update game status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async ({ userGameId, status }: { userGameId: string; status: string }) => {
      return await userGamesAPI.updateStatus(userGameId, status);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      toast.success('Game status updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update game status:', error);
      toast.error('Failed to update game status');
    }
  });
  
  // Update personal rating mutation
  const updateRatingMutation = useMutation({
    mutationFn: async ({ userGameId, rating }: { userGameId: string; rating: number }) => {
      return await userGamesAPI.updateRating(userGameId, rating);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      toast.success('Rating updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update rating:', error);
      toast.error('Failed to update rating');
    }
  });
  
  // Update hours played mutation
  const updateHoursMutation = useMutation({
    mutationFn: async ({ userGameId, hours }: { userGameId: string; hours: number }) => {
      return await userGamesAPI.updateHoursPlayed(userGameId, hours);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      toast.success('Hours played updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update hours played:', error);
      toast.error('Failed to update hours played');
    }
  });
  
  // Add personal notes mutation
  const updateNotesMutation = useMutation({
    mutationFn: async ({ userGameId, notes }: { userGameId: string; notes: string }) => {
      return await userGamesAPI.updateNotes(userGameId, notes);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      toast.success('Notes updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update notes:', error);
      toast.error('Failed to update notes');
    }
  });
  
  // Remove game from library mutation
  const removeGameMutation = useMutation({
    mutationFn: async (userGameId: string) => {
      return await userGamesAPI.removeFromLibrary(userGameId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      toast.success('Game removed from library');
    },
    onError: (error) => {
      console.error('Failed to remove game:', error);
      toast.error('Failed to remove game from library');
    }
  });
  
  // Move game to wishlist mutation
  const moveToWishlistMutation = useMutation({
    mutationFn: async (userGameId: string) => {
      return await userGamesAPI.updateStatus(userGameId, 'wishlist');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      toast.success('Game moved to wishlist');
    },
    onError: (error) => {
      console.error('Failed to move to wishlist:', error);
      toast.error('Failed to move game to wishlist');
    }
  });
  
  // Bulk update status for multiple games
  const bulkUpdateStatusMutation = useMutation({
    mutationFn: async ({ userGameIds, status }: { userGameIds: string[]; status: string }) => {
      return await Promise.all(
        userGameIds.map(id => userGamesAPI.updateStatus(id, status))
      );
    },
    onSuccess: (_, { userGameIds }) => {
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      toast.success(`Updated status for ${userGameIds.length} games`);
    },
    onError: (error) => {
      console.error('Failed to bulk update status:', error);
      toast.error('Failed to update game statuses');
    }
  });
  
  // Bulk remove games from library
  const bulkRemoveMutation = useMutation({
    mutationFn: async (userGameIds: string[]) => {
      return await Promise.all(
        userGameIds.map(id => userGamesAPI.removeFromLibrary(id))
      );
    },
    onSuccess: (_, userGameIds) => {
      queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
      toast.success(`Removed ${userGameIds.length} games from library`);
    },
    onError: (error) => {
      console.error('Failed to bulk remove games:', error);
      toast.error('Failed to remove games from library');
    }
  });
  
  // Callback functions for UI components
  const updateGameStatus = useCallback((userGameId: string, status: string) => {
    updateStatusMutation.mutate({ userGameId, status });
  }, [updateStatusMutation]);
  
  const updateGameRating = useCallback((userGameId: string, rating: number) => {
    updateRatingMutation.mutate({ userGameId, rating });
  }, [updateRatingMutation]);
  
  const updateGameHours = useCallback((userGameId: string, hours: number) => {
    updateHoursMutation.mutate({ userGameId, hours });
  }, [updateHoursMutation]);
  
  const updateGameNotes = useCallback((userGameId: string, notes: string) => {
    updateNotesMutation.mutate({ userGameId, notes });
  }, [updateNotesMutation]);
  
  const removeGame = useCallback((userGameId: string) => {
    removeGameMutation.mutate(userGameId);
  }, [removeGameMutation]);
  
  const moveToWishlist = useCallback((userGameId: string) => {
    moveToWishlistMutation.mutate(userGameId);
  }, [moveToWishlistMutation]);
  
  const bulkUpdateStatus = useCallback((userGameIds: string[], status: string) => {
    bulkUpdateStatusMutation.mutate({ userGameIds, status });
  }, [bulkUpdateStatusMutation]);
  
  const bulkRemoveGames = useCallback((userGameIds: string[]) => {
    bulkRemoveMutation.mutate(userGameIds);
  }, [bulkRemoveMutation]);
  
  // Quick action helpers
  const markAsPlaying = useCallback((userGameId: string) => {
    updateGameStatus(userGameId, 'playing');
  }, [updateGameStatus]);
  
  const markAsCompleted = useCallback((userGameId: string) => {
    updateGameStatus(userGameId, 'completed');
  }, [updateGameStatus]);
  
  const markAsBacklog = useCallback((userGameId: string) => {
    updateGameStatus(userGameId, 'backlog');
  }, [updateGameStatus]);
  
  const markAsDropped = useCallback((userGameId: string) => {
    updateGameStatus(userGameId, 'dropped');
  }, [updateGameStatus]);
  
  // Check if any mutations are loading
  const isLoading = 
    updateStatusMutation.isPending ||
    updateRatingMutation.isPending ||
    updateHoursMutation.isPending ||
    updateNotesMutation.isPending ||
    removeGameMutation.isPending ||
    moveToWishlistMutation.isPending ||
    bulkUpdateStatusMutation.isPending ||
    bulkRemoveMutation.isPending;
  
  return {
    // Main action functions
    updateGameStatus,
    updateGameRating,
    updateGameHours,
    updateGameNotes,
    removeGame,
    moveToWishlist,
    
    // Bulk operations
    bulkUpdateStatus,
    bulkRemoveGames,
    
    // Quick actions
    markAsPlaying,
    markAsCompleted,
    markAsBacklog,
    markAsDropped,
    
    // State
    isLoading,
    
    // Individual mutation states for granular loading states
    isUpdatingStatus: updateStatusMutation.isPending,
    isUpdatingRating: updateRatingMutation.isPending,
    isUpdatingHours: updateHoursMutation.isPending,
    isUpdatingNotes: updateNotesMutation.isPending,
    isRemoving: removeGameMutation.isPending,
    isMovingToWishlist: moveToWishlistMutation.isPending,
    isBulkUpdating: bulkUpdateStatusMutation.isPending,
    isBulkRemoving: bulkRemoveMutation.isPending
  };
}