import { Card, CardContent, CardDescription, CardHeader, CardTitle, Badge, Button } from '@/components/ui/base';
import { LoadingSpinner } from '@/components/ui/utils';
import { useAuth } from '@/contexts/AuthContext';
import { useQuery } from '@tanstack/react-query';
import { db } from '@/lib/supabase';
import { useDeals, formatPrice, formatDiscount, getDealBadgeColor } from '@/hooks/usePriceTracking';
import { UserGameWithDetails, PriceTrackingRecord } from '@/types/database';
import { BadgePercent, AlertCircle, TrendingDown, ExternalLink, Flame, RefreshCw } from 'lucide-react';

export default function Deals() {
  const { user } = useAuth();

  // Fetch user's wishlist for potential deals
  const { data: wishlist, isLoading } = useQuery({
    queryKey: ['user-wishlist-deals', user?.id],
    queryFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const { data, error } = await db.userGames.getUserCollection(user.id);
      if (error) throw error;
      
      return data?.filter(game => game.status === 'wishlist') || [];
    },
    enabled: !!user?.id,
  });

  // Use the new deals hook
  const {
    bestDeals,
    wishlistWithPrices,
    dealStats,
    isLoadingDeals,
    isLoadingWishlist,
    refetchDeals
  } = useDeals();

  const wishlistCount = wishlist?.length || 0;
  const activeDeals = bestDeals.length;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Deals</h1>
          <p className="text-muted-foreground">Track price drops on your wishlist games</p>
        </div>
        <Badge variant="outline" className="bg-accent/10 text-accent-foreground">
          <BadgePercent className="h-3 w-3 mr-1" />
          {activeDeals} active deals
        </Badge>
      </div>
      
      {isLoading ? (
        <div className="flex items-center justify-center min-h-[200px]">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        <>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Wishlist Games</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-primary">{wishlistCount}</div>
                <p className="text-xs text-muted-foreground">games being tracked for deals</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Active Deals</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-accent">{activeDeals}</div>
                <p className="text-xs text-muted-foreground">
                  {activeDeals > 0 ? 'price drops on your wishlist' : 'no current deals'}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Best Discount</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {dealStats.biggestDiscount > 0 ? `${Math.round(dealStats.biggestDiscount)}%` : '—'}
                </div>
                <p className="text-xs text-muted-foreground">
                  {dealStats.biggestDiscount > 0 ? 'maximum discount found' : 'no deals yet'}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Potential Savings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {dealStats.totalSavings > 0 ? formatPrice(dealStats.totalSavings) : '—'}
                </div>
                <p className="text-xs text-muted-foreground">
                  {dealStats.totalSavings > 0 ? 'if you buy all deals now' : 'no savings yet'}
                </p>
              </CardContent>
            </Card>
          </div>
        </>
      )}
      
          {/* Hot Deals Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Flame className="h-5 w-5 text-orange-500" />
                  Hot Deals
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">
                    {bestDeals.length} deals found
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => refetchDeals()}
                    disabled={isLoadingDeals}
                  >
                    <RefreshCw className={`h-4 w-4 ${isLoadingDeals ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </CardTitle>
              <CardDescription>Price drops detected on games in your wishlist</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingDeals ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="lg" />
                </div>
              ) : bestDeals.length === 0 ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    {wishlistCount === 0 ? 'No games in your wishlist to track' : 'No price drops found'}
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    {wishlistCount === 0
                      ? 'Add games to your wishlist and we\'ll monitor prices for you'
                      : 'We\'re monitoring prices on your wishlist games - deals will appear here when found'
                    }
                  </p>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {bestDeals.map((deal, index) => (
                    <div
                      key={index}
                      className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-sm mb-1 line-clamp-2">
                            {deal.gameName}
                          </h3>
                          <p className="text-xs text-muted-foreground mb-2">
                            {deal.shopName}
                          </p>
                        </div>
                        <Badge className={getDealBadgeColor(deal.discountPercent)}>
                          {formatDiscount(deal.discountPercent)}
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Current Price:</span>
                          <span className="font-semibold text-green-600">
                            {formatPrice(deal.currentPrice, deal.currency)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Original Price:</span>
                          <span className="text-sm line-through text-muted-foreground">
                            {formatPrice(deal.originalPrice, deal.currency)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">You Save:</span>
                          <span className="font-semibold text-orange-600">
                            {formatPrice(deal.originalPrice - deal.currentPrice, deal.currency)}
                          </span>
                        </div>
                      </div>
                      
                      <Button
                        className="w-full mt-3"
                        size="sm"
                        onClick={() => window.open(deal.shopUrl, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Deal
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Price Tracking Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingDown className="h-4 w-4" />
                Price Tracking Overview
              </CardTitle>
              <CardDescription>All games in your wishlist are automatically tracked for price changes</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingWishlist ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="lg" />
                </div>
              ) : wishlistWithPrices.length === 0 ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    {wishlistCount === 0 ? 'No wishlist games to track' : 'Price tracking in progress'}
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    {wishlistCount === 0
                      ? 'Games added to your wishlist will automatically be monitored for price changes'
                      : 'We\'re collecting price data for your wishlist games - check back soon for updates'
                    }
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {wishlistWithPrices.slice(0, 5).map((item: UserGameWithDetails & { price_tracking?: PriceTrackingRecord[] }, index: number) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-muted/30 rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                          <span className="text-xs font-medium">
                            {item.game?.title?.charAt(0) || '?'}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-sm">{item.game?.title}</p>
                          <p className="text-xs text-muted-foreground">
                            Added to wishlist {item.created_at ? new Date(item.created_at).toLocaleDateString() : 'Unknown date'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-sm">
                          Tracking Active
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {item.price_tracking?.length || 0} price points
                        </p>
                      </div>
                    </div>
                  ))}
                  
                  {wishlistWithPrices.length > 5 && (
                    <div className="text-center pt-4">
                      <p className="text-sm text-muted-foreground">
                        And {wishlistWithPrices.length - 5} more games being tracked...
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
    </div>
  );
}