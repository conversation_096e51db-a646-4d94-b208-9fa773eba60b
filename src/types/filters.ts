// Core filter types for the game library application
import { Platform } from './index';

export type SortField = 'title' | 'release_date' | 'metacritic_score' | 'added_at' | 'popularity';
export type SortDirection = 'asc' | 'desc';

export interface SortOptions {
  field: SortField;
  direction: SortDirection;
}

// Filter interfaces
export interface PlatformFilter {
  enabled: boolean;
  platforms: Platform[];
}

export interface GenreFilter {
  enabled: boolean;
  genres: string[];
  mode: 'include' | 'exclude'; // Include any of these genres OR exclude all of these genres
}

export interface YearFilter {
  enabled: boolean;
  minYear?: number;
  maxYear?: number;
}

export interface RatingFilter {
  enabled: boolean;
  minRating?: number;
  maxRating?: number;
}

export interface DeveloperFilter {
  enabled: boolean;
  developers: string[];
}

export interface PublisherFilter {
  enabled: boolean;
  publishers: string[];
}

export interface SearchFilter {
  enabled: boolean;
  query: string;
  searchType: 'exact' | 'fuzzy' | 'smart';
}

// Enhanced tagging system interfaces
export interface UserTag {
  id: string;
  name: string;
  description?: string;
  color: string;
  usage_count: number;
  is_system: boolean;
  created_at: Date;
}

export interface CustomTagsFilter {
  enabled: boolean;
  tags: string[]; // Tag IDs
  mode: 'include' | 'exclude'; // Include games with any of these tags OR exclude games with all of these tags
  includeUntagged: boolean; // Whether to include games without any tags
}

export interface StatusFilter {
  enabled: boolean;
  statuses: Array<'playing' | 'completed' | 'backlog' | 'wishlist'>;
  mode: 'include' | 'exclude';
}

export interface CollectionFilter {
  enabled: boolean;
  inLibrary: boolean;
  inWishlist: boolean;
  notInCollection: boolean;
}

// Main filter state interface
export interface GameFilters {
  search: SearchFilter;
  platforms: PlatformFilter;
  genres: GenreFilter;
  year: YearFilter;
  rating: RatingFilter;
  developer: DeveloperFilter;
  publisher: PublisherFilter;
  customTags: CustomTagsFilter;
  status: StatusFilter;
  collection: CollectionFilter;
  sort: SortOptions;
}

// Filter preset for saved searches
export interface FilterPreset {
  id: string;
  name: string;
  description?: string;
  filters: GameFilters;
  createdAt: Date;
  updatedAt: Date;
  isPublic: boolean;
  usageCount: number;
}

// Filter application result
export interface FilterResult<T> {
  data: T[];
  totalCount: number;
  filteredCount: number;
  appliedFilters: string[];
  searchTime: number;
}

// Filter validation result
export interface FilterValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Pagination options
export interface PaginationOptions {
  page: number;
  limit: number;
  offset: number;
}

// Search and filter context
export interface SearchContext {
  filters: GameFilters;
  pagination: PaginationOptions;
  isLoading: boolean;
  error: string | null;
  results: FilterResult<unknown> | null;
}

// Filter statistics for analytics
export interface FilterStats {
  totalGames: number;
  filteredGames: number;
  popularFilters: Array<{
    filterType: keyof GameFilters;
    usageCount: number;
    lastUsed: Date;
  }>;
  averageSearchTime: number;
}

// Default filter values
export const DEFAULT_FILTERS: GameFilters = {
  search: {
    enabled: false,
    query: '',
    searchType: 'smart'
  },
  platforms: {
    enabled: false,
    platforms: []
  },
  genres: {
    enabled: false,
    genres: [],
    mode: 'include'
  },
  year: {
    enabled: false,
    minYear: undefined,
    maxYear: undefined
  },
  rating: {
    enabled: false,
    minRating: undefined,
    maxRating: undefined
  },
  developer: {
    enabled: false,
    developers: []
  },
  publisher: {
    enabled: false,
    publishers: []
  },
  customTags: {
    enabled: false,
    tags: [],
    mode: 'include',
    includeUntagged: false
  },
  status: {
    enabled: false,
    statuses: [],
    mode: 'include'
  },
  collection: {
    enabled: false,
    inLibrary: false,
    inWishlist: false,
    notInCollection: false
  },
  sort: {
    field: 'title',
    direction: 'asc'
  }
};

export const DEFAULT_PAGINATION: PaginationOptions = {
  page: 1,
  limit: 24,
  offset: 0
};

// Filter utility types
export type FilterUpdateAction<T extends keyof GameFilters> = {
  type: 'UPDATE_FILTER';
  filterType: T;
  payload: Partial<GameFilters[T]>;
};

export type FilterResetAction = {
  type: 'RESET_FILTERS';
  keepSearch?: boolean;
};

export type FilterPresetAction = {
  type: 'LOAD_PRESET';
  preset: FilterPreset;
};

export type FilterAction<T extends keyof GameFilters = keyof GameFilters> = 
  | FilterUpdateAction<T>
  | FilterResetAction
  | FilterPresetAction;

// Filter hook return type
export interface UseFiltersReturn {
  filters: GameFilters;
  updateFilter: <T extends keyof GameFilters>(
    filterType: T, 
    update: Partial<GameFilters[T]>
  ) => void;
  resetFilters: (keepSearch?: boolean) => void;
  loadPreset: (preset: FilterPreset) => void;
  hasActiveFilters: boolean;
  activeFilterCount: number;
  validateFilters: () => FilterValidation;
}