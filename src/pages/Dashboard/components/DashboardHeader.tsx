import { Badge } from '@/components/ui/base';
import { useAuth } from '@/contexts/AuthContext';
import { TrendingUp } from 'lucide-react';
import { DashboardHeaderProps } from '../types';

export function DashboardHeader({ stats }: DashboardHeaderProps) {
  const { user } = useAuth();

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome back, {user?.user_metadata?.username || user?.email}
        </p>
      </div>
      <Badge variant="outline" className="bg-primary/10">
        <TrendingUp className="h-3 w-3 mr-1" />
        {stats?.totalGames || 0} games tracked
      </Badge>
    </div>
  );
}