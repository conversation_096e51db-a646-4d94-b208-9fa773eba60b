import { useState, useEffect, useCallback } from 'react';

export type ThemePreference = 'light' | 'dark' | 'system';
export type ResolvedTheme = 'light' | 'dark';

interface ThemePreferenceData {
  theme: ThemePreference;
  lastUpdated: string;
  autoDetectSystem: boolean;
  contrastPreference?: 'normal' | 'high';
  motionPreference?: 'normal' | 'reduced';
}

interface UseThemePreferenceReturn {
  theme: ThemePreference;
  resolvedTheme: ResolvedTheme;
  systemTheme: ResolvedTheme;
  setTheme: (theme: ThemePreference) => void;
  isLoading: boolean;
  error: string | null;
  preferences: {
    contrast: 'normal' | 'high';
    motion: 'normal' | 'reduced';
    colorScheme: ResolvedTheme;
  };
}

const STORAGE_KEY = 'codexa-theme-preference';
const STORAGE_VERSION = '1.0';

// Detect system theme preference
function getSystemTheme(): ResolvedTheme {
  if (typeof window === 'undefined') return 'light';
  
  try {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  } catch {
    return 'light';
  }
}

// Detect system contrast preference
function getSystemContrast(): 'normal' | 'high' {
  if (typeof window === 'undefined') return 'normal';
  
  try {
    return window.matchMedia('(prefers-contrast: high)').matches ? 'high' : 'normal';
  } catch {
    return 'normal';
  }
}

// Detect system motion preference
function getSystemMotion(): 'normal' | 'reduced' {
  if (typeof window === 'undefined') return 'normal';
  
  try {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches ? 'reduced' : 'normal';
  } catch {
    return 'normal';
  }
}

// Load theme preference from localStorage
function loadThemePreference(): ThemePreferenceData {
  if (typeof window === 'undefined') {
    return {
      theme: 'system',
      lastUpdated: new Date().toISOString(),
      autoDetectSystem: true,
      contrastPreference: 'normal',
      motionPreference: 'normal',
    };
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) {
      return {
        theme: 'system',
        lastUpdated: new Date().toISOString(),
        autoDetectSystem: true,
        contrastPreference: getSystemContrast(),
        motionPreference: getSystemMotion(),
      };
    }

    const parsed = JSON.parse(stored);
    
    // Validate stored data structure
    if (typeof parsed !== 'object' || !parsed.theme) {
      throw new Error('Invalid stored theme data');
    }

    return {
      theme: parsed.theme || 'system',
      lastUpdated: parsed.lastUpdated || new Date().toISOString(),
      autoDetectSystem: parsed.autoDetectSystem ?? true,
      contrastPreference: parsed.contrastPreference || getSystemContrast(),
      motionPreference: parsed.motionPreference || getSystemMotion(),
    };
  } catch (error) {
    console.warn('Failed to load theme preference:', error);
    return {
      theme: 'system',
      lastUpdated: new Date().toISOString(),
      autoDetectSystem: true,
      contrastPreference: getSystemContrast(),
      motionPreference: getSystemMotion(),
    };
  }
}

// Save theme preference to localStorage
function saveThemePreference(data: ThemePreferenceData): void {
  if (typeof window === 'undefined') return;

  try {
    const toStore = {
      ...data,
      version: STORAGE_VERSION,
      lastUpdated: new Date().toISOString(),
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(toStore));
  } catch (error) {
    console.warn('Failed to save theme preference:', error);
  }
}

// Apply theme to document
function applyThemeToDocument(theme: ResolvedTheme, preferences: {
  contrast: 'normal' | 'high';
  motion: 'normal' | 'reduced';
}): void {
  if (typeof document === 'undefined') return;

  const root = document.documentElement;
  
  // Add transition class for smooth changes (unless reduced motion is preferred)
  if (preferences.motion === 'normal') {
    root.classList.add('theme-changing');
  }

  // Remove existing theme classes
  root.classList.remove('light', 'dark');
  
  // Add new theme class
  root.classList.add(theme);

  // Set color-scheme for better browser integration
  root.style.colorScheme = theme;

  // Update meta theme-color for mobile browsers
  const metaThemeColor = document.querySelector('meta[name="theme-color"]');
  if (metaThemeColor) {
    const bgColor = theme === 'dark' 
      ? 'hsl(222, 84%, 5%)' 
      : 'hsl(250, 20%, 98%)';
    metaThemeColor.setAttribute('content', bgColor);
  }

  // Remove transition class after animation
  if (preferences.motion === 'normal') {
    setTimeout(() => {
      root.classList.remove('theme-changing');
    }, 300);
  }
}

// Custom hook for theme preference management
export function useThemePreference(): UseThemePreferenceReturn {
  const [themeData, setThemeData] = useState<ThemePreferenceData>(() => loadThemePreference());
  const [systemTheme, setSystemTheme] = useState<ResolvedTheme>(() => getSystemTheme());
  const [systemContrast, setSystemContrast] = useState<'normal' | 'high'>(() => getSystemContrast());
  const [systemMotion, setSystemMotion] = useState<'normal' | 'reduced'>(() => getSystemMotion());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Calculate resolved theme
  const resolvedTheme: ResolvedTheme = themeData.theme === 'system' ? systemTheme : themeData.theme;

  // Set up system preference listeners
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const colorSchemeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const contrastQuery = window.matchMedia('(prefers-contrast: high)');
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');

    const handleColorSchemeChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    const handleContrastChange = (e: MediaQueryListEvent) => {
      setSystemContrast(e.matches ? 'high' : 'normal');
    };

    const handleMotionChange = (e: MediaQueryListEvent) => {
      setSystemMotion(e.matches ? 'reduced' : 'normal');
    };

    // Add listeners
    colorSchemeQuery.addEventListener('change', handleColorSchemeChange);
    contrastQuery.addEventListener('change', handleContrastChange);
    motionQuery.addEventListener('change', handleMotionChange);

    // Initial load complete
    setIsLoading(false);

    return () => {
      colorSchemeQuery.removeEventListener('change', handleColorSchemeChange);
      contrastQuery.removeEventListener('change', handleContrastChange);
      motionQuery.removeEventListener('change', handleMotionChange);
    };
  }, []);

  // Apply theme when resolved theme changes
  useEffect(() => {
    if (!isLoading) {
      const preferences = {
        contrast: themeData.contrastPreference || systemContrast,
        motion: themeData.motionPreference || systemMotion,
      };
      
      applyThemeToDocument(resolvedTheme, preferences);
    }
  }, [resolvedTheme, systemContrast, systemMotion, themeData.contrastPreference, themeData.motionPreference, isLoading]);

  // Theme setter with persistence
  const setTheme = useCallback((newTheme: ThemePreference) => {
    try {
      const newThemeData: ThemePreferenceData = {
        ...themeData,
        theme: newTheme,
        lastUpdated: new Date().toISOString(),
      };
      
      setThemeData(newThemeData);
      saveThemePreference(newThemeData);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update theme');
    }
  }, [themeData]);

  return {
    theme: themeData.theme,
    resolvedTheme,
    systemTheme,
    setTheme,
    isLoading,
    error,
    preferences: {
      contrast: themeData.contrastPreference || systemContrast,
      motion: themeData.motionPreference || systemMotion,
      colorScheme: resolvedTheme,
    },
  };
}
