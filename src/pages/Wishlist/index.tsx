import { useState } from 'react';
import { EnhancedGameDetailModal } from '@/components/ui/game/enhanced-game-detail-modal';
import { LoadingSpinner } from '@/components/ui/utils';
import { Game } from '@/types';
import { UserGameWithDetails } from '@/types/database';
import { WishlistHeader } from './components/WishlistHeader';
import { WishlistStats } from './components/WishlistStats';
import { WishlistContent } from './components/WishlistContent';
import { useWishlistData } from './hooks';

export default function Wishlist() {
  const { wishlistGames, stats, isLoading, error } = useWishlistData();
  const [selectedGame, setSelectedGame] = useState<UserGameWithDetails | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'steam'>('grid');

  const handleGameClick = (gameData: UserGameWithDetails) => {
    setSelectedGame(gameData);
    setIsModalOpen(true);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="text-center py-8">
          <p className="text-destructive">Failed to load wishlist</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <WishlistHeader
        gameCount={wishlistGames.length}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
      />

      <WishlistStats stats={stats} />

      <WishlistContent
        wishlistGames={wishlistGames}
        onGameClick={handleGameClick}
        viewMode={viewMode}
      />

      <EnhancedGameDetailModal
        gameData={selectedGame}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onStatusUpdate={(userGameId, status) => {
          console.log('Status update:', userGameId, status);
          // Add status update functionality here
        }}
        onQuickPlay={(gameData) => {
          console.log('Quick play:', gameData.game?.title);
          // Add quick play functionality here
        }}
        onAddToWishlist={(gameData) => {
          console.log('Add to wishlist:', gameData.game?.title);
          // Already in wishlist
        }}
      />
    </div>
  );
}