// Platform name normalization and extraction utilities
import { Game } from '@/types';

/**
 * Platform mapping between different naming conventions
 */
export const PLATFORM_NAME_MAP: Record<string, string> = {
  // IGDB variations to normalized names
  'PC (Microsoft Windows)': 'PC',
  'PlayStation 5': 'PlayStation 5',
  'PlayStation 4': 'PlayStation 4', 
  'PlayStation 3': 'PlayStation 3',
  'PlayStation 2': 'PlayStation 2',
  'PlayStation': 'PlayStation',
  'Xbox Series X|S': 'Xbox Series X/S', // Fix pipe vs slash inconsistency
  'Xbox Series X/S': 'Xbox Series X/S',
  'Xbox One': 'Xbox One',
  'Xbox 360': 'Xbox 360',
  'Xbox': 'Xbox',
  'Nintendo Switch': 'Nintendo Switch',
  'Nintendo 3DS': 'Nintendo 3DS',
  'Nintendo DS': 'Nintendo DS',
  'Wii U': 'Wii U',
  'Wii': 'Wii',
  'GameCube': 'GameCube',
  'Nintendo GameCube': 'GameCube',
  'Nintendo 64': 'Nintendo 64',
  'iOS': 'iOS',
  'Android': 'Android',
  'macOS': 'Mac',
  'Mac': 'Mac',
  'Linux': 'Linux',
  'Steam Deck': 'Steam Deck'
};

/**
 * Display name abbreviations for compact UI
 */
export const PLATFORM_ABBREVIATIONS: Record<string, string> = {
  'PC': 'PC',
  'PlayStation 5': 'PS5',
  'PlayStation 4': 'PS4', 
  'PlayStation 3': 'PS3',
  'PlayStation 2': 'PS2',
  'PlayStation': 'PS1',
  'Xbox Series X/S': 'Xbox S|X',
  'Xbox One': 'Xbox One',
  'Xbox 360': 'Xbox 360',
  'Xbox': 'Xbox',
  'Nintendo Switch': 'Switch',
  'Nintendo 3DS': '3DS',
  'Nintendo DS': 'DS',
  'Wii U': 'Wii U',
  'Wii': 'Wii',
  'GameCube': 'GameCube',
  'Nintendo 64': 'N64',
  'iOS': 'iOS',
  'Android': 'Android',
  'Mac': 'Mac',
  'Linux': 'Linux',
  'Steam Deck': 'Steam Deck'
};

/**
 * Platform families for categorization
 */
export const PLATFORM_FAMILIES: Record<string, string[]> = {
  'PC & Digital': ['PC', 'Mac', 'Linux', 'Steam Deck'],
  'PlayStation': ['PlayStation 5', 'PlayStation 4', 'PlayStation 3', 'PlayStation 2', 'PlayStation'],
  'Xbox': ['Xbox Series X/S', 'Xbox One', 'Xbox 360', 'Xbox'],
  'Nintendo': ['Nintendo Switch', 'Nintendo 3DS', 'Nintendo DS', 'Wii U', 'Wii', 'GameCube', 'Nintendo 64'],
  'Mobile': ['iOS', 'Android']
};

/**
 * Normalize a platform name to a consistent format
 */
export function normalizePlatformName(platformName: string): string {
  return PLATFORM_NAME_MAP[platformName] || platformName;
}

/**
 * Get abbreviated version of platform name for compact display
 */
export function getPlatformAbbreviation(platformName: string): string {
  const normalized = normalizePlatformName(platformName);
  return PLATFORM_ABBREVIATIONS[normalized] || normalized;
}

/**
 * Extract unique platforms from a list of games
 */
export function extractPlatformsFromGames(games: Game[]): string[] {
  const platformSet = new Set<string>();
  
  games.forEach(game => {
    if (game.platforms) {
      game.platforms.forEach(platform => {
        if (platform) {
          platformSet.add(normalizePlatformName(platform));
        }
      });
    }
  });
  
  return Array.from(platformSet).sort();
}

/**
 * Get platform family for a given platform
 */
export function getPlatformFamily(platformName: string): string {
  const normalized = normalizePlatformName(platformName);
  
  for (const [family, platforms] of Object.entries(PLATFORM_FAMILIES)) {
    if (platforms.includes(normalized)) {
      return family;
    }
  }
  
  return 'Other';
}

/**
 * Group platforms by family
 */
export function groupPlatformsByFamily(platforms: string[]): Record<string, string[]> {
  const grouped: Record<string, string[]> = {};
  
  platforms.forEach(platform => {
    const family = getPlatformFamily(platform);
    if (!grouped[family]) {
      grouped[family] = [];
    }
    grouped[family].push(platform);
  });
  
  // Sort platforms within each family
  Object.keys(grouped).forEach(family => {
    grouped[family].sort();
  });
  
  return grouped;
}

/**
 * Count games per platform
 */
export function countGamesByPlatform(games: Game[]): Record<string, number> {
  const counts: Record<string, number> = {};
  
  games.forEach(game => {
    if (game.platforms) {
      game.platforms.forEach(platform => {
        if (platform) {
          const normalized = normalizePlatformName(platform);
          counts[normalized] = (counts[normalized] || 0) + 1;
        }
      });
    }
  });
  
  return counts;
}

/**
 * Get platforms sorted by frequency in games list
 */
export function getPlatformsByFrequency(games: Game[]): Array<{platform: string, count: number}> {
  const counts = countGamesByPlatform(games);
  
  return Object.entries(counts)
    .map(([platform, count]) => ({ platform, count }))
    .sort((a, b) => b.count - a.count);
}