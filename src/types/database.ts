// Database-specific types for Supabase operations

import { Session, AuthChangeEvent } from '@supabase/supabase-js';

// Enhanced tagging system database types
export interface UserTagRecord {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  color: string;
  created_at: string;
  updated_at: string;
  usage_count: number;
  is_system: boolean;
}

export interface UserGameTagRecord {
  id: string;
  user_id: string;
  user_game_id: string;
  tag_id: string;
  created_at: string;
}

export interface FilterPresetRecord {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  filters: unknown; // JSONB field containing filter state
  is_public: boolean;
  usage_count: number;
  created_at: string;
  updated_at: string;
}

export interface TagSuggestionRecord {
  id: string;
  user_id: string;
  tag_name: string;
  context?: unknown; // JSONB field for suggestion context
  confidence_score: number;
  created_at: string;
  is_accepted: boolean;
}

// Auth types
export interface AuthStateChangeCallback {
  (event: AuthChangeEvent, session: Session | null): void;
}

// Game database record
export interface GameRecord {
  id: string;
  title: string;
  platform: string; // Required field with CHECK constraint
  description?: string;
  cover_image?: string;
  screenshots?: string[];
  release_date?: string; // Date as string (YYYY-MM-DD)
  metacritic_score?: number;
  developer?: string; // Single developer (not array)
  publisher?: string; // Single publisher (not array)
  genres?: string[]; // Array of genre strings
  igdb_id?: string;
  youtube_links?: string[];
  steam_app_id?: string; // Steam-specific field
  steam_url?: string; // Steam store URL
  created_at?: string;
  updated_at?: string;
}

// Custom artwork record
export interface UserGameArtworkRecord {
  id: string;
  user_game_id: string;
  artwork_type: 'front' | 'back' | 'spine' | '3d' | 'manual' | 'disc';
  file_url: string;
  file_size?: number;
  file_type?: string;
  is_primary: boolean;
  uploaded_at: string;
  created_at: string;
}

// User game relationship
export interface UserGameRecord {
  id: string;
  user_id: string;
  game_id: string;
  status: 'playing' | 'completed' | 'backlog' | 'wishlist' | 'owned'; // Matches CHECK constraint
  personal_rating?: number; // 1-5 rating
  personal_notes?: string;
  hours_played?: number; // Decimal hours played
  date_added: string;
  date_completed?: string; // When game was completed
  is_wishlist?: boolean; // Whether this is a wishlist item
  import_source?: string; // Where the game was imported from (steam, epic, etc.)
  last_played?: string; // When the game was last played
  achievements_unlocked?: number; // Number of achievements unlocked for this game
}

// User game with game details (from join)
export interface UserGameWithDetails extends UserGameRecord {
  game: GameRecord;
}

// Price tracking record
export interface PriceTrackingRecord {
  id: string;
  game_id: string;
  store_name: string;
  price: number;
  currency: string;
  url?: string;
  last_updated: string;
  is_on_sale: boolean;
  original_price?: number;
  discount_percentage?: number;
  created_at?: string;
}

// Price alert record
export interface PriceAlertRecord {
  id: string;
  user_id: string;
  game_id: string;
  target_price: number;
  currency: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

// Price alert with game details
export interface PriceAlertWithGame extends PriceAlertRecord {
  game: GameRecord;
}

// User profile record
export interface UserProfileRecord {
  id: string;
  username?: string;
  display_name?: string;
  bio?: string;
  avatar_url?: string;
  location?: string;
  website?: string;
  social_links?: Record<string, string>;
  created_at?: string;
  updated_at?: string;
}

// User preferences record
export interface UserPreferencesRecord {
  id: string;
  user_id: string;
  theme: 'light' | 'dark' | 'system';
  notifications: {
    price_alerts: boolean;
    new_deals: boolean;
    game_updates: boolean;
    newsletter: boolean;
  };
  privacy: {
    profile_public: boolean;
    library_public: boolean;
    activity_public: boolean;
  };
  display: {
    games_per_page: number;
    default_sort: string;
    show_metacritic: boolean;
    show_screenshots: boolean;
  };
  created_at?: string;
  updated_at?: string;
}

// Collection stats record
export interface UserCollectionStatsRecord {
  id?: string;
  user_id: string;
  total_games: number;
  completed_games: number;
  wishlist_count: number;
  total_hours: number;
  total_value?: number;
  average_rating: number;
  most_played_genre?: string;
  completion_rate: number;
  last_calculated?: string;
  created_at?: string;
  updated_at?: string;
}

// Database operation update types
export type GameUpdateData = Partial<Omit<GameRecord, 'id' | 'created_at' | 'updated_at'>>;

export type UserGameUpdateData = Partial<Omit<UserGameRecord, 'id' | 'user_id' | 'game_id' | 'created_at' | 'updated_at'>>;

export type PriceTrackingUpdateData = Partial<Omit<PriceTrackingRecord, 'id' | 'created_at'>>;

export type PriceAlertUpdateData = Partial<Omit<PriceAlertRecord, 'id' | 'user_id' | 'game_id' | 'created_at' | 'updated_at'>>;

export type UserProfileUpdateData = Partial<Omit<UserProfileRecord, 'id' | 'created_at' | 'updated_at'>>;

export type UserPreferencesUpdateData = Partial<Omit<UserPreferencesRecord, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;

// Additional price tracking interfaces
export interface PriceEntry {
  id: string;
  gameId: string;
  store: string;
  price: number;
  currency: string;
  url?: string;
  isOnSale: boolean;
  originalPrice?: number;
  discountPercentage?: number;
  lastUpdated: string;
}

export interface PriceAlert {
  id: string;
  userId: string;
  gameId: string;
  targetPrice: number;
  currency: string;
  isActive: boolean;
  notificationMethods?: string[];
  createdAt?: string;
  updatedAt?: string;
}

// User recommendation record (simplified AI recommendations)
export interface UserRecommendationRecord {
  id: string;
  user_id: string;
  game_id?: string;
  igdb_id?: string;
  game_name: string;
  reason: string;
  confidence: number;
  categories: string[];
  similar_to: string[];
  genres: string[];
  platforms: string[];
  description?: string;
  tags: string[];
  estimated_play_time?: number;
  cover_image?: string;
  recommendation_type: 'user_based' | 'similar_game';
  source_game_id?: string;
  created_at?: string;
  updated_at?: string;
}

// User recommendation with game details (from join)
export interface UserRecommendationWithDetails extends UserRecommendationRecord {
  actual_game_title?: string;
  actual_cover_image?: string;
  actual_platforms?: string[];
  actual_genres?: string[];
  developer?: string;
  publisher?: string;
  release_date?: string;
  metacritic_score?: number;
  source_game_title?: string;
}

// Update types for user recommendation operations
export type UserRecommendationUpdateData = Partial<Omit<UserRecommendationRecord, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;