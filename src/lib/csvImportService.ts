// import { userGamesAPI } from './api/collections';

interface CSVGameData {
  name: string;
  platforms?: string[];
  status?: 'playing' | 'completed' | 'backlog' | 'wishlist' | 'library';
  playtime_hours?: number;
  rating?: number;
  notes?: string;
  date_added?: string;
  last_played?: string;
  completion_percentage?: number;
  purchase_price?: number;
  developer?: string;
  publisher?: string;
  release_date?: string;
  genre?: string;
  tags?: string;
}

interface ImportResult {
  success: boolean;
  imported: number;
  skipped: number;
  errors: string[];
  data?: CSVGameData[];
}

interface ExportOptions {
  includeStats?: boolean;
  includeNotes?: boolean;
  includeMetadata?: boolean;
  format?: 'csv' | 'json';
}

class CSVImportService {
  /**
   * Parse CSV content and return structured data
   */
  parseCSV(csvContent: string): CSVGameData[] {
    const lines = csvContent.trim().split('\n');
    if (lines.length < 2) {
      throw new Error('CSV must have at least a header row and one data row');
    }

    const headers = this.parseCSVLine(lines[0]).map(h => h.toLowerCase().trim());
    const data: CSVGameData[] = [];

    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i]);
      if (values.length === 0 || values.every(v => !v.trim())) {
        continue; // Skip empty lines
      }

      const gameData: CSVGameData = {
        name: ''
      };

      headers.forEach((header, index) => {
        const value = values[index]?.trim();
        if (!value) return;

        switch (header) {
          case 'name':
          case 'title':
          case 'game':
          case 'game_name':
            gameData.name = value;
            break;
          case 'platform':
          case 'system':
            gameData.platforms = [value];
            break;
          case 'status':
          case 'completion_status':
            if (['playing', 'completed', 'backlog', 'wishlist', 'library'].includes(value.toLowerCase())) {
              gameData.status = value.toLowerCase() as CSVGameData['status'];
            }
            break;
          case 'playtime':
          case 'playtime_hours':
          case 'hours_played': {
            const playtime = parseFloat(value);
            if (!isNaN(playtime)) {
              gameData.playtime_hours = playtime;
            }
            break;
          }
          case 'rating':
          case 'score':
          case 'user_rating': {
            const rating = parseFloat(value);
            if (!isNaN(rating) && rating >= 0 && rating <= 10) {
              gameData.rating = rating;
            }
            break;
          }
          case 'notes':
          case 'comment':
          case 'description':
            gameData.notes = value;
            break;
          case 'date_added':
          case 'added_date':
          case 'purchase_date':
            if (this.isValidDate(value)) {
              gameData.date_added = new Date(value).toISOString();
            }
            break;
          case 'last_played':
          case 'last_play_date':
            if (this.isValidDate(value)) {
              gameData.last_played = new Date(value).toISOString();
            }
            break;
          case 'completion_percentage':
          case 'progress': {
            const completion = parseFloat(value);
            if (!isNaN(completion) && completion >= 0 && completion <= 100) {
              gameData.completion_percentage = completion;
            }
            break;
          }
          case 'purchase_price':
          case 'price':
          case 'cost': {
            const price = parseFloat(value.replace(/[$€£¥]/g, ''));
            if (!isNaN(price)) {
              gameData.purchase_price = price;
            }
            break;
          }
          case 'developer':
            gameData.developer = value;
            break;
          case 'publisher':
            gameData.publisher = value;
            break;
          case 'release_date':
          case 'release_year':
            if (this.isValidDate(value)) {
              gameData.release_date = new Date(value).toISOString().split('T')[0];
            } else if (/^\d{4}$/.test(value)) {
              gameData.release_date = `${value}-01-01`;
            }
            break;
          case 'genre':
          case 'genres':
            gameData.genre = value;
            break;
          case 'tags':
          case 'categories':
            gameData.tags = value;
            break;
        }
      });

      if (gameData.name) {
        data.push(gameData);
      }
    }

    return data;
  }

  /**
   * Parse a single CSV line, handling quoted values
   */
  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
      const char = line[i];
      const nextChar = line[i + 1];

      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          // Escaped quote
          current += '"';
          i += 2;
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === ',' && !inQuotes) {
        // Field separator
        result.push(current);
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }

    result.push(current);
    return result;
  }

  /**
   * Validate date string
   */
  private isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * Import CSV data to user's collection
   */
  async importCSVData(
    userId: string,
    csvData: CSVGameData[],
    options: {
      skipDuplicates?: boolean;
      updateExisting?: boolean;
    } = {}
  ): Promise<ImportResult> {
    const errors: string[] = [];
    let imported = 0;
    let skipped = 0;

    try {
      // Get existing user games to check for duplicates
      const { data: existingUserGames, error: userGamesError } = await db.userGames.getUserCollection(userId);
      if (userGamesError) {
        throw new Error(`Failed to fetch existing games: ${userGamesError.message}`);
      }

      const existingGameNames = new Set(
        existingUserGames?.map(g => g.game?.name?.toLowerCase()) || []
      );

      for (const csvGame of csvData) {
        try {
          // Check for duplicates
          if (options.skipDuplicates && existingGameNames.has(csvGame.name.toLowerCase())) {
            skipped++;
            continue;
          }

          // Find or create game in games table
          let gameId: string;
          const { data: existingGames, error: gamesError } = await db.games.getAll();
          if (gamesError) {
            throw new Error(`Failed to search games: ${gamesError.message}`);
          }

          const existingGame = existingGames?.find(g => 
            g.name?.toLowerCase() === csvGame.name.toLowerCase()
          );

          if (existingGame) {
            gameId = existingGame.id;
          } else {
            // Create new game entry
            const newGameData = {
              name: csvGame.name,
              description: csvGame.notes || '',
              developers: csvGame.developer ? [csvGame.developer] : [],
              publishers: csvGame.publisher ? [csvGame.publisher] : [],
              genres: csvGame.genre ? csvGame.genre.split(',').map(g => g.trim()) : [],
              platforms: csvGame.platforms || ['PC'],
              release_date: csvGame.release_date || null,
              tags: csvGame.tags ? csvGame.tags.split(',').map(t => t.trim()) : []
            };

            const { data: createdGame, error: createError } = await db.games.create(newGameData);
            if (createError) {
              errors.push(`Failed to create game "${csvGame.name}": ${createError.message}`);
              continue;
            }
            gameId = createdGame.id;
          }

          // Add to user's collection
          const userGameData = {
            user_id: userId,
            game_id: gameId,
            status: csvGame.status || 'library',
            platform: csvGame.platforms?.[0] || 'PC',
            playtime_hours: csvGame.playtime_hours || 0,
            user_rating: csvGame.rating || null,
            notes: csvGame.notes || null,
            date_added: csvGame.date_added || new Date().toISOString(),
            last_played: csvGame.last_played || null,
            completion_percentage: csvGame.completion_percentage || null,
            purchase_price: csvGame.purchase_price || null,
            import_source: 'csv'
          };

          const { error: addError } = await db.userGames.addToCollection(userGameData);
          if (addError) {
            errors.push(`Failed to add "${csvGame.name}" to collection: ${addError.message}`);
            continue;
          }

          imported++;

        } catch (gameError) {
          const errorMessage = gameError instanceof Error ? gameError.message : 'Unknown error';
          errors.push(`Error processing "${csvGame.name}": ${errorMessage}`);
        }
      }

      return {
        success: imported > 0,
        imported,
        skipped,
        errors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Import failed';
      return {
        success: false,
        imported,
        skipped,
        errors: [errorMessage, ...errors]
      };
    }
  }

  /**
   * Export user's collection to CSV
   */
  async exportUserCollection(
    userId: string,
    options: ExportOptions = {}
  ): Promise<string> {
    try {
      const { data: userGames, error } = await db.userGames.getUserCollection(userId);
      if (error) {
        throw new Error(`Failed to fetch user collection: ${error.message}`);
      }

      if (!userGames || userGames.length === 0) {
        throw new Error('No games found in your collection');
      }

      // Define CSV headers
      const baseHeaders = [
        'name',
        'platform',
        'status',
        'playtime_hours',
        'user_rating',
        'date_added'
      ];

      const extendedHeaders = options.includeStats ? [
        ...baseHeaders,
        'last_played',
        'completion_percentage',
        'purchase_price'
      ] : baseHeaders;

      const metadataHeaders = options.includeMetadata ? [
        ...extendedHeaders,
        'developer',
        'publisher',
        'release_date',
        'genres',
        'metacritic_score'
      ] : extendedHeaders;

      const finalHeaders = options.includeNotes ? [
        ...metadataHeaders,
        'notes'
      ] : metadataHeaders;

      // Convert data to CSV format
      const csvRows = [finalHeaders.join(',')];

      for (const userGame of userGames) {
        const game = userGame.game;
        const row: string[] = [];

        finalHeaders.forEach(header => {
          let value = '';

          switch (header) {
            case 'name':
              value = game?.name || '';
              break;
            case 'platform':
              value = userGame.platform || '';
              break;
            case 'status':
              value = userGame.status || '';
              break;
            case 'playtime_hours':
              value = userGame.playtime_hours?.toString() || '0';
              break;
            case 'user_rating':
              value = userGame.user_rating?.toString() || '';
              break;
            case 'date_added':
              value = userGame.date_added ? new Date(userGame.date_added).toLocaleDateString() : '';
              break;
            case 'last_played':
              value = userGame.last_played ? new Date(userGame.last_played).toLocaleDateString() : '';
              break;
            case 'completion_percentage':
              value = userGame.completion_percentage?.toString() || '';
              break;
            case 'purchase_price':
              value = userGame.purchase_price?.toString() || '';
              break;
            case 'developer':
              value = Array.isArray(game?.developers) ? game.developers.join('; ') : '';
              break;
            case 'publisher':
              value = Array.isArray(game?.publishers) ? game.publishers.join('; ') : '';
              break;
            case 'release_date':
              value = game?.release_date || '';
              break;
            case 'genres':
              value = Array.isArray(game?.genres) ? game.genres.join('; ') : '';
              break;
            case 'metacritic_score':
              value = game?.metacritic_score?.toString() || '';
              break;
            case 'notes':
              value = userGame.notes || '';
              break;
          }

          // Escape CSV value
          if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            value = `"${value.replace(/"/g, '""')}"`;
          }

          row.push(value);
        });

        csvRows.push(row.join(','));
      }

      return csvRows.join('\n');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Export failed';
      throw new Error(errorMessage);
    }
  }

  /**
   * Generate CSV template
   */
  generateTemplate(): string {
    const headers = [
      'name',
      'platform',
      'status',
      'playtime_hours',
      'user_rating',
      'notes',
      'date_added',
      'last_played',
      'completion_percentage',
      'purchase_price',
      'developer',
      'publisher',
      'release_date',
      'genre',
      'tags'
    ];

    const exampleRow = [
      'The Witcher 3: Wild Hunt',
      'PC',
      'completed',
      '120',
      '9.5',
      'Amazing open-world RPG',
      '2023-01-15',
      '2023-06-20',
      '100',
      '29.99',
      'CD Projekt RED',
      'CD Projekt',
      '2015-05-19',
      'RPG, Action',
      'Fantasy, Open World'
    ];

    return [headers.join(','), exampleRow.join(',')].join('\n');
  }

  /**
   * Validate CSV file before import
   */
  validateCSV(csvContent: string): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    gameCount: number;
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const data = this.parseCSV(csvContent);
      
      if (data.length === 0) {
        errors.push('No valid game data found in CSV');
        return { isValid: false, errors, warnings, gameCount: 0 };
      }

      // Check for required fields
      const gamesWithoutNames = data.filter(game => !game.name);
      if (gamesWithoutNames.length > 0) {
        errors.push(`${gamesWithoutNames.length} games missing names`);
      }

      // Check for potential issues
      const duplicateNames = new Set();
      const duplicates = data.filter(game => {
        if (duplicateNames.has(game.name.toLowerCase())) {
          return true;
        }
        duplicateNames.add(game.name.toLowerCase());
        return false;
      });

      if (duplicates.length > 0) {
        warnings.push(`${duplicates.length} duplicate game names found`);
      }

      const invalidRatings = data.filter(game => 
        game.rating !== undefined && (game.rating < 0 || game.rating > 10)
      );
      if (invalidRatings.length > 0) {
        warnings.push(`${invalidRatings.length} games have invalid ratings (should be 0-10)`);
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        gameCount: data.length
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to parse CSV';
      return {
        isValid: false,
        errors: [errorMessage],
        warnings,
        gameCount: 0
      };
    }
  }
}

export const csvImportService = new CSVImportService();
export type { CSVGameData, ImportResult, ExportOptions };