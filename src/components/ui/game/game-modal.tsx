import {
  X,
  Gamepad2,
  Star,
  Calendar,
  User,
  Building,
  Tag,
  Plus,
  Heart,
  Loader2,
  CheckCircle,
  Edit3,
  Save,
  FileText,
  Play,
  ExternalLink
} from 'lucide-react';
import { Game } from '../../types';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { StarRating } from './star-rating';
import { Textarea } from '@/components/ui/base/textarea';
import { ImageGallery } from '../utils/image-gallery';
import { ArtworkGallery } from '../analytics/ArtworkGallery';
import { useAddToLibrary, useAddToWishlist, useUpdateGameStatus } from '@/hooks/useGameActions';
import { useUserGameStatus } from '@/hooks/useUserLibrary';
import { useCustomArtwork } from '@/hooks/useCustomArtwork';
import { useGameTags } from '@/hooks/useGameTags';
import { TagInput } from '@/components/ui/tags/TagInput';
import { TagManager } from '@/components/ui/tags/TagManager';
import { useState, useEffect, useCallback, useMemo } from 'react';

interface GameModalProps {
  game: Game | null;
  isOpen: boolean;
  onClose: () => void;
}

export function GameModal({ game, isOpen, onClose }: GameModalProps) {
  const addToLibrary = useAddToLibrary();
  const addToWishlist = useAddToWishlist();
  const updateGameStatus = useUpdateGameStatus();
  const { data: userGame } = useUserGameStatus(game?.id || '');
  const { getBestCoverImage, hasCustomArtwork } = useCustomArtwork(userGame?.id || '');
  const [isEditingRating, setIsEditingRating] = useState(false);
  const [tempRating, setTempRating] = useState(0);
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [tempNotes, setTempNotes] = useState('');
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const [galleryStartIndex, setGalleryStartIndex] = useState(0);
  const [showTagManager, setShowTagManager] = useState(false);
  
  // Tag functionality
  const { gameTags: gameTagsData, removeTag, bulkAddTags } = useGameTags(userGame?.id || '');
  const gameTags = useMemo(() => {
    return userGame ? gameTagsData : [];
  }, [userGame, gameTagsData]);

  // Handle escape key and backdrop click
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen && !isGalleryOpen) {
      onClose();
    }
  }, [isOpen, isGalleryOpen, onClose]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleScreenshotClick = (index: number) => {
    setGalleryStartIndex(index);
    setIsGalleryOpen(true);
  };

  const getYouTubeVideoId = (url: string) => {
    const regex = /(?:youtube\.com\/(?:[^/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  const handleTagsChange = useCallback(async (selectedTags: string[]) => {
    if (!userGame) return;
    
    try {
      // Find tags to add and remove
      const currentTagIds = gameTags.map(tag => tag.id);
      const tagsToAdd = selectedTags.filter(tagId => !currentTagIds.includes(tagId));
      const tagsToRemove = currentTagIds.filter(tagId => !selectedTags.includes(tagId));
      
      // Add new tags
      if (tagsToAdd.length > 0) {
        bulkAddTags(tagsToAdd);
      }
      
      // Remove tags
      for (const tagId of tagsToRemove) {
        removeTag(tagId);
      }
    } catch (error) {
      console.error('Error updating game tags:', error);
    }
  }, [userGame, gameTags, bulkAddTags, removeTag]);

  const getYouTubeThumbnail = (videoId: string, quality: 'maxres' | 'hq' | 'mq' | 'sd' = 'maxres') => {
    const qualityMap = {
      maxres: 'maxresdefault.jpg',
      hq: 'hqdefault.jpg',
      mq: 'mqdefault.jpg',
      sd: 'sddefault.jpg'
    };
    return `https://img.youtube.com/vi/${videoId}/${qualityMap[quality]}`;
  };

  if (!isOpen || !game) return null;

  const handleAddToLibrary = () => {
    addToLibrary.mutate(game);
  };

  const handleAddToWishlist = () => {
    addToWishlist.mutate(game);
  };

  const handleRatingEdit = () => {
    setTempRating(userGame?.personal_rating || 0);
    setIsEditingRating(true);
  };

  const handleRatingSave = () => {
    if (userGame) {
      updateGameStatus.mutate({
        userGameId: userGame.id,
        status: userGame.status,
        rating: tempRating
      });
    }
    setIsEditingRating(false);
  };

  const handleRatingCancel = () => {
    setIsEditingRating(false);
    setTempRating(0);
  };

  const handleNotesEdit = () => {
    setTempNotes(userGame?.personal_notes || '');
    setIsEditingNotes(true);
  };

  const handleNotesSave = () => {
    if (userGame) {
      updateGameStatus.mutate({
        userGameId: userGame.id,
        status: userGame.status,
        rating: userGame.personal_rating,
        notes: tempNotes
      });
    }
    setIsEditingNotes(false);
  };

  const handleNotesCancel = () => {
    setIsEditingNotes(false);
    setTempNotes('');
  };


  return (
    <>
      <div 
        className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4 animate-fade-in"
        onClick={handleBackdropClick}
      >
        <Card className="max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border-primary/20 animate-scale-in">
        {/* Header */}
        <CardHeader className="sticky top-0 bg-background border-b border-border px-8 py-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Gamepad2 className="h-6 w-6 text-primary" />
              </div>
              <div>
                <CardTitle className="text-3xl font-bold bg-gradient-to-r from-primary to-orange-600 bg-clip-text text-transparent">
                  {game.title}
                </CardTitle>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
                    IGDB
                  </Badge>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {!userGame ? (
                <>
                  <Button
                    variant="default"
                    onClick={handleAddToLibrary}
                    disabled={addToLibrary.isPending}
                    className="bg-primary hover:bg-primary/90"
                  >
                    {addToLibrary.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : addToLibrary.isSuccess ? (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    {addToLibrary.isSuccess ? 'Added to Library' : 'Add to Library'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleAddToWishlist}
                    disabled={addToWishlist.isPending}
                    className="border-secondary text-secondary hover:bg-secondary hover:text-secondary-foreground"
                  >
                    {addToWishlist.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : addToWishlist.isSuccess ? (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    ) : (
                      <Heart className="h-4 w-4 mr-2" />
                    )}
                    {addToWishlist.isSuccess ? 'Added to Wishlist' : 'Add to Wishlist'}
                  </Button>
                </>
              ) : (
                <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200 px-3 py-1">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {userGame.status === 'wishlist' ? 'In Wishlist' : 'In Library'}
                </Badge>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="h-10 w-10 rounded-full hover:bg-muted"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Content */}
        <CardContent className="p-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Cover & Basic Info */}
            <div className="lg:col-span-1 space-y-6">
              {(getBestCoverImage() || game.cover_image) && (
                <Card className="overflow-hidden border-primary/20">
                  <div className="aspect-[2/3] bg-gradient-to-br from-muted to-muted/80 relative overflow-hidden">
                    <img
                      src={getBestCoverImage() || game.cover_image}
                      alt={game.title}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    />
                    {/* Custom artwork badge */}
                    {hasCustomArtwork && (
                      <div className="absolute top-2 left-2">
                        <Badge variant="secondary" className="bg-primary text-primary-foreground border-0 text-xs">
                          Custom Artwork
                        </Badge>
                      </div>
                    )}
                  </div>
                </Card>
              )}


              {/* Rating */}
              {game.metacritic_score && (
                <Card className="p-4 border-primary/20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium text-muted-foreground">Metacritic Score</span>
                    </div>
                    <Badge className={`${
                      game.metacritic_score >= 80 ? 'bg-green-500 hover:bg-green-600' :
                      game.metacritic_score >= 60 ? 'bg-yellow-500 hover:bg-yellow-600' :
                      'bg-red-500 hover:bg-red-600'
                    }`}>
                      {game.metacritic_score}
                    </Badge>
                  </div>
                </Card>
              )}

              {/* User Rating */}
              {userGame && userGame.status !== 'wishlist' && (
                <Card className="p-4 border-primary/20">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium text-muted-foreground">Your Rating</span>
                    </div>
                    {!isEditingRating && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleRatingEdit}
                        className="h-8 w-8 p-0"
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                  {isEditingRating ? (
                    <div className="space-y-3">
                      <StarRating
                        value={tempRating}
                        onChange={setTempRating}
                        size="md"
                      />
                      <div className="flex gap-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={handleRatingSave}
                          disabled={updateGameStatus.isPending}
                        >
                          {updateGameStatus.isPending ? (
                            <Loader2 className="h-3 w-3 animate-spin mr-1" />
                          ) : (
                            <Save className="h-3 w-3 mr-1" />
                          )}
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleRatingCancel}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <StarRating
                        value={userGame.personal_rating || 0}
                        onChange={() => {}}
                        size="md"
                        readOnly
                      />
                      {!userGame.personal_rating && (
                        <span className="text-sm text-muted-foreground">Not rated yet</span>
                      )}
                    </div>
                  )}
                </Card>
              )}

              {/* User Notes */}
              {userGame && userGame.status !== 'wishlist' && (
                <Card className="p-4 border-primary/20">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium text-muted-foreground">Your Notes</span>
                    </div>
                    {!isEditingNotes && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleNotesEdit}
                        className="h-8 w-8 p-0"
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                  {isEditingNotes ? (
                    <div className="space-y-3">
                      <Textarea
                        value={tempNotes}
                        onChange={(e) => setTempNotes(e.target.value)}
                        placeholder="Add your thoughts about this game..."
                        className="min-h-[100px] resize-none"
                      />
                      <div className="flex gap-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={handleNotesSave}
                          disabled={updateGameStatus.isPending}
                        >
                          {updateGameStatus.isPending ? (
                            <Loader2 className="h-3 w-3 animate-spin mr-1" />
                          ) : (
                            <Save className="h-3 w-3 mr-1" />
                          )}
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleNotesCancel}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">
                      {userGame.personal_notes ? (
                        <p className="whitespace-pre-wrap">{userGame.personal_notes}</p>
                      ) : (
                        <p className="italic">No notes yet</p>
                      )}
                    </div>
                  )}
                </Card>
              )}
            </div>

            {/* Right Column - Details */}
            <div className="lg:col-span-2 space-y-6">
              {/* Genres */}
              {game.genres.length > 0 && (
                <Card className="p-6 border-primary/20">
                  <div className="flex items-center gap-2 mb-4">
                    <Tag className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-semibold text-foreground">Genres</h3>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {game.genres.map((genre) => (
                      <Badge
                        key={genre}
                        variant="secondary"
                        className="bg-primary/10 text-primary border-primary/20"
                      >
                        {genre}
                      </Badge>
                    ))}
                  </div>
                </Card>
              )}

              {/* User Tags */}
              {userGame && (
                <Card className="p-6 border-primary/20">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Tag className="h-5 w-5 text-primary" />
                      <h3 className="text-lg font-semibold text-foreground">My Tags</h3>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowTagManager(!showTagManager)}
                      >
                        <Edit3 className="h-4 w-4 mr-2" />
                        {showTagManager ? 'Close' : 'Manage Tags'}
                      </Button>
                    </div>
                  </div>

                  {gameTags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {gameTags.map((tag) => (
                        <Badge
                          key={tag.id}
                          variant="secondary"
                          className="text-sm"
                          style={{ 
                            backgroundColor: `${tag.color}20`, 
                            borderColor: tag.color,
                            color: tag.color 
                          }}
                        >
                          {tag.name}
                        </Badge>
                      ))}
                    </div>
                  )}

                  <TagInput
                    selectedTags={gameTags.map(tag => tag.id)}
                    onTagsChange={handleTagsChange}
                    placeholder={gameTags.length === 0 ? "Add tags to organize your games..." : "Add or remove tags..."}
                    maxTags={10}
                  />

                  {showTagManager && (
                    <div className="mt-4 pt-4 border-t border-border">
                      <TagManager />
                    </div>
                  )}
                </Card>
              )}

              {/* Game Details */}
              <Card className="p-6 border-primary/20">
                <h3 className="text-lg font-semibold text-foreground mb-4">Game Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {game.developer && (
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <span className="text-sm font-medium text-muted-foreground">Developer</span>
                        <p className="text-foreground font-medium">{game.developer}</p>
                      </div>
                    </div>
                  )}

                  {game.publisher && (
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Building className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <span className="text-sm font-medium text-muted-foreground">Publisher</span>
                        <p className="text-foreground font-medium">{game.publisher}</p>
                      </div>
                    </div>
                  )}

                  {game.release_date && (
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Calendar className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <span className="text-sm font-medium text-muted-foreground">Release Date</span>
                        <p className="text-foreground font-medium">{new Date(game.release_date).getFullYear()}</p>
                      </div>
                    </div>
                  )}
                </div>
              </Card>

              {/* Description */}
              {game.description && (
                <Card className="p-6 border-primary/20">
                  <h3 className="text-lg font-semibold text-foreground mb-4">Description</h3>
                  <p className="text-muted-foreground leading-relaxed">{game.description}</p>
                </Card>
              )}

              {/* Screenshots */}
              {game.screenshots && game.screenshots.length > 0 && (
                <Card className="p-6 border-primary/20">
                  <h3 className="text-lg font-semibold text-foreground mb-4">Screenshots</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {game.screenshots.slice(0, 6).map((screenshot, index) => (
                      <Card 
                        key={index} 
                        className="overflow-hidden border-primary/10 hover:border-primary/30 transition-colors cursor-pointer group"
                        onClick={() => handleScreenshotClick(index)}
                      >
                        <div className="aspect-video bg-muted relative overflow-hidden">
                          <img
                            src={screenshot}
                            alt={`${game.title} screenshot ${index + 1}`}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black/50 rounded-full p-2">
                              <ExternalLink className="h-5 w-5 text-white" />
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                  {game.screenshots.length > 6 && (
                    <p className="text-sm text-muted-foreground mt-2">
                      +{game.screenshots.length - 6} more screenshots (click to view all)
                    </p>
                  )}
                </Card>
              )}

              {/* YouTube Gameplay Videos */}
              {game.youtube_links && game.youtube_links.length > 0 && (
                <Card className="p-6 border-primary/20">
                  <h3 className="text-lg font-semibold text-foreground mb-4">Gameplay Videos</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {game.youtube_links.slice(0, 4).map((link, index) => {
                      const videoId = getYouTubeVideoId(link);
                      if (!videoId) return null;
                      
                      return (
                        <Card 
                          key={index} 
                          className="overflow-hidden border-primary/10 hover:border-primary/30 transition-colors cursor-pointer group"
                          onClick={() => window.open(link, '_blank', 'noopener,noreferrer')}
                        >
                          <div className="aspect-video bg-muted relative overflow-hidden">
                            <img
                              src={getYouTubeThumbnail(videoId, 'hq')}
                              alt={`${game.title} gameplay video ${index + 1}`}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                              loading="lazy"
                              onError={(e) => {
                                // Fallback chain: hq -> mq -> sd
                                const target = e.target as HTMLImageElement;
                                const currentSrc = target.src;
                                
                                if (currentSrc.includes('hqdefault.jpg')) {
                                  target.src = getYouTubeThumbnail(videoId, 'mq');
                                } else if (currentSrc.includes('mqdefault.jpg')) {
                                  target.src = getYouTubeThumbnail(videoId, 'sd');
                                } else {
                                  // Final fallback - hide image and show placeholder
                                  target.style.display = 'none';
                                  const parent = target.parentElement;
                                  if (parent) {
                                    const placeholder = document.createElement('div');
                                    placeholder.className = 'w-full h-full flex items-center justify-center bg-gradient-to-br from-muted to-muted/80';
                                    placeholder.innerHTML = `
                                      <div class="text-center text-muted-foreground">
                                        <div class="mb-2">
                                          <svg class="h-12 w-12 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                          </svg>
                                        </div>
                                        <div class="text-sm font-medium">Video Thumbnail</div>
                                      </div>
                                    `;
                                    parent.appendChild(placeholder);
                                  }
                                }
                              }}
                            />
                            <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center">
                              <div className="bg-red-600 rounded-full p-3 group-hover:scale-110 transition-transform duration-300">
                                <Play className="h-6 w-6 text-white fill-white" />
                              </div>
                            </div>
                            <div className="absolute top-2 right-2">
                              <Badge variant="secondary" className="bg-red-600 text-white">
                                YouTube
                              </Badge>
                            </div>
                          </div>
                        </Card>
                      );
                    })}
                  </div>
                  {game.youtube_links.length > 4 && (
                    <p className="text-sm text-muted-foreground mt-2">
                      +{game.youtube_links.length - 4} more videos
                    </p>
                  )}
                </Card>
              )}

              {/* Custom Artwork Gallery - only show if user has the game in collection */}
              {userGame && (
                <ArtworkGallery
                  userGameId={userGame.id}
                  showUpload={true}
                  onArtworkUpdate={() => {
                    // Refresh user game data when artwork is updated
                    // This could trigger a refetch of the user game data
                  }}
                />
              )}
            </div>
          </div>
        </CardContent>
        </Card>
      </div>

      {/* Image Gallery */}
      <ImageGallery
        images={game.screenshots || []}
        isOpen={isGalleryOpen}
        onClose={() => setIsGalleryOpen(false)}
        initialIndex={galleryStartIndex}
        alt={game.title}
      />
    </>
  );
}
