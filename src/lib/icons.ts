import { 
  ExternalLink,
  HelpCircle,
  MessageSquare,
  Bookmark,
  Library,
  Home,
  Grid3X3,
  Layers,
  FolderPlus,
  Folder,
  Users,
  Copy,
  RefreshCw,
  Trophy,
  Target,
  Sparkles,
  CheckCircle2,
  SlidersHorizontal,
  List,
  LogOut,
  Minus,
  Download,
  Upload,
  Share2,
  Heart,
  Eye,
  EyeOff,
  MoreHorizontal,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Calendar,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Award,
  Zap,
  Database,
  PieChart,
  Activity,
  LineChart,
  Timer,
  ArrowUp,
  ArrowDown,
  Save,
  Move,
  Archive,
  Info,
  AlertCircle,
  Check,
  Loader2,
  RotateCcw,
  FileText,
  FileJson,
  FileSpreadsheet,
  Cloud,
  VolumeX,
  Volume2,
  Video,
  ImageIcon,
  X,
  MoreVertical,
  Trash2,
  Edit3,
  Plus,
  User,
  Settings,
  Filter,
  Search,
  Gamepad2,
  Clock,
  Star,
  Pause,
  CheckCircle,
  Play,
  Tag
} from 'lucide-react';




// Centralized icon exports to reduce bundle size and improve consistency
export {
  // Status Icons
  Play,
  CheckCircle,
  CheckCircle2,
  Pause,
  Clock,
  Star,
  Gamepad2,

  // UI Icons
  Search,
  Filter,
  SlidersHorizontal,
  Grid3X3,
  List,
  Settings,
  User,
  LogOut,
  Plus,
  Minus,
  Edit3,
  Trash2,
  Download,
  Upload,
  Share2,
  Heart,
  Eye,
  EyeOff,
  MoreVertical,
  MoreHorizontal,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  X,
  Layers,
  Tag,
  
  // Collection Icons
  FolderPlus,
  Folder,
  Trophy,
  Target,
  Sparkles,
  Users,
  Copy,
  RefreshCw,

  // Media Icons
  ImageIcon,
  Video,
  Volume2,
  VolumeX,

  // Data Icons
  Calendar,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Award,
  Zap,
  Database,
  PieChart,
  Activity,
  LineChart,
  Timer,
  ArrowUp,
  ArrowDown,

  // Navigation Icons
  Home,
  Library,
  Bookmark,
  MessageSquare,
  HelpCircle,
  ExternalLink,

  // Action Icons
  Save,
  Move,
  Archive,
  Info,
  AlertCircle,
  Check,
  Loader2,
  RotateCcw,
  
  // File Icons
  FileText,
  FileJson,
  FileSpreadsheet,
  
  // Cloud Icons
  Cloud,
};

// Icon collections for easy access
export const StatusIcons = {
  playing: Play,
  completed: CheckCircle,
  backlog: Pause,
  wishlist: Star,
  not_started: Clock,
  default: Gamepad2,
} as const;

export const UIIcons = {
  search: Search,
  filter: Filter,
  settings: Settings,
  user: User,
  plus: Plus,
  edit: Edit3,
  delete: Trash2,
  more: MoreVertical,
  close: X,
} as const;

export const MediaIcons = {
  image: ImageIcon,
  video: Video,
  sound: Volume2,
  muted: VolumeX,
} as const;

export const NavigationIcons = {
  home: Home,
  library: Library,
  wishlist: Bookmark,
  chat: MessageSquare,
  help: HelpCircle,
  external: ExternalLink,
} as const;