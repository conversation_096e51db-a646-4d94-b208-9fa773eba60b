import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { Progress } from '@/components/ui/base/progress';
import { Skeleton } from '@/components/ui/base/skeleton';
import { 
  Brain, 
  Target, 
  TrendingUp, 
  Clock, 
  Star,
  ChevronRight,
  Trophy,
  Lightbulb
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useCollectionStats, useBacklogOptimization, useGamingPersonality, useGameInsights } from '@/hooks/useCollectionInsights';
import { cn } from '@/lib/utils';

interface CollectionInsightsCardProps {
  className?: string;
  showDetails?: boolean;
  onViewDetails?: () => void;
}

export const CollectionInsightsCard: React.FC<CollectionInsightsCardProps> = ({ 
  className = '',
  showDetails = false,
  onViewDetails 
}) => {
  const { stats, derivedStats, isLoading: isLoadingStats } = useCollectionStats();
  const { quickStats, isLoading: isLoadingBacklog } = useBacklogOptimization();
  const { personality, personalityInsights, isLoading: isLoadingPersonality } = useGamingPersonality();
  const { insightStats, isLoading: isLoadingInsights } = useGameInsights();

  const isLoading = isLoadingStats || isLoadingBacklog || isLoadingPersonality || isLoadingInsights;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Collection Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-20 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats || !personality) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Collection Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-muted-foreground">
              Add games to your collection to see insights
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const insights = [
    {
      icon: <Trophy className="h-4 w-4" />,
      title: 'Completion Rate',
      value: `${Math.round(stats.completionRate)}%`,
      description: 'Games completed vs total',
      score: stats.completionRate,
      color: stats.completionRate > 50 ? 'text-green-600' : 'text-yellow-600'
    },
    {
      icon: <Target className="h-4 w-4" />,
      title: 'Backlog Efficiency',
      value: `${quickStats.highLikelihood}`,
      description: 'High-likelihood completions',
      score: quickStats.highLikelihood > 0 ? 85 : 45,
      color: quickStats.highLikelihood > 0 ? 'text-green-600' : 'text-gray-600'
    },
    {
      icon: <TrendingUp className="h-4 w-4" />,
      title: 'Genre Diversity',
      value: `${stats.topGenres.length}`,
      description: 'Different genres explored',
      score: derivedStats.diversityScore,
      color: derivedStats.diversityScore > 60 ? 'text-blue-600' : 'text-yellow-600'
    },
    {
      icon: <Star className="h-4 w-4" />,
      title: 'Gaming Personality',
      value: personality.type.charAt(0).toUpperCase() + personality.type.slice(1),
      description: `${personality.score}% confidence`,
      score: personality.score,
      color: personalityInsights.personalityColor
    }
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Collection Insights
          </div>
          {insightStats.totalInsights > 0 && (
            <Badge variant="secondary">
              {insightStats.totalInsights} insights
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Gaming Personality Highlight */}
          <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg">
            <div className="text-3xl">
              {personalityInsights.personalityIcon}
            </div>
            <div className="flex-1">
              <p className="font-semibold text-lg">
                {personality.type.charAt(0).toUpperCase() + personality.type.slice(1)} Gamer
              </p>
              <p className="text-sm text-muted-foreground">
                {personality.description}
              </p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-blue-600">
                {personality.score}%
              </p>
              <p className="text-xs text-muted-foreground">
                Confidence
              </p>
            </div>
          </div>

          {/* Key Insights Grid */}
          <div className="grid grid-cols-2 gap-4">
            {insights.map((insight, index) => (
              <div
                key={index}
                className="p-3 border rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-2 mb-2">
                  <div className={cn("text-muted-foreground", insight.color)}>
                    {insight.icon}
                  </div>
                  <p className="text-sm font-medium">{insight.title}</p>
                </div>
                <p className={cn("text-xl font-bold", insight.color)}>
                  {insight.value}
                </p>
                <p className="text-xs text-muted-foreground">
                  {insight.description}
                </p>
              </div>
            ))}
          </div>

          {/* Top Recommendation */}
          {personality.recommendations.length > 0 && (
            <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Lightbulb className="h-4 w-4 text-yellow-600" />
                <p className="font-medium text-yellow-800 dark:text-yellow-200">
                  Top Recommendation
                </p>
              </div>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                {personality.recommendations[0]}
              </p>
            </div>
          )}

          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <p className="text-2xl font-bold text-primary">
                {stats.totalGames}
              </p>
              <p className="text-xs text-muted-foreground">
                Total Games
              </p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {stats.gamesCompleted}
              </p>
              <p className="text-xs text-muted-foreground">
                Completed
              </p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">
                {stats.gamesInBacklog}
              </p>
              <p className="text-xs text-muted-foreground">
                In Backlog
              </p>
            </div>
          </div>

          {/* View Details Button */}
          {!showDetails && onViewDetails && (
            <Button 
              variant="outline" 
              className="w-full" 
              onClick={onViewDetails}
            >
              View Detailed Insights
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

interface BacklogOptimizationCardProps {
  className?: string;
  maxGames?: number;
}

export const BacklogOptimizationCard: React.FC<BacklogOptimizationCardProps> = ({ 
  className = '',
  maxGames = 5
}) => {
  const { optimizations, quickStats, isLoading } = useBacklogOptimization();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Backlog Optimization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (optimizations.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Backlog Optimization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-muted-foreground">
              No games in backlog to optimize
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const priorityColors = {
    high: 'bg-red-500',
    medium: 'bg-yellow-500',
    low: 'bg-green-500'
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Backlog Optimization
          </div>
          <Badge variant="outline">
            {quickStats.totalBacklogGames} games
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
            <div className="text-center">
              <p className="text-lg font-bold text-red-600">
                {quickStats.highPriorityGames}
              </p>
              <p className="text-xs text-muted-foreground">
                High Priority
              </p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-blue-600">
                {quickStats.quickCompletions}
              </p>
              <p className="text-xs text-muted-foreground">
                Quick Wins
              </p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-green-600">
                {quickStats.highLikelihood}
              </p>
              <p className="text-xs text-muted-foreground">
                High Likelihood
              </p>
            </div>
          </div>

          {/* Top Recommendations */}
          <div className="space-y-3">
            {optimizations.slice(0, maxGames).map((optimization, index) => (
              <Link to={`/game/${optimization.igdb_id}`} key={index} className="block">
                <div className="p-3 border rounded-lg hover:shadow-md transition-all duration-200 flex items-start gap-4">
                  {optimization.cover_image ? (
                    <img
                      src={optimization.cover_image}
                      alt={optimization.gameName}
                      className="w-16 h-auto rounded-md object-cover"
                    />
                  ) : (
                    <div className="w-16 h-[85px] bg-muted rounded-md flex items-center justify-center">
                      <Target className="w-6 h-6 text-muted-foreground" />
                    </div>
                  )}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <div className={cn("w-2 h-2 rounded-full", priorityColors[optimization.priority])} />
                      <p className="font-medium text-sm line-clamp-1">
                        {optimization.gameName}
                      </p>
                    </div>
                    <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                      {optimization.reason}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{optimization.estimatedPlayTime}h</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3" />
                        <span>{optimization.completionLikelihood}% to complete</span>
                      </div>
                    </div>
                    <Progress
                      value={optimization.completionLikelihood}
                      className="h-1 mt-2"
                    />
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {optimizations.length > maxGames && (
            <div className="text-center pt-2">
              <p className="text-sm text-muted-foreground">
                And {optimizations.length - maxGames} more recommendations...
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};