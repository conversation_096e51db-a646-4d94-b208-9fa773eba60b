import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base';
import { CollectionsTabProps } from '../../types';

export function CollectionsTab({ 
  libraryGames, 
  onCollectionCreate: _onCollectionCreate, 
  onCollectionUpdate: _onCollectionUpdate, 
  onCollectionDelete: _onCollectionDelete, 
  onCollectionDuplicate: _onCollectionDuplicate 
}: CollectionsTabProps) {
  // Calculate recently added games (last 30 days)
  const recentlyAddedCount = libraryGames.filter(g => {
    const addedDate = new Date(g.date_added);
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    return addedDate >= thirtyDaysAgo;
  }).length;

  // Calculate highly rated games (8+ personal rating)
  const highlyRatedCount = libraryGames.filter(g => (g.personal_rating || 0) >= 8).length;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🗂️ Smart Collections</CardTitle>
          <CardDescription>
            Automatically organize your games with intelligent rules
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Recently Added</CardTitle>
                  <CardDescription>Games added in the last 30 days</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {recentlyAddedCount}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Highly Rated</CardTitle>
                  <CardDescription>Games with 8+ personal rating</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {highlyRatedCount}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}