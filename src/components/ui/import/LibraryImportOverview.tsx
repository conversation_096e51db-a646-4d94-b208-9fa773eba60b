/**
 * Library Import Overview Component
 * Displays overview of library import capabilities and status
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { 
  Database, 
  TrendingUp, 
  Calendar, 
  Users, 
  Gamepad2,
  CheckCircle,
  RefreshCw,
  BarChart3,
  Activity,
  Zap
} from 'lucide-react';
import { useUserStats } from '@/hooks/useUserStats';
import { cn } from '@/lib/utils';

interface LibraryImportOverviewProps {
  className?: string;
}

interface ImportStats {
  totalGames: number;
  recentImports: number;
  platformsConnected: number;
  lastImportDate: Date | null;
  importSuccess: number;
  importErrors: number;
}

export const LibraryImportOverview: React.FC<LibraryImportOverviewProps> = ({ 
  className 
}) => {
  const { data: userStats, isLoading: statsLoading } = useUserStats();
  
  const [importStats, setImportStats] = useState<ImportStats>({
    totalGames: 0,
    recentImports: 0,
    platformsConnected: 0,
    lastImportDate: null,
    importSuccess: 0,
    importErrors: 0
  });
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Simulate loading import statistics (in real implementation, this would come from API)
  useEffect(() => {
    if (userStats) {
      setImportStats({
        totalGames: userStats.totalGames || 0,
        recentImports: userStats.recentlyAdded || 0,
        platformsConnected: 1, // For now, only Steam is implemented
        lastImportDate: new Date(),
        importSuccess: Math.floor((userStats.totalGames || 0) * 0.95),
        importErrors: Math.floor((userStats.totalGames || 0) * 0.05)
      });
    }
  }, [userStats]);

  const handleRefreshStats = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsRefreshing(false);
  };

  const successRate = importStats.totalGames > 0 
    ? Math.round((importStats.importSuccess / importStats.totalGames) * 100)
    : 0;

  if (statsLoading) {
    return (
      <Card className={cn("h-full", className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Library Import Overview
              </CardTitle>
              <CardDescription>Import statistics and platform connections</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="animate-pulse space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                  <div className="h-6 bg-gray-200 rounded w-12"></div>
                </div>
              ))}
            </div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-10 bg-gray-200 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Library Import Overview
            </CardTitle>
            <CardDescription>Import statistics and platform connections</CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleRefreshStats}
            disabled={isRefreshing}
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Statistics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Gamepad2 className="h-5 w-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{importStats.totalGames}</div>
            <div className="text-xs text-gray-600">Total Games</div>
          </div>
          
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">{importStats.recentImports}</div>
            <div className="text-xs text-gray-600">Recent Imports</div>
          </div>
          
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-5 w-5 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-purple-600">{importStats.platformsConnected}</div>
            <div className="text-xs text-gray-600">Platforms</div>
          </div>
          
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Activity className="h-5 w-5 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-orange-600">{successRate}%</div>
            <div className="text-xs text-gray-600">Success Rate</div>
          </div>
        </div>

        {/* Import Success Rate */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Import Success Rate
            </h4>
            <Badge variant="secondary" className="text-xs">
              {importStats.importSuccess}/{importStats.totalGames} successful
            </Badge>
          </div>
          <Progress value={successRate} className="h-2" />
          <div className="flex justify-between text-xs text-gray-600">
            <span>{importStats.importSuccess} successful imports</span>
            <span>{importStats.importErrors} errors</span>
          </div>
        </div>

        {/* Platform Status */}
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Platform Connections
          </h4>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="font-medium">Steam</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Connected
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg opacity-60">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="font-medium">PlayStation Network</span>
              </div>
              <Badge variant="outline" className="text-xs">
                Coming Soon
              </Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg opacity-60">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="font-medium">Xbox Live</span>
              </div>
              <Badge variant="outline" className="text-xs">
                Coming Soon
              </Badge>
            </div>
          </div>
        </div>

        {/* Last Import Info */}
        {importStats.lastImportDate && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-blue-600" />
              <span className="text-blue-700">
                Last import: {importStats.lastImportDate.toLocaleDateString()}
              </span>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex gap-2 pt-2">
          <Button variant="outline" size="sm" className="flex-1">
            <TrendingUp className="h-4 w-4 mr-2" />
            View Detailed Stats
          </Button>
          <Button variant="outline" size="sm" className="flex-1">
            <Database className="h-4 w-4 mr-2" />
            Import History
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default LibraryImportOverview;