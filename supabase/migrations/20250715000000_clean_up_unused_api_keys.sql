-- Clean up unused API keys (keep only Steam)
-- This migration removes all non-Steam API keys and their associated import history

-- Remove all non-Steam API keys
DELETE FROM user_api_keys 
WHERE platform != 'steam';

-- Remove all non-Steam import history
DELETE FROM import_history 
WHERE platform != 'steam';

-- Update the platform column to use a constraint (optional - ensures only Steam is allowed)
-- This is commented out to avoid breaking existing functionality if needed later
-- ALTER TABLE user_api_keys ADD CONSTRAINT check_platform_steam CHECK (platform = 'steam');
-- ALTER TABLE import_history ADD CONSTRAINT check_platform_steam CHECK (platform = 'steam');

-- Add a comment to document this cleanup
COMMENT ON TABLE user_api_keys IS 'Stores encrypted API keys for Steam platform only (cleaned up on 2025-07-15)';
COMMENT ON TABLE import_history IS 'Tracks import history for Steam platform only (cleaned up on 2025-07-15)';