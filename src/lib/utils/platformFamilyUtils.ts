import { 
  Monitor, 
  Gamepad2, 
  Smartphone, 
  Zap,
  HelpCircle,
  type LucideIcon 
} from 'lucide-react';
import { UserGameWithDetails } from '@/types/database';

/**
 * Platform family types representing major gaming ecosystems
 */
export type PlatformFamily = 'PC' | 'Xbox' | 'PlayStation' | 'Nintendo' | 'Mobile' | 'Other';

/**
 * Statistics for a platform family group
 */
export interface PlatformFamilyStats {
  total: number;
  playing: number;
  completed: number;
  backlog: number;
  wishlist: number;
  owned: number;
  completionRate: number;
  averageRating?: number;
  totalHours?: number;
}

/**
 * Platform family group containing games and metadata
 */
export interface PlatformFamilyGroup {
  family: PlatformFamily;
  platforms: string[];
  games: UserGameWithDetails[];
  stats: PlatformFamilyStats;
}

/**
 * Platform family configuration with display properties
 */
export interface PlatformFamilyConfig {
  name: string;
  icon: LucideIcon;
  color: string;
  bgColor: string;
  borderColor: string;
  textColor: string;
}

/**
 * Comprehensive platform to family mapping
 * Based on major gaming ecosystems and hardware manufacturers
 */
const PLATFORM_FAMILY_MAP: Record<string, PlatformFamily> = {
  // PC Family - All desktop/laptop platforms
  'PC': 'PC',
  'Mac': 'PC',
  'Linux': 'PC',
  'Steam Deck': 'PC',
  'Windows': 'PC',
  'macOS': 'PC',
  
  // Xbox Family - Microsoft gaming platforms
  'Xbox': 'Xbox',
  'Xbox 360': 'Xbox',
  'Xbox One': 'Xbox',
  'Xbox Series X/S': 'Xbox',
  'Xbox Series X': 'Xbox',
  'Xbox Series S': 'Xbox',
  'Xbox Original': 'Xbox',
  
  // PlayStation Family - Sony gaming platforms
  'PlayStation': 'PlayStation',
  'PlayStation 2': 'PlayStation',
  'PlayStation 3': 'PlayStation',
  'PlayStation 4': 'PlayStation',
  'PlayStation 5': 'PlayStation',
  'PS1': 'PlayStation',
  'PS2': 'PlayStation',
  'PS3': 'PlayStation',
  'PS4': 'PlayStation',
  'PS5': 'PlayStation',
  'PSP': 'PlayStation',
  'PS Vita': 'PlayStation',
  'PlayStation Portable': 'PlayStation',
  'PlayStation Vita': 'PlayStation',
  
  // Nintendo Family - Nintendo gaming platforms
  'Nintendo Switch': 'Nintendo',
  'Nintendo 3DS': 'Nintendo',
  'Nintendo DS': 'Nintendo',
  'Wii U': 'Nintendo',
  'Wii': 'Nintendo',
  'GameCube': 'Nintendo',
  'Nintendo 64': 'Nintendo',
  'N64': 'Nintendo',
  'Super Nintendo': 'Nintendo',
  'SNES': 'Nintendo',
  'Nintendo Entertainment System': 'Nintendo',
  'NES': 'Nintendo',
  'Game Boy': 'Nintendo',
  'Game Boy Color': 'Nintendo',
  'Game Boy Advance': 'Nintendo',
  'GBA': 'Nintendo',
  'New Nintendo 3DS': 'Nintendo',
  
  // Mobile Family - Mobile platforms
  'iOS': 'Mobile',
  'Android': 'Mobile',
  'iPhone': 'Mobile',
  'iPad': 'Mobile',
  'Windows Phone': 'Mobile',
  
  // Other platforms that don't fit major families
  '3DO': 'Other',
  'Atari': 'Other',
  'Sega Genesis': 'Other',
  'Sega Saturn': 'Other',
  'Dreamcast': 'Other',
  'Neo Geo': 'Other',
  'Arcade': 'Other',
};

/**
 * Platform family display configurations
 */
const PLATFORM_FAMILY_CONFIGS: Record<PlatformFamily, PlatformFamilyConfig> = {
  PC: {
    name: 'PC',
    icon: Monitor,
    color: '#0078D4',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800',
  },
  Xbox: {
    name: 'Xbox',
    icon: Gamepad2,
    color: '#107C10',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    textColor: 'text-green-800',
  },
  PlayStation: {
    name: 'PlayStation',
    icon: Gamepad2,
    color: '#003087',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800',
  },
  Nintendo: {
    name: 'Nintendo',
    icon: Zap,
    color: '#E60012',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
  },
  Mobile: {
    name: 'Mobile',
    icon: Smartphone,
    color: '#8B5CF6',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    textColor: 'text-purple-800',
  },
  Other: {
    name: 'Other',
    icon: HelpCircle,
    color: '#6B7280',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    textColor: 'text-gray-800',
  },
};

/**
 * Categorize a single platform into its platform family
 */
export function categorizePlatform(platform: string): PlatformFamily {
  // Normalize platform name for lookup
  const normalizedPlatform = platform.trim();
  
  // Direct lookup first
  if (PLATFORM_FAMILY_MAP[normalizedPlatform]) {
    return PLATFORM_FAMILY_MAP[normalizedPlatform];
  }
  
  // Case-insensitive lookup
  const lowerPlatform = normalizedPlatform.toLowerCase();
  for (const [key, family] of Object.entries(PLATFORM_FAMILY_MAP)) {
    if (key.toLowerCase() === lowerPlatform) {
      return family;
    }
  }
  
  // Partial matching for common variations
  if (lowerPlatform.includes('xbox')) return 'Xbox';
  if (lowerPlatform.includes('playstation') || lowerPlatform.includes('ps')) return 'PlayStation';
  if (lowerPlatform.includes('nintendo') || lowerPlatform.includes('switch')) return 'Nintendo';
  if (lowerPlatform.includes('pc') || lowerPlatform.includes('windows') || lowerPlatform.includes('steam')) return 'PC';
  if (lowerPlatform.includes('ios') || lowerPlatform.includes('android') || lowerPlatform.includes('mobile')) return 'Mobile';
  
  // Default to Other for unknown platforms
  return 'Other';
}

/**
 * Categorize multiple platforms into their platform families
 */
export function categorizePlatformsByFamily(platforms: string[]): PlatformFamily[] {
  if (!platforms || platforms.length === 0) {
    return ['Other'];
  }
  
  const families = new Set<PlatformFamily>();
  
  platforms.forEach(platform => {
    families.add(categorizePlatform(platform));
  });
  
  return Array.from(families);
}

/**
 * Calculate statistics for a group of games
 */
export function calculatePlatformFamilyStats(games: UserGameWithDetails[]): PlatformFamilyStats {
  const stats: PlatformFamilyStats = {
    total: games.length,
    playing: 0,
    completed: 0,
    backlog: 0,
    wishlist: 0,
    owned: 0,
    completionRate: 0,
    averageRating: undefined,
    totalHours: undefined,
  };
  
  if (games.length === 0) {
    return stats;
  }
  
  let totalRating = 0;
  let ratedGamesCount = 0;
  let totalHours = 0;
  let gamesWithHours = 0;
  
  games.forEach(game => {
    // Count by status
    switch (game.status) {
      case 'playing':
        stats.playing++;
        break;
      case 'completed':
        stats.completed++;
        break;
      case 'backlog':
        stats.backlog++;
        break;
      case 'wishlist':
        stats.wishlist++;
        break;
      case 'owned':
        stats.owned++;
        break;
    }
    
    // Calculate average rating
    if (game.personal_rating && game.personal_rating > 0) {
      totalRating += game.personal_rating;
      ratedGamesCount++;
    }
    
    // Calculate total hours
    if (game.hours_played && game.hours_played > 0) {
      totalHours += game.hours_played;
      gamesWithHours++;
    }
  });
  
  // Calculate completion rate (completed games / non-wishlist games)
  const nonWishlistGames = stats.total - stats.wishlist;
  if (nonWishlistGames > 0) {
    stats.completionRate = Math.round((stats.completed / nonWishlistGames) * 100);
  }
  
  // Calculate average rating
  if (ratedGamesCount > 0) {
    stats.averageRating = Math.round((totalRating / ratedGamesCount) * 10) / 10;
  }
  
  // Calculate total hours
  if (gamesWithHours > 0) {
    stats.totalHours = Math.round(totalHours * 10) / 10;
  }
  
  return stats;
}

/**
 * Group games by their platform families
 */
export function groupGamesByPlatformFamily(games: UserGameWithDetails[]): PlatformFamilyGroup[] {
  if (!games || games.length === 0) {
    return [];
  }
  
  // Create a map to group games by family
  const familyGroups = new Map<PlatformFamily, {
    platforms: Set<string>;
    games: UserGameWithDetails[];
  }>();
  
  games.forEach(game => {
    // Get platform from the game record
    const gamePlatform = game.game?.platform;
    if (!gamePlatform) {
      // Handle games without platform information
      if (!familyGroups.has('Other')) {
        familyGroups.set('Other', { platforms: new Set(), games: [] });
      }
      familyGroups.get('Other')!.games.push(game);
      return;
    }
    
    // Categorize the platform
    const family = categorizePlatform(gamePlatform);
    
    // Initialize family group if it doesn't exist
    if (!familyGroups.has(family)) {
      familyGroups.set(family, { platforms: new Set(), games: [] });
    }
    
    // Add platform and game to the family group
    const group = familyGroups.get(family)!;
    group.platforms.add(gamePlatform);
    group.games.push(game);
  });
  
  // Convert map to array of PlatformFamilyGroup objects
  const result: PlatformFamilyGroup[] = [];
  
  familyGroups.forEach((group, family) => {
    const stats = calculatePlatformFamilyStats(group.games);
    
    result.push({
      family,
      platforms: Array.from(group.platforms).sort(),
      games: group.games,
      stats,
    });
  });
  
  // Sort by family priority (PC, Xbox, PlayStation, Nintendo, Mobile, Other)
  const familyPriority: Record<PlatformFamily, number> = {
    PC: 1,
    Xbox: 2,
    PlayStation: 3,
    Nintendo: 4,
    Mobile: 5,
    Other: 6,
  };
  
  result.sort((a, b) => familyPriority[a.family] - familyPriority[b.family]);
  
  return result;
}

/**
 * Get the icon component for a platform family
 */
export function getPlatformFamilyIcon(family: PlatformFamily): LucideIcon {
  return PLATFORM_FAMILY_CONFIGS[family].icon;
}

/**
 * Get the primary color for a platform family
 */
export function getPlatformFamilyColor(family: PlatformFamily): string {
  return PLATFORM_FAMILY_CONFIGS[family].color;
}

/**
 * Get the background color class for a platform family
 */
export function getPlatformFamilyBgColor(family: PlatformFamily): string {
  return PLATFORM_FAMILY_CONFIGS[family].bgColor;
}

/**
 * Get the border color class for a platform family
 */
export function getPlatformFamilyBorderColor(family: PlatformFamily): string {
  return PLATFORM_FAMILY_CONFIGS[family].borderColor;
}

/**
 * Get the text color class for a platform family
 */
export function getPlatformFamilyTextColor(family: PlatformFamily): string {
  return PLATFORM_FAMILY_CONFIGS[family].textColor;
}

/**
 * Get the display name for a platform family
 */
export function getPlatformFamilyDisplayName(family: PlatformFamily): string {
  return PLATFORM_FAMILY_CONFIGS[family].name;
}

/**
 * Get complete configuration for a platform family
 */
export function getPlatformFamilyConfig(family: PlatformFamily): PlatformFamilyConfig {
  return PLATFORM_FAMILY_CONFIGS[family];
}

/**
 * Get all available platform families
 */
export function getAllPlatformFamilies(): PlatformFamily[] {
  return ['PC', 'Xbox', 'PlayStation', 'Nintendo', 'Mobile', 'Other'];
}

/**
 * Check if a platform belongs to a specific family
 */
export function isPlatformInFamily(platform: string, family: PlatformFamily): boolean {
  return categorizePlatform(platform) === family;
}

/**
 * Get all platforms that belong to a specific family
 */
export function getPlatformsInFamily(family: PlatformFamily): string[] {
  return Object.entries(PLATFORM_FAMILY_MAP)
    .filter(([, platformFamily]) => platformFamily === family)
    .map(([platform]) => platform)
    .sort();
}

/**
 * Filter games by platform family
 */
export function filterGamesByPlatformFamily(
  games: UserGameWithDetails[], 
  family: PlatformFamily
): UserGameWithDetails[] {
  return games.filter(game => {
    const gamePlatform = game.game?.platform;
    if (!gamePlatform) {
      return family === 'Other';
    }
    return isPlatformInFamily(gamePlatform, family);
  });
}

/**
 * Get platform family statistics for a user's entire library
 */
export function getLibraryPlatformFamilyBreakdown(games: UserGameWithDetails[]): Record<PlatformFamily, number> {
  const breakdown: Record<PlatformFamily, number> = {
    PC: 0,
    Xbox: 0,
    PlayStation: 0,
    Nintendo: 0,
    Mobile: 0,
    Other: 0,
  };
  
  games.forEach(game => {
    const gamePlatform = game.game?.platform;
    const family = gamePlatform ? categorizePlatform(gamePlatform) : 'Other';
    breakdown[family]++;
  });
  
  return breakdown;
}