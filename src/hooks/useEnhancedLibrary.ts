import { useCallback, useMemo, useEffect } from 'react';
import { useLibrary } from '@/contexts/LibraryContext';
import { LibraryService } from '@/lib/libraryService';
import { 
  ViewMode, 
  Collection, 
  FilterPreset, 
  GroupingOption,
  LibraryPreferences
} from '@/types/library';
import { GameFilters } from '@/types/filters';

/**
 * Enhanced Library Hook
 * Provides a comprehensive interface for library operations
 */
export function useEnhancedLibrary() {
  const { state, dispatch, enhancedGames, isLoading, error } = useLibrary();

  // Memoized filtered and sorted games
  const processedGames = useMemo(() => {
    let games = [...state.games];
    
    // Apply filters
    games = LibraryService.filterGames(games, state.filters);
    
    // Apply sorting
    games = LibraryService.sortGames(
      games, 
      state.filters.sort.field, 
      state.filters.sort.direction
    );
    
    return games;
  }, [state.games, state.filters]);

  // Memoized grouped games
  const groupedGames = useMemo(() => {
    const groupBy = state.viewSettings.list.groupBy || 'none';
    return LibraryService.groupGames(processedGames, groupBy);
  }, [processedGames, state.viewSettings.list.groupBy]);

  // Memoized search results
  const searchResults = useMemo(() => {
    if (!state.searchState.query.trim()) {
      return processedGames;
    }
    return LibraryService.searchGames(processedGames, state.searchState);
  }, [processedGames, state.searchState]);

  // Memoized library statistics
  const libraryStats = useMemo(() => {
    return LibraryService.calculateLibraryStats(state.games);
  }, [state.games]);

  // Update filtered games when processed games change
  useEffect(() => {
    dispatch({ type: 'SET_FILTERED_GAMES', payload: processedGames });
  }, [processedGames, dispatch]);

  // View mode operations
  const setViewMode = useCallback((viewMode: ViewMode) => {
    dispatch({ type: 'SET_VIEW_MODE', payload: viewMode });
  }, [dispatch]);

  const updateGridSettings = useCallback((settings: Partial<typeof state.viewSettings.grid>) => {
    dispatch({ type: 'UPDATE_GRID_SETTINGS', payload: settings });
  }, [dispatch]);

  const updateListSettings = useCallback((settings: Partial<typeof state.viewSettings.list>) => {
    dispatch({ type: 'UPDATE_LIST_SETTINGS', payload: settings });
  }, [dispatch]);

  const updateCoverFlowSettings = useCallback((settings: Partial<typeof state.viewSettings.coverFlow>) => {
    dispatch({ type: 'UPDATE_COVER_FLOW_SETTINGS', payload: settings });
  }, [dispatch]);

  const updateTimelineSettings = useCallback((settings: Partial<typeof state.viewSettings.timeline>) => {
    dispatch({ type: 'UPDATE_TIMELINE_SETTINGS', payload: settings });
  }, [dispatch]);

  const updateStatsSettings = useCallback((settings: Partial<typeof state.viewSettings.stats>) => {
    dispatch({ type: 'UPDATE_STATS_SETTINGS', payload: settings });
  }, [dispatch]);

  // Filter operations
  const updateFilters = useCallback((filters: GameFilters) => {
    dispatch({ type: 'UPDATE_FILTERS', payload: filters });
  }, [dispatch]);

  const resetFilters = useCallback(() => {
    dispatch({ type: 'UPDATE_FILTERS', payload: state.filters });
  }, [dispatch, state.filters]);

  // Search operations
  const setSearchQuery = useCallback((query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });
    
    if (query.trim()) {
      dispatch({ type: 'ADD_RECENT_SEARCH', payload: query });
    }
  }, [dispatch]);

  const clearSearch = useCallback(() => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: '' });
    dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] });
  }, [dispatch]);

  const generateSearchSuggestions = useCallback((query: string) => {
    return LibraryService.generateSearchSuggestions(state.games, query);
  }, [state.games]);

  // Selection operations
  const toggleGameSelection = useCallback((gameId: string) => {
    dispatch({ type: 'TOGGLE_GAME_SELECTION', payload: gameId });
  }, [dispatch]);

  const selectAllGames = useCallback(() => {
    dispatch({ type: 'SELECT_ALL_GAMES' });
  }, [dispatch]);

  const clearSelection = useCallback(() => {
    dispatch({ type: 'CLEAR_SELECTION' });
  }, [dispatch]);

  const getSelectedGames = useCallback(() => {
    return state.games.filter(game => state.selectedGames.has(game.id));
  }, [state.games, state.selectedGames]);

  // Section expansion operations
  const toggleSectionExpansion = useCallback((sectionId: string) => {
    dispatch({ type: 'TOGGLE_SECTION_EXPANSION', payload: sectionId });
  }, [dispatch]);

  const isSectionExpanded = useCallback((sectionId: string) => {
    return state.expandedSections.has(sectionId);
  }, [state.expandedSections]);

  // Collection operations
  const addCollection = useCallback((collection: Collection) => {
    dispatch({ type: 'ADD_COLLECTION', payload: collection });
  }, [dispatch]);

  const updateCollection = useCallback((id: string, updates: Partial<Collection>) => {
    dispatch({ type: 'UPDATE_COLLECTION', payload: { id, updates } });
  }, [dispatch]);

  const removeCollection = useCallback((id: string) => {
    dispatch({ type: 'REMOVE_COLLECTION', payload: id });
  }, [dispatch]);

  const getCollectionGames = useCallback((collection: Collection) => {
    return LibraryService.applyCollectionRules(state.games, collection);
  }, [state.games]);

  // Filter preset operations
  const addFilterPreset = useCallback((preset: FilterPreset) => {
    dispatch({ type: 'ADD_FILTER_PRESET', payload: preset });
  }, [dispatch]);

  const updateFilterPreset = useCallback((id: string, updates: Partial<FilterPreset>) => {
    dispatch({ type: 'UPDATE_FILTER_PRESET', payload: { id, updates } });
  }, [dispatch]);

  const removeFilterPreset = useCallback((id: string) => {
    dispatch({ type: 'REMOVE_FILTER_PRESET', payload: id });
  }, [dispatch]);

  const applyFilterPreset = useCallback((preset: FilterPreset) => {
    dispatch({ type: 'UPDATE_FILTERS', payload: preset.filters });
  }, [dispatch]);

  // Preferences operations
  const updatePreferences = useCallback((preferences: Partial<LibraryPreferences>) => {
    dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
  }, [dispatch]);

  // Loading state operations
  const setLoadingState = useCallback((key: keyof typeof state.loadingStates, value: boolean) => {
    dispatch({ type: 'SET_LOADING_STATE', payload: { key, value } });
  }, [dispatch]);

  const setError = useCallback((key: string, value: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: { key, value } });
  }, [dispatch]);

  // Export operations
  const exportLibrary = useCallback((format: 'json' | 'csv' | 'xml') => {
    return LibraryService.exportLibrary(state.games, format);
  }, [state.games]);

  // Grouping operations
  const setGroupBy = useCallback((groupBy: GroupingOption) => {
    dispatch({ 
      type: 'UPDATE_LIST_SETTINGS', 
      payload: { groupBy } 
    });
  }, [dispatch]);

  // Utility functions
  const getGameById = useCallback((gameId: string) => {
    return state.games.find(game => game.id === gameId);
  }, [state.games]);

  const isGameSelected = useCallback((gameId: string) => {
    return state.selectedGames.has(gameId);
  }, [state.selectedGames]);

  const hasActiveFilters = useMemo(() => {
    const filters = state.filters;
    return filters.search.enabled ||
           filters.platforms.enabled ||
           filters.genres.enabled ||
           filters.year.enabled ||
           filters.rating.enabled ||
           filters.developer.enabled ||
           filters.publisher.enabled ||
           filters.customTags.enabled ||
           filters.status.enabled ||
           filters.collection.enabled;
  }, [state.filters]);

  const activeFilterCount = useMemo(() => {
    let count = 0;
    const filters = state.filters;
    
    if (filters.search.enabled) count++;
    if (filters.platforms.enabled) count++;
    if (filters.genres.enabled) count++;
    if (filters.year.enabled) count++;
    if (filters.rating.enabled) count++;
    if (filters.developer.enabled) count++;
    if (filters.publisher.enabled) count++;
    if (filters.customTags.enabled) count++;
    if (filters.status.enabled) count++;
    if (filters.collection.enabled) count++;
    
    return count;
  }, [state.filters]);

  return {
    // State
    state,
    enhancedGames,
    processedGames,
    groupedGames,
    searchResults,
    libraryStats,
    isLoading,
    error,
    
    // View operations
    setViewMode,
    updateGridSettings,
    updateListSettings,
    updateCoverFlowSettings,
    updateTimelineSettings,
    updateStatsSettings,
    
    // Filter operations
    updateFilters,
    resetFilters,
    hasActiveFilters,
    activeFilterCount,
    
    // Search operations
    setSearchQuery,
    clearSearch,
    generateSearchSuggestions,
    
    // Selection operations
    toggleGameSelection,
    selectAllGames,
    clearSelection,
    getSelectedGames,
    isGameSelected,
    
    // Section operations
    toggleSectionExpansion,
    isSectionExpanded,
    
    // Collection operations
    addCollection,
    updateCollection,
    removeCollection,
    getCollectionGames,
    
    // Filter preset operations
    addFilterPreset,
    updateFilterPreset,
    removeFilterPreset,
    applyFilterPreset,
    
    // Preferences operations
    updatePreferences,
    
    // Loading state operations
    setLoadingState,
    setError,
    
    // Export operations
    exportLibrary,
    
    // Grouping operations
    setGroupBy,
    
    // Utility functions
    getGameById
  };
}