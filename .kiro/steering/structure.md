# Project Structure & Organization

## Root Directory Structure

```
├── .kiro/                    # Kiro IDE configuration and steering
├── docs/                     # Project documentation
├── src/                      # Source code (main application)
├── supabase/                 # Database migrations and functions
├── scripts/                  # Utility scripts
├── node_modules/             # Dependencies
└── config files              # Build and tool configurations
```

## Source Code Organization (`src/`)

### Core Application Structure
```
src/
├── components/               # Reusable UI components
│   ├── ui/                  # shadcn/ui components and custom UI
│   │   ├── base/           # Base shadcn/ui components
│   │   ├── analytics/      # Analytics-specific components
│   │   ├── chat/           # AI chat components
│   │   ├── enhanced/       # Enhanced feature components
│   │   ├── filters/        # Search and filter components
│   │   ├── game/           # Game-related components
│   │   ├── import/         # Import/export components
│   │   ├── library/        # Library management components
│   │   ├── tags/           # Tagging system components
│   │   └── utils/          # Utility components
│   └── layout/             # Layout components (Header, Sidebar, etc.)
├── pages/                   # Page components (route handlers)
├── hooks/                   # Custom React hooks
├── lib/                     # Utility functions and services
├── types/                   # TypeScript type definitions
├── contexts/                # React context providers
└── styles/                  # Global styles and CSS
```

### Page Structure Pattern
Each major page follows a consistent modular structure:
```
pages/[PageName]/
├── index.tsx               # Main page component
├── components/             # Page-specific components
├── hooks/                  # Page-specific hooks
└── types.ts               # Page-specific TypeScript types
```

Example: `pages/Dashboard/`, `pages/Library/`, `pages/Search/`

## Component Organization Principles

### UI Components (`src/components/ui/`)
- **base/**: Core shadcn/ui components (button, card, dialog, etc.)
- **[feature]/**: Feature-specific component groups
- **index.ts**: Barrel exports for clean imports

### Component Naming Conventions
- **PascalCase** for component files and names
- **kebab-case** for utility files
- **Descriptive names** that indicate purpose
- **Co-located** related components in feature folders

### Import/Export Patterns
```typescript
// Barrel exports in index.ts files
export { Button } from './button';
export { Card } from './card';

// Clean imports using path aliases
import { Button, Card } from '@/components/ui/base';
import { GameCard } from '@/components/ui/game';
```

## Library Organization (`src/lib/`)

### Service Layer Structure
```
lib/
├── api/                     # API service modules
├── services/                # Business logic services
├── supabase/               # Database operations
├── utils/                  # Utility functions (organized by domain)
├── imports/                # Platform import services
└── [feature]Service.ts     # Feature-specific services
```

### Utility Organization (`src/lib/utils/`)
- **Domain-specific** utility files (gameUtils, dateUtils, etc.)
- **index.ts** for barrel exports
- **Pure functions** with no side effects
- **Well-typed** with TypeScript

## Hook Organization (`src/hooks/`)

### Hook Categories
```
hooks/
├── filters/                # Filter-related hooks
├── search/                 # Search functionality hooks
├── use[Feature].ts         # Feature-specific hooks
└── index.ts               # Barrel exports
```

### Hook Naming Convention
- **use** prefix for all custom hooks
- **Descriptive names** indicating purpose
- **Composed hooks** for complex functionality
- **Single responsibility** principle

## Database Structure (`supabase/`)

### Migration Management
```
supabase/
├── migrations/             # Database schema migrations
├── functions/              # Edge Functions for API proxying
└── config.toml            # Supabase configuration
```

### Migration Naming Convention
- **Timestamp prefix**: `YYYYMMDDHHMMSS_description.sql`
- **Descriptive names** for migration purpose
- **Incremental changes** only
- **Rollback considerations**

## Type Definitions (`src/types/`)

### Type Organization
```
types/
├── index.ts               # Core game and API types
├── database.ts            # Database schema types
├── filters.ts             # Filter and search types
└── library.ts             # Library management types
```

### Type Naming Conventions
- **PascalCase** for interfaces and types
- **Descriptive names** with context
- **Generic types** where appropriate
- **Exported from index.ts** for clean imports

## File Naming Conventions

### General Rules
- **PascalCase**: React components (`GameCard.tsx`)
- **camelCase**: Hooks, utilities, services (`useGameActions.ts`)
- **kebab-case**: CSS files, configuration (`index.css`)
- **UPPERCASE**: Constants and environment files (`.ENV`)

### File Extensions
- **`.tsx`**: React components with JSX
- **`.ts`**: TypeScript files without JSX
- **`.css`**: Stylesheets
- **`.md`**: Documentation files

## Import Organization Standards

### Import Order
1. **React and React-related** imports
2. **Third-party libraries**
3. **Internal components** (using path aliases)
4. **Utilities and services**
5. **Types and interfaces**
6. **Relative imports** (same directory)

### Path Aliases
- **`@/`**: Points to `src/` directory
- **`@/components`**: UI components
- **`@/lib`**: Utilities and services
- **`@/hooks`**: Custom hooks
- **`@/types`**: Type definitions

## Architecture Patterns

### Component Patterns
- **Container/Presentational** separation
- **Custom hooks** for business logic
- **Compound components** for complex UI
- **Render props** for flexible composition

### State Management
- **TanStack Query** for server state
- **React Context** for global client state
- **Local state** with useState/useReducer
- **Form state** with React Hook Form

### Error Handling
- **Error boundaries** for component errors
- **Try-catch blocks** for async operations
- **Graceful degradation** for failed features
- **User-friendly error messages**

## Performance Considerations

### Code Organization for Performance
- **Lazy loading** for route components
- **Dynamic imports** for heavy features
- **Memoization** for expensive calculations
- **Virtualization** for large lists

### Bundle Optimization
- **Tree shaking** friendly exports
- **Code splitting** at route level
- **Minimal dependencies** in critical paths
- **Efficient re-exports** in barrel files