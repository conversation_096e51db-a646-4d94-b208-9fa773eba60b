import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { GameCard } from '@/components/ui/game/game-card';
import { UserGameWithDetails } from '@/types/database';
import { 
  PlatformFamily, 
  PlatformFamilyStats,
  getPlatformFamilyIcon,
  getPlatformFamilyConfig
} from '@/lib/utils/platformFamilyUtils';
import { ChevronDown } from 'lucide-react';
import { memo, useRef } from 'react';

interface PlatformFamilyGroupProps {
  family: PlatformFamily;
  games: UserGameWithDetails[];
  platforms: string[];
  stats: PlatformFamilyStats;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onGameClick: (game: UserGameWithDetails) => void;
  onStatusUpdate: (userGameId: string, status: string) => void;
  onRemoveGame: (userGameId: string) => void;
}

export const PlatformFamilyGroup = memo<PlatformFamilyGroupProps>(({
  family,
  games,
  platforms,
  stats,
  isCollapsed,
  onToggleCollapse,
  onGameClick,
  onStatusUpdate,
  onRemoveGame
}) => {
  const config = getPlatformFamilyConfig(family);
  const IconComponent = getPlatformFamilyIcon(family);
  const contentRef = useRef<HTMLDivElement>(null);

  return (
    <Card className="overflow-hidden transition-all duration-300 ease-in-out hover:shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div 
              className={`p-2 rounded-lg ${config.bgColor} ${config.borderColor} border transition-all duration-200 hover:scale-105`}
              style={{ backgroundColor: `${config.color}15` }}
            >
              <IconComponent 
                className="h-5 w-5 transition-colors duration-200" 
                style={{ color: config.color }}
              />
            </div>
            <div className="flex flex-col">
              <CardTitle className="text-lg flex items-center gap-2">
                {config.name}
                <Badge variant="outline" className="bg-primary/5 transition-colors duration-200">
                  {stats.total} games
                </Badge>
              </CardTitle>
              {platforms.length > 0 && (
                <div className="text-xs text-muted-foreground mt-1 transition-opacity duration-200">
                  {platforms.slice(0, 3).join(', ')}
                  {platforms.length > 3 && ` +${platforms.length - 3} more`}
                </div>
              )}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="h-8 w-8 p-0 transition-all duration-300 ease-in-out hover:bg-muted/50"
            style={{ transform: isCollapsed ? 'rotate(0deg)' : 'rotate(180deg)' }}
          >
            <ChevronDown className="h-4 w-4" />
          </Button>
        </div>
        
        <CardDescription className="flex items-center gap-4 text-sm flex-wrap">
          {stats.playing > 0 && (
            <span className="flex items-center gap-1 transition-all duration-200 hover:scale-105">
              <span className="w-2 h-2 rounded-full bg-blue-500 animate-pulse"></span>
              {stats.playing} playing
            </span>
          )}
          {stats.completed > 0 && (
            <span className="flex items-center gap-1 transition-all duration-200 hover:scale-105">
              <span className="w-2 h-2 rounded-full bg-green-500"></span>
              {stats.completed} completed
            </span>
          )}
          {stats.backlog > 0 && (
            <span className="flex items-center gap-1 transition-all duration-200 hover:scale-105">
              <span className="w-2 h-2 rounded-full bg-orange-500"></span>
              {stats.backlog} backlog
            </span>
          )}
          {stats.wishlist > 0 && (
            <span className="flex items-center gap-1 transition-all duration-200 hover:scale-105">
              <span className="w-2 h-2 rounded-full bg-purple-500"></span>
              {stats.wishlist} wishlist
            </span>
          )}
          {stats.owned > 0 && (
            <span className="flex items-center gap-1 transition-all duration-200 hover:scale-105">
              <span className="w-2 h-2 rounded-full bg-gray-500"></span>
              {stats.owned} owned
            </span>
          )}
          {stats.completionRate > 0 && (
            <span className="text-muted-foreground transition-colors duration-200">
              • {stats.completionRate}% completed
            </span>
          )}
          {stats.averageRating && (
            <span className="text-muted-foreground transition-colors duration-200">
              • ⭐ {stats.averageRating}/10 avg
            </span>
          )}
          {stats.totalHours && (
            <span className="text-muted-foreground transition-colors duration-200">
              • {stats.totalHours}h played
            </span>
          )}
        </CardDescription>
      </CardHeader>
      
      {!isCollapsed && (
        <div 
          className="transition-all duration-500 ease-in-out"
          ref={contentRef}
        >
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
              {games.map((userGame, index) => (
                <div
                  key={userGame.id}
                  className="animate-fade-in"
                  style={{ 
                    animationDelay: `${index * 50}ms`,
                    animationFillMode: 'both'
                  }}
                >
                  <GameCard
                    gameData={userGame}
                    onGameClick={onGameClick}
                    onUpdateStatus={onStatusUpdate}
                    onRemoveGame={onRemoveGame}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </div>
      )}
    </Card>
  );
});

PlatformFamilyGroup.displayName = 'PlatformFamilyGroup';