import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { filterPresets } from '@/lib/supabase';
import { GameFilters } from '@/types/filters';

export function useFilterPresets() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const {
    data: presets = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['filter-presets', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      try {
        const { data, error } = await filterPresets.getAll(user.id);
        if (error) {
          // Handle table not found or other DB errors gracefully
          if (error.code === '42P01' || error.message?.includes('does not exist')) {
            console.warn('Filter presets table not found, returning empty array');
            return [];
          }
          throw error;
        }
        return data;
      } catch (err) {
        console.error('Error fetching filter presets:', err);
        return [];
      }
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: unknown) => {
      // Don't retry if table doesn't exist
      if ((error as { code?: string; message?: string })?.code === '42P01' || (error as { code?: string; message?: string })?.message?.includes('does not exist')) {
        return false;
      }
      return failureCount < 2;
    }
  });

  const createPresetMutation = useMutation({
    mutationFn: async (presetData: { 
      name: string; 
      description?: string; 
      filters: GameFilters; 
      isPublic?: boolean 
    }) => {
      if (!user?.id) throw new Error('User not authenticated');
      const { data, error } = await filterPresets.create(user.id, presetData);
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['filter-presets', user?.id] });
    }
  });

  const updatePresetMutation = useMutation({
    mutationFn: async ({ 
      presetId, 
      updates 
    }: { 
      presetId: string; 
      updates: Partial<{ name: string; description: string; filters: GameFilters; is_public: boolean }> 
    }) => {
      const { data, error } = await filterPresets.update(presetId, updates);
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['filter-presets', user?.id] });
    }
  });

  const deletePresetMutation = useMutation({
    mutationFn: async (presetId: string) => {
      const { error } = await filterPresets.delete(presetId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['filter-presets', user?.id] });
    }
  });

  const incrementUsageMutation = useMutation({
    mutationFn: async (presetId: string) => {
      const { error } = await filterPresets.incrementUsage(presetId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['filter-presets', user?.id] });
    }
  });

  return {
    presets,
    isLoading,
    error,
    refetch,
    createPreset: createPresetMutation.mutate,
    updatePreset: updatePresetMutation.mutate,
    deletePreset: deletePresetMutation.mutate,
    incrementUsage: incrementUsageMutation.mutate,
    isCreating: createPresetMutation.isPending,
    isUpdating: updatePresetMutation.isPending,
    isDeleting: deletePresetMutation.isPending
  };
}

export function usePublicFilterPresets() {
  const {
    data: publicPresets = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['public-filter-presets'],
    queryFn: async () => {
      const { data, error } = await filterPresets.getPublic(20);
      if (error) throw error;
      return data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  });

  return {
    publicPresets,
    isLoading,
    error,
    refetch
  };
}

// Helper hook for creating quick presets from current filters
export function useQuickPresets() {
  const { createPreset, isCreating } = useFilterPresets();

  const createQuickPreset = (filters: GameFilters, name: string) => {
    // Generate a description based on active filters
    const activeFilters = [];
    
    if (filters.platforms.enabled && filters.platforms.platforms.length > 0) {
      activeFilters.push(`Platforms: ${filters.platforms.platforms.join(', ')}`);
    }
    if (filters.genres.enabled && filters.genres.genres.length > 0) {
      activeFilters.push(`Genres: ${filters.genres.genres.join(', ')}`);
    }
    if (filters.customTags.enabled && filters.customTags.tags.length > 0) {
      activeFilters.push(`Tags: ${filters.customTags.tags.length} selected`);
    }
    if (filters.year.enabled) {
      const yearRange = [];
      if (filters.year.minYear) yearRange.push(`From ${filters.year.minYear}`);
      if (filters.year.maxYear) yearRange.push(`To ${filters.year.maxYear}`);
      if (yearRange.length > 0) activeFilters.push(`Year: ${yearRange.join(' ')}`);
    }
    if (filters.rating.enabled) {
      const ratingRange = [];
      if (filters.rating.minRating) ratingRange.push(`Min ${filters.rating.minRating}`);
      if (filters.rating.maxRating) ratingRange.push(`Max ${filters.rating.maxRating}`);
      if (ratingRange.length > 0) activeFilters.push(`Rating: ${ratingRange.join(' ')}`);
    }

    const description = activeFilters.length > 0 
      ? activeFilters.join(' • ') 
      : 'No active filters';

    createPreset({
      name,
      description,
      filters,
      isPublic: false
    });
  };

  return {
    createQuickPreset,
    isCreating
  };
}