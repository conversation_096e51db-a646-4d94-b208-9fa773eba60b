import { useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Skeleton } from '@/components/ui/base/skeleton';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  BarChart3,
  DollarSign
} from 'lucide-react';
import { usePriceAnalytics, formatPrice } from '@/hooks/usePriceTracking';

interface PriceChartProps {
  gameId: string;
  className?: string;
}

export const PriceChart: React.FC<PriceChartProps> = ({ 
  gameId, 
  className = ''
}) => {
  const {
    priceHistory,
    analytics,
    timeRange,
    setTimeRange,
    isLoadingHistory,
    historyError
  } = usePriceAnalytics(gameId);

  const timeRangeOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' }
  ] as const;

  // Simple ASCII-style chart for demonstration
  // In a real implementation, you'd use a proper chart library like Chart.js or Recharts
  const chartData = useMemo(() => {
    if (!priceHistory || priceHistory.length === 0) return [];

    const prices = priceHistory.map(h => h.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;

    return priceHistory.map((history, index) => {
      const normalizedPrice = priceRange > 0 ? (history.price - minPrice) / priceRange : 0.5;
      const height = Math.max(10, normalizedPrice * 60); // 10-70px height
      
      return {
        ...history,
        height,
        index
      };
    });
  }, [priceHistory]);

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-red-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-green-500" />;
      default:
        return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendColor = (direction: string) => {
    switch (direction) {
      case 'up':
        return 'text-red-500';
      case 'down':
        return 'text-green-500';
      default:
        return 'text-gray-500';
    }
  };

  if (isLoadingHistory) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Price History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-32 w-full" />
            <div className="grid grid-cols-4 gap-4">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (historyError) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Price History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-muted-foreground">
              Unable to load price history
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (priceHistory.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Price History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-muted-foreground">
              No price history available for this game
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Price History
          </div>
          <div className="flex items-center gap-2">
            {timeRangeOptions.map((option) => (
              <Button
                key={option.value}
                variant={timeRange === option.value ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange(option.value)}
              >
                {option.label}
              </Button>
            ))}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Price Analytics Summary */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-center gap-2 mb-1">
                {getTrendIcon(analytics.trendDirection)}
                <p className="text-sm font-medium">Trend</p>
              </div>
              <p className={`text-lg font-semibold ${getTrendColor(analytics.trendDirection)}`}>
                {analytics.priceChangePercent > 0 ? '+' : ''}
                {analytics.priceChangePercent.toFixed(1)}%
              </p>
            </div>
            
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-center gap-2 mb-1">
                <TrendingDown className="h-4 w-4 text-green-500" />
                <p className="text-sm font-medium">Lowest</p>
              </div>
              <p className="text-lg font-semibold text-green-600">
                {formatPrice(analytics.lowestPrice)}
              </p>
            </div>
            
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-center gap-2 mb-1">
                <TrendingUp className="h-4 w-4 text-red-500" />
                <p className="text-sm font-medium">Highest</p>
              </div>
              <p className="text-lg font-semibold text-red-600">
                {formatPrice(analytics.highestPrice)}
              </p>
            </div>
            
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-center gap-2 mb-1">
                <DollarSign className="h-4 w-4 text-blue-500" />
                <p className="text-sm font-medium">Average</p>
              </div>
              <p className="text-lg font-semibold text-blue-600">
                {formatPrice(analytics.averagePrice)}
              </p>
            </div>
          </div>

          {/* Simple Bar Chart */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">Price Chart</p>
              <Badge variant="outline" className="text-xs">
                {priceHistory.length} data points
              </Badge>
            </div>
            
            <div className="bg-muted/30 rounded-lg p-4">
              <div className="flex items-end justify-between space-x-1 h-24">
                {chartData.map((data, index) => (
                  <div
                    key={index}
                    className="flex-1 max-w-8 group relative"
                  >
                    <div
                      className="bg-blue-500 hover:bg-blue-600 transition-colors rounded-t-sm cursor-pointer"
                      style={{ height: `${data.height}px` }}
                      title={`${data.shopName}: ${formatPrice(data.price, data.currency)} on ${new Date(data.date).toLocaleDateString()}`}
                    />
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
                      <div className="bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                        <p className="font-medium">{formatPrice(data.price, data.currency)}</p>
                        <p className="text-gray-300">{data.shopName}</p>
                        <p className="text-gray-300">{new Date(data.date).toLocaleDateString()}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* X-axis labels */}
              <div className="flex justify-between text-xs text-muted-foreground mt-2">
                <span>
                  {new Date(priceHistory[0]?.date).toLocaleDateString()}
                </span>
                <span>
                  {new Date(priceHistory[priceHistory.length - 1]?.date).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {/* Recent Price Changes */}
          <div className="space-y-2">
            <p className="text-sm font-medium">Recent Price Changes</p>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {priceHistory.slice(-5).reverse().map((history, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-muted/30 rounded text-sm"
                >
                  <div>
                    <p className="font-medium">{history.shopName}</p>
                    <p className="text-muted-foreground">
                      {new Date(history.date).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">
                      {formatPrice(history.price, history.currency)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};