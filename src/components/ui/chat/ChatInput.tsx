import React, { useState, useRef, KeyboardEvent } from 'react';
import { Button } from '@/components/ui/base/button';
import { Textarea } from '@/components/ui/base/textarea';
import { 
  Send, 
  Gamepad2,
  <PERSON>rk<PERSON>,
  Mic,
  Paperclip
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = "Type your message...",
  className = ""
}) => {
  const [message, setMessage] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSend = () => {
    if (!message.trim() || disabled) return;

    onSendMessage(message.trim());
    setMessage('');
    
    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleQuickAction = (action: string) => {
    let quickMessage = '';
    
    switch (action) {
      case 'recommend':
        quickMessage = 'Can you recommend some games based on my preferences?';
        break;
      case 'discover':
        quickMessage = 'Help me discover some new and interesting games to play.';
        break;
      default:
        return;
    }
    
    setMessage(quickMessage);
    textareaRef.current?.focus();
  };

  // Auto-resize textarea
  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const textarea = e.target;
    setMessage(textarea.value);
    
    // Reset height to recalculate
    textarea.style.height = 'auto';
    
    // Set new height based on scroll height
    const newHeight = Math.min(textarea.scrollHeight, 120); // Max height of ~4 lines
    textarea.style.height = `${newHeight}px`;
  };

  return (
    <div className={cn("space-y-2", className)}>
      {/* Quick Actions */}
      <div className="flex gap-2 text-xs">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleQuickAction('recommend')}
          disabled={disabled}
          className="text-xs px-2 py-1 h-6"
        >
          <Sparkles className="h-3 w-3 mr-1" />
          Recommend
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleQuickAction('discover')}
          disabled={disabled}
          className="text-xs px-2 py-1 h-6"
        >
          <Gamepad2 className="h-3 w-3 mr-1" />
          Discover
        </Button>
      </div>

      {/* Main Input */}
      <div className="flex gap-2 items-end">
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={handleInput}
            onKeyDown={handleKeyDown}
            onCompositionStart={() => setIsComposing(true)}
            onCompositionEnd={() => setIsComposing(false)}
            placeholder={placeholder}
            disabled={disabled}
            className="min-h-[40px] max-h-[120px] resize-none pr-12 text-sm"
            style={{ height: '40px' }}
          />
          
          {/* Additional input actions */}
          <div className="absolute right-2 bottom-2 flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
              disabled={disabled}
            >
              <Paperclip className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
              disabled={disabled}
            >
              <Mic className="h-3 w-3" />
            </Button>
          </div>
        </div>

        <Button
          onClick={handleSend}
          disabled={!message.trim() || disabled}
          size="sm"
          className="h-10 px-3"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>

      {/* Input hints */}
      <div className="text-xs text-muted-foreground">
        <span>Press Enter to send, Shift+Enter for new line</span>
      </div>
    </div>
  );
};