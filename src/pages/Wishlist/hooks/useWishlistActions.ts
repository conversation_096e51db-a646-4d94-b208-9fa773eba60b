import { useCallback } from 'react';
import { useAddToLibrary, useRemoveFromCollection } from '@/hooks/useGameActions';
import { useMoveToLibrary } from '@/hooks/useMoveToLibrary';
import { Game } from '@/types';
import { UserGameWithDetails } from '@/types/database';
import { WishlistActions } from '../types';

export function useWishlistActions(): WishlistActions {
  const addToLibraryMutation = useAddToLibrary();
  const moveToLibraryMutation = useMoveToLibrary();
  const removeFromCollectionMutation = useRemoveFromCollection();

  // Convert UserGameWithDetails to Game format for mutations
  const convertToGame = useCallback((gameData: UserGameWithDetails): Game => {
    return {
      id: gameData.game?.id || gameData.game_id,
      title: gameData.game?.title || 'Unknown Game',
      platforms: gameData.game?.platforms,
      genres: gameData.game?.genres || [],
      developer: gameData.game?.developer,
      publisher: gameData.game?.publisher,
      release_date: gameData.game?.release_date,
      description: gameData.game?.description,
      cover_image: gameData.game?.cover_image,
      screenshots: gameData.game?.screenshots || [],
      youtube_links: gameData.game?.youtube_links || [],
      metacritic_score: gameData.game?.metacritic_score,
      igdb_id: gameData.game?.igdb_id,
    };
  }, []);

  // Add game to library while keeping it in wishlist
  const addToLibrary = useCallback((gameData: UserGameWithDetails) => {
    const game = convertToGame(gameData);
    addToLibraryMutation.mutate(game);
  }, [addToLibraryMutation, convertToGame]);

  // Move game from wishlist to library (removes from wishlist)
  const moveToLibrary = useCallback((userGameId: string, gameData: UserGameWithDetails) => {
    const game = convertToGame(gameData);
    moveToLibraryMutation.mutate({ userGameId, game });
  }, [moveToLibraryMutation, convertToGame]);

  // Remove game from wishlist completely
  const removeFromWishlist = useCallback((userGameId: string) => {
    removeFromCollectionMutation.mutate(userGameId);
  }, [removeFromCollectionMutation]);

  // Toggle price tracking for a game (placeholder - would need actual price tracking implementation)
  const togglePriceTracking = useCallback((userGameId: string) => {
    // This would need to be implemented with actual price tracking functionality
    console.log('Toggle price tracking for game:', userGameId);
    // TODO: Implement actual price tracking toggle
  }, []);

  return {
    addToLibrary,
    moveToLibrary,
    removeFromWishlist,
    togglePriceTracking
  };
}