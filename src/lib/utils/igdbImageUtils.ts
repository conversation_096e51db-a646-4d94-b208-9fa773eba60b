/**
 * IGDB Image size options
 */
export type IGDBImageSize = 
  | 't_thumb'           // 90x90 square
  | 't_cover_small'     // 90x128
  | 't_cover_big'       // 264x374
  | 't_cover_big_2x'    // 528x748
  | 't_screenshot_med'  // 569x320
  | 't_screenshot_big'  // 889x500
  | 't_screenshot_huge' // 1280x720

/**
 * Transform IGDB image URL to desired size
 * @param url - The original IGDB image URL (usually with t_thumb)
 * @param size - Desired image size
 * @returns Full HTTPS URL for the image
 */
export function getIGDBImageUrl(url: string | undefined, size: IGDBImageSize): string | undefined {
  if (!url) return undefined;
  
  // IGDB URLs come without protocol, add https:
  const fullUrl = url.startsWith('//') ? `https:${url}` : url;
  
  // Replace the size parameter
  return fullUrl.replace(/t_\w+/, size);
}

/**
 * Get cover image URL with appropriate size for different contexts
 */
export function getCoverImageUrl(url: string | undefined, context: 'thumbnail' | 'card' | 'detail' | 'hero'): string | undefined {
  if (!url) return undefined;
  
  const sizeMap: Record<typeof context, IGDBImageSize> = {
    thumbnail: 't_cover_small',   // Small thumbnails in lists
    card: 't_cover_big',          // Game cards
    detail: 't_cover_big_2x',     // Detail pages
    hero: 't_cover_big_2x'        // Hero sections
  };
  
  return getIGDBImageUrl(url, sizeMap[context]);
}

/**
 * Get screenshot URL with appropriate size
 */
export function getScreenshotUrl(url: string | undefined, size: 'medium' | 'large' | 'huge' = 'medium'): string | undefined {
  if (!url) return undefined;
  
  const sizeMap = {
    medium: 't_screenshot_med',
    large: 't_screenshot_big', 
    huge: 't_screenshot_huge'
  } as const;
  
  return getIGDBImageUrl(url, sizeMap[size]);
}