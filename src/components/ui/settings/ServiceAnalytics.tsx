/**
 * Service Analytics Component
 * Displays analytics and performance metrics for artwork API services
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Activity, AlertCircle, CheckCircle } from 'lucide-react';
import { ArtworkService } from '@/lib/apiKeyStore';

interface ServiceAnalyticsProps {
  statistics: Record<ArtworkService, {
    totalRequests: number;
    successfulRequests: number;
    averageResponseTime: number;
    availableKeys: number;
    isCurrentlyLimited: boolean;
  }> | null;
  queueStatus: {
    queueLength: number;
    activeRequests: number;
    processing: boolean;
  } | null;
}

const ServiceAnalytics: React.FC<ServiceAnalyticsProps> = ({ statistics, queueStatus }) => {
  if (!statistics && !queueStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-gray-500 py-8 flex flex-col items-center gap-2">
            <AlertCircle className="h-8 w-8 text-gray-400" />
            <p>Analytics data could not be loaded</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          System Analytics
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Queue Status */}
        {queueStatus ? (
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{queueStatus.queueLength}</div>
              <div className="text-sm text-gray-600">Queued Requests</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{queueStatus.activeRequests}</div>
              <div className="text-sm text-gray-600">Active Requests</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600 flex items-center justify-center gap-2">
                {queueStatus.processing ? (
                  <>
                    <CheckCircle className="h-5 w-5" />
                    Active
                  </>
                ) : (
                  'Idle'
                )}
              </div>
              <div className="text-sm text-gray-600">Processing Status</div>
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-500 py-4 mb-6">
            <p>Queue statistics unavailable</p>
          </div>
        )}

        {/* Service Performance */}
        {statistics ? (
          <div className="space-y-4">
            <h3 className="font-medium">Service Performance</h3>
            {Object.entries(statistics).map(([service, stats]) => {
              const hasActivity = stats.totalRequests > 0;
              
              return (
                <div key={service} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium capitalize">{service}</h4>
                    <div className="flex items-center gap-2">
                      {stats.isCurrentlyLimited ? (
                        <Badge variant="destructive" className="flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          Rate Limited
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" />
                          Available
                        </Badge>
                      )}
                      {!hasActivity && (
                        <Badge variant="outline" className="text-xs">
                          No Activity
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                    <div className="text-center">
                      <div className="font-medium text-blue-600">{stats.totalRequests || 0}</div>
                      <div className="text-gray-600">Total Requests</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-green-600">{stats.successfulRequests || 0}</div>
                      <div className="text-gray-600">Successful</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-purple-600">
                        {hasActivity ? `${Math.round(stats.averageResponseTime || 0)}ms` : '—'}
                      </div>
                      <div className="text-gray-600">Avg Response</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-orange-600">{stats.availableKeys || 0}</div>
                      <div className="text-gray-600">Available Keys</div>
                    </div>
                  </div>
                  
                  {hasActivity && (
                    <div className="mt-2 text-xs text-gray-500">
                      Success Rate: {stats.totalRequests > 0 
                        ? `${Math.round((stats.successfulRequests / stats.totalRequests) * 100)}%`
                        : 'N/A'
                      }
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">
            <p>Service statistics unavailable</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ServiceAnalytics;