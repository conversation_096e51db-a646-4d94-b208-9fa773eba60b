const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, content-type, apikey, x-client-info, x-api-key",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

// Response cache for performance optimization
const responseCache = new Map<string, { data: unknown; expiry: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

// Cache utility functions
function getCacheKey(endpoint: string, params: unknown, apiKey: string): string {
  const input = `${endpoint}:${JSON.stringify(params)}:${apiKey.substring(0, 8)}`;
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

function getCachedResponse(cacheKey: string): unknown | null {
  const cached = responseCache.get(cacheKey);
  if (!cached) return null;
  
  const now = Date.now();
  if (now > cached.expiry) {
    responseCache.delete(cacheKey);
    return null;
  }
  
  return cached.data;
}

function setCachedResponse(cacheKey: string, data: unknown): void {
  const expiry = Date.now() + CACHE_TTL;
  responseCache.set(cacheKey, { data, expiry });
  
  // Clean up expired entries if cache gets too large
  if (responseCache.size > 1000) {
    const now = Date.now();
    for (const [key, value] of responseCache.entries()) {
      if (now > value.expiry) {
        responseCache.delete(key);
      }
    }
  }
}

Deno.serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { endpoint, params } = await req.json();
    console.log("Received request for TheGamesDB endpoint:", endpoint);
    console.log("Request params:", params);

    if (!endpoint) {
      console.error("Endpoint is missing from the request body.");
      return new Response(JSON.stringify({ error: "Endpoint missing from request body" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const apiKey = req.headers.get("X-API-Key");
    console.log("API Key:", apiKey ? "Present" : "Missing");

    if (!apiKey) {
      console.error("Missing TheGamesDB API key");
      return new Response(JSON.stringify({ error: "Missing API key header" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Check cache first for performance
    const cacheKey = getCacheKey(endpoint, params, apiKey);
    const cachedData = getCachedResponse(cacheKey);
    
    if (cachedData) {
      console.log("✅ Cache hit for TheGamesDB query, returning cached response");
      return new Response(JSON.stringify(cachedData), {
        status: 200,
        headers: { 
          ...corsHeaders, 
          "Content-Type": "application/json",
          "X-Cache": "HIT"
        },
      });
    }

    // Build the request URL with parameters
    const url = new URL(`https://api.thegamesdb.net${endpoint}`);
    url.searchParams.append('apikey', apiKey);
    
    // Add other parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            url.searchParams.append(key, value.join(','));
          } else {
            url.searchParams.append(key, value.toString());
          }
        }
      });
    }

    console.log("Making request to TheGamesDB API:", url.toString());
    
    const tgdbResponse = await fetch(url.toString(), {
      method: "GET",
      headers: {
        "Accept": "application/json",
        "User-Agent": "Codexa-Game-Library/1.0"
      },
    });

    console.log("TheGamesDB Response status:", tgdbResponse.status);
    const responseText = await tgdbResponse.text();
    console.log("TheGamesDB Response data length:", responseText.length);
    console.log("TheGamesDB Response data sample:", responseText.substring(0, 200));

    if (!tgdbResponse.ok) {
      console.error("TheGamesDB API error:", responseText);
      
      // Handle specific error cases
      if (tgdbResponse.status === 403) {
        throw new Error("TheGamesDB API: Rate limit exceeded or invalid API key");
      } else if (tgdbResponse.status === 400) {
        throw new Error("TheGamesDB API: Bad request - check parameters");
      } else {
        throw new Error(`TheGamesDB API error: ${tgdbResponse.status} - ${responseText}`);
      }
    }

    // Parse the response as JSON to validate it
    let jsonData;
    try {
      jsonData = JSON.parse(responseText);
      console.log("Successfully parsed JSON response");
      console.log("Response code:", jsonData.code);
      console.log("Remaining allowance:", jsonData.remaining_monthly_allowance);
    } catch (parseError) {
      console.error("Failed to parse TheGamesDB response as JSON:", parseError);
      throw new Error(`Invalid JSON response from TheGamesDB: ${responseText}`);
    }

    // Check if TheGamesDB returned an error in the response
    if (jsonData.code !== 200) {
      console.error("TheGamesDB returned error code:", jsonData.code, jsonData.status);
      throw new Error(`TheGamesDB error: ${jsonData.status}`);
    }

    // Cache the successful response for future requests
    setCachedResponse(cacheKey, jsonData);
    console.log("✅ TheGamesDB response cached for future requests");

    return new Response(JSON.stringify(jsonData), {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json",
        "X-Cache": "MISS"
      },
    });
  } catch (error) {
    console.error("Edge function error:", error);
    return new Response(JSON.stringify({ 
      error: error.message,
      details: "Failed to fetch data from TheGamesDB API"
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});