/*
  # Add Platform Preferences Migration
  
  This migration adds support for:
  1. User preferred platforms array
  2. Auto-filter platforms setting
  3. Enhanced platform preference functionality
*/

-- Add new columns to user_preferences table
ALTER TABLE user_preferences 
ADD COLUMN IF NOT EXISTS preferred_platforms text[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS auto_filter_platforms boolean DEFAULT false;

-- Create index for better performance on platform preferences
CREATE INDEX IF NOT EXISTS idx_user_preferences_preferred_platforms ON user_preferences USING GIN(preferred_platforms);

-- Update the trigger function to handle new columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language plpgsql;

-- Ensure the trigger exists for user_preferences
DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON user_preferences;
CREATE TRIGGER update_user_preferences_updated_at
  BEFORE UPDATE ON user_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();