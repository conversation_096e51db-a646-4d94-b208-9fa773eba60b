import { <PERSON><PERSON>, But<PERSON> } from '@/components/ui/base';
import { Heart, Grid3X3, Gamepad2 } from 'lucide-react';

interface WishlistHeaderProps {
  gameCount: number;
  viewMode?: 'grid' | 'steam';
  onViewModeChange?: (mode: 'grid' | 'steam') => void;
}

export function WishlistHeader({ gameCount, viewMode = 'grid', onViewModeChange }: WishlistHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold">Wishlist</h1>
        <p className="text-muted-foreground">Games you want to play</p>
      </div>
      <div className="flex items-center gap-4">
        {onViewModeChange && (
          <div className="flex items-center gap-1 p-1 bg-muted rounded-lg">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('grid')}
              className="h-8 px-3"
            >
              <Grid3X3 className="h-4 w-4 mr-1" />
              Grid
            </Button>
            <Button
              variant={viewMode === 'steam' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('steam')}
              className="h-8 px-3"
            >
              <Gamepad2 className="h-4 w-4 mr-1" />
              Steam
            </Button>
          </div>
        )}
        <Badge variant="outline" className="bg-secondary/10 text-secondary-foreground">
          <Heart className="h-3 w-3 mr-1" />
          {gameCount} games
        </Badge>
      </div>
    </div>
  );
}