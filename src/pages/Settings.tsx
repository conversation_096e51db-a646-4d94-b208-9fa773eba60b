import { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button, Label, Badge, Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/base';
import { LoadingSpinner } from '@/components/ui/utils';
import { SteamImportCard, LibraryImportOverview, CSVImportCard, DataPortabilityOverview, ApiKeyManager, PlatformImportGrid } from '@/components/ui/import';
import ArtworkAPIKeyManager from '@/components/ui/settings/ArtworkAPIKeyManager';
import { SystemAnalyticsDashboard } from '@/components/ui/analytics/SystemAnalyticsDashboard';
import { useAuth } from '@/contexts/AuthContext';
import { useUserPreferences, UserPreferences } from '@/hooks/useUserPreferences';
import { 
  Settings as SettingsIcon, 
  Bell, 
  Database, 
  Palette,
  Save,
  AlertCircle,
  Gamepad2,
  Key,
  Layers,
  Grid3X3,
  Bar<PERSON>hart3
} from 'lucide-react';


export default function Settings() {
  const { user } = useAuth();
  const { 
    preferences, 
    isLoading, 
    error, 
    updatePreferences, 
    isUpdating 
  } = useUserPreferences();
  
  const [formData, setFormData] = useState<UserPreferences>({
    theme: 'system',
    default_platform: '',
    preferred_platforms: [],
    show_completed_games: true,
    enable_notifications: false,
    auto_backup: false,
    onboarding_completed: false,
    auto_filter_platforms: false,
  });
  const [hasChanges, setHasChanges] = useState(false);


  // Initialize form data when preferences load
  useEffect(() => {
    if (preferences) {
      setFormData(preferences);
    }
  }, [preferences]);

  const handleInputChange = (field: keyof UserPreferences, value: string | boolean | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };


  const handleSave = () => {
    updatePreferences(formData);
    setHasChanges(false);
    toast.success('Settings saved successfully');
  };


  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
            <p className="text-destructive">Failed to load settings</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-muted-foreground">Manage your application preferences and data</p>
        </div>
        <Badge variant="outline" className="bg-primary/10">
          <SettingsIcon className="h-3 w-3 mr-1" />
          Settings
        </Badge>
      </div>

      <Tabs defaultValue="preferences" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="preferences" className="flex items-center gap-2">
            <SettingsIcon className="h-4 w-4" />
            Preferences
          </TabsTrigger>
          <TabsTrigger value="apis" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            APIs
          </TabsTrigger>
          <TabsTrigger value="data" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Data
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="preferences" className="space-y-6 mt-6">
          <div className="grid gap-6">
        {/* Theme Settings */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Appearance
            </CardTitle>
            <CardDescription>Customize how the application looks</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="theme">Theme</Label>
              <select
                id="theme"
                value={formData.theme}
                onChange={(e) => handleInputChange('theme', e.target.value)}
                className="w-full p-2 border rounded-md bg-background"
              >
                <option value="system">System</option>
                <option value="light">Light</option>
                <option value="dark">Dark</option>
              </select>
            </div>
          </CardContent>
        </Card>

          {/* Notifications */}
          <Card>
            <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Notifications
            </CardTitle>
            <CardDescription>Control what notifications you receive</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="notifications"
                checked={formData.enable_notifications}
                onChange={(e) => handleInputChange('enable_notifications', e.target.checked)}
                className="rounded"
              />
              <Label htmlFor="notifications">Enable price drop notifications</Label>
            </div>
          </CardContent>
        </Card>

        {/* Data & Backup */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Data & Backup
            </CardTitle>
            <CardDescription>Manage your data and backup settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="auto_backup"
                checked={formData.auto_backup}
                onChange={(e) => handleInputChange('auto_backup', e.target.checked)}
                className="rounded"
              />
              <Label htmlFor="auto_backup">Enable automatic backup</Label>
            </div>
            
            <div className="pt-4 border-t">
              <p className="text-sm text-muted-foreground mb-2">Account Information</p>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Email:</span>
                  <span className="text-sm font-medium">{user?.email}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Account Created:</span>
                  <span className="text-sm">
                    {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        </div>

        {/* Gaming Preferences */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gamepad2 className="h-4 w-4" />
              Gaming Preferences
            </CardTitle>
            <CardDescription>Set your gaming preferences and library display options</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="show_completed"
                  checked={formData.show_completed_games}
                  onChange={(e) => handleInputChange('show_completed_games', e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="show_completed">Show completed games in library</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="auto_filter_platforms"
                  checked={formData.auto_filter_platforms}
                  onChange={(e) => handleInputChange('auto_filter_platforms', e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="auto_filter_platforms">Auto-filter by preferred platforms</Label>
              </div>
            </div>
            
            <div className="space-y-3">
              <Label className="text-sm font-medium">Default Library View</Label>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
                  <Button
                    variant={formData.default_platform === 'status' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleInputChange('default_platform', 'status')}
                    className="h-8 text-xs"
                  >
                    <Grid3X3 className="h-3 w-3 mr-1" />
                    Status View
                  </Button>
                  <Button
                    variant={formData.default_platform === 'platform' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleInputChange('default_platform', 'platform')}
                    className="h-8 text-xs"
                  >
                    <Layers className="h-3 w-3 mr-1" />
                    Platform View
                  </Button>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                Choose how your library is organized by default: by game status (playing, completed, backlog) or by platform (PC, PlayStation, Xbox, etc.)
              </p>
            </div>
            
            <div className="p-4 bg-muted/10 rounded-lg border border-border/50">
              <p className="text-sm text-muted-foreground">
                <strong>Platform Organization:</strong> The library now supports platform-based organization! 
                Toggle between Status and Platform views in your Library, or set your default preference above.
              </p>
            </div>
          </CardContent>
        </Card>
          </div>

          {/* Save Button */}
          {hasChanges && (
            <Card className="border-primary/20 bg-primary/5">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-primary" />
                    <span className="text-sm text-primary">You have unsaved changes</span>
                  </div>
                  <Button 
                    onClick={handleSave}
                    disabled={isUpdating}
                    className="min-w-[100px]"
                  >
                    {isUpdating ? (
                      <LoadingSpinner className="h-4 w-4" />
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="apis" className="space-y-6 mt-6">
          <div className="space-y-6">
            <ApiKeyManager />
            <ArtworkAPIKeyManager />
          </div>
        </TabsContent>

        <TabsContent value="data" className="space-y-6 mt-6">
          <div className="space-y-6">
            <PlatformImportGrid />
            <div className="grid gap-6 lg:grid-cols-2">
              <SteamImportCard />
              <LibraryImportOverview />
            </div>
            <div className="grid gap-6 lg:grid-cols-2">
              <CSVImportCard />
              <DataPortabilityOverview />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6 mt-6">
          <SystemAnalyticsDashboard />
        </TabsContent>
      </Tabs>
    </div>
  );
}