import { Button, Input } from '@/components/ui/base';
import { SortOptionsComponent } from '@/components/ui/filters';
import { Search } from '@/lib/icons';
import { SearchControlsProps } from '../types';

export function SearchControls({
  searchQuery,
  onSearchChange,
  sortOptions,
  onSortChange,
  onClearSearch
}: SearchControlsProps) {
  return (
    <div className="flex items-center justify-between pt-4">
      <div className="flex items-center gap-4 flex-1">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder="Search your library..."
            className="pl-10"
          />
        </div>
        {searchQuery && (
          <Button
            variant="outline"
            size="sm"
            onClick={onClearSearch}
            className="shrink-0"
          >
            Clear
          </Button>
        )}
      </div>
      <SortOptionsComponent
        sortOptions={sortOptions}
        onSortChange={onSortChange}
        showAddedAt={true}
      />
    </div>
  );
}