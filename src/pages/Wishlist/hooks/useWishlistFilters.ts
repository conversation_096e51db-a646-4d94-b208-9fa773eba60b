import { useState, useMemo, useCallback } from 'react';
import { UserGameWithDetails } from '@/types/database';
import { WishlistFilters, WishlistSettings } from '../types';
import { SortOptions } from '@/components/ui/filters';
import { sortUserGames } from '@/lib/sortUtils';

export function useWishlistFilters(wishlistGames: UserGameWithDetails[]) {
  const [filters, setFilters] = useState<WishlistFilters>({});
  const [settings, setSettings] = useState<WishlistSettings>({
    sortOptions: {
      field: 'added_at',
      direction: 'desc'
    },
    searchQuery: '',
    showPriceTracking: false
  });

  // Filter games based on search query
  const searchFilteredGames = useMemo(() => {
    if (!settings.searchQuery.trim()) return wishlistGames;
    
    return wishlistGames.filter((game: UserGameWithDetails) => {
      const title = game?.game?.title?.toLowerCase() || '';
      const developer = game?.game?.developer?.toLowerCase() || '';
      const publisher = game?.game?.publisher?.toLowerCase() || '';
      const genres = game?.game?.genres?.join(' ').toLowerCase() || '';
      const query = settings.searchQuery.toLowerCase();
      
      return title.includes(query) || 
             developer.includes(query) || 
             publisher.includes(query) || 
             genres.includes(query);
    });
  }, [wishlistGames, settings.searchQuery]);

  // Apply additional filters
  const filteredGames = useMemo(() => {
    let filtered = searchFilteredGames;

    if (filters.genre) {
      filtered = filtered.filter(game => 
        game.game?.genres?.includes(filters.genre!)
      );
    }

    if (filters.developer) {
      filtered = filtered.filter(game => 
        game.game?.developer?.toLowerCase().includes(filters.developer!.toLowerCase())
      );
    }

    if (filters.publisher) {
      filtered = filtered.filter(game => 
        game.game?.publisher?.toLowerCase().includes(filters.publisher!.toLowerCase())
      );
    }

    if (filters.minMetacriticScore) {
      filtered = filtered.filter(game => 
        (game.game?.metacritic_score || 0) >= filters.minMetacriticScore!
      );
    }

    if (filters.priceTrackingEnabled !== undefined) {
      filtered = filtered.filter(game => 
        Boolean(game.price_tracking_enabled) === filters.priceTrackingEnabled
      );
    }

    return filtered;
  }, [searchFilteredGames, filters]);

  // Sort games based on current sort options
  const sortedAndFilteredGames = useMemo(() => {
    const sortOptions: SortOptions = {
      field: settings.sortOptions.field,
      direction: settings.sortOptions.direction
    };
    return sortUserGames(filteredGames, sortOptions);
  }, [filteredGames, settings.sortOptions]);

  // Update search query
  const updateSearchQuery = useCallback((query: string) => {
    setSettings(prev => ({
      ...prev,
      searchQuery: query
    }));
  }, []);

  // Update sort options
  const updateSortOptions = useCallback((sortOptions: { field: string; direction: 'asc' | 'desc' }) => {
    setSettings(prev => ({
      ...prev,
      sortOptions
    }));
  }, []);

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<WishlistFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSettings(prev => ({
      ...prev,
      searchQuery: ''
    }));
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  // Toggle price tracking view
  const togglePriceTrackingView = useCallback(() => {
    setSettings(prev => ({
      ...prev,
      showPriceTracking: !prev.showPriceTracking
    }));
  }, []);

  return {
    filteredGames: sortedAndFilteredGames,
    filters,
    settings,
    updateSearchQuery,
    updateSortOptions,
    updateFilters,
    clearSearch,
    clearFilters,
    togglePriceTrackingView,
    hasActiveFilters: Object.keys(filters).length > 0 || settings.searchQuery.trim().length > 0
  };
}