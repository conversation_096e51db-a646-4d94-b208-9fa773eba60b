import { GameResults } from '@/components/ui/game';
import { SortOptionsComponent, SortOptions } from '@/components/ui/filters';
import { StatsBar } from '@/components/ui/analytics';
import { Game } from '@/types';
import { useEffect } from 'react';
import { extractPlatformsFromGames } from '@/lib/utils/platformMapper';

interface SearchResultsProps {
  games: Game[];
  allGames: Game[];
  loading: boolean;
  error: string | null;
  sortOptions: SortOptions;
  onSortChange: (options: SortOptions) => void;
  hasActiveFilters: boolean;
  onAvailablePlatformsChange?: (platforms: string[]) => void;
}

export function SearchResults({ 
  games, 
  allGames, 
  loading, 
  error, 
  sortOptions, 
  onSortChange, 
  hasActiveFilters,
  onAvailablePlatformsChange
}: SearchResultsProps) {
  // Extract available platforms from current search results
  useEffect(() => {
    if (onAvailablePlatformsChange && allGames.length > 0) {
      const availablePlatforms = extractPlatformsFromGames(allGames);
      onAvailablePlatformsChange(availablePlatforms);
    }
  }, [allGames, onAvailablePlatformsChange]);
  return (
    <>
      {/* Statistics */}
      <StatsBar games={games} />

      {/* Results Header */}
      {allGames.length > 0 && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold">Search Results</h2>
            <p className="text-sm text-muted-foreground">
              {hasActiveFilters 
                ? `Showing ${games.length} filtered games` 
                : `Showing ${games.length} games from IGDB`
              }
            </p>
          </div>
          <SortOptionsComponent
            sortOptions={sortOptions}
            onSortChange={onSortChange}
          />
        </div>
      )}

      {/* Game Results */}
      <GameResults
        games={games}
        loading={loading}
        error={error}
      />
    </>
  );
}