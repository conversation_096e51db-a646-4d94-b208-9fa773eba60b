import { useMemo, useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base';
import { Button } from '@/components/ui/base/button';
import { SteamLibraryCard } from '@/components/ui/game/steam-library-card';
import { SearchControls } from '../SearchControls';

import { ErrorBoundaryWrapper } from '@/components/ui/error-boundary';
import { Search, Gamepad2 } from '@/lib/icons';
import { LibraryTabProps } from '../../types';
import { UserGameWithDetails } from '@/types/database';
import { sortUserGames } from '@/lib/sortUtils';

export function LibraryTab({
  libraryGames,
  searchQuery,
  setSearchQuery,
  sortOptions,
  setSortOptions,
  onGameClick,
  onStatusUpdate,
  onRemoveGame
}: Omit<LibraryTabProps, 'viewMode' | 'collapsedPlatforms' | 'onPlatformToggle' | 'collapsedFamilies' | 'onFamilyToggle'>) {

  // Bulk operations state


  // Filter games based on search query
  const filteredLibraryGames = useMemo(() => {
    if (!searchQuery.trim()) return libraryGames;
    
    return libraryGames.filter((game: unknown) => {
      const gameData = game as { game?: { title?: string; developer?: string; publisher?: string; genres?: string[] } };
      const title = gameData?.game?.title?.toLowerCase() || '';
      const developer = gameData?.game?.developer?.toLowerCase() || '';
      const publisher = gameData?.game?.publisher?.toLowerCase() || '';
      const genres = gameData?.game?.genres?.join(' ').toLowerCase() || '';
      const query = searchQuery.toLowerCase();
      
      return title.includes(query) || 
             developer.includes(query) || 
             publisher.includes(query) || 
             genres.includes(query);
    });
  }, [libraryGames, searchQuery]);
  
  // Sort games based on current sort options
  const sortedLibraryGames = useMemo(() => {
    return sortUserGames(filteredLibraryGames, sortOptions);
  }, [filteredLibraryGames, sortOptions]);





  return (
    <div className="space-y-6">
      {/* Search and Sort Controls */}
      {libraryGames.length > 0 && (
        <Card>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Your Collection</CardTitle>
                <CardDescription>Games you've added to your library</CardDescription>
              </div>
              <div className="flex items-center gap-2">
              </div>
            </div>
            <SearchControls
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              sortOptions={sortOptions}
              onSortChange={setSortOptions}
              onClearSearch={() => setSearchQuery('')}
            />

          </CardHeader>
        </Card>
      )}

      {/* Library Content */}
      {libraryGames.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Gamepad2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">Your library is empty</p>
              <p className="text-sm text-muted-foreground mt-2">
                Search for games and add them to your collection
              </p>
            </div>
          </CardContent>
        </Card>
      ) : sortedLibraryGames.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">No games found</p>
              <p className="text-sm text-muted-foreground mt-2">
                Try adjusting your search terms
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <ErrorBoundaryWrapper name="Steam Library Grid">
          <Card>
            <CardContent className="pt-6">
              <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6">
                {sortedLibraryGames.map((userGame: unknown) => {
                  const gameData = userGame as UserGameWithDetails;
                  return (
                    <ErrorBoundaryWrapper key={gameData.id} name="Steam Game Card">
                      <SteamLibraryCard
                        gameData={gameData}
                        onGameClick={onGameClick}
                        onStatusUpdate={onStatusUpdate}
                        onQuickPlay={(gameData) => {
                          console.log('Quick play:', gameData.game.title);
                          // Add quick play functionality here
                        }}
                        showHoverDetails={true}
                        enableQuickActions={true}
                        showSteamHoverCard={true}
                      />
                    </ErrorBoundaryWrapper>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </ErrorBoundaryWrapper>
      )}
    </div>
  );
}