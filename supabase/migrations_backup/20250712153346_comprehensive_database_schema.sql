/*
  Comprehensive Database Migration for Bolt Import
  
  This single migration file contains all the necessary database schema
  for the Game Collection web application to be imported into Bolt.
  
  It includes:
  1. Core game tables (games, user_games, price_tracking)
  2. User system (profiles, preferences) 
  3. AI conversations table
  4. Price alerts and collection stats
  5. All indexes, constraints, and RLS policies
  6. Storage buckets and policies
  7. Triggers and functions
*/

-- ============================================================================
-- Core Tables
-- ============================================================================

-- Create games table
CREATE TABLE IF NOT EXISTS games (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  platform text NOT NULL CHECK (platform IN (
    'PC', 
    'PlayStation 4', 
    'PlayStation 5', 
    'PlayStation 3', 
    'PlayStation 2', 
    'PlayStation',
    'Xbox Series X/S', 
    'Xbox One', 
    'Xbox 360', 
    'Xbox',
    'Nintendo Switch', 
    'Nintendo 3DS', 
    'Nintendo DS', 
    'Wii U', 
    'Wii', 
    'GameCube',
    'iOS', 
    'Android', 
    'Steam Deck',
    'Mac'
  )),
  genres text[] DEFAULT '{}',
  developer text,
  publisher text,
  release_date date,
  description text,
  cover_image text,
  screenshots text[] DEFAULT '{}',
  youtube_links text[] DEFAULT '{}',
  metacritic_score integer CHECK (metacritic_score >= 0 AND metacritic_score <= 100),
  igdb_id text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create user_games table (junction table for user's game collection)
CREATE TABLE IF NOT EXISTS user_games (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  game_id uuid REFERENCES games(id) ON DELETE CASCADE NOT NULL,
  status text NOT NULL DEFAULT 'backlog' CHECK (status IN ('playing', 'completed', 'backlog', 'wishlist')),
  personal_rating integer CHECK (personal_rating >= 1 AND personal_rating <= 5),
  personal_notes text,
  hours_played decimal CHECK (hours_played >= 0),
  date_added timestamptz DEFAULT now(),
  date_completed timestamptz,
  is_wishlist boolean DEFAULT false,
  UNIQUE(user_id, game_id)
);

-- Create price_tracking table with all required columns
CREATE TABLE IF NOT EXISTS price_tracking (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  game_id uuid REFERENCES games(id) ON DELETE CASCADE NOT NULL,
  store_name text NOT NULL,
  url text,
  price decimal NOT NULL CHECK (price >= 0),
  original_price decimal,
  currency text NOT NULL DEFAULT 'USD',
  is_on_sale boolean DEFAULT false,
  discount_percentage integer,
  last_updated timestamptz DEFAULT now(),
  CONSTRAINT check_original_price_positive CHECK (original_price IS NULL OR original_price >= 0),
  CONSTRAINT check_discount_percentage_valid CHECK (discount_percentage IS NULL OR (discount_percentage >= 0 AND discount_percentage <= 100))
);

-- ============================================================================
-- User System Tables
-- ============================================================================

-- Create user_profiles table to extend auth.users
CREATE TABLE IF NOT EXISTS user_profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username text UNIQUE,
  display_name text,
  avatar_url text,
  bio text,
  location text,
  website text,
  social_links jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create user_preferences table for app settings
CREATE TABLE IF NOT EXISTS user_preferences (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  theme text DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
  default_platform text,
  show_completed_games boolean DEFAULT true,
  enable_notifications boolean DEFAULT false,
  auto_backup boolean DEFAULT false,
  onboarding_completed boolean DEFAULT false,
  notifications jsonb DEFAULT '{"price_alerts": true, "new_deals": true, "game_updates": true, "newsletter": false}',
  privacy jsonb DEFAULT '{"profile_public": true, "library_public": false, "activity_public": false}',
  display jsonb DEFAULT '{"games_per_page": 20, "default_sort": "date_added", "show_metacritic": true, "show_screenshots": true}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- ============================================================================
-- AI and Analytics Tables
-- ============================================================================

-- Create AI conversations table for chat persistence
CREATE TABLE IF NOT EXISTS ai_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL DEFAULT 'New Chat',
  messages JSONB NOT NULL DEFAULT '[]'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create price_alerts table for user price tracking alerts
CREATE TABLE IF NOT EXISTS price_alerts (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  game_id uuid NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  target_price decimal(10,2) NOT NULL,
  currency varchar(3) NOT NULL DEFAULT 'USD',
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT check_target_price_positive CHECK (target_price > 0)
);

-- Create user_collection_stats table for cached user statistics
CREATE TABLE IF NOT EXISTS user_collection_stats (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  total_games integer NOT NULL DEFAULT 0,
  total_value decimal(12,2) NOT NULL DEFAULT 0,
  average_rating decimal(3,2),
  most_played_genre varchar(100),
  completion_rate decimal(5,2) NOT NULL DEFAULT 0,
  last_calculated timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  UNIQUE(user_id),
  CONSTRAINT check_total_games_non_negative CHECK (total_games >= 0),
  CONSTRAINT check_completion_rate_valid CHECK (completion_rate >= 0 AND completion_rate <= 100)
);

-- ============================================================================
-- Indexes for Performance
-- ============================================================================

-- Games table indexes
CREATE INDEX IF NOT EXISTS idx_games_title ON games(title);
CREATE INDEX IF NOT EXISTS idx_games_platform ON games(platform);
CREATE INDEX IF NOT EXISTS idx_games_genres ON games USING GIN(genres);
CREATE INDEX IF NOT EXISTS idx_games_igdb_id ON games(igdb_id);
CREATE INDEX IF NOT EXISTS idx_games_release_date ON games(release_date);
CREATE INDEX IF NOT EXISTS idx_games_metacritic_score ON games(metacritic_score);

-- User games table indexes
CREATE INDEX IF NOT EXISTS idx_user_games_user_id ON user_games(user_id);
CREATE INDEX IF NOT EXISTS idx_user_games_game_id ON user_games(game_id);
CREATE INDEX IF NOT EXISTS idx_user_games_status ON user_games(status);
CREATE INDEX IF NOT EXISTS idx_user_games_date_added ON user_games(date_added);
CREATE INDEX IF NOT EXISTS idx_user_games_personal_rating ON user_games(personal_rating);
CREATE INDEX IF NOT EXISTS idx_user_games_is_wishlist ON user_games(is_wishlist);

-- Composite indexes for complex queries
CREATE INDEX IF NOT EXISTS idx_user_games_user_status ON user_games(user_id, status);
CREATE INDEX IF NOT EXISTS idx_user_games_user_date_added ON user_games(user_id, date_added DESC);
CREATE INDEX IF NOT EXISTS idx_user_games_user_rating ON user_games(user_id, personal_rating DESC);

-- Price tracking indexes
CREATE INDEX IF NOT EXISTS idx_price_tracking_game_id ON price_tracking(game_id);
CREATE INDEX IF NOT EXISTS idx_price_tracking_store_name ON price_tracking(store_name);
CREATE INDEX IF NOT EXISTS idx_price_tracking_is_on_sale ON price_tracking(is_on_sale);
CREATE INDEX IF NOT EXISTS idx_price_tracking_last_updated ON price_tracking(last_updated);
CREATE INDEX IF NOT EXISTS idx_price_tracking_game_store ON price_tracking(game_id, store_name);

-- User profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);
CREATE INDEX IF NOT EXISTS idx_user_profiles_display_name ON user_profiles(display_name);

-- User preferences indexes
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_theme ON user_preferences(theme);

-- AI conversations indexes
CREATE INDEX IF NOT EXISTS idx_ai_conversations_user_id ON ai_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_conversations_updated_at ON ai_conversations(updated_at DESC);

-- Price alerts indexes
CREATE INDEX IF NOT EXISTS idx_price_alerts_user_id ON price_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_price_alerts_game_id ON price_alerts(game_id);
CREATE INDEX IF NOT EXISTS idx_price_alerts_is_active ON price_alerts(is_active);
CREATE INDEX IF NOT EXISTS idx_price_alerts_user_active ON price_alerts(user_id, is_active);

-- Collection stats indexes
CREATE INDEX IF NOT EXISTS idx_user_collection_stats_user_id ON user_collection_stats(user_id);

-- ============================================================================
-- Functions and Triggers
-- ============================================================================

-- Updated_at timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language plpgsql;

-- Trigger for games table
DROP TRIGGER IF EXISTS update_games_updated_at ON games;
CREATE TRIGGER update_games_updated_at
  BEFORE UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Trigger for user_profiles table
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Trigger for user_preferences table
DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON user_preferences;
CREATE TRIGGER update_user_preferences_updated_at
  BEFORE UPDATE ON user_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- AI conversations updated_at trigger
CREATE OR REPLACE FUNCTION update_ai_conversations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_ai_conversations_updated_at ON ai_conversations;
CREATE TRIGGER update_ai_conversations_updated_at
    BEFORE UPDATE ON ai_conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_ai_conversations_updated_at();

-- Price alerts updated_at trigger
DROP TRIGGER IF EXISTS update_price_alerts_updated_at ON price_alerts;
CREATE TRIGGER update_price_alerts_updated_at
  BEFORE UPDATE ON price_alerts
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Collection stats updated_at trigger
DROP TRIGGER IF EXISTS update_user_collection_stats_updated_at ON user_collection_stats;
CREATE TRIGGER update_user_collection_stats_updated_at
  BEFORE UPDATE ON user_collection_stats
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Discount calculation function
CREATE OR REPLACE FUNCTION calculate_discount_percentage()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.original_price IS NOT NULL AND NEW.original_price > 0 AND NEW.price < NEW.original_price THEN
    NEW.discount_percentage = ROUND(((NEW.original_price - NEW.price) / NEW.original_price * 100)::numeric, 0)::integer;
    NEW.is_on_sale = true;
  ELSE
    NEW.discount_percentage = NULL;
    NEW.is_on_sale = false;
  END IF;
  
  RETURN NEW;
END;
$$ language plpgsql;

-- Trigger for automatic discount calculation
DROP TRIGGER IF EXISTS calculate_discount_on_price_update ON price_tracking;
CREATE TRIGGER calculate_discount_on_price_update
  BEFORE INSERT OR UPDATE ON price_tracking
  FOR EACH ROW
  EXECUTE FUNCTION calculate_discount_percentage();

-- User creation function with error handling
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Insert user profile with error handling
  BEGIN
    INSERT INTO user_profiles (id, username, display_name)
    VALUES (
      new.id,
      new.raw_user_meta_data->>'username',
      COALESCE(
        new.raw_user_meta_data->>'display_name', 
        new.raw_user_meta_data->>'username', 
        split_part(new.email, '@', 1)
      )
    );
  EXCEPTION WHEN OTHERS THEN
    -- Log error but don't fail the user creation
    RAISE WARNING 'Failed to create user profile for user %: %', new.id, SQLERRM;
  END;
  
  -- Insert user preferences with error handling
  BEGIN
    INSERT INTO user_preferences (user_id)
    VALUES (new.id);
  EXCEPTION WHEN OTHERS THEN
    -- Log error but don't fail the user creation
    RAISE WARNING 'Failed to create user preferences for user %: %', new.id, SQLERRM;
  END;
  
  RETURN new;
END;
$$ language plpgsql security definer;

-- User creation trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- ============================================================================
-- Row Level Security (RLS) Setup
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE games ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_games ENABLE ROW LEVEL SECURITY;
ALTER TABLE price_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE price_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_collection_stats ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- RLS Policies
-- ============================================================================

-- Games policies (readable by all authenticated users, writable by authenticated users)
DROP POLICY IF EXISTS "Games are readable by authenticated users" ON games;
CREATE POLICY "Games are readable by authenticated users"
  ON games
  FOR SELECT
  TO authenticated
  USING (true);

DROP POLICY IF EXISTS "Authenticated users can create games" ON games;
CREATE POLICY "Authenticated users can create games"
  ON games
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

DROP POLICY IF EXISTS "Authenticated users can update games" ON games;
CREATE POLICY "Authenticated users can update games"
  ON games
  FOR UPDATE
  TO authenticated
  USING (true);

-- User games policies (users can only see and modify their own entries)
DROP POLICY IF EXISTS "Users can view their own game entries" ON user_games;
CREATE POLICY "Users can view their own game entries"
  ON user_games
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own game entries" ON user_games;
CREATE POLICY "Users can insert their own game entries"
  ON user_games
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own game entries" ON user_games;
CREATE POLICY "Users can update their own game entries"
  ON user_games
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own game entries" ON user_games;
CREATE POLICY "Users can delete their own game entries"
  ON user_games
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Price tracking policies
DROP POLICY IF EXISTS "Price tracking is readable by authenticated users" ON price_tracking;
CREATE POLICY "Price tracking is readable by authenticated users"
  ON price_tracking
  FOR SELECT
  TO authenticated
  USING (true);

DROP POLICY IF EXISTS "Authenticated users can insert price tracking" ON price_tracking;
CREATE POLICY "Authenticated users can insert price tracking"
  ON price_tracking
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

DROP POLICY IF EXISTS "Authenticated users can update price tracking" ON price_tracking;
CREATE POLICY "Authenticated users can update price tracking"
  ON price_tracking
  FOR UPDATE
  TO authenticated
  USING (true);

-- User profiles policies
DROP POLICY IF EXISTS "Users can view all profiles for public info" ON user_profiles;
CREATE POLICY "Users can view all profiles for public info"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (true);

DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
CREATE POLICY "Users can update their own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;
CREATE POLICY "Users can insert their own profile"
  ON user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- User preferences policies
DROP POLICY IF EXISTS "Users can view their own preferences" ON user_preferences;
CREATE POLICY "Users can view their own preferences"
  ON user_preferences
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own preferences" ON user_preferences;
CREATE POLICY "Users can update their own preferences"
  ON user_preferences
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own preferences" ON user_preferences;
CREATE POLICY "Users can insert their own preferences"
  ON user_preferences
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- AI conversations policies
DROP POLICY IF EXISTS "Users can view their own conversations" ON ai_conversations;
CREATE POLICY "Users can view their own conversations" ON ai_conversations
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own conversations" ON ai_conversations;
CREATE POLICY "Users can insert their own conversations" ON ai_conversations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own conversations" ON ai_conversations;
CREATE POLICY "Users can update their own conversations" ON ai_conversations
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own conversations" ON ai_conversations;
CREATE POLICY "Users can delete their own conversations" ON ai_conversations
    FOR DELETE USING (auth.uid() = user_id);

-- Price alerts policies
DROP POLICY IF EXISTS "Users can view their own price alerts" ON price_alerts;
CREATE POLICY "Users can view their own price alerts" ON price_alerts
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own price alerts" ON price_alerts;
CREATE POLICY "Users can insert their own price alerts" ON price_alerts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own price alerts" ON price_alerts;
CREATE POLICY "Users can update their own price alerts" ON price_alerts
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own price alerts" ON price_alerts;
CREATE POLICY "Users can delete their own price alerts" ON price_alerts
  FOR DELETE USING (auth.uid() = user_id);

-- Collection stats policies
DROP POLICY IF EXISTS "Users can view their own collection stats" ON user_collection_stats;
CREATE POLICY "Users can view their own collection stats" ON user_collection_stats
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own collection stats" ON user_collection_stats;
CREATE POLICY "Users can insert their own collection stats" ON user_collection_stats
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own collection stats" ON user_collection_stats;
CREATE POLICY "Users can update their own collection stats" ON user_collection_stats
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own collection stats" ON user_collection_stats;
CREATE POLICY "Users can delete their own collection stats" ON user_collection_stats
  FOR DELETE USING (auth.uid() = user_id);

-- ============================================================================
-- Storage Buckets and Policies
-- ============================================================================

-- Create storage buckets if they don't exist
INSERT INTO storage.buckets (id, name, public) 
VALUES 
  ('game-covers', 'game-covers', true),
  ('game-screenshots', 'game-screenshots', true),
  ('user-avatars', 'user-avatars', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for game assets
DROP POLICY IF EXISTS "Game covers are publicly accessible" ON storage.objects;
CREATE POLICY "Game covers are publicly accessible"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'game-covers');

DROP POLICY IF EXISTS "Authenticated users can upload game covers" ON storage.objects;
CREATE POLICY "Authenticated users can upload game covers"
  ON storage.objects FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'game-covers');

DROP POLICY IF EXISTS "Authenticated users can update game covers" ON storage.objects;
CREATE POLICY "Authenticated users can update game covers"
  ON storage.objects FOR UPDATE
  TO authenticated
  USING (bucket_id = 'game-covers');

DROP POLICY IF EXISTS "Game screenshots are publicly accessible" ON storage.objects;
CREATE POLICY "Game screenshots are publicly accessible"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'game-screenshots');

DROP POLICY IF EXISTS "Authenticated users can upload game screenshots" ON storage.objects;
CREATE POLICY "Authenticated users can upload game screenshots"
  ON storage.objects FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'game-screenshots');

-- Storage policies for user avatars
DROP POLICY IF EXISTS "User avatars are publicly accessible" ON storage.objects;
CREATE POLICY "User avatars are publicly accessible"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'user-avatars');

DROP POLICY IF EXISTS "Users can upload their own avatar" ON storage.objects;
CREATE POLICY "Users can upload their own avatar"
  ON storage.objects FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'user-avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

DROP POLICY IF EXISTS "Users can update their own avatar" ON storage.objects;
CREATE POLICY "Users can update their own avatar"
  ON storage.objects FOR UPDATE
  TO authenticated
  USING (bucket_id = 'user-avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

DROP POLICY IF EXISTS "Users can delete their own avatar" ON storage.objects;
CREATE POLICY "Users can delete their own avatar"
  ON storage.objects FOR DELETE
  TO authenticated
  USING (bucket_id = 'user-avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

-- ============================================================================
-- Views for Common Queries
-- ============================================================================

-- User games with details view
DROP VIEW IF EXISTS user_games_with_details;
CREATE OR REPLACE VIEW user_games_with_details AS
SELECT 
  ug.*,
  g.title,
  g.cover_image,
  g.platform,
  g.genres,
  g.developer,
  g.publisher,
  g.release_date,
  g.metacritic_score
FROM user_games ug
JOIN games g ON ug.game_id = g.id;

-- Price alerts with games view
DROP VIEW IF EXISTS price_alerts_with_games;
CREATE OR REPLACE VIEW price_alerts_with_games AS
SELECT 
  pa.*,
  g.title,
  g.cover_image,
  g.platform
FROM price_alerts pa
JOIN games g ON pa.game_id = g.id;

-- User collection stats view
DROP VIEW IF EXISTS user_collection_stats_view;
CREATE OR REPLACE VIEW user_collection_stats_view AS
SELECT 
  user_id,
  COUNT(*) FILTER (WHERE NOT is_wishlist) as total_games,
  COUNT(*) FILTER (WHERE status = 'completed' AND NOT is_wishlist) as completed_games,
  COUNT(*) FILTER (WHERE status = 'playing' AND NOT is_wishlist) as currently_playing,
  COUNT(*) FILTER (WHERE is_wishlist) as wishlist_count,
  COALESCE(SUM(hours_played) FILTER (WHERE NOT is_wishlist), 0) as total_hours,
  COALESCE(AVG(personal_rating) FILTER (WHERE personal_rating IS NOT NULL AND NOT is_wishlist), 0) as average_rating
FROM user_games
GROUP BY user_id;

-- ============================================================================
-- Grant Permissions
-- ============================================================================

-- Grant permissions on views
GRANT SELECT ON user_games_with_details TO authenticated;
GRANT SELECT ON price_alerts_with_games TO authenticated;
GRANT SELECT ON user_collection_stats_view TO authenticated;

-- Grant usage permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON user_profiles TO authenticated;
GRANT ALL ON user_preferences TO authenticated;

-- ============================================================================
-- Utility Functions
-- ============================================================================

-- Function to create missing user profiles for existing users
CREATE OR REPLACE FUNCTION create_missing_user_profiles()
RETURNS void AS $$
DECLARE
  user_record RECORD;
BEGIN
  FOR user_record IN 
    SELECT id, email, raw_user_meta_data 
    FROM auth.users 
    WHERE id NOT IN (SELECT id FROM user_profiles)
  LOOP
    BEGIN
      INSERT INTO user_profiles (id, username, display_name)
      VALUES (
        user_record.id,
        user_record.raw_user_meta_data->>'username',
        COALESCE(
          user_record.raw_user_meta_data->>'display_name', 
          user_record.raw_user_meta_data->>'username', 
          split_part(user_record.email, '@', 1)
        )
      );
    EXCEPTION WHEN OTHERS THEN
      RAISE WARNING 'Failed to create profile for existing user %: %', user_record.id, SQLERRM;
    END;
  END LOOP;
  
  FOR user_record IN 
    SELECT id 
    FROM auth.users 
    WHERE id NOT IN (SELECT user_id FROM user_preferences)
  LOOP
    BEGIN
      INSERT INTO user_preferences (user_id)
      VALUES (user_record.id);
    EXCEPTION WHEN OTHERS THEN
      RAISE WARNING 'Failed to create preferences for existing user %: %', user_record.id, SQLERRM;
    END;
  END LOOP;
END;
$$ language plpgsql security definer;

-- Run the function to create missing profiles for any existing users
SELECT create_missing_user_profiles();