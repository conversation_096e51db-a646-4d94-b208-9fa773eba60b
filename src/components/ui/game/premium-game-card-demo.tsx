/**
 * DEMO COMPONENT - NOT FOR PRODUCTION USE
 * 
 * This component contains hardcoded sample data for demonstration purposes only.
 * It showcases the premium game card component with various configurations and
 * visual examples. This file should NOT be imported in production code.
 * 
 * The sample data in this file is intentionally static to provide consistent
 * demo experiences for UI testing and development.
 */

import React, { useState } from 'react';
import { PremiumGameCard, CardVariant, CardStyle, InteractionMode } from './premium-game-card';
import { UserGameWithDetails } from '@/types/database';
import { GameCardInteractions, AnimationConfig } from '@/types/library';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Slider } from '@/components/ui/base/slider';
import { Switch } from '@/components/ui/base/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/base/select';
import { Separator } from '@/components/ui/base/separator';
import { 
  Settings, 
  Palette, 
  Zap, 
  Eye,
  Sparkles,
  RotateCcw
} from 'lucide-react';

// DEMO DATA - Hardcoded sample data for demonstration purposes only
// This data is intentionally static to provide consistent demo experiences
const sampleGameData: UserGameWithDetails = {
  id: 'demo-game-1',
  user_id: 'demo-user',
  game_id: 'demo-game',
  status: 'playing',
  personal_rating: 9,
  date_added: '2024-01-01T00:00:00Z',
  last_played: '2024-01-15T00:00:00Z',
  total_play_time: 14400, // 4 hours in seconds
  achievement_progress: 85,
  game: {
    id: 'demo-game',
    title: 'Cyberpunk 2077',
    developer: 'CD Projekt RED',
    publisher: 'CD Projekt',
    release_date: '2020-12-10',
    genres: ['Action', 'RPG', 'Open World', 'Sci-Fi'],
    platforms: ['PC', 'PlayStation 5', 'Xbox Series X'],
    cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co2nv8.webp',
    metacritic_score: 86,
    description: 'An open-world, action-adventure RPG set in the dark future of Night City.',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const alternativeGames: UserGameWithDetails[] = [
  {
    ...sampleGameData,
    id: 'demo-game-2',
    status: 'completed',
    personal_rating: 8,
    game: {
      ...sampleGameData.game,
      id: 'demo-game-2',
      title: 'The Witcher 3',
      developer: 'CD Projekt RED',
      genres: ['RPG', 'Fantasy', 'Open World'],
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co1wyy.webp',
      metacritic_score: 93,
    }
  },
  {
    ...sampleGameData,
    id: 'demo-game-3',
    status: 'backlog',
    personal_rating: null,
    game: {
      ...sampleGameData.game,
      id: 'demo-game-3',
      title: 'Elden Ring',
      developer: 'FromSoftware',
      genres: ['Action', 'RPG', 'Souls-like'],
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co4jni.webp',
      metacritic_score: 96,
    }
  },
  {
    ...sampleGameData,
    id: 'demo-game-4',
    status: 'wishlist',
    personal_rating: null,
    game: {
      ...sampleGameData.game,
      id: 'demo-game-4',
      title: 'No Cover Art Game',
      developer: 'Demo Studio',
      genres: ['Indie', 'Puzzle'],
      cover_image: null, // No cover art to test fallback
      metacritic_score: 78,
    }
  }
];

export const PremiumGameCardDemo: React.FC = () => {
  // Demo state
  const [selectedGame, setSelectedGame] = useState(0);
  const [variant, setVariant] = useState<CardVariant>('standard');
  const [style, setStyle] = useState<CardStyle>('modern');
  const [interactionMode, setInteractionMode] = useState<InteractionMode>('hover');
  const [showQuickActions, setShowQuickActions] = useState(true);
  const [enableHoverEffects, setEnableHoverEffects] = useState(true);
  const [customArtwork, setCustomArtwork] = useState(true);
  const [isSelected, setIsSelected] = useState(false);
  
  // Animation configuration
  const [animations, setAnimations] = useState<AnimationConfig>({
    hoverScale: 1.02,
    transitionDuration: 400,
    staggerDelay: 100,
    parallaxEffect: true,
    glowEffect: true,
  });

  // Demo interactions
  const interactions: GameCardInteractions = {
    onQuickPlay: () => console.log('Quick play clicked'),
    onStatusChange: (status) => console.log('Status changed to:', status),
    onRatingChange: (rating) => console.log('Rating changed to:', rating),
    onAddToCollection: (collectionId) => console.log('Added to collection:', collectionId),
    onShare: (platform) => console.log('Shared to:', platform),
    onCustomize: () => console.log('Customize clicked'),
    onSelect: (selected) => setIsSelected(selected),
  };

  const currentGameData = selectedGame < alternativeGames.length 
    ? alternativeGames[selectedGame] 
    : sampleGameData;

  const resetToDefaults = () => {
    setVariant('standard');
    setStyle('modern');
    setInteractionMode('hover');
    setShowQuickActions(true);
    setEnableHoverEffects(true);
    setCustomArtwork(true);
    setIsSelected(false);
    setAnimations({
      hoverScale: 1.02,
      transitionDuration: 400,
      staggerDelay: 100,
      parallaxEffect: true,
      glowEffect: true,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent mb-4">
            Premium Game Card Demo
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Explore the enhanced game card component with premium visual effects, 
            multiple variants, and advanced interaction patterns.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Demo Card Display */}
          <div className="lg:col-span-2">
            <Card className="p-8 bg-gradient-to-br from-card via-card to-card/95 border-0 shadow-2xl">
              <CardHeader className="text-center pb-6">
                <CardTitle className="flex items-center justify-center gap-2 text-2xl">
                  <Sparkles className="h-6 w-6 text-primary animate-pulse" />
                  Live Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <div className="relative">
                    {/* Showcase background effects */}
                    <div className="absolute -inset-4 bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 rounded-2xl opacity-50 animate-pulse" />
                    
                    <PremiumGameCard
                      gameData={currentGameData}
                      variant={variant}
                      style={style}
                      interactionMode={interactionMode}
                      showQuickActions={showQuickActions}
                      enableHoverEffects={enableHoverEffects}
                      customArtwork={customArtwork}
                      animations={animations}
                      interactions={interactions}
                      isSelected={isSelected}
                      className="relative z-10"
                    />
                  </div>
                </div>

                {/* Game Selection */}
                <div className="mt-8 text-center">
                  <p className="text-sm text-muted-foreground mb-4">Sample Games</p>
                  <div className="flex justify-center gap-2 flex-wrap">
                    {[...alternativeGames, sampleGameData].map((game, index) => (
                      <Button
                        key={game.id}
                        variant={selectedGame === index ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedGame(index)}
                        className="text-xs"
                      >
                        {game.game.title}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Feature Showcase Grid */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="p-4 text-center">
                <Eye className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold mb-1">Multiple Variants</h3>
                <p className="text-xs text-muted-foreground">Compact, Standard, and Detailed layouts</p>
              </Card>
              <Card className="p-4 text-center">
                <Palette className="h-8 w-8 text-secondary mx-auto mb-2" />
                <h3 className="font-semibold mb-1">Visual Styles</h3>
                <p className="text-xs text-muted-foreground">Modern, Classic, and Minimal themes</p>
              </Card>
              <Card className="p-4 text-center">
                <Zap className="h-8 w-8 text-accent mx-auto mb-2" />
                <h3 className="font-semibold mb-1">Premium Effects</h3>
                <p className="text-xs text-muted-foreground">Parallax, glow, and smooth animations</p>
              </Card>
            </div>
          </div>

          {/* Controls Panel */}
          <div className="space-y-6">
            <Card className="p-6">
              <CardHeader className="px-0 pt-0">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Settings className="h-5 w-5" />
                  Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 space-y-6">
                {/* Card Variant */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Card Variant</label>
                  <Select value={variant} onValueChange={(value: CardVariant) => setVariant(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="compact">Compact</SelectItem>
                      <SelectItem value="standard">Standard</SelectItem>
                      <SelectItem value="detailed">Detailed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Card Style */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Visual Style</label>
                  <Select value={style} onValueChange={(value: CardStyle) => setStyle(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="modern">Modern</SelectItem>
                      <SelectItem value="classic">Classic</SelectItem>
                      <SelectItem value="minimal">Minimal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Interaction Mode */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Interaction Mode</label>
                  <Select value={interactionMode} onValueChange={(value: InteractionMode) => setInteractionMode(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hover">Hover</SelectItem>
                      <SelectItem value="click">Click</SelectItem>
                      <SelectItem value="focus">Focus</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                {/* Feature Toggles */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Quick Actions</label>
                    <Switch checked={showQuickActions} onCheckedChange={setShowQuickActions} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Hover Effects</label>
                    <Switch checked={enableHoverEffects} onCheckedChange={setEnableHoverEffects} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Custom Artwork</label>
                    <Switch checked={customArtwork} onCheckedChange={setCustomArtwork} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Selected State</label>
                    <Switch checked={isSelected} onCheckedChange={setIsSelected} />
                  </div>
                </div>

                <Separator />

                {/* Animation Controls */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Animation Settings</h4>
                  
                  <div>
                    <label className="text-xs text-muted-foreground mb-2 block">
                      Hover Scale: {animations.hoverScale}
                    </label>
                    <Slider
                      value={[animations.hoverScale]}
                      onValueChange={([value]) => setAnimations(prev => ({ ...prev, hoverScale: value }))}
                      min={1}
                      max={1.1}
                      step={0.01}
                      className="w-full"
                    />
                  </div>
                  
                  <div>
                    <label className="text-xs text-muted-foreground mb-2 block">
                      Transition Duration: {animations.transitionDuration}ms
                    </label>
                    <Slider
                      value={[animations.transitionDuration]}
                      onValueChange={([value]) => setAnimations(prev => ({ ...prev, transitionDuration: value }))}
                      min={100}
                      max={1000}
                      step={50}
                      className="w-full"
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm">Parallax Effect</label>
                    <Switch 
                      checked={animations.parallaxEffect} 
                      onCheckedChange={(checked) => setAnimations(prev => ({ ...prev, parallaxEffect: checked }))} 
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm">Glow Effect</label>
                    <Switch 
                      checked={animations.glowEffect} 
                      onCheckedChange={(checked) => setAnimations(prev => ({ ...prev, glowEffect: checked }))} 
                    />
                  </div>
                </div>

                <Separator />

                {/* Reset Button */}
                <Button 
                  variant="outline" 
                  onClick={resetToDefaults}
                  className="w-full"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset to Defaults
                </Button>
              </CardContent>
            </Card>

            {/* Status Information */}
            <Card className="p-4">
              <CardHeader className="px-0 pt-0 pb-3">
                <CardTitle className="text-sm">Current Configuration</CardTitle>
              </CardHeader>
              <CardContent className="px-0 space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Variant:</span>
                  <Badge variant="outline" className="text-xs">{variant}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Style:</span>
                  <Badge variant="outline" className="text-xs">{style}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Interaction:</span>
                  <Badge variant="outline" className="text-xs">{interactionMode}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Game:</span>
                  <Badge variant="outline" className="text-xs">{currentGameData.game.title}</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Code Example */}
        <Card className="mt-8 p-6">
          <CardHeader className="px-0 pt-0">
            <CardTitle className="text-lg">Usage Example</CardTitle>
          </CardHeader>
          <CardContent className="px-0">
            <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
              <code>{`<PremiumGameCard
  gameData={gameData}
  variant="${variant}"
  style="${style}"
  interactionMode="${interactionMode}"
  showQuickActions={${showQuickActions}}
  enableHoverEffects={${enableHoverEffects}}
  customArtwork={${customArtwork}}
  animations={{
    hoverScale: ${animations.hoverScale},
    transitionDuration: ${animations.transitionDuration},
    parallaxEffect: ${animations.parallaxEffect},
    glowEffect: ${animations.glowEffect}
  }}
  interactions={{
    onStatusChange: (status) => console.log(status),
    onQuickPlay: () => console.log('play'),
    onSelect: (selected) => setSelected(selected)
  }}
  isSelected={${isSelected}}
/>`}</code>
            </pre>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PremiumGameCardDemo;