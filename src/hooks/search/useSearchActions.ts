import { useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';
import { enhancedSearchService, SearchResult } from '../../lib/enhancedSearchService';
import { useSearchAnalytics } from '../useSearchAnalytics';
import { SearchFilters } from './useSearchState';

export function useSearchActions() {
  const { user } = useAuth();
  const { recordSearch, recordSearchResults } = useSearchAnalytics();

  // Enhanced search mutation
  const searchMutation = useMutation({
    mutationFn: async ({ query, searchSource = 'manual' }: { query: string; searchSource?: 'manual' | 'voice' | 'ai_assisted' | 'autocomplete' }): Promise<{ result: SearchResult; queryId: string | null }> => {
      if (!query.trim()) {
        throw new Error('Search query is required');
      }

      // Record search analytics
      const queryId = await recordSearch(query, { searchSource });
      const startTime = Date.now();

      try {
        const result = await enhancedSearchService.enhancedSearch(query, user?.id);
        const responseTimeMs = Date.now() - startTime;

        // Record search results analytics
        if (queryId) {
          await recordSearchResults(queryId, {
            apiSource: 'igdb', // Primary API source 
            totalResults: result.games.length,
            resultsShown: result.games.length,
            responseTimeMs,
            cacheHit: false, // Enhanced service handles caching internally
            success: true
          });
        }

        return { result, queryId };
      } catch (error) {
        // Record failed search analytics
        if (queryId) {
          await recordSearchResults(queryId, {
            apiSource: 'igdb',
            totalResults: 0,
            resultsShown: 0,
            responseTimeMs: Date.now() - startTime,
            cacheHit: false,
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error'
          });
        }
        throw error;
      }
    },
    onSuccess: ({ result, queryId }) => {
      // Show success message with search stats
      const gameCount = result.games.length;
      const searchTime = result.searchTime;
      console.log(`🔍 Search analytics recorded for query ID: ${queryId}`);
      toast.success(`Found ${gameCount} games in ${searchTime}ms`, {
        duration: 2000
      });
    },
    onError: (error) => {
      // Enhanced error logging for search
      const errorDetails = {
        message: error?.message || 'Unknown search error',
        name: error?.name || 'SearchError',
        stack: error?.stack || 'No stack trace',
        timestamp: new Date().toISOString(),
        userId: user?.id || 'anonymous',
        action: 'enhancedSearch',
        context: 'useSearchActions',
        query: 'search query'
      };
      
      console.error('❌ Enhanced Search Error:', errorDetails);
      
      // Log to external service if available
      if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
        try {
          window.navigator.sendBeacon('/api/log-error', JSON.stringify(errorDetails));
        } catch (logError) {
          console.warn('Failed to send error log:', logError);
        }
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Search failed';
      toast.error(errorMessage);
    }
  });

  // Voice search mutation
  const voiceSearchMutation = useMutation({
    mutationFn: async (transcript: string): Promise<{ result: SearchResult; queryId: string | null }> => {
      // Record search analytics for voice search
      const queryId = await recordSearch(transcript, { searchSource: 'voice' });
      const startTime = Date.now();

      try {
        const result = await enhancedSearchService.processVoiceSearch(transcript);
        const responseTimeMs = Date.now() - startTime;

        // Record search results analytics
        if (queryId) {
          await recordSearchResults(queryId, {
            apiSource: 'igdb',
            totalResults: result.games.length,
            resultsShown: result.games.length,
            responseTimeMs,
            cacheHit: false,
            success: true
          });
        }

        return { result, queryId };
      } catch (error) {
        // Record failed voice search analytics
        if (queryId) {
          await recordSearchResults(queryId, {
            apiSource: 'igdb',
            totalResults: 0,
            resultsShown: 0,
            responseTimeMs: Date.now() - startTime,
            cacheHit: false,
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error'
          });
        }
        throw error;
      }
    },
    onSuccess: ({ result, queryId }) => {
      console.log(`🎤 Voice search analytics recorded for query ID: ${queryId}`);
      toast.success(`Voice search complete: "${result.intent.originalQuery}"`);
    },
    onError: (error) => {
      // Enhanced error logging for voice search
      const errorDetails = {
        message: error?.message || 'Unknown voice search error',
        name: error?.name || 'VoiceSearchError',
        stack: error?.stack || 'No stack trace',
        timestamp: new Date().toISOString(),
        userId: user?.id || 'anonymous',
        action: 'voiceSearch',
        context: 'useSearchActions'
      };
      
      console.error('❌ Voice Search Error:', errorDetails);
      
      // Log to external service if available
      if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
        try {
          window.navigator.sendBeacon('/api/log-error', JSON.stringify(errorDetails));
        } catch (logError) {
          console.warn('Failed to send error log:', logError);
        }
      }
      
      toast.error('Voice search failed');
    }
  });

  // Perform search
  const performSearch = useCallback((query: string, searchSource: 'manual' | 'ai_assisted' | 'autocomplete' = 'manual') => {
    if (!query.trim()) {
      toast.error('Please enter a search query');
      return;
    }
    searchMutation.mutate({ query, searchSource });
  }, [searchMutation]);

  // Apply filters to current search
  const applyFilters = useCallback((filters: SearchFilters, currentQuery: string) => {
    // If we have a current query, re-search with filters
    if (currentQuery) {
      // For now, we'll just re-run the search
      // In a more advanced implementation, we'd modify the search with filters
      performSearch(currentQuery);
    }
  }, [performSearch]);

  // Search with suggestion
  const searchWithSuggestion = useCallback((suggestion: string) => {
    performSearch(suggestion);
  }, [performSearch]);

  // Clear search history
  const clearSearchHistory = useCallback(() => {
    if (user?.id) {
      localStorage.removeItem(`search_history_${user.id}`);
      toast.success('Search history cleared');
    }
  }, [user?.id]);

  return {
    // Mutations
    searchMutation,
    voiceSearchMutation,
    
    // Actions
    performSearch,
    applyFilters,
    searchWithSuggestion,
    clearSearchHistory,
    
    // State
    isSearching: searchMutation.isPending,
    searchError: searchMutation.error,
    searchResults: searchMutation.data?.result,
    searchQueryId: searchMutation.data?.queryId,
    isVoiceSearching: voiceSearchMutation.isPending,
    voiceSearchError: voiceSearchMutation.error,
    voiceSearchResults: voiceSearchMutation.data?.result,
    voiceSearchQueryId: voiceSearchMutation.data?.queryId
  };
}