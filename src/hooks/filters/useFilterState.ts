import { useState, useCallback, useMemo } from 'react';
import { GameFilters, DEFAULT_FILTERS } from '@/types/filters';

export interface UseFilterStateOptions {
  initialFilters?: GameFilters;
}

export function useFilterState(options: UseFilterStateOptions = {}) {
  const { initialFilters = DEFAULT_FILTERS } = options;
  
  const [filters, setFilters] = useState<GameFilters>(initialFilters);
  const [searchQuery, setSearchQuery] = useState('');

  const updateFilter = useCallback(<T extends keyof GameFilters>(
    filterType: T,
    update: Partial<GameFilters[T]>
  ) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: {
        ...prev[filterType],
        ...update
      }
    }));
  }, []);

  const resetFilters = useCallback((keepSearch = false) => {
    const newFilters = {
      ...DEFAULT_FILTERS,
      search: keepSearch ? filters.search : DEFAULT_FILTERS.search
    };
    setFilters(newFilters);
    
    if (!keepSearch) {
      setSearchQuery('');
    }
  }, [filters.search]);

  const loadPreset = useCallback((preset: { filters: GameFilters }) => {
    setFilters(preset.filters);
    if (preset.filters.search.enabled && preset.filters.search.query) {
      setSearchQuery(preset.filters.search.query);
    }
  }, []);

  // Compute derived state
  const hasActiveFilters = useMemo(() => {
    return (
      filters.platforms.enabled ||
      filters.genres.enabled ||
      filters.year.enabled ||
      filters.rating.enabled ||
      filters.developer.enabled ||
      filters.publisher.enabled ||
      filters.customTags.enabled ||
      filters.status.enabled ||
      filters.collection.enabled ||
      searchQuery.trim().length > 0
    );
  }, [filters, searchQuery]);

  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (filters.platforms.enabled && filters.platforms.platforms.length > 0) count++;
    if (filters.genres.enabled && filters.genres.genres.length > 0) count++;
    if (filters.year.enabled && (filters.year.minYear || filters.year.maxYear)) count++;
    if (filters.rating.enabled && (filters.rating.minRating || filters.rating.maxRating)) count++;
    if (filters.developer.enabled && filters.developer.developers.length > 0) count++;
    if (filters.publisher.enabled && filters.publisher.publishers.length > 0) count++;
    if (filters.customTags.enabled && (filters.customTags.tags.length > 0 || filters.customTags.includeUntagged)) count++;
    if (filters.status.enabled && filters.status.statuses.length > 0) count++;
    if (filters.collection.enabled && (filters.collection.inLibrary || filters.collection.inWishlist || filters.collection.notInCollection)) count++;
    if (searchQuery.trim().length > 0) count++;
    return count;
  }, [filters, searchQuery]);

  const validateFilters = useCallback(() => {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Validate year range
    if (filters.year.enabled && filters.year.minYear && filters.year.maxYear) {
      if (filters.year.minYear > filters.year.maxYear) {
        errors.push('Minimum year cannot be greater than maximum year');
      }
    }
    
    // Validate rating range
    if (filters.rating.enabled && filters.rating.minRating && filters.rating.maxRating) {
      if (filters.rating.minRating > filters.rating.maxRating) {
        errors.push('Minimum rating cannot be greater than maximum rating');
      }
    }
    
    // Warning for too restrictive filters
    if (activeFilterCount > 5) {
      warnings.push('Many filters active - this might result in very few matches');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }, [filters, activeFilterCount]);

  return {
    filters,
    searchQuery,
    setFilters,
    setSearchQuery,
    updateFilter,
    resetFilters,
    loadPreset,
    hasActiveFilters,
    activeFilterCount,
    validateFilters
  };
}