import { useState, useCallback, useMemo } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { steamImportService, ImportProgress } from '../lib/steamImportService';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';

/**
 * Hook for Steam library import functionality
 */
export const useSteamImport = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [importProgress, setImportProgress] = useState<ImportProgress | null>(null);
  const [steamId, setSteamId] = useState('');

  // Import Steam library mutation
  const importMutation = useMutation({
    mutationFn: async (steamId: string) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const extractedSteamId = steamImportService.extractSteamId(steamId);
      
      if (!steamImportService.validateSteamId(extractedSteamId)) {
        throw new Error('Invalid Steam ID format');
      }

      return steamImportService.importSteamLibrary(
        user.id,
        extractedSteamId,
        setImportProgress
      );
    },
    onSuccess: (result) => {
      if (result.success) {
        // Use the result message if available, otherwise default message
        const successMessage = result.message || `Successfully imported ${result.imported} games from Steam`;
        toast.success(successMessage);
        
        // Show errors if any
        if (result.errors.length > 0) {
          toast.error(`${result.errors.length} games failed to import. Check console for details.`);
          console.warn('Import errors:', result.errors);
        }
        
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
        queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
        queryClient.invalidateQueries({ queryKey: ['import-stats', user?.id] });
      } else {
        // Use the result message if available, otherwise combine errors
        const errorMessage = result.message || `Import failed: ${result.errors.join(', ')}`;
        toast.error(errorMessage);
      }
    },
    onError: (error) => {
      console.error('Steam import error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to import Steam library';
      toast.error(errorMessage);
      setImportProgress(null);
    },
    onSettled: () => {
      // Clear progress after a delay
      setTimeout(() => {
        setImportProgress(null);
      }, 3000);
    }
  });

  // Get import statistics
  const {
    data: importStats,
    isLoading: isLoadingStats,
    error: statsError,
    refetch: refetchStats
  } = useQuery({
    queryKey: ['import-stats', user?.id],
    queryFn: () => steamImportService.getImportStats(user?.id || ''),
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  // Start import process
  const startImport = useCallback((steamIdInput: string) => {
    if (!steamIdInput.trim()) {
      toast.error('Please enter a Steam ID or profile URL');
      return;
    }

    if (!user?.id) {
      toast.error('Please log in to import your Steam library');
      return;
    }

    importMutation.mutate(steamIdInput);
  }, [user?.id, importMutation]);

  // Validate Steam ID input
  const validateSteamIdInput = useCallback((input: string): {
    isValid: boolean;
    error?: string;
    extractedId?: string;
  } => {
    if (!input.trim()) {
      return { isValid: false, error: 'Steam ID is required' };
    }

    const extractedId = steamImportService.extractSteamId(input.trim());
    const isValid = steamImportService.validateSteamId(extractedId);

    if (!isValid) {
      return {
        isValid: false,
        error: 'Invalid Steam ID. Please enter a valid Steam ID, custom URL, or profile URL.'
      };
    }

    return {
      isValid: true,
      extractedId
    };
  }, []);

  // Cancel import (if needed)
  const cancelImport = useCallback(() => {
    // Note: This is a placeholder - actual cancellation would require
    // more complex implementation with AbortController
    setImportProgress(null);
    toast('Import cancelled');
  }, []);

  return {
    // State
    steamId,
    setSteamId,
    importProgress,
    importStats,
    
    // Loading states
    isImporting: importMutation.isPending,
    isLoadingStats,
    
    // Errors
    importError: importMutation.error,
    statsError,
    
    // Actions
    startImport,
    cancelImport,
    validateSteamIdInput,
    refetchStats,
    
    // Utilities
    extractSteamId: steamImportService.extractSteamId,
    validateSteamId: steamImportService.validateSteamId
  };
};

/**
 * Hook for managing multiple platform imports
 */
export const useLibraryImport = () => {
  const [activeImport, setActiveImport] = useState<'steam' | 'epic' | 'console' | null>(null);

  const steamImport = useSteamImport();

  // Epic Games import (placeholder for future implementation)
  const epicImport = useMemo(() => ({
    isImporting: false,
    startImport: () => {
      toast('Epic Games import coming soon');
    }
  }), []);

  // Console import (placeholder for future implementation)
  const consoleImport = useMemo(() => ({
    isImporting: false,
    startImport: () => {
      toast('Console import coming soon');
    }
  }), []);

  // Get overall import statistics
  const overallStats = {
    totalImported: steamImport.importStats?.totalImported || 0,
    steamGames: steamImport.importStats?.steamGames || 0,
    epicGames: steamImport.importStats?.epicGames || 0,
    consoleGames: steamImport.importStats?.consoleGames || 0,
    lastImport: steamImport.importStats?.lastImport
  };

  const startPlatformImport = useCallback((platform: 'steam' | 'epic' | 'console', data?: { steamId?: string }) => {
    setActiveImport(platform);
    
    switch (platform) {
      case 'steam':
        if (data?.steamId) {
          steamImport.startImport(data.steamId);
        }
        break;
      case 'epic':
        epicImport.startImport();
        break;
      case 'console':
        consoleImport.startImport();
        break;
    }
  }, [steamImport, epicImport, consoleImport]);

  const isAnyImporting = steamImport.isImporting || epicImport.isImporting || consoleImport.isImporting;

  return {
    // Platform-specific imports
    steamImport,
    epicImport,
    consoleImport,
    
    // Overall state
    activeImport,
    setActiveImport,
    isAnyImporting,
    overallStats,
    
    // Actions
    startPlatformImport,
    
    // Utilities
    isLoadingStats: steamImport.isLoadingStats
  };
};

/**
 * Helper function to format import progress
 */
export const formatImportProgress = (progress: ImportProgress): string => {
  switch (progress.phase) {
    case 'authenticating':
      return 'Connecting to Steam...';
    case 'fetching_profile':
      return `Fetching profile: ${progress.message}`;
    case 'fetching_games':
      return 'Loading your Steam library...';
    case 'processing':
      if (progress.gamesProcessed && progress.totalGames) {
        return `Processing ${progress.gamesProcessed}/${progress.totalGames} games...`;
      }
      return progress.message;
    case 'saving':
      return 'Saving to your collection...';
    case 'complete':
      return 'Import complete!';
    default:
      return progress.message;
  }
};

/**
 * Helper function to get import phase color
 */
export const getImportPhaseColor = (phase: ImportProgress['phase']): string => {
  switch (phase) {
    case 'authenticating':
      return 'text-blue-600';
    case 'fetching_profile':
    case 'fetching_games':
      return 'text-purple-600';
    case 'processing':
    case 'saving':
      return 'text-orange-600';
    case 'complete':
      return 'text-green-600';
    default:
      return 'text-gray-600';
  }
};