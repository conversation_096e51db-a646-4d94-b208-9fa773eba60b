import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { Switch } from '@/components/ui/base/switch';
import { Separator } from '@/components/ui/base/separator';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { 
  HardDrive, 
  Trash2, 
  RefreshCw, 
  TrendingUp,
  Clock,
  Target,
  BarChart3,
  Zap,
  Database
} from 'lucide-react';
import { useSearchCache, useCacheManagement, useCachePerformance } from '@/hooks/useSearchCache';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

interface CacheManagementProps {
  className?: string;
  compact?: boolean;
}

export function CacheManagement({ className, compact = false }: CacheManagementProps) {
  const { 
    cacheStats, 
    isEnabled, 
    setEnabled, 
    clearCache, 
    warmupCache 
  } = useSearchCache();
  
  const { 
    getWarmupQueries, 
    performWarmup 
  } = useCacheManagement();
  
  const {
    performance,
    isMonitoring,
    startPerformanceMonitoring,
    stopPerformanceMonitoring
  } = useCachePerformance();

  const [isClearing, setIsClearing] = useState(false);
  const [isWarming, setIsWarming] = useState(false);
  const [warmupQueries, setWarmupQueries] = useState<Array<{ query: string; priority: string }>>([]);

  // Load warmup queries on mount
  useEffect(() => {
    const loadWarmupQueries = async () => {
      try {
        const queries = await getWarmupQueries();
        setWarmupQueries(queries);
      } catch (error) {
        console.error('Failed to load warmup queries:', error);
      }
    };

    loadWarmupQueries();
  }, [getWarmupQueries]);

  const handleClearCache = async () => {
    setIsClearing(true);
    try {
      await clearCache();
      toast.success('Search cache cleared successfully');
    } catch (error) {
      console.error('Failed to clear cache:', error);
      toast.error('Failed to clear cache');
    } finally {
      setIsClearing(false);
    }
  };

  const handleWarmupCache = async () => {
    setIsWarming(true);
    try {
      await warmupCache();
      toast.success('Cache warmup completed');
    } catch (error) {
      console.error('Failed to warm up cache:', error);
      toast.error('Failed to warm up cache');
    } finally {
      setIsWarming(false);
    }
  };

  const handlePerformWarmup = async () => {
    setIsWarming(true);
    try {
      const queries = warmupQueries.map(q => q.query);
      await performWarmup(queries);
      toast.success('Custom warmup completed');
    } catch (error) {
      console.error('Failed to perform warmup:', error);
      toast.error('Failed to perform warmup');
    } finally {
      setIsWarming(false);
    }
  };

  const getCacheHealthStatus = () => {
    if (!cacheStats) return { status: 'unknown', color: 'gray' };
    
    const hitRate = cacheStats.hitRate;
    if (hitRate >= 70) return { status: 'excellent', color: 'green' };
    if (hitRate >= 50) return { status: 'good', color: 'blue' };
    if (hitRate >= 30) return { status: 'fair', color: 'yellow' };
    return { status: 'poor', color: 'red' };
  };


  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const health = getCacheHealthStatus();

  if (compact) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Database className="h-4 w-4" />
            Search Cache
            <Badge 
              variant={health.color === 'green' ? 'default' : 'secondary'} 
              className="ml-auto"
            >
              {isEnabled ? 'Enabled' : 'Disabled'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Cache Status</span>
            <Switch checked={isEnabled} onCheckedChange={setEnabled} />
          </div>
          
          {isEnabled && cacheStats && (
            <>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Hit Rate</span>
                <span className="text-sm font-medium">{cacheStats.hitRate.toFixed(1)}%</span>
              </div>
              
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleClearCache}
                  disabled={isClearing}
                  className="flex-1"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Clear
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleWarmupCache}
                  disabled={isWarming}
                  className="flex-1"
                >
                  <Zap className="h-3 w-3 mr-1" />
                  Warmup
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Cache Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5 text-primary" />
            Search Cache Management
            <Badge 
              variant={isEnabled ? (health.color === 'green' ? 'default' : 'secondary') : 'outline'}
              className="ml-auto"
            >
              {isEnabled ? health.status.toUpperCase() : 'DISABLED'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm font-medium">Enable Search Caching</p>
              <p className="text-xs text-muted-foreground">
                Cache search results for faster response times
              </p>
            </div>
            <Switch checked={isEnabled} onCheckedChange={setEnabled} />
          </div>

          {isEnabled && cacheStats && (
            <>
              <Separator />
              
              {/* Cache Statistics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Hit Rate</span>
                  </div>
                  <div className="space-y-1">
                    <Progress value={cacheStats.hitRate} className="h-2" />
                    <p className="text-xs text-muted-foreground">
                      {cacheStats.hitRate.toFixed(1)}%
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">Total Queries</span>
                  </div>
                  <p className="text-lg font-semibold">{cacheStats.totalQueries}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-medium">Cache Size</span>
                  </div>
                  <p className="text-lg font-semibold">{cacheStats.cacheSize}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-orange-500" />
                    <span className="text-sm font-medium">Avg Response</span>
                  </div>
                  <p className="text-lg font-semibold">
                    {formatTime(cacheStats.averageResponseTime)}
                  </p>
                </div>
              </div>

              {/* Cache Actions */}
              <Separator />
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  onClick={handleClearCache}
                  disabled={isClearing}
                  className="flex-1"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {isClearing ? 'Clearing...' : 'Clear Cache'}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleWarmupCache}
                  disabled={isWarming}
                  className="flex-1"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  {isWarming ? 'Warming...' : 'Warmup Cache'}
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Performance Monitoring */}
      {isEnabled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              Performance Monitoring
              <Badge variant={isMonitoring ? 'default' : 'outline'} className="ml-auto">
                {isMonitoring ? 'Active' : 'Inactive'}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium">Real-time Performance Tracking</p>
                <p className="text-xs text-muted-foreground">
                  Monitor cache hit/miss performance in real-time
                </p>
              </div>
              <Button 
                variant={isMonitoring ? 'destructive' : 'default'}
                size="sm"
                onClick={isMonitoring ? stopPerformanceMonitoring : startPerformanceMonitoring}
              >
                {isMonitoring ? 'Stop' : 'Start'} Monitoring
              </Button>
            </div>

            {isMonitoring && (
              <>
                <Separator />
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Cache Hit Time</p>
                    <p className="text-lg font-semibold text-green-600">
                      {formatTime(performance.cacheHitTime)}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Cache Miss Time</p>
                    <p className="text-lg font-semibold text-red-600">
                      {formatTime(performance.cacheMissTime)}
                    </p>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}

      {/* Popular Queries */}
      {isEnabled && cacheStats && cacheStats.popularQueries.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              Popular Search Queries
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {cacheStats.popularQueries.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded-lg border">
                    <span className="text-sm font-medium">{item.query}</span>
                    <Badge variant="secondary">{item.count} searches</Badge>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Warmup Management */}
      {isEnabled && warmupQueries.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5 text-primary" />
              Cache Warmup Management
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <ScrollArea className="h-32">
              <div className="space-y-2">
                {warmupQueries.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded-lg border">
                    <span className="text-sm">{item.query}</span>
                    <Badge 
                      variant={
                        item.priority === 'high' ? 'destructive' : 
                        item.priority === 'medium' ? 'default' : 'secondary'
                      }
                    >
                      {item.priority}
                    </Badge>
                  </div>
                ))}
              </div>
            </ScrollArea>
            
            <Button 
              onClick={handlePerformWarmup}
              disabled={isWarming}
              className="w-full"
            >
              <Zap className="h-4 w-4 mr-2" />
              {isWarming ? 'Performing Warmup...' : 'Perform Warmup'}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}