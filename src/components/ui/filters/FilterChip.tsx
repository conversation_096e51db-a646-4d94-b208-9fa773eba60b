import { ReactNode } from 'react';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { X, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FilterChipProps {
  label: string;
  count?: number;
  isActive?: boolean;
  isExpandable?: boolean;
  isExpanded?: boolean;
  variant?: 'default' | 'platform' | 'genre' | 'year' | 'rating' | 'developer' | 'publisher';
  icon?: ReactNode;
  onToggle?: () => void;
  onRemove?: () => void;
  onExpand?: () => void;
  className?: string;
  disabled?: boolean;
}

const variantStyles = {
  default: 'bg-primary/10 text-primary border-primary/20 hover:bg-primary/20',
  platform: 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100',
  genre: 'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100',
  year: 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100',
  rating: 'bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100',
  developer: 'bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100',
  publisher: 'bg-pink-50 text-pink-700 border-pink-200 hover:bg-pink-100'
};

export function FilterChip({
  label,
  count,
  isActive = false,
  isExpandable = false,
  isExpanded = false,
  variant = 'default',
  icon,
  onToggle,
  onRemove,
  onExpand,
  className,
  disabled = false
}: FilterChipProps) {
  const baseStyles = cn(
    "inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 cursor-pointer select-none",
    "hover:scale-105 hover:shadow-md active:scale-95",
    "border",
    isActive ? variantStyles[variant] : 'bg-muted/50 text-muted-foreground border-muted-foreground/20 hover:bg-muted',
    disabled && 'opacity-50 cursor-not-allowed hover:scale-100',
    className
  );

  const handleClick = () => {
    if (disabled) return;
    if (isExpandable && onExpand) {
      onExpand();
    } else if (onToggle) {
      onToggle();
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (disabled || !onRemove) return;
    onRemove();
  };

  return (
    <div className={baseStyles} onClick={handleClick}>
      {icon && <span className="flex-shrink-0">{icon}</span>}
      
      <span className="truncate">
        {label}
      </span>
      
      {count !== undefined && count > 0 && (
        <Badge 
          variant="secondary" 
          className="h-5 w-5 flex items-center justify-center p-0 text-xs bg-white/80 text-current"
        >
          {count}
        </Badge>
      )}
      
      {isExpandable && (
        <ChevronDown 
          className={cn(
            "h-3 w-3 transition-transform duration-200 flex-shrink-0",
            isExpanded && "rotate-180"
          )} 
        />
      )}
      
      {isActive && onRemove && (
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0 hover:bg-white/20 rounded-full flex-shrink-0"
          onClick={handleRemove}
          disabled={disabled}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
}