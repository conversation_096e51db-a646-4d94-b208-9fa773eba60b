import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { enhancedSearchService, SearchHistory, SearchIntent } from '../../lib/enhancedSearchService';

/**
 * Hook for search history management
 */
export function useSearchHistory() {
  const { user } = useAuth();
  const [history, setHistory] = useState<SearchHistory[]>([]);

  useEffect(() => {
    if (user?.id) {
      const userHistory = enhancedSearchService.getSearchHistory(user.id);
      setHistory(userHistory);
    }
  }, [user?.id]);

  const getRecentSearches = useCallback((limit: number = 10): string[] => {
    return history
      .slice(0, limit)
      .map(h => h.query);
  }, [history]);

  const getTopSearches = useCallback((limit: number = 5): { query: string; count: number }[] => {
    const queryCount = new Map<string, number>();
    
    history.forEach(h => {
      const count = queryCount.get(h.query) || 0;
      queryCount.set(h.query, count + 1);
    });
    
    return Array.from(queryCount.entries())
      .map(([query, count]) => ({ query, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }, [history]);

  const getSearchesByIntent = useCallback((intentType: SearchIntent['type']): SearchHistory[] => {
    return history.filter(h => h.intent.type === intentType);
  }, [history]);

  return {
    history,
    getRecentSearches,
    getTopSearches,
    getSearchesByIntent
  };
}