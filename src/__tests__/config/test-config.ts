/**
 * Centralized test configuration and constants
 */

/**
 * Test environment configuration
 */
export const TEST_CONFIG = {
  // Default timeout for async operations in tests
  DEFAULT_TIMEOUT: 5000,
  
  // Mock API response delay
  MOCK_API_DELAY: 100,
  
  // Test database configuration
  TEST_DB: {
    USER_ID: 'test-user-123',
    GAME_ID_PREFIX: 'test-game-',
  },
  
  // Test data limits
  LIMITS: {
    MAX_GAMES_PER_TEST: 100,
    MAX_PLATFORMS_PER_TEST: 20,
  },
} as const;

/**
 * Common test utilities
 */
export const TEST_UTILS = {
  /**
   * Generate a unique test ID
   */
  generateTestId: (prefix = 'test') => `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  
  /**
   * Create a delay for testing async operations
   */
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  /**
   * Generate random number within range
   */
  randomBetween: (min: number, max: number) => Math.floor(Math.random() * (max - min + 1)) + min,
  
  /**
   * Generate random string
   */
  randomString: (length = 10) => Math.random().toString(36).substring(2, length + 2),
} as const;

/**
 * Mock data generators
 */
export const MOCK_DATA = {
  /**
   * Generate random game title
   */
  gameTitle: () => `Test Game ${TEST_UTILS.randomString(8)}`,
  
  /**
   * Generate random developer name
   */
  developer: () => `Test Developer ${TEST_UTILS.randomString(6)}`,
  
  /**
   * Generate random publisher name
   */
  publisher: () => `Test Publisher ${TEST_UTILS.randomString(6)}`,
  
  /**
   * Generate random genre
   */
  genre: () => {
    const genres = ['Action', 'Adventure', 'RPG', 'Strategy', 'Simulation', 'Sports', 'Racing', 'Puzzle'];
    return genres[TEST_UTILS.randomBetween(0, genres.length - 1)];
  },
  
  /**
   * Generate random rating (1-10)
   */
  rating: () => TEST_UTILS.randomBetween(1, 10),
  
  /**
   * Generate random hours played (0-1000)
   */
  hoursPlayed: () => TEST_UTILS.randomBetween(0, 1000),
} as const;

/**
 * Test assertion helpers
 */
export const ASSERTIONS = {
  /**
   * Assert that a value is defined and not null
   */
  isDefined: <T>(value: T | undefined | null): value is T => {
    return value !== undefined && value !== null;
  },
  
  /**
   * Assert that an array has expected length
   */
  hasLength: <T>(array: T[], expectedLength: number) => {
    expect(array).toHaveLength(expectedLength);
    return array;
  },
  
  /**
   * Assert that an object has required properties
   */
  hasProperties: <T extends Record<string, any>>(obj: T, properties: (keyof T)[]) => {
    properties.forEach(prop => {
      expect(obj).toHaveProperty(prop);
    });
    return obj;
  },
} as const;