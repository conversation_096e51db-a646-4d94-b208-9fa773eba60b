import React, { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { 
  Image as ImageIcon, 
  Star, 
  MoreVertical, 
  Trash2, 
  Download, 
  Eye,
  Loader2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/base/tabs';
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/base/dropdown-menu';
// Using button and card for modal functionality
import { LoadingSpinner } from '@/components/ui/utils/loading-spinner';
import { customArtworkService, CustomArtwork, ArtworkType } from '@/lib/customArtworkService';
import { cn } from '@/lib/utils';
import { toast } from 'react-hot-toast';
import { ThreeDBoxView } from '@/components/ui/game/ThreeDBoxView';

interface ArtworkGalleryProps {
  userGameId: string;
  className?: string;
  onArtworkUpdate?: () => void;
}

interface ArtworkByType {
  [key: string]: CustomArtwork[];
}

export const ArtworkGallery: React.FC<ArtworkGalleryProps> = ({
  userGameId,
  className = '',
  onArtworkUpdate
}) => {
  const queryClient = useQueryClient();
  const [artwork, setArtwork] = useState<CustomArtwork[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('front');
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const [processingActions, setProcessingActions] = useState<Set<string>>(new Set());

  const artworkTypes = customArtworkService.getArtworkTypes();


  // Load artwork
  const loadArtwork = async () => {
    try {
      setLoading(true);
      const artworkData = await customArtworkService.getArtworkForUserGame(userGameId);
      setArtwork(artworkData);
    } catch (error) {
      console.error('Error loading artwork:', error);
      toast.error('Failed to load artwork');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userGameId) {
      loadArtwork();
    }
  }, [userGameId]); // eslint-disable-line react-hooks/exhaustive-deps

  // Group artwork by type
  const artworkByType: ArtworkByType = artwork.reduce((acc, item) => {
    if (!acc[item.artwork_type]) {
      acc[item.artwork_type] = [];
    }
    acc[item.artwork_type].push(item);
    return acc;
  }, {} as ArtworkByType);

  // Set artwork as primary
  const handleSetPrimary = async (artworkId: string) => {
    setProcessingActions(prev => new Set(prev).add(`primary_${artworkId}`));
    try {
      const success = await customArtworkService.setPrimaryArtwork(artworkId);
      if (success) {
        toast.success('Set as primary artwork');
        
        // Invalidate all artwork-related queries more broadly to update all components
        queryClient.invalidateQueries({ queryKey: ['custom-artwork'] });
        
        // Also invalidate any game-related queries that might cache artwork data
        queryClient.invalidateQueries({ queryKey: ['user-games'] });
        queryClient.invalidateQueries({ queryKey: ['games'] });
        
        // Wait a moment for the database changes to propagate
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Reload local artwork data
        await loadArtwork();
        
        // Notify parent component
        onArtworkUpdate?.();
      } else {
        toast.error('Failed to set as primary');
      }
    } catch (error) {
      console.error('Error setting primary artwork:', error);
      toast.error('Failed to set as primary artwork');
    } finally {
      setProcessingActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(`primary_${artworkId}`);
        return newSet;
      });
    }
  };

  // Unset artwork as primary
  const handleUnsetPrimary = async (artworkId: string) => {
    try {
      const success = await customArtworkService.unsetPrimaryArtwork(artworkId);
      if (success) {
        toast.success('Removed primary status');
        
        // Invalidate all artwork-related queries more broadly to update all components
        queryClient.invalidateQueries({ queryKey: ['custom-artwork'] });
        
        // Also invalidate any game-related queries that might cache artwork data
        queryClient.invalidateQueries({ queryKey: ['user-games'] });
        queryClient.invalidateQueries({ queryKey: ['games'] });
        
        // Wait a moment for the database changes to propagate
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Reload local artwork data
        await loadArtwork();
        
        // Notify parent component
        onArtworkUpdate?.();
      } else {
        toast.error('Failed to remove primary status');
      }
    } catch (error) {
      console.error('Error unsetting primary artwork:', error);
      toast.error('Failed to remove primary status');
    }
  };

  // Toggle primary status
  const handleTogglePrimary = async (artworkId: string) => {
    try {
      const success = await customArtworkService.togglePrimaryArtwork(artworkId);
      if (success) {
        // The service handles the toggle logic, so we just show a generic success message
        toast.success('Updated primary status');
        
        // Invalidate all artwork-related queries more broadly to update all components
        queryClient.invalidateQueries({ queryKey: ['custom-artwork'] });
        
        // Also invalidate any game-related queries that might cache artwork data
        queryClient.invalidateQueries({ queryKey: ['user-games'] });
        queryClient.invalidateQueries({ queryKey: ['games'] });
        
        // Wait a moment for the database changes to propagate
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Reload local artwork data
        await loadArtwork();
        
        // Notify parent component
        onArtworkUpdate?.();
      } else {
        toast.error('Failed to update primary status');
      }
    } catch (error) {
      console.error('Error toggling primary artwork:', error);
      toast.error('Failed to update primary status');
    }
  };

  // Delete artwork
  const handleDelete = async (artworkId: string) => {
    // Show confirmation dialog
    if (!confirm('Are you sure you want to delete this artwork? This action cannot be undone.')) {
      return;
    }

    setProcessingActions(prev => new Set(prev).add(`delete_${artworkId}`));
    try {
      const success = await customArtworkService.deleteArtwork(artworkId);
      if (success) {
        toast.success('Artwork deleted');
        
        // Invalidate all artwork-related queries more broadly to update all components
        queryClient.invalidateQueries({ queryKey: ['custom-artwork'] });
        
        // Also invalidate any game-related queries that might cache artwork data
        queryClient.invalidateQueries({ queryKey: ['user-games'] });
        queryClient.invalidateQueries({ queryKey: ['games'] });
        
        // Wait a moment for the database changes to propagate
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Reload local artwork data
        await loadArtwork();
        
        // Notify parent component
        onArtworkUpdate?.();
      } else {
        toast.error('Failed to delete artwork');
      }
    } catch (error) {
      console.error('Error deleting artwork:', error);
      toast.error('Failed to delete artwork');
    } finally {
      setProcessingActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(`delete_${artworkId}`);
        return newSet;
      });
    }
  };

  // Download artwork
  const handleDownload = (artwork: CustomArtwork) => {
    const link = document.createElement('a');
    link.href = artwork.file_url;
    link.download = `${artwork.artwork_type}_artwork.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('Download started');
  };


  // Get tab badge count
  const getTabBadge = (type: ArtworkType) => {
    const count = artworkByType[type]?.length || 0;
    return count > 0 ? count : null;
  };

  // Get primary artwork for type (commented out as unused)
  // const getPrimaryArtwork = (type: ArtworkType) => {
  //   return artworkByType[type]?.find(art => art.is_primary);
  // };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Custom Artwork
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Custom Artwork
            {artwork.length > 0 && (
              <Badge variant="secondary">{artwork.length}</Badge>
            )}
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {artwork.length === 0 ? (
          <div className="text-center py-8">
            <ImageIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Custom Artwork</h3>
            <p className="text-muted-foreground mb-4">
              Upload custom box art to personalize this game
            </p>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6">
              {artworkTypes.map(type => (
                <TabsTrigger 
                  key={type.value} 
                  value={type.value}
                  className="relative"
                >
                  <span className="truncate">{type.label}</span>
                  {getTabBadge(type.value) && (
                    <Badge 
                      variant="secondary" 
                      className="ml-1 h-4 min-w-4 text-xs px-1"
                    >
                      {getTabBadge(type.value)}
                    </Badge>
                  )}
                </TabsTrigger>
              ))}
            </TabsList>

            {artworkTypes.map(type => (
              <TabsContent key={type.value} value={type.value} className="mt-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{type.label}</h4>
                      <p className="text-sm text-muted-foreground">
                        {type.description}
                      </p>
                    </div>
                    {artworkByType[type.value]?.length > 0 && (
                      <Badge variant="outline">
                        {artworkByType[type.value].length} uploaded
                      </Badge>
                    )}
                  </div>

                  {/* Special 3D View for 3D artwork type */}
                  {type.value === '3d' && (
                    <div className="mb-6">
                      <ThreeDBoxView
                        frontImage={artworkByType['front']?.find(art => art.is_primary)?.file_url || artworkByType['front']?.[0]?.file_url}
                        backImage={artworkByType['back']?.find(art => art.is_primary)?.file_url || artworkByType['back']?.[0]?.file_url}
                        spineImage={artworkByType['spine']?.find(art => art.is_primary)?.file_url || artworkByType['spine']?.[0]?.file_url}
                        title={`Game Box - 3D View`}
                        className="max-w-md mx-auto"
                        autoRotate={false}
                      />
                    </div>
                  )}

                  {artworkByType[type.value]?.length > 0 ? (
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                      {artworkByType[type.value].map(artwork => (
                        <Card 
                          key={artwork.id}
                          className={cn(
                            'group relative overflow-hidden transition-all hover:shadow-lg',
                            artwork.is_primary && 'ring-2 ring-primary'
                          )}
                        >
                          <div className="aspect-[2/3] bg-muted relative overflow-hidden">
                            <img
                              src={artwork.file_url}
                              alt={`${artwork.artwork_type} artwork`}
                              className="w-full h-full object-cover transition-transform group-hover:scale-105"
                              loading="lazy"
                            />

                            {/* Primary Badge */}
                            {artwork.is_primary && (
                              <Badge className="absolute top-2 left-2 bg-primary">
                                <Star className="h-3 w-3 mr-1 fill-current" />
                                Primary
                              </Badge>
                            )}

                            {/* Actions Overlay */}
                            <div className={cn(
                              "absolute inset-0 bg-black/60 transition-opacity flex items-center justify-center gap-2",
                              openDropdownId === artwork.id ? "opacity-100" : "opacity-0 group-hover:opacity-100"
                            )}>
                              <Button
                                size="sm"
                                variant="secondary"
                                className="bg-black/80 hover:bg-black/90 text-white border-white/20  shadow-lg"
                                onClick={() => window.open(artwork.file_url, '_blank')}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>

                              <DropdownMenu
                                trigger={
                                  <Button
                                    size="sm"
                                    variant="secondary"
                                    className="bg-black/80 hover:bg-black/90 text-white border-white/20  shadow-lg"
                                  >
                                    <MoreVertical className="h-4 w-4" />
                                  </Button>
                                }
                                align="end"
                                className="w-48"
                                onOpenChange={(open) => {
                                  setOpenDropdownId(open ? artwork.id : null);
                                }}
                              >
                                {artwork.is_primary ? (
                                  <DropdownMenuItem
                                    onClick={() => handleUnsetPrimary(artwork.id)}
                                  >
                                    <Star className="h-4 w-4 mr-2 fill-current text-yellow-500" />
                                    Unset as Primary
                                  </DropdownMenuItem>
                                ) : (
                                  <DropdownMenuItem
                                    onClick={() => handleSetPrimary(artwork.id)}
                                  >
                                    <Star className="h-4 w-4 mr-2" />
                                    Set as Primary
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuItem
                                  onClick={() => handleTogglePrimary(artwork.id)}
                                >
                                  <Star className="h-4 w-4 mr-2" />
                                  Toggle Primary
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleDownload(artwork)}
                                >
                                  <Download className="h-4 w-4 mr-2" />
                                  Download
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleDelete(artwork.id)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenu>
                            </div>
                          </div>

                          <div className="p-3">
                            <div className="flex items-center justify-between">
                              <div className="text-sm flex-1">
                                <p className="font-medium truncate">
                                  {artwork.artwork_type.charAt(0).toUpperCase() + artwork.artwork_type.slice(1)} Cover
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {artwork.file_size && (
                                    `${(artwork.file_size / 1024 / 1024).toFixed(1)}MB`
                                  )}
                                </p>
                              </div>
                              <div className="flex items-center gap-2">
                                {artwork.is_primary && (
                                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                )}
                                {/* Mobile-friendly dropdown button */}
                                <DropdownMenu
                                  trigger={
                                    <Button
                                      size="sm"
                                      variant="ghost" 
                                      className="h-8 w-8 p-0 hover:bg-muted shrink-0"
                                    >
                                      <MoreVertical className="h-4 w-4" />
                                    </Button>
                                  }
                                  align="end"
                                  className="w-48"
                                  onOpenChange={(open) => {
                                    setOpenDropdownId(open ? `mobile_${artwork.id}` : null);
                                  }}
                                >
                                  {artwork.is_primary ? (
                                    <DropdownMenuItem
                                      onClick={() => handleUnsetPrimary(artwork.id)}
                                      disabled={processingActions.has(`primary_${artwork.id}`)}
                                    >
                                      {processingActions.has(`primary_${artwork.id}`) ? (
                                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                      ) : (
                                        <Star className="h-4 w-4 mr-2 fill-current text-yellow-500" />
                                      )}
                                      Unset as Primary
                                    </DropdownMenuItem>
                                  ) : (
                                    <DropdownMenuItem
                                      onClick={() => handleSetPrimary(artwork.id)}
                                      disabled={processingActions.has(`primary_${artwork.id}`)}
                                    >
                                      {processingActions.has(`primary_${artwork.id}`) ? (
                                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                      ) : (
                                        <Star className="h-4 w-4 mr-2" />
                                      )}
                                      Set as Primary
                                    </DropdownMenuItem>
                                  )}
                                  <DropdownMenuItem
                                    onClick={() => handleTogglePrimary(artwork.id)}
                                    disabled={processingActions.has(`primary_${artwork.id}`)}
                                  >
                                    {processingActions.has(`primary_${artwork.id}`) ? (
                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                      <Star className="h-4 w-4 mr-2" />
                                    )}
                                    Toggle Primary
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleDownload(artwork)}
                                  >
                                    <Download className="h-4 w-4 mr-2" />
                                    Download
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => window.open(artwork.file_url, '_blank')}
                                  >
                                    <Eye className="h-4 w-4 mr-2" />
                                    View Full Size
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleDelete(artwork.id)}
                                    className="text-red-600"
                                    disabled={processingActions.has(`delete_${artwork.id}`)}
                                  >
                                    {processingActions.has(`delete_${artwork.id}`) ? (
                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                      <Trash2 className="h-4 w-4 mr-2" />
                                    )}
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenu>
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                      <ImageIcon className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground mb-3">
                        No {type.label.toLowerCase()} artwork uploaded
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
};

export default ArtworkGallery;