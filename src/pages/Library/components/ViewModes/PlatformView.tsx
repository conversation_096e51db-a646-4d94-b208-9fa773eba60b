import { PlatformGroup } from '@/components/ui/utils';
import { PlatformViewProps } from '../../types';
import { UserGameWithDetails } from '@/types/database';
import { useMemo } from 'react';

export function PlatformView({ 
  games, 
  collapsedPlatforms, 
  onPlatformToggle, 
  onGameClick, 
  onStatusUpdate, 
  onRemoveGame 
}: PlatformViewProps) {
  // Group games by platform
  const gamesByPlatform = useMemo(() => {
    const grouped: { [key: string]: UserGameWithDetails[] } = {};
    
    games.forEach((game) => {
      const typedGame = game as UserGameWithDetails;
      const platforms = typedGame.game?.platforms || ['Unknown'];
      
      // Add game to each platform it's available on
      platforms.forEach(platform => {
        if (!grouped[platform]) {
          grouped[platform] = [];
        }
        grouped[platform].push(typedGame);
      });
    });
    
    // Sort platforms by game count (descending)
    const sortedPlatforms = Object.keys(grouped).sort((a, b) => 
      grouped[b].length - grouped[a].length
    );
    
    const result: { [key: string]: UserGameWithDetails[] } = {};
    sortedPlatforms.forEach(platform => {
      result[platform] = grouped[platform];
    });
    
    return result;
  }, [games]);

  return (
    <div className="space-y-4">
      {Object.entries(gamesByPlatform).map(([platform, platformGames]) => (
        <PlatformGroup
          key={platform}
          platform={platform}
          games={platformGames}
          onGameClick={onGameClick}
          onUpdateStatus={onStatusUpdate}
          onRemoveGame={onRemoveGame}
          isCollapsed={collapsedPlatforms.has(platform)}
          onToggleCollapse={() => onPlatformToggle(platform)}
        />
      ))}
    </div>
  );
}