import { renderHook, act } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useViewModeState } from '@/hooks/useViewModeState';
import { useUserPreferences } from '@/hooks/useUserPreferences';

// Mock the useUserPreferences hook
vi.mock('@/hooks/useUserPreferences');

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock the migration utilities
vi.mock('@/lib/utils/viewModePreferenceMigration', () => ({
  migrateLocalStorageViewPreferences: vi.fn(),
  getDefaultViewModeFromLegacy: vi.fn(() => 'status'),
  validateMigratedViewMode: vi.fn((mode) => mode || 'status'),
}));

describe('useViewModeState', () => {
  const mockUpdatePreferences = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    
    (useUserPreferences as any).mockReturnValue({
      preferences: {
        default_view_mode: 'status',
        default_platform: 'status',
      },
      updatePreferences: mockUpdatePreferences,
      isLoading: false,
    });
  });

  it('should initialize with default view mode', () => {
    const { result } = renderHook(() => useViewModeState());
    
    expect(result.current.viewMode).toBe('status');
    expect(result.current.collapsedPlatforms).toEqual(new Set());
    expect(result.current.collapsedFamilies).toEqual(new Set());
  });

  it('should load view mode from localStorage', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'library-view-mode') return 'platform';
      return null;
    });

    const { result } = renderHook(() => useViewModeState());
    
    expect(result.current.viewMode).toBe('platform');
  });

  it('should save view mode to localStorage and preferences when changed', () => {
    const { result } = renderHook(() => useViewModeState());
    
    act(() => {
      result.current.setViewMode('platform-family');
    });

    expect(localStorageMock.setItem).toHaveBeenCalledWith('library-view-mode', 'platform-family');
    expect(mockUpdatePreferences).toHaveBeenCalledWith(
      expect.objectContaining({
        default_view_mode: 'platform-family',
      })
    );
  });

  it('should toggle platform collapse state', () => {
    const { result } = renderHook(() => useViewModeState());
    
    act(() => {
      result.current.togglePlatform('PC');
    });

    expect(result.current.collapsedPlatforms.has('PC')).toBe(true);
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'library-collapsed-platforms',
      JSON.stringify(['PC'])
    );

    act(() => {
      result.current.togglePlatform('PC');
    });

    expect(result.current.collapsedPlatforms.has('PC')).toBe(false);
  });

  it('should toggle family collapse state', () => {
    const { result } = renderHook(() => useViewModeState());
    
    act(() => {
      result.current.toggleFamily('PC');
    });

    expect(result.current.collapsedFamilies.has('PC')).toBe(true);
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'library-collapsed-families',
      JSON.stringify(['PC'])
    );

    act(() => {
      result.current.toggleFamily('PC');
    });

    expect(result.current.collapsedFamilies.has('PC')).toBe(false);
  });

  it('should restore collapsed states from localStorage', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'library-collapsed-platforms') return JSON.stringify(['PC', 'Xbox']);
      if (key === 'library-collapsed-families') return JSON.stringify(['PlayStation']);
      return null;
    });

    const { result } = renderHook(() => useViewModeState());
    
    expect(result.current.collapsedPlatforms).toEqual(new Set(['PC', 'Xbox']));
    expect(result.current.collapsedFamilies).toEqual(new Set(['PlayStation']));
  });

  it('should handle invalid localStorage data gracefully', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'library-collapsed-platforms') return 'invalid-json';
      if (key === 'library-collapsed-families') return 'invalid-json';
      return null;
    });

    const { result } = renderHook(() => useViewModeState());
    
    // Should fall back to empty sets
    expect(result.current.collapsedPlatforms).toEqual(new Set());
    expect(result.current.collapsedFamilies).toEqual(new Set());
  });

  it('should show loading state while preferences are loading', () => {
    (useUserPreferences as any).mockReturnValue({
      preferences: {},
      updatePreferences: mockUpdatePreferences,
      isLoading: true,
    });

    const { result } = renderHook(() => useViewModeState());
    
    expect(result.current.isLoading).toBe(true);
  });
});