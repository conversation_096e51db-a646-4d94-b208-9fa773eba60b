-- COMPREHENSIVE FIX for user_api_keys RLS issue
-- This migration ensures proper table creation and RLS policies for API key storage

-- First, create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  platform TEXT NOT NULL,
  key_name TEXT NOT NULL,
  encrypted_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, platform, key_name)
);

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_user_api_keys_user_id ON user_api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_user_api_keys_platform ON user_api_keys(platform);
CREATE INDEX IF NOT EXISTS idx_user_api_keys_user_platform ON user_api_keys(user_id, platform);

-- Enable RLS
ALTER TABLE user_api_keys ENABLE ROW LEVEL SECURITY;

-- Drop ALL existing policies to start fresh
DROP POLICY IF EXISTS "Users can only access their own API keys" ON user_api_keys;
DROP POLICY IF EXISTS "user_api_keys_select" ON user_api_keys;
DROP POLICY IF EXISTS "user_api_keys_insert" ON user_api_keys;
DROP POLICY IF EXISTS "user_api_keys_update" ON user_api_keys;
DROP POLICY IF EXISTS "user_api_keys_delete" ON user_api_keys;
DROP POLICY IF EXISTS "user_api_keys_all" ON user_api_keys;

-- Create comprehensive RLS policies that support upsert operations
CREATE POLICY "user_api_keys_select" ON user_api_keys
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "user_api_keys_insert" ON user_api_keys
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "user_api_keys_update" ON user_api_keys
  FOR UPDATE 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "user_api_keys_delete" ON user_api_keys
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Also create the import_history table if it doesn't exist (needed for Steam import)
CREATE TABLE IF NOT EXISTS import_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  platform TEXT NOT NULL,
  games_imported INTEGER DEFAULT 0,
  games_updated INTEGER DEFAULT 0,
  duplicates_found INTEGER DEFAULT 0,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  error_message TEXT,
  metadata JSONB DEFAULT '{}'
);

-- Create indexes for import history
CREATE INDEX IF NOT EXISTS idx_import_history_user_id ON import_history(user_id);
CREATE INDEX IF NOT EXISTS idx_import_history_platform ON import_history(platform);
CREATE INDEX IF NOT EXISTS idx_import_history_status ON import_history(status);
CREATE INDEX IF NOT EXISTS idx_import_history_started_at ON import_history(started_at DESC);

-- Enable RLS for import history
ALTER TABLE import_history ENABLE ROW LEVEL SECURITY;

-- Drop existing import history policies
DROP POLICY IF EXISTS "Users can only access their own import history" ON import_history;
DROP POLICY IF EXISTS "import_history_select" ON import_history;
DROP POLICY IF EXISTS "import_history_insert" ON import_history;
DROP POLICY IF EXISTS "import_history_update" ON import_history;
DROP POLICY IF EXISTS "import_history_delete" ON import_history;

-- Create RLS policies for import history
CREATE POLICY "import_history_select" ON import_history
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "import_history_insert" ON import_history
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "import_history_update" ON import_history
  FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "import_history_delete" ON import_history
  FOR DELETE USING (auth.uid() = user_id);

-- Create function for updating timestamps if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for user_api_keys if it doesn't exist
DROP TRIGGER IF EXISTS update_user_api_keys_updated_at ON user_api_keys;
CREATE TRIGGER update_user_api_keys_updated_at 
    BEFORE UPDATE ON user_api_keys 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();