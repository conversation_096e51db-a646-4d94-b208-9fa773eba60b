import { supabase } from '../supabase';
import {
  UserRecommendationRecord,
  UserRecommendationUpdateData,
} from '../../types/database';

// AI Recommendation interfaces
export interface GameRecommendation {
  igdb_id: number;
  title: string;
  reason: string;
  confidence: number;
  genres?: string[];
  platforms?: string[];
  release_year?: number;
  metacritic_score?: number;
  similar_to?: string[];
}

export interface UserGameProfile {
  game_id: string;
  title: string;
  genres: string[];
  platforms: string[];
  user_rating?: number;
  hours_played?: number;
  status: string;
  date_added: string;
  last_played?: string;
}

export interface RecommendationRequest {
  count?: number;
  platforms?: string[];
  genres?: string[];
  exclude_owned?: boolean;
  min_rating?: number;
  max_age_rating?: string;
  include_upcoming?: boolean;
}

/**
 * AI Recommendation Service
 * Handles AI-powered game recommendations using external APIs
 */
export class AIRecommendationService {
  private readonly DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';
  private readonly OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';
  private readonly DEEPSEEK_API_KEY: string;
  private readonly OPENAI_API_KEY: string;

  constructor() {
    this.DEEPSEEK_API_KEY = import.meta.env.VITE_DEEPSEEK_API_KEY || '';
    this.OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY || '';
    
    if (!this.DEEPSEEK_API_KEY && !this.OPENAI_API_KEY) {
      throw new Error('Either VITE_DEEPSEEK_API_KEY or VITE_OPENAI_API_KEY environment variable is required for AI recommendations');
    }
  }

  /**
   * Generate AI-powered game recommendations based on user's actual games
   */
  async generateUserBasedRecommendations(
    userId: string, 
    userGames: UserGameProfile[], 
    request: RecommendationRequest = {}
  ): Promise<GameRecommendation[]> {
    try {
      const userProfile = this.buildUserProfile(userGames);
      
      const recommendations = await this.callAIAPI(userProfile, request);
      const validatedRecommendations = await this.validateAndEnrichRecommendations(recommendations, userId);
      
      return validatedRecommendations.slice(0, request.count || 10);
    } catch (error) {
      console.error('Error generating user-based recommendations:', error);
      throw error;
    }
  }

  /**
   * Generate similar game recommendations based on a specific game
   */
  async generateSimilarGameRecommendations(
    gameTitle: string,
    gameGenres: string[] = [],
    gamePlatforms: string[] = [],
    count: number = 6
  ): Promise<GameRecommendation[]> {
    try {
      const prompt = this.buildSimilarGamePrompt(gameTitle, gameGenres, gamePlatforms, count);
      const recommendations = await this.callAIAPIWithPrompt(prompt);
      
      return recommendations.slice(0, count);
    } catch (error) {
      console.error('Error generating similar game recommendations:', error);
      throw error;
    }
  }

  /**
   * Generate recommendations based on user preferences and trends
   */
  async generateTrendingRecommendations(
    preferences: {
      genres?: string[];
      platforms?: string[];
      rating_range?: [number, number];
    } = {},
    count: number = 10
  ): Promise<GameRecommendation[]> {
    try {
      const prompt = this.buildTrendingPrompt(preferences, count);
      const recommendations = await this.callAIAPIWithPrompt(prompt);
      
      return recommendations.slice(0, count);
    } catch (error) {
      console.error('Error generating trending recommendations:', error);
      throw error;
    }
  }

  /**
   * Call AI API with user profile and request
   */
  private async callAIAPI(userProfile: string, request: RecommendationRequest): Promise<GameRecommendation[]> {
    const prompt = this.buildRecommendationPrompt(userProfile, request);
    return this.callAIAPIWithPrompt(prompt);
  }

  /**
   * Call AI API with custom prompt (DeepSeek or OpenAI)
   */
  private async callAIAPIWithPrompt(prompt: string): Promise<GameRecommendation[]> {
    // Try DeepSeek first, then fallback to OpenAI
    if (this.DEEPSEEK_API_KEY) {
      try {
        return await this.callDeepSeekAPI(prompt);
      } catch (error) {
        console.warn('DeepSeek API failed, trying OpenAI:', error);
        if (this.OPENAI_API_KEY) {
          return await this.callOpenAIAPI(prompt);
        }
        throw error;
      }
    } else if (this.OPENAI_API_KEY) {
      return await this.callOpenAIAPI(prompt);
    } else {
      throw new Error('No AI API keys configured');
    }
  }

  /**
   * Call DeepSeek API
   */
  private async callDeepSeekAPI(prompt: string): Promise<GameRecommendation[]> {
    const response = await fetch(this.DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.DEEPSEEK_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const aiResponse = data.choices?.[0]?.message?.content;

    if (!aiResponse) {
      throw new Error('No response from DeepSeek API');
    }

    return this.parseAIResponse(aiResponse);
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAIAPI(prompt: string): Promise<GameRecommendation[]> {
    const response = await fetch(this.OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const aiResponse = data.choices?.[0]?.message?.content;

    if (!aiResponse) {
      throw new Error('No response from OpenAI API');
    }

    return this.parseAIResponse(aiResponse);
  }

  /**
   * Build recommendation prompt for AI
   */
  private buildRecommendationPrompt(userProfile: string, request: RecommendationRequest): string {
    return `
You are a gaming AI assistant that provides personalized game recommendations based on a user's actual game collection and preferences.

USER'S GAME COLLECTION ANALYSIS:
${userProfile}

RECOMMENDATION REQUIREMENTS:
- Number of recommendations: ${request.count || 10}
- Preferred platforms: ${request.platforms?.join(', ') || 'Any'}
- Preferred genres: ${request.genres?.join(', ') || 'Any'}
- Exclude owned games: ${request.exclude_owned ? 'Yes' : 'No'}
- Minimum rating: ${request.min_rating || 'Any'}
- Maximum age rating: ${request.max_age_rating || 'Any'}
- Include upcoming games: ${request.include_upcoming ? 'Yes' : 'No'}

Please provide game recommendations in the following JSON format:
[
  {
    "igdb_id": 1942,
    "title": "The Witcher 3: Wild Hunt",
    "reason": "Based on your love for RPGs like Skyrim and your high rating for story-driven games",
    "confidence": 0.95,
    "genres": ["RPG", "Open World"],
    "platforms": ["PC", "PlayStation 4", "Xbox One"],
    "release_year": 2015,
    "metacritic_score": 93,
    "similar_to": ["The Elder Scrolls V: Skyrim", "Dragon Age: Inquisition"]
  }
]

Focus on games that match the user's demonstrated preferences and playing patterns. Provide detailed reasoning for each recommendation.`;
  }

  /**
   * Build similar game prompt
   */
  private buildSimilarGamePrompt(gameTitle: string, genres: string[], platforms: string[], count: number): string {
    return `
You are a gaming expert. Recommend ${count} games similar to "${gameTitle}".

Game Details:
- Genres: ${genres.join(', ') || 'Unknown'}
- Platforms: ${platforms.join(', ') || 'Unknown'}

Please provide recommendations in JSON format:
[
  {
    "igdb_id": 1942,
    "title": "Game Title",
    "reason": "Why this game is similar",
    "confidence": 0.85,
    "genres": ["Genre1", "Genre2"],
    "platforms": ["Platform1", "Platform2"],
    "release_year": 2020,
    "metacritic_score": 85,
    "similar_to": ["${gameTitle}"]
  }
]

Focus on games with similar gameplay mechanics, themes, or style.`;
  }

  /**
   * Build trending recommendations prompt
   */
  private buildTrendingPrompt(preferences: any, count: number): string {
    return `
You are a gaming expert. Recommend ${count} trending and highly-rated games from recent years.

User Preferences:
- Preferred genres: ${preferences.genres?.join(', ') || 'Any'}
- Preferred platforms: ${preferences.platforms?.join(', ') || 'Any'}
- Rating range: ${preferences.rating_range ? `${preferences.rating_range[0]}-${preferences.rating_range[1]}` : 'Any'}

Please provide recommendations in JSON format focusing on popular, well-reviewed games from 2020-2024:
[
  {
    "igdb_id": 1942,
    "title": "Game Title",
    "reason": "Why this game is trending and matches preferences",
    "confidence": 0.90,
    "genres": ["Genre1", "Genre2"],
    "platforms": ["Platform1", "Platform2"],
    "release_year": 2023,
    "metacritic_score": 88
  }
]`;
  }

  /**
   * Build user profile from games
   */
  private buildUserProfile(userGames: UserGameProfile[]): string {
    const totalGames = userGames.length;
    const completedGames = userGames.filter(g => g.status === 'completed').length;
    const currentlyPlaying = userGames.filter(g => g.status === 'playing').length;
    
    // Analyze genres
    const genreCount: Record<string, number> = {};
    userGames.forEach(game => {
      game.genres.forEach(genre => {
        genreCount[genre] = (genreCount[genre] || 0) + 1;
      });
    });
    
    const topGenres = Object.entries(genreCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([genre, count]) => `${genre} (${count} games)`);

    // Analyze platforms
    const platformCount: Record<string, number> = {};
    userGames.forEach(game => {
      game.platforms.forEach(platform => {
        platformCount[platform] = (platformCount[platform] || 0) + 1;
      });
    });
    
    const topPlatforms = Object.entries(platformCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([platform, count]) => `${platform} (${count} games)`);

    // Get highly rated games
    const highlyRated = userGames
      .filter(g => g.user_rating && g.user_rating >= 8)
      .sort((a, b) => (b.user_rating || 0) - (a.user_rating || 0))
      .slice(0, 5)
      .map(g => `${g.title} (${g.user_rating}/10)`);

    return `
COLLECTION OVERVIEW:
- Total games: ${totalGames}
- Completed: ${completedGames}
- Currently playing: ${currentlyPlaying}

TOP GENRES:
${topGenres.join('\n')}

PREFERRED PLATFORMS:
${topPlatforms.join('\n')}

HIGHLY RATED GAMES:
${highlyRated.join('\n')}

RECENT ACTIVITY:
${userGames.slice(0, 5).map(g => `${g.title} (${g.status})`).join('\n')}`;
  }

  /**
   * Parse AI response and extract recommendations
   */
  private parseAIResponse(aiResponse: string): GameRecommendation[] {
    try {
      const cleanResponse = aiResponse.replace(/```json\n?|\n?```/g, '').trim();
      
      // Try to find JSON array in the response
      const jsonMatch = cleanResponse.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No JSON array found in AI response');
      }
      
      const recommendations = JSON.parse(jsonMatch[0]);
      
      if (!Array.isArray(recommendations)) {
        throw new Error('AI response is not an array');
      }
      
      return recommendations.map(rec => ({
        igdb_id: rec.igdb_id || 0,
        title: rec.title || 'Unknown Game',
        reason: rec.reason || 'No reason provided',
        confidence: rec.confidence || 0.5,
        genres: rec.genres || [],
        platforms: rec.platforms || [],
        release_year: rec.release_year,
        metacritic_score: rec.metacritic_score,
        similar_to: rec.similar_to || []
      }));
    } catch (error) {
      console.error('Error parsing AI response:', error);
      throw new Error('Failed to parse AI recommendations');
    }
  }

  /**
   * Validate and enrich recommendations with additional data
   */
  private async validateAndEnrichRecommendations(
    recommendations: GameRecommendation[], 
    userId: string
  ): Promise<GameRecommendation[]> {
    // Here you could add validation logic, check against user's existing games, etc.
    // For now, just return the recommendations as-is
    console.debug(`Validating ${recommendations.length} recommendations for user ${userId}`);
    return recommendations.filter(rec => rec.title && rec.title !== 'Unknown Game');
  }
}

/**
 * User Recommendations Database API
 * Handles storing and retrieving recommendations from the database
 */
export class UserRecommendationsAPI {
  /**
   * Get user-based recommendations from database
   */
  async getUserBasedRecommendations(userId: string, limit: number = 20) {
    try {
      await this.ensureUserRecords(userId);
      
      return supabase
        .from('user_recommendations')
        .select('*')
        .eq('user_id', userId)
        .eq('recommendation_type', 'user_based')
        .order('confidence_score', { ascending: false })
        .order('created_at', { ascending: false })
        .limit(limit);
    } catch (error) {
      console.error('Error getting user-based recommendations:', error);
      throw error;
    }
  }

  /**
   * Get similar game recommendations from database
   */
  async getSimilarGameRecommendations(userId: string, sourceGameId?: string, limit: number = 20) {
    try {
      await this.ensureUserRecords(userId);
      
      let query = supabase
        .from('user_recommendations')
        .select('*')
        .eq('user_id', userId)
        .eq('recommendation_type', 'similar_game')
        .order('confidence_score', { ascending: false })
        .limit(limit);

      if (sourceGameId) {
        query = query.eq('source_game_id', sourceGameId);
      }

      return query;
    } catch (error) {
      console.error('Error getting similar game recommendations:', error);
      throw error;
    }
  }

  /**
   * Save recommendations to database
   */
  async saveRecommendations(recommendations: Omit<UserRecommendationRecord, 'id' | 'created_at' | 'updated_at'>[]) {
    return supabase
      .from('user_recommendations')
      .upsert(recommendations, {
        onConflict: 'user_id,igdb_id,recommendation_type,source_game_id'
      })
      .select();
  }

  /**
   * Clear user recommendations
   */
  async clearUserRecommendations(userId: string, recommendationType?: 'user_based' | 'similar_game') {
    let query = supabase
      .from('user_recommendations')
      .delete()
      .eq('user_id', userId);

    if (recommendationType) {
      query = query.eq('recommendation_type', recommendationType);
    }

    return query;
  }

  /**
   * Update a specific recommendation
   */
  async updateRecommendation(id: string, updates: UserRecommendationUpdateData) {
    return supabase.from('user_recommendations').update(updates).eq('id', id).select().single();
  }

  /**
   * Delete a specific recommendation
   */
  async deleteRecommendation(id: string) {
    return supabase.from('user_recommendations').delete().eq('id', id);
  }

  /**
   * Get recommendation statistics for a user
   */
  async getRecommendationStats(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_recommendations')
        .select('recommendation_type, confidence_score')
        .eq('user_id', userId);

      if (error) throw error;

      const stats = {
        total: data.length,
        user_based: data.filter(r => r.recommendation_type === 'user_based').length,
        similar_game: data.filter(r => r.recommendation_type === 'similar_game').length,
        avg_confidence: data.reduce((sum, r) => sum + (r.confidence_score || 0), 0) / data.length || 0
      };

      return { data: stats, error: null };
    } catch (error) {
      console.error('Error getting recommendation stats:', error);
      return { data: null, error };
    }
  }

  /**
   * Helper function to ensure user records exist
   */
  private async ensureUserRecords(userId: string) {
    try {
      // Check if user profile exists
      const { error: profileError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        const { error: createProfileError } = await supabase
          .from('user_profiles')
          .insert({ id: userId });
        
        if (createProfileError) {
          console.warn('Failed to create user profile:', createProfileError);
        }
      }

      // Check if user preferences exist
      const { error: preferencesError } = await supabase
        .from('user_preferences')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (preferencesError && preferencesError.code === 'PGRST116') {
        // Preferences don't exist, create them
        const { error: createPreferencesError } = await supabase
          .from('user_preferences')
          .insert({ user_id: userId });
        
        if (createPreferencesError) {
          console.warn('Failed to create user preferences:', createPreferencesError);
        }
      }
    } catch (error) {
      console.warn('Error ensuring user records:', error);
    }
  }
}

// Export API service instances
export const aiRecommendationService = new AIRecommendationService();
export const userRecommendationsAPI = new UserRecommendationsAPI();

// Keep backward compatibility
export const recommendations = userRecommendationsAPI;