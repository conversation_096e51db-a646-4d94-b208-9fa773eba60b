import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { searchCacheService, CacheStats } from '@/lib/services/searchCacheService';
import { SearchResult, SearchIntent } from '@/lib/enhancedSearchService';
import { useAuth } from '@/contexts/AuthContext';

interface UseSearchCacheOptions {
  enabled?: boolean;
  ttl?: number;
  autoWarmup?: boolean;
}

interface UseSearchCacheReturn {
  getCachedResult: (query: string, additionalParams?: Record<string, unknown>) => Promise<SearchResult | null>;
  cacheResult: (query: string, intent: SearchIntent, result: SearchResult, additionalParams?: Record<string, unknown>) => Promise<void>;
  clearCache: () => Promise<void>;
  warmupCache: (customQueries?: string[]) => Promise<void>;
  cacheStats: CacheStats | null;
  isEnabled: boolean;
  setEnabled: (enabled: boolean) => void;
}

/**
 * Hook for managing search result caching
 */
export function useSearchCache(options: UseSearchCacheOptions = {}): UseSearchCacheReturn {
  const { 
    enabled: enabledProp = true, 
    ttl = 30 * 60 * 1000, // 30 minutes
    autoWarmup = true 
  } = options;
  
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isEnabled, setIsEnabled] = useState(enabledProp);
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);

  // Query for cache statistics
  const { data: stats } = useQuery({
    queryKey: ['cache-stats'],
    queryFn: () => searchCacheService.getCacheStats(),
    refetchInterval: 30000, // Update every 30 seconds
    enabled: isEnabled
  });

  // Update local cache stats when query data changes
  useEffect(() => {
    if (stats) {
      setCacheStats(stats);
    }
  }, [stats]);

  // Auto warmup on mount if enabled
  useEffect(() => {
    if (isEnabled && autoWarmup) {
      warmupCache();
    }
  }, [isEnabled, autoWarmup, warmupCache]);

  /**
   * Get cached search result
   */
  const getCachedResult = useCallback(async (
    query: string, 
    additionalParams?: Record<string, unknown>
  ): Promise<SearchResult | null> => {
    if (!isEnabled) return null;
    
    try {
      return await searchCacheService.getCachedResult(
        query,
        user?.id,
        additionalParams
      );
    } catch (error) {
      console.error('Error getting cached result:', error);
      return null;
    }
  }, [isEnabled, user?.id]);

  /**
   * Cache search result
   */
  const cacheResult = useCallback(async (
    query: string,
    intent: SearchIntent,
    result: SearchResult,
    additionalParams?: Record<string, unknown>
  ): Promise<void> => {
    if (!isEnabled) return;
    
    try {
      await searchCacheService.cacheResult(
        query,
        intent,
        result,
        user?.id,
        ttl,
        additionalParams
      );
      
      // Invalidate cache stats to trigger refresh
      queryClient.invalidateQueries({ queryKey: ['cache-stats'] });
    } catch (error) {
      console.error('Error caching result:', error);
    }
  }, [isEnabled, user?.id, ttl, queryClient]);

  /**
   * Clear cache
   */
  const clearCache = useCallback(async (): Promise<void> => {
    try {
      await searchCacheService.clearCache(user?.id);
      
      // Invalidate cache stats and refresh
      queryClient.invalidateQueries({ queryKey: ['cache-stats'] });
      
      // Clear React Query cache as well
      queryClient.clear();
      
      console.log('Cache cleared successfully');
    } catch (error) {
      console.error('Error clearing cache:', error);
      throw error;
    }
  }, [user?.id, queryClient]);

  /**
   * Warm up cache with popular queries
   */
  const warmupCache = useCallback(async (customQueries?: string[]): Promise<void> => {
    if (!isEnabled) return;
    
    try {
      await searchCacheService.warmupCache(customQueries);
      console.log('Cache warmup completed');
    } catch (error) {
      console.error('Error warming up cache:', error);
    }
  }, [isEnabled]);

  /**
   * Enable/disable caching
   */
  const handleSetEnabled = useCallback((enabled: boolean) => {
    setIsEnabled(enabled);
    
    // Save preference to localStorage
    try {
      localStorage.setItem('codexa_cache_enabled', JSON.stringify(enabled));
    } catch (error) {
      console.error('Error saving cache preference:', error);
    }
  }, []);

  // Load cache preference on mount
  useEffect(() => {
    try {
      const savedPreference = localStorage.getItem('codexa_cache_enabled');
      if (savedPreference !== null) {
        setIsEnabled(JSON.parse(savedPreference));
      }
    } catch (error) {
      console.error('Error loading cache preference:', error);
    }
  }, []);

  return {
    getCachedResult,
    cacheResult,
    clearCache,
    warmupCache,
    cacheStats,
    isEnabled,
    setEnabled: handleSetEnabled
  };
}

/**
 * Hook for cache management operations
 */
export function useCacheManagement() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const getCacheStats = useCallback((): CacheStats => {
    return searchCacheService.getCacheStats();
  }, []);

  const clearUserCache = useCallback(async (): Promise<void> => {
    await searchCacheService.clearCache(user?.id);
    queryClient.invalidateQueries({ queryKey: ['cache-stats'] });
  }, [user?.id, queryClient]);

  const clearAllCache = useCallback(async (): Promise<void> => {
    await searchCacheService.clearCache();
    queryClient.clear();
    queryClient.invalidateQueries({ queryKey: ['cache-stats'] });
  }, [queryClient]);

  const getWarmupQueries = useCallback(async () => {
    return await searchCacheService.getWarmupQueries();
  }, []);

  const performWarmup = useCallback(async (queries?: string[]) => {
    await searchCacheService.warmupCache(queries);
  }, []);

  return {
    getCacheStats,
    clearUserCache,
    clearAllCache,
    getWarmupQueries,
    performWarmup
  };
}

/**
 * Hook for monitoring cache performance
 */
export function useCachePerformance() {
  const [performance, setPerformance] = useState({
    enabled: false,
    metrics: {
      searchTime: 0,
      cacheHitTime: 0,
      cacheMissTime: 0,
      avgSearchTime: 0
    }
  });

  const startPerformanceMonitoring = useCallback(() => {
    setPerformance(prev => ({ ...prev, enabled: true }));
  }, []);

  const stopPerformanceMonitoring = useCallback(() => {
    setPerformance(prev => ({ ...prev, enabled: false }));
  }, []);

  const recordSearchTime = useCallback((time: number, fromCache: boolean) => {
    if (!performance.enabled) return;

    setPerformance(prev => {
      const newMetrics = { ...prev.metrics };
      
      if (fromCache) {
        newMetrics.cacheHitTime = time;
      } else {
        newMetrics.cacheMissTime = time;
      }
      
      // Update average
      newMetrics.avgSearchTime = (newMetrics.cacheHitTime + newMetrics.cacheMissTime) / 2;
      
      return {
        ...prev,
        metrics: newMetrics
      };
    });
  }, [performance.enabled]);

  return {
    performance: performance.metrics,
    isMonitoring: performance.enabled,
    startPerformanceMonitoring,
    stopPerformanceMonitoring,
    recordSearchTime
  };
}

// Export types
export type { UseSearchCacheOptions, UseSearchCacheReturn };