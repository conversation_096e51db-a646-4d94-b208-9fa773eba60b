import { describe, it, expect } from 'vitest';
import {
  groupGamesByPlatformFamily,
  calculatePlatformFamilyStats,
} from '@/lib/utils/platformFamilyUtils';
import {
  createMockGameCollection,
  assertPlatformFamilyStats,
} from '@/__tests__/utils/test-helpers';

describe('Platform Family Statistics Updates', () => {
  it('should recalculate statistics when games are modified', () => {
    // Initial game collection
    const initialGames = createMockGameCollection([
      { id: '1', platform: 'PC', status: 'playing', personalRating: 8.0, hoursPlayed: 10 },
      { id: '2', platform: 'PC', status: 'backlog', personalRating: 7.5, hoursPlayed: 0 },
      { id: '3', platform: 'Xbox One', status: 'completed', personalRating: 9.0, hoursPlayed: 25 },
    ]);

    // Group games and get initial stats
    const initialGroups = groupGamesByPlatformFamily(initialGames);
    const pcGroup = initialGroups.find(g => g.family === 'PC')!;
    const xboxGroup = initialGroups.find(g => g.family === 'Xbox')!;

    // Verify initial PC stats
    assertPlatformFamilyStats(pcGroup.stats, {
      total: 2,
      playing: 1,
      backlog: 1,
      completed: 0,
      completionRate: 0, // 0 completed out of 2 games
      averageRating: 7.8, // (8.0 + 7.5) / 2
      totalHours: 10,
    });

    // Verify initial Xbox stats
    assertPlatformFamilyStats(xboxGroup.stats, {
      total: 1,
      completed: 1,
      completionRate: 100, // 1 completed out of 1 game
      averageRating: 9.0,
      totalHours: 25,
    });

    // Simulate game status update: change PC game from backlog to completed
    const updatedGames = initialGames.map(game => 
      game.id === '2' 
        ? { ...game, status: 'completed' as const, hours_played: 15 }
        : game
    );

    // Recalculate statistics with updated games
    const updatedGroups = groupGamesByPlatformFamily(updatedGames);
    const updatedPcGroup = updatedGroups.find(g => g.family === 'PC')!;

    // Verify updated PC stats reflect the change
    assertPlatformFamilyStats(updatedPcGroup.stats, {
      total: 2,
      playing: 1,
      backlog: 0,
      completed: 1,
      completionRate: 50, // 1 completed out of 2 games
      averageRating: 7.8, // Same rating average
      totalHours: 25, // 10 + 15
    });
  });

  it('should handle adding new games to platform families', () => {
    // Start with empty collection
    const initialGames = createMockGameCollection([]);
    const initialGroups = groupGamesByPlatformFamily(initialGames);
    
    expect(initialGroups).toHaveLength(0);

    // Add games to different platform families
    const gamesWithNewAdditions = createMockGameCollection([
      { id: '1', platform: 'PC', status: 'playing', personalRating: 8.0 },
      { id: '2', platform: 'PlayStation 4', status: 'completed', personalRating: 9.0 },
      { id: '3', platform: 'Nintendo Switch', status: 'wishlist' },
    ]);

    const updatedGroups = groupGamesByPlatformFamily(gamesWithNewAdditions);
    
    expect(updatedGroups).toHaveLength(3);
    
    const pcGroup = updatedGroups.find(g => g.family === 'PC')!;
    const playstationGroup = updatedGroups.find(g => g.family === 'PlayStation')!;
    const nintendoGroup = updatedGroups.find(g => g.family === 'Nintendo')!;

    assertPlatformFamilyStats(pcGroup.stats, {
      total: 1,
      playing: 1,
      completionRate: 0, // 0 completed out of 1 non-wishlist game
    });

    assertPlatformFamilyStats(playstationGroup.stats, {
      total: 1,
      completed: 1,
      completionRate: 100, // 1 completed out of 1 non-wishlist game
    });

    assertPlatformFamilyStats(nintendoGroup.stats, {
      total: 1,
      wishlist: 1,
      completionRate: 0, // No non-wishlist games to complete
    });
  });

  it('should handle removing games from platform families', () => {
    // Start with games in multiple families
    const initialGames = createMockGameCollection([
      { id: '1', platform: 'PC', status: 'completed', personalRating: 8.0 },
      { id: '2', platform: 'PC', status: 'playing', personalRating: 7.0 },
      { id: '3', platform: 'Xbox One', status: 'backlog' },
    ]);

    const initialGroups = groupGamesByPlatformFamily(initialGames);
    expect(initialGroups).toHaveLength(2);

    const initialPcGroup = initialGroups.find(g => g.family === 'PC')!;
    assertPlatformFamilyStats(initialPcGroup.stats, {
      total: 2,
      playing: 1,
      completed: 1,
      completionRate: 50,
    });

    // Remove one PC game
    const gamesAfterRemoval = initialGames.filter(game => game.id !== '2');
    const updatedGroups = groupGamesByPlatformFamily(gamesAfterRemoval);
    
    const updatedPcGroup = updatedGroups.find(g => g.family === 'PC')!;
    assertPlatformFamilyStats(updatedPcGroup.stats, {
      total: 1,
      playing: 0,
      completed: 1,
      completionRate: 100, // 1 completed out of 1 game
    });

    // Remove all PC games
    const gamesWithNoPc = initialGames.filter(game => !game.game?.platform?.includes('PC'));
    const groupsWithNoPc = groupGamesByPlatformFamily(gamesWithNoPc);
    
    // PC family should not exist anymore
    const pcGroupAfterRemoval = groupsWithNoPc.find(g => g.family === 'PC');
    expect(pcGroupAfterRemoval).toBeUndefined();
    
    // Only Xbox family should remain
    expect(groupsWithNoPc).toHaveLength(1);
    expect(groupsWithNoPc[0].family).toBe('Xbox');
  });

  it('should recalculate stats when game ratings change', () => {
    const games = createMockGameCollection([
      { id: '1', platform: 'PC', status: 'completed', personalRating: 6.0 },
      { id: '2', platform: 'PC', status: 'completed', personalRating: 8.0 },
    ]);

    const initialGroups = groupGamesByPlatformFamily(games);
    const initialPcGroup = initialGroups.find(g => g.family === 'PC')!;
    
    assertPlatformFamilyStats(initialPcGroup.stats, {
      total: 2,
      averageRating: 7.0, // (6.0 + 8.0) / 2
    });

    // Update one game's rating
    const updatedGames = games.map(game => 
      game.id === '1' 
        ? { ...game, personal_rating: 10.0 }
        : game
    );

    const updatedGroups = groupGamesByPlatformFamily(updatedGames);
    const updatedPcGroup = updatedGroups.find(g => g.family === 'PC')!;
    
    assertPlatformFamilyStats(updatedPcGroup.stats, {
      total: 2,
      averageRating: 9.0, // (10.0 + 8.0) / 2
    });
  });

  it('should recalculate stats when game hours change', () => {
    const games = createMockGameCollection([
      { id: '1', platform: 'PC', status: 'playing', hoursPlayed: 10.5 },
      { id: '2', platform: 'PC', status: 'completed', hoursPlayed: 25.0 },
    ]);

    const initialGroups = groupGamesByPlatformFamily(games);
    const initialPcGroup = initialGroups.find(g => g.family === 'PC')!;
    
    assertPlatformFamilyStats(initialPcGroup.stats, {
      total: 2,
      totalHours: 35.5, // 10.5 + 25.0
    });

    // Update one game's hours
    const updatedGames = games.map(game => 
      game.id === '1' 
        ? { ...game, hours_played: 20.0 }
        : game
    );

    const updatedGroups = groupGamesByPlatformFamily(updatedGames);
    const updatedPcGroup = updatedGroups.find(g => g.family === 'PC')!;
    
    assertPlatformFamilyStats(updatedPcGroup.stats, {
      total: 2,
      totalHours: 45.0, // 20.0 + 25.0
    });
  });

  it('should handle direct stats recalculation', () => {
    const games = createMockGameCollection([
      { id: '1', platform: 'PC', status: 'playing', personalRating: 8.0, hoursPlayed: 10 },
      { id: '2', platform: 'PC', status: 'completed', personalRating: 9.0, hoursPlayed: 25 },
      { id: '3', platform: 'PC', status: 'backlog' },
    ]);

    // Calculate stats directly
    const stats = calculatePlatformFamilyStats(games);

    assertPlatformFamilyStats(stats, {
      total: 3,
      playing: 1,
      completed: 1,
      backlog: 1,
      completionRate: 33, // 1 completed out of 3 games (rounded)
      averageRating: 8.5, // (8.0 + 9.0) / 2
      totalHours: 35.0, // 10 + 25
    });

    // Modify games and recalculate
    const modifiedGames = games.map(game => 
      game.id === '3' 
        ? { ...game, status: 'completed' as const, personal_rating: 7.0, hours_played: 15 }
        : game
    );

    const updatedStats = calculatePlatformFamilyStats(modifiedGames);

    assertPlatformFamilyStats(updatedStats, {
      total: 3,
      playing: 1,
      completed: 2,
      backlog: 0,
      completionRate: 67, // 2 completed out of 3 games (rounded)
      averageRating: 8.0, // (8.0 + 9.0 + 7.0) / 3
      totalHours: 50.0, // 10 + 25 + 15
    });
  });
});