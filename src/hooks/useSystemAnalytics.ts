/**
 * useSystemAnalytics Hook
 * Unified hook for managing all system analytics data
 */

import { useState, useCallback, useMemo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { databasePerformanceService } from '@/lib/services/databasePerformanceService';
import { errorAnalyticsService } from '@/lib/services/errorAnalyticsService';
import { artworkAPIRouter } from '@/lib/services/artworkAPIRouter';
import { rateLimitManager } from '@/lib/services/rateLimitManager';
import { ArtworkService } from '@/lib/apiKeyStore';

// Type definitions
export interface SystemHealth {
  uptime: number;
  avgResponseTime: number;
  errorRate: number;
  databaseHealth: number;
  activeUsers: number;
  resources: {
    cpu: number;
    memory: number;
    networkIn: number;
    networkOut: number;
  };
  performance: {
    apiResponseTime: number;
    dbResponseTime: number;
    externalApiResponseTime: number;
    requestsPerSecond: number;
    concurrentUsers: number;
    queueLength: number;
    cacheHitRate: number;
  };
}

export interface DatabaseMetrics {
  connections: {
    active: number;
    max: number;
  };
  avgQueryTime: number;
  slowQueries: number;
  indexHitRate: number;
  storage: {
    size: number;
    usage: number;
  };
  tables: {
    count: number;
    indexes: number;
    records: number;
    avgSize: number;
  };
}

export interface ErrorAnalytics {
  total: number;
  rate: number;
  byType: Record<string, number>;
  recent: Array<{
    type: string;
    message: string;
    timestamp: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>;
}

export interface ArtworkAnalytics {
  serviceStats: Record<ArtworkService, {
    totalRequests: number;
    successfulRequests: number;
    averageResponseTime: number;
    availableKeys: number;
    isCurrentlyLimited: boolean;
  }> | null;
  queueStatus: {
    queueLength: number;
    activeRequests: number;
    processing: boolean;
  } | null;
}

export function useSystemAnalytics() {
  const queryClient = useQueryClient();
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds

  // System Health Query
  const {
    data: systemHealth,
    isLoading: systemHealthLoading,
    error: systemHealthError
  } = useQuery({
    queryKey: ['system-health'],
    queryFn: async (): Promise<SystemHealth> => {
      // TODO: Replace with real system monitoring integration
      // This is currently MOCK DATA for demonstration purposes
      // Need to implement: Server metrics, Docker stats, or cloud monitoring APIs
      const mockHealth: SystemHealth = {
        // TODO: Get real uptime from server monitoring service
        uptime: 99.8, // MOCK: Should come from system uptime
        
        // TODO: Calculate from actual API response time logs
        avgResponseTime: Math.floor(Math.random() * 500) + 200, // MOCK: 200-700ms
        
        // TODO: Get from error tracking service (Sentry, etc.)
        errorRate: Math.random() * 2, // MOCK: 0-2%
        
        // TODO: Get from Supabase health checks or database monitoring
        databaseHealth: 95 + Math.random() * 5, // MOCK: 95-100%
        
        // TODO: Get from authentication service active sessions
        activeUsers: Math.floor(Math.random() * 100) + 50, // MOCK
        
        resources: {
          // TODO: Get from system monitoring (htop, Docker stats, cloud metrics)
          cpu: Math.floor(Math.random() * 40) + 30, // MOCK: 30-70%
          memory: Math.floor(Math.random() * 30) + 50, // MOCK: 50-80%
          networkIn: Math.floor(Math.random() * 1000000), // MOCK: Random bytes
          networkOut: Math.floor(Math.random() * 1000000) // MOCK: Random bytes
        },
        
        performance: {
          // TODO: Get from API gateway logs and response time monitoring
          apiResponseTime: Math.floor(Math.random() * 300) + 100, // MOCK
          dbResponseTime: Math.floor(Math.random() * 50) + 20, // MOCK
          externalApiResponseTime: Math.floor(Math.random() * 1000) + 500, // MOCK
          requestsPerSecond: Math.floor(Math.random() * 50) + 10, // MOCK
          concurrentUsers: Math.floor(Math.random() * 25) + 5, // MOCK
          queueLength: Math.floor(Math.random() * 10), // MOCK
          cacheHitRate: Math.floor(Math.random() * 30) + 60 // MOCK: 60-90%
        }
      };
      return mockHealth;
    },
    refetchInterval: refreshInterval,
    staleTime: 25000 // 25 seconds
  });

  // Database Metrics Query
  const {
    data: databaseMetrics,
    isLoading: databaseLoading
  } = useQuery({
    queryKey: ['database-metrics'],
    queryFn: async (): Promise<DatabaseMetrics> => {
      try {
        return await databasePerformanceService.getMetrics();
      } catch (error) {
        console.warn('Database metrics unavailable, using fallback');
        // TODO: Implement real database performance monitoring
        // Return MOCK DATA if service is unavailable
        return {
          connections: { active: 5, max: 20 }, // MOCK
          avgQueryTime: Math.floor(Math.random() * 100) + 10, // MOCK
          slowQueries: Math.floor(Math.random() * 3), // MOCK
          indexHitRate: Math.floor(Math.random() * 20) + 80, // MOCK
          storage: {
            size: 1024 * 1024 * 500, // MOCK: 500MB
            usage: Math.floor(Math.random() * 30) + 40 // MOCK: 40-70%
          },
          tables: {
            count: 15, // MOCK
            indexes: 45, // MOCK
            records: 10000, // MOCK
            avgSize: 1024 * 100 // MOCK: 100KB average
          }
        };
      }
    },
    refetchInterval: refreshInterval * 2, // Refresh less frequently
    staleTime: 50000
  });

  // Error Analytics Query
  const {
    data: errorAnalytics,
    isLoading: errorLoading
  } = useQuery({
    queryKey: ['error-analytics'],
    queryFn: async (): Promise<ErrorAnalytics | null> => {
      try {
        return await errorAnalyticsService.getAnalytics();
      } catch (error) {
        console.warn('Error analytics unavailable');
        // TODO: Implement comprehensive error tracking service
        return null;
      }
    },
    refetchInterval: refreshInterval,
    staleTime: 25000
  });

  // Artwork Analytics Query
  const {
    data: artworkAnalytics,
    isLoading: artworkLoading
  } = useQuery({
    queryKey: ['artwork-analytics'],
    queryFn: async (): Promise<ArtworkAnalytics> => {
      try {
        // Get statistics from artwork router and rate limiter
        const serviceStats: Record<ArtworkService, {
          totalRequests: number;
          successfulRequests: number;
          averageResponseTime: number;
          availableKeys: number;
          isCurrentlyLimited: boolean;
        }> = {} as Record<ArtworkService, {
          totalRequests: number;
          successfulRequests: number;
          averageResponseTime: number;
          availableKeys: number;
          isCurrentlyLimited: boolean;
        }>;
        
        // Collect stats from each service
        const services: ArtworkService[] = ['thegamesdb', 'steamgriddb'];
        
        for (const service of services) {
          try {
            const stats = artworkAPIRouter.getServiceStatistics(service);
            const rateLimitStatus = rateLimitManager.getServiceStatus(service);
            
            serviceStats[service] = {
              totalRequests: stats?.totalRequests || 0,
              successfulRequests: stats?.successfulRequests || 0,
              averageResponseTime: stats?.averageResponseTime || 0,
              availableKeys: rateLimitStatus?.availableRequests || 0,
              isCurrentlyLimited: rateLimitStatus?.isRateLimited || false
            };
          } catch (error) {
            serviceStats[service] = {
              totalRequests: 0,
              successfulRequests: 0,
              averageResponseTime: 0,
              availableKeys: 0,
              isCurrentlyLimited: false
            };
          }
        }

        const queueStatus = artworkAPIRouter.getQueueStatus();
        
        return {
          serviceStats,
          queueStatus: queueStatus || {
            queueLength: 0,
            activeRequests: 0,
            processing: false
          }
        };
      } catch (error) {
        console.warn('Artwork analytics unavailable');
        // TODO: Enhance artwork API analytics with more detailed metrics
        return {
          serviceStats: null,
          queueStatus: null
        };
      }
    },
    refetchInterval: refreshInterval / 2, // More frequent updates for real-time data
    staleTime: 10000
  });

  // Combined loading state
  const isLoading = systemHealthLoading || databaseLoading || errorLoading || artworkLoading;
  
  // Combined error state
  const error = systemHealthError;

  // Refresh all analytics
  const refreshAnalytics = useCallback(async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: ['system-health'] }),
      queryClient.invalidateQueries({ queryKey: ['database-metrics'] }),
      queryClient.invalidateQueries({ queryKey: ['error-analytics'] }),
      queryClient.invalidateQueries({ queryKey: ['artwork-analytics'] })
    ]);
  }, [queryClient]);

  // Set refresh interval
  const setRefreshRate = useCallback((intervalMs: number) => {
    setRefreshInterval(Math.max(5000, intervalMs)); // Minimum 5 seconds
  }, []);

  // Calculate overall system status
  const systemStatus = useMemo(() => {
    if (!systemHealth) return 'unknown';
    
    const healthScore = (
      systemHealth.uptime * 0.3 +
      (systemHealth.databaseHealth) * 0.3 +
      Math.max(0, (100 - systemHealth.errorRate * 10)) * 0.2 +
      Math.max(0, (3000 - systemHealth.avgResponseTime) / 30) * 0.2
    );
    
    if (healthScore >= 90) return 'excellent';
    if (healthScore >= 75) return 'good';
    if (healthScore >= 60) return 'warning';
    return 'critical';
  }, [systemHealth]);

  // Get service alerts
  const alerts = useMemo(() => {
    const alertList: Array<{
      type: 'info' | 'warning' | 'error';
      message: string;
      service: string;
    }> = [];

    if (systemHealth) {
      if (systemHealth.errorRate > 5) {
        alertList.push({
          type: 'error',
          message: `High error rate: ${systemHealth.errorRate.toFixed(1)}%`,
          service: 'system'
        });
      }
      
      if (systemHealth.avgResponseTime > 2000) {
        alertList.push({
          type: 'warning',
          message: `Slow response time: ${systemHealth.avgResponseTime}ms`,
          service: 'api'
        });
      }
      
      if (systemHealth.resources.cpu > 80) {
        alertList.push({
          type: 'warning',
          message: `High CPU usage: ${systemHealth.resources.cpu}%`,
          service: 'system'
        });
      }
      
      if (systemHealth.resources.memory > 85) {
        alertList.push({
          type: 'error',
          message: `High memory usage: ${systemHealth.resources.memory}%`,
          service: 'system'
        });
      }
    }

    if (databaseMetrics) {
      if (databaseMetrics.slowQueries > 5) {
        alertList.push({
          type: 'warning',
          message: `${databaseMetrics.slowQueries} slow queries detected`,
          service: 'database'
        });
      }
      
      if ((databaseMetrics.connections.active / databaseMetrics.connections.max) > 0.8) {
        alertList.push({
          type: 'warning',
          message: 'Database connection pool near capacity',
          service: 'database'
        });
      }
    }

    return alertList;
  }, [systemHealth, databaseMetrics]);

  return {
    // Data
    systemHealth,
    databaseMetrics,
    errorAnalytics,
    artworkAnalytics,
    
    // States
    isLoading,
    error,
    systemStatus,
    alerts,
    
    // Actions
    refreshAnalytics,
    setRefreshRate,
    
    // Configuration
    refreshInterval
  };
}