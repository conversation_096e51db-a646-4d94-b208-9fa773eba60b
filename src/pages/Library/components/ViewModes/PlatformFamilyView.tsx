import { PlatformFamilyGroup } from '@/components/ui/utils/PlatformFamilyGroup';
import { PlatformFamilyViewProps } from '../../types';
import { groupGamesByPlatformFamily, calculatePlatformFamilyStats } from '@/lib/utils/platformFamilyUtils';
import { applyFilters } from '@/lib/utils/filterUtils';
import { GameFilters, SortOptions, SortField } from '@/types/filters';

import { useMemo } from 'react';

export function PlatformFamilyView({ 
  games, 
  collapsedFamilies, 
  onFamilyToggle, 
  onGameClick, 
  onStatusUpdate, 
  onRemoveGame,
  searchQuery = '',
  filters,
  sortOptions
}: PlatformFamilyViewProps) {
  // Apply search and filters to games, then group by platform family
  const familyGroups = useMemo(() => {
    // First, apply search and filters to the entire game collection
    let filteredGames = games;
    
    // Apply search filter if there's a search query
    if (searchQuery.trim()) {
      const searchFilter: GameFilters = {
        search: {
          enabled: true,
          query: searchQuery,
          searchType: 'smart'
        },
        platforms: { enabled: false, platforms: [] },
        genres: { enabled: false, genres: [], mode: 'include' },
        year: { enabled: false },
        rating: { enabled: false },
        developer: { enabled: false, developers: [] },
        publisher: { enabled: false, publishers: [] },
        customTags: { enabled: false, tags: [], mode: 'include', includeUntagged: false },
        status: { enabled: false, statuses: [], mode: 'include' },
        collection: { enabled: false, inLibrary: false, inWishlist: false, notInCollection: false },
        sort: sortOptions || { field: 'title' as SortField, direction: 'asc' as const }
      };
      
      // Convert UserGameWithDetails to Game-like objects for filtering
      const gameObjects = filteredGames.map(userGame => ({
        id: userGame.game?.id || userGame.game_id || '',
        title: userGame.game?.title || 'Unknown Game',
        platforms: [userGame.game?.platform || 'Unknown'],
        genres: userGame.game?.genres || [],
        developer: userGame.game?.developer,
        publisher: userGame.game?.publisher,
        release_date: userGame.game?.release_date,
        description: userGame.game?.description,
        cover_image: userGame.game?.cover_image,
        screenshots: userGame.game?.screenshots || [],
        youtube_links: userGame.game?.youtube_links || [],
        metacritic_score: userGame.game?.metacritic_score,
        igdb_id: userGame.game?.igdb_id,
      }));
      
      const filterResult = applyFilters(gameObjects, searchFilter);
      const filteredGameIds = new Set(filterResult.data.map(game => game.id));
      
      filteredGames = filteredGames.filter(userGame => 
        filteredGameIds.has(userGame.game?.id || userGame.game_id || '')
      );
    }
    
    // Apply additional filters if provided
    if (filters) {
      const gameObjects = filteredGames.map(userGame => ({
        id: userGame.game?.id || userGame.game_id || '',
        title: userGame.game?.title || 'Unknown Game',
        platforms: [userGame.game?.platform || 'Unknown'],
        genres: userGame.game?.genres || [],
        developer: userGame.game?.developer,
        publisher: userGame.game?.publisher,
        release_date: userGame.game?.release_date,
        description: userGame.game?.description,
        cover_image: userGame.game?.cover_image,
        screenshots: userGame.game?.screenshots || [],
        youtube_links: userGame.game?.youtube_links || [],
        metacritic_score: userGame.game?.metacritic_score,
        igdb_id: userGame.game?.igdb_id,
      }));
      
      const filterResult = applyFilters(gameObjects, filters);
      const filteredGameIds = new Set(filterResult.data.map(game => game.id));
      
      filteredGames = filteredGames.filter(userGame => 
        filteredGameIds.has(userGame.game?.id || userGame.game_id || '')
      );
    }
    
    // Group the filtered games by platform family
    const groups = groupGamesByPlatformFamily(filteredGames);
    
    // Apply family-level sorting that maintains grouping
    const sortedGroups = groups.map(group => {
      const sortedGames = [...group.games];
      
      if (sortOptions) {
        sortedGames.sort((a, b) => {
          let comparison = 0;
          
          switch (sortOptions.field) {
            case 'title':
              comparison = (a.game?.title || '').localeCompare(b.game?.title || '');
              break;
              
            case 'release_date': {
              const dateA = a.game?.release_date ? new Date(a.game.release_date).getTime() : 0;
              const dateB = b.game?.release_date ? new Date(b.game.release_date).getTime() : 0;
              comparison = dateA - dateB;
              break;
            }
            
            case 'metacritic_score': {
              const scoreA = a.game?.metacritic_score || 0;
              const scoreB = b.game?.metacritic_score || 0;
              comparison = scoreA - scoreB;
              break;
            }
            
            case 'added_at': {
              const dateA = a.date_added ? new Date(a.date_added).getTime() : 0;
              const dateB = b.date_added ? new Date(b.date_added).getTime() : 0;
              comparison = dateA - dateB;
              break;
            }
            
            default:
              comparison = (a.game?.title || '').localeCompare(b.game?.title || '');
          }
          
          return sortOptions.direction === 'asc' ? comparison : -comparison;
        });
      }
      
      // Recalculate stats for the filtered and sorted games
      const updatedStats = calculatePlatformFamilyStats(sortedGames);
      
      return {
        ...group,
        games: sortedGames,
        stats: updatedStats
      };
    });
    
    // Filter out empty family groups (hide them from display when no games match filters)
    return sortedGroups.filter(group => group.games.length > 0);
  }, [games, searchQuery, filters, sortOptions]);

  // If no games or no family groups after filtering, show appropriate empty state
  if (!games.length) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="text-muted-foreground">
          <p className="text-lg font-medium">No games found</p>
          <p className="text-sm">Add some games to your library to see them organized by platform family.</p>
        </div>
      </div>
    );
  }
  
  if (!familyGroups.length) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="text-muted-foreground">
          <p className="text-lg font-medium">No games match your current filters</p>
          <p className="text-sm">Try adjusting your search terms or filters to see more results.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {familyGroups.map((group) => (
        <PlatformFamilyGroup
          key={group.family}
          family={group.family}
          games={group.games}
          platforms={group.platforms}
          stats={group.stats}
          isCollapsed={collapsedFamilies.has(group.family)}
          onToggleCollapse={() => onFamilyToggle(group.family)}
          onGameClick={onGameClick}
          onStatusUpdate={onStatusUpdate}
          onRemoveGame={onRemoveGame}
        />
      ))}
    </div>
  );
}