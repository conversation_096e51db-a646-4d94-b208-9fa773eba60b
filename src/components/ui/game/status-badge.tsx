import { Badge } from '@/components/ui/base/badge';
import { getStatusColor, getStatusDisplayName } from '@/lib/gameStatusUtils';
import { cn } from '@/lib/utils';

interface StatusBadgeProps {
  status: string;
  variant?: 'default' | 'secondary' | 'outline';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  showIcon?: boolean;
}

export const StatusBadge = ({ 
  status, 
  variant = 'default', 
  size = 'default',
  className,
  showIcon = false 
}: StatusBadgeProps) => {
  const statusClass = variant === 'default' ? getStatusColor(status) : '';
  const sizeClass = size === 'sm' ? 'text-xs px-2 py-1' : size === 'lg' ? 'text-sm px-3 py-2' : '';
  
  return (
    <Badge 
      variant={variant === 'default' ? undefined : variant}
      className={cn(statusClass, sizeClass, className)}
    >
      {showIcon && getStatusDisplayName(status)}
      {!showIcon && status}
    </Badge>
  );
};