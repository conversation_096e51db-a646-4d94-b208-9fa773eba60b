// Core interfaces for the enhanced library UI/UX redesign

import { UserGameWithDetails } from './database';
import { GameFilters } from './filters';

// View mode types
export type ViewMode = 'grid' | 'list' | 'coverFlow' | 'timeline' | 'stats';

// Base view settings interface
export interface ViewSettings {
  id: string;
  name: string;
  icon: string;
  description: string;
}

// Grid view specific settings
export interface GridViewSettings extends ViewSettings {
  density: 'compact' | 'normal' | 'large';
  aspectRatio: 'square' | 'portrait' | 'landscape';
  showMetadata: boolean;
  enableHoverEffects: boolean;
  cardStyle: 'modern' | 'classic' | 'minimal';
  overlayPosition: 'bottom' | 'center' | 'top';
  animationStyle: 'subtle' | 'dynamic' | 'premium';
  colorScheme: 'auto' | 'vibrant' | 'muted';
}

// List view specific settings
export interface ListViewSettings extends ViewSettings {
  columns: ColumnConfiguration[];
  showThumbnails: boolean;
  compactMode: boolean;
  groupBy?: GroupingOption;
}

// Cover flow view specific settings
export interface CoverFlowViewSettings extends ViewSettings {
  centerIndex: number;
  visibleCount: number;
  perspective: number;
  autoRotate: boolean;
  rotationSpeed: number;
  touchEnabled: boolean;
  keyboardNavigation: boolean;
}

// Timeline view specific settings
export interface TimelineViewSettings extends ViewSettings {
  timelineType: 'added' | 'completed' | 'release' | 'lastPlayed';
  grouping: 'day' | 'week' | 'month' | 'year';
  showMilestones: boolean;
  interactiveTimeline: boolean;
}

// Stats dashboard specific settings
export interface StatsDashboardSettings extends ViewSettings {
  widgets: DashboardWidget[];
  layout: DashboardLayout;
  timeRange: TimeRange;
  comparisons: ComparisonConfig[];
}

// Column configuration for list view
export interface ColumnConfiguration {
  key: string;
  label: string;
  width: number | 'auto';
  sortable: boolean;
  filterable: boolean;
  visible: boolean;
  order: number;
  customRenderer?: string; // Component name to be resolved at runtime
}

// Grouping options for list view
export type GroupingOption = 
  | 'none'
  | 'platform'
  | 'genre'
  | 'developer'
  | 'publisher'
  | 'year'
  | 'status'
  | 'rating'
  | 'tag';

// Dashboard widget configuration
export interface DashboardWidget {
  id: string;
  type: 'chart' | 'metric' | 'list' | 'heatmap' | 'progress';
  title: string;
  size: WidgetSize;
  config: Record<string, unknown>;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
}

// Widget size options
export type WidgetSize = 'small' | 'medium' | 'large' | 'custom';

// Dashboard layout configuration
export interface DashboardLayout {
  columns: number;
  rowHeight: number;
  margin: [number, number];
  containerPadding: [number, number];
  isDraggable: boolean;
  isResizable: boolean;
  isBounded: boolean;
}

// Time range configuration for stats
export interface TimeRange {
  start: Date | null;
  end: Date | null;
  preset?: 'last7days' | 'last30days' | 'last90days' | 'lastYear' | 'allTime';
}

// Comparison configuration for stats
export interface ComparisonConfig {
  id: string;
  name: string;
  timeRange: TimeRange;
  color: string;
}

// Animation configuration for game cards
export interface AnimationConfig {
  hoverScale: number;
  transitionDuration: number;
  staggerDelay: number;
  parallaxEffect: boolean;
  glowEffect: boolean;
}

// Enhanced library state interface
export interface LibraryState {
  // View Configuration
  viewMode: ViewMode;
  viewSettings: {
    grid: GridViewSettings;
    list: ListViewSettings;
    coverFlow: CoverFlowViewSettings;
    timeline: TimelineViewSettings;
    stats: StatsDashboardSettings;
  };
  
  // Data & Filtering
  games: UserGameWithDetails[];
  filteredGames: UserGameWithDetails[];
  filters: GameFilters;
  searchState: SearchState;
  
  // UI State
  selectedGames: Set<string>;
  expandedSections: Set<string>;
  loadingStates: LoadingStates;
  
  // User Preferences
  preferences: LibraryPreferences;
  customCollections: Collection[];
  savedPresets: FilterPreset[];
}

// Search state interface
export interface SearchState {
  query: string;
  isSearching: boolean;
  results: UserGameWithDetails[];
  suggestions: string[];
  recentSearches: string[];
  savedSearches: SavedSearch[];
}

// Loading states interface
export interface LoadingStates {
  isLoadingGames: boolean;
  isLoadingFilters: boolean;
  isApplyingFilters: boolean;
  isChangingView: boolean;
  isLoadingCollections: boolean;
  errors: Record<string, string | null>;
}

// Library preferences interface
export interface LibraryPreferences {
  defaultViewMode: ViewMode;
  rememberFilters: boolean;
  showTags: boolean;
  showRatings: boolean;
  showPlatformIcons: boolean;
  enableAnimations: boolean;
  cardSize: 'small' | 'medium' | 'large';
  theme: 'light' | 'dark' | 'system' | 'custom';
  customTheme?: CustomTheme;
}

// Custom theme interface
export interface CustomTheme {
  name: string;
  colors: Record<string, string>;
  fonts: {
    body: string;
    heading: string;
  };
}

// Collection interface
export interface Collection {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  games: string[];
  rules?: CollectionRule[];
  isSmartCollection: boolean;
  visibility: 'private' | 'public' | 'friends';
  createdAt: Date;
  updatedAt: Date;
}

// Collection rule interface
export interface CollectionRule {
  id: string;
  field: string;
  operator: 'equals' | 'contains' | 'greaterThan' | 'lessThan' | 'between';
  value: unknown;
  logic: 'AND' | 'OR';
}

// Filter preset interface
export interface FilterPreset {
  id: string;
  name: string;
  description?: string;
  filters: GameFilters;
  isPublic: boolean;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// Saved search interface
export interface SavedSearch {
  id: string;
  name: string;
  query: string;
  filters: GameFilters;
  createdAt: Date;
  updatedAt: Date;
}

// Game card interaction interface
export interface GameCardInteractions {
  onQuickPlay?: () => void;
  onStatusChange?: (status: string) => void;
  onRatingChange?: (rating: number) => void;
  onAddToCollection?: (collectionId: string) => void;
  onShare?: (platform: string) => void;
  onCustomize?: () => void;
  onSelect?: (selected: boolean) => void;
}

// Enhanced game data with additional metadata
export interface EnhancedGameData extends UserGameWithDetails {
  // Enhanced metadata
  acquisitionDate?: Date;
  acquisitionPrice?: number;
  acquisitionSource?: 'purchase' | 'gift' | 'subscription' | 'free';
  
  // Play tracking
  totalPlayTime?: number;
  lastPlayedAt?: Date;
  playingSessions?: PlaySession[];
  
  // Progress tracking
  achievementProgress?: AchievementProgress;
  completionPercentage?: number;
  milestones?: Milestone[];
  
  // Social data
  personalReview?: Review;
  personalRating?: number;
  personalTags?: string[];
  sharedCollections?: string[];
  
  // Customization
  customArtwork?: CustomArtwork[];
  displayPreferences?: DisplayPreferences;
  
  // Analytics
  engagementScore?: number;
  recommendationWeight?: number;
  lastAnalyzed?: Date;
}

// Play session interface
export interface PlaySession {
  id: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  platform?: string;
  notes?: string;
}

// Achievement progress interface
export interface AchievementProgress {
  total: number;
  earned: number;
  percentage: number;
  lastEarned?: Date;
  recentAchievements?: Achievement[];
}

// Achievement interface
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon?: string;
  rarity?: number;
  earnedAt?: Date;
}

// Milestone interface
export interface Milestone {
  id: string;
  name: string;
  description: string;
  completedAt?: Date;
  isCompleted: boolean;
  progress?: number;
}

// Review interface
export interface Review {
  id: string;
  text: string;
  rating: number;
  createdAt: Date;
  updatedAt?: Date;
  isPublic: boolean;
  likes?: number;
  comments?: number;
}

// Custom artwork interface
export interface CustomArtwork {
  id: string;
  type: 'cover' | 'background' | 'banner' | 'logo' | 'screenshot';
  url: string;
  isPrimary: boolean;
  uploadedAt: Date;
}

// Display preferences interface
export interface DisplayPreferences {
  preferredArtworkId?: string;
  hideFromGrid?: boolean;
  pinToTop?: boolean;
  customColor?: string;
  customLabel?: string;
  notes?: string;
}