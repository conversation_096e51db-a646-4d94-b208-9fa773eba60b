/*
  # Add Missing Tables and Performance Indexes Migration
  
  This migration adds:
  1. price_alerts table for user price tracking alerts
  2. user_collection_stats table for cached user statistics
  3. Performance indexes for frequently queried data
  4. Foreign key constraints for data integrity
  5. Composite indexes for complex queries
*/

-- Create price_alerts table
CREATE TABLE IF NOT EXISTS price_alerts (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  game_id uuid NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  target_price decimal(10,2) NOT NULL,
  currency varchar(3) NOT NULL DEFAULT 'USD',
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create user_collection_stats table
CREATE TABLE IF NOT EXISTS user_collection_stats (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  total_games integer NOT NULL DEFAULT 0,
  total_value decimal(12,2) NOT NULL DEFAULT 0,
  average_rating decimal(3,2),
  most_played_genre varchar(100),
  completion_rate decimal(5,2) NOT NULL DEFAULT 0,
  last_calculated timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  UNIQUE(user_id)
);

-- Add RLS policies for price_alerts
ALTER TABLE price_alerts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own price alerts" ON price_alerts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own price alerts" ON price_alerts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own price alerts" ON price_alerts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own price alerts" ON price_alerts
  FOR DELETE USING (auth.uid() = user_id);

-- Add RLS policies for user_collection_stats
ALTER TABLE user_collection_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own collection stats" ON user_collection_stats
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own collection stats" ON user_collection_stats
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own collection stats" ON user_collection_stats
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own collection stats" ON user_collection_stats
  FOR DELETE USING (auth.uid() = user_id);

-- Performance indexes for games table
CREATE INDEX IF NOT EXISTS idx_games_igdb_id ON games(igdb_id);
CREATE INDEX IF NOT EXISTS idx_games_release_date ON games(release_date);
CREATE INDEX IF NOT EXISTS idx_games_metacritic_score ON games(metacritic_score);
CREATE INDEX IF NOT EXISTS idx_games_platform ON games(platform);
CREATE INDEX IF NOT EXISTS idx_games_genres ON games USING GIN(genres);

-- Performance indexes for user_games table
CREATE INDEX IF NOT EXISTS idx_user_games_user_id ON user_games(user_id);
CREATE INDEX IF NOT EXISTS idx_user_games_game_id ON user_games(game_id);
CREATE INDEX IF NOT EXISTS idx_user_games_status ON user_games(status);
CREATE INDEX IF NOT EXISTS idx_user_games_date_added ON user_games(date_added);
CREATE INDEX IF NOT EXISTS idx_user_games_personal_rating ON user_games(personal_rating);

-- Composite indexes for complex queries
CREATE INDEX IF NOT EXISTS idx_user_games_user_status ON user_games(user_id, status);
CREATE INDEX IF NOT EXISTS idx_user_games_user_date_added ON user_games(user_id, date_added DESC);
CREATE INDEX IF NOT EXISTS idx_user_games_user_rating ON user_games(user_id, personal_rating DESC);

-- Performance indexes for price_tracking table
CREATE INDEX IF NOT EXISTS idx_price_tracking_game_id ON price_tracking(game_id);
CREATE INDEX IF NOT EXISTS idx_price_tracking_store_name ON price_tracking(store_name);
CREATE INDEX IF NOT EXISTS idx_price_tracking_is_on_sale ON price_tracking(is_on_sale);
CREATE INDEX IF NOT EXISTS idx_price_tracking_last_updated ON price_tracking(last_updated);

-- Performance indexes for price_alerts table
CREATE INDEX IF NOT EXISTS idx_price_alerts_user_id ON price_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_price_alerts_game_id ON price_alerts(game_id);
CREATE INDEX IF NOT EXISTS idx_price_alerts_is_active ON price_alerts(is_active);

-- Composite indexes for price tracking
CREATE INDEX IF NOT EXISTS idx_price_tracking_game_store ON price_tracking(game_id, store_name);
CREATE INDEX IF NOT EXISTS idx_price_alerts_user_active ON price_alerts(user_id, is_active);

-- Performance indexes for user_profiles table
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);

-- Performance indexes for user_preferences table
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);

-- Update triggers for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language plpgsql;

-- Add triggers for updated_at columns
DROP TRIGGER IF EXISTS update_price_alerts_updated_at ON price_alerts;
CREATE TRIGGER update_price_alerts_updated_at
  BEFORE UPDATE ON price_alerts
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_collection_stats_updated_at ON user_collection_stats;
CREATE TRIGGER update_user_collection_stats_updated_at
  BEFORE UPDATE ON user_collection_stats
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add foreign key constraints for data integrity (if they don't exist)
-- Note: These will be added only if the referenced columns exist

-- Add check constraints for data validation
ALTER TABLE price_alerts 
ADD CONSTRAINT IF NOT EXISTS check_target_price_positive 
CHECK (target_price > 0);

ALTER TABLE user_collection_stats 
ADD CONSTRAINT IF NOT EXISTS check_total_games_non_negative 
CHECK (total_games >= 0);

ALTER TABLE user_collection_stats 
ADD CONSTRAINT IF NOT EXISTS check_completion_rate_valid 
CHECK (completion_rate >= 0 AND completion_rate <= 100);

-- Create database views for frequently joined data
CREATE OR REPLACE VIEW user_games_with_details AS
SELECT 
  ug.*,
  g.title,
  g.cover_image,
  g.platform,
  g.genres,
  g.developer,
  g.publisher,
  g.release_date,
  g.metacritic_score
FROM user_games ug
JOIN games g ON ug.game_id = g.id;

CREATE OR REPLACE VIEW price_alerts_with_games AS
SELECT 
  pa.*,
  g.title,
  g.cover_image,
  g.platform
FROM price_alerts pa
JOIN games g ON pa.game_id = g.id;

-- Grant necessary permissions
GRANT SELECT ON user_games_with_details TO authenticated;
GRANT SELECT ON price_alerts_with_games TO authenticated;