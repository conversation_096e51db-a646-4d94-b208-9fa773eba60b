import { useState } from 'react';
import { Input } from '@/components/ui/base/input';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Switch } from '@/components/ui/base/switch';
import { Label } from '@/components/ui/base/label';
import { Slider } from '@/components/ui/base/slider';
import { Calendar, X } from 'lucide-react';
import { YearFilter } from '@/types/filters';

interface YearFilterComponentProps {
  filter: YearFilter;
  onChange: (update: Partial<YearFilter>) => void;
  disabled?: boolean;
}

const CURRENT_YEAR = new Date().getFullYear();
const MIN_YEAR = 1970; // Early gaming era
const MAX_YEAR = CURRENT_YEAR + 2; // Include upcoming games

// Preset year ranges
const YEAR_PRESETS = [
  { label: 'Classic (1970-1989)', minYear: 1970, maxYear: 1989 },
  { label: '<PERSON><PERSON> (1990-1999)', minYear: 1990, maxYear: 1999 },
  { label: '2000s', minYear: 2000, maxYear: 2009 },
  { label: '2010s', minYear: 2010, maxYear: 2019 },
  { label: 'Modern (2020+)', minYear: 2020, maxYear: MAX_YEAR },
  { label: 'Last 5 Years', minYear: CURRENT_YEAR - 5, maxYear: CURRENT_YEAR },
  { label: 'This Year', minYear: CURRENT_YEAR, maxYear: CURRENT_YEAR },
  { label: 'Upcoming', minYear: CURRENT_YEAR + 1, maxYear: MAX_YEAR }
];

export function YearFilterComponent({ 
  filter, 
  onChange, 
  disabled = false 
}: YearFilterComponentProps) {
  const [sliderValues, setSliderValues] = useState<number[]>([
    filter.minYear || MIN_YEAR,
    filter.maxYear || MAX_YEAR
  ]);

  const handleToggleEnabled = (enabled: boolean) => {
    onChange({ enabled });
  };

  const handleMinYearChange = (value: string) => {
    const year = value === '' ? undefined : parseInt(value);
    if (year === undefined || (year >= MIN_YEAR && year <= MAX_YEAR)) {
      onChange({ minYear: year });
      if (year !== undefined) {
        setSliderValues([year, filter.maxYear || MAX_YEAR]);
      }
    }
  };

  const handleMaxYearChange = (value: string) => {
    const year = value === '' ? undefined : parseInt(value);
    if (year === undefined || (year >= MIN_YEAR && year <= MAX_YEAR)) {
      onChange({ maxYear: year });
      if (year !== undefined) {
        setSliderValues([filter.minYear || MIN_YEAR, year]);
      }
    }
  };

  const handleSliderChange = (values: number[]) => {
    setSliderValues(values);
    onChange({
      minYear: values[0],
      maxYear: values[1]
    });
  };

  const handlePresetClick = (preset: { minYear: number; maxYear: number }) => {
    onChange({
      minYear: preset.minYear,
      maxYear: preset.maxYear
    });
    setSliderValues([preset.minYear, preset.maxYear]);
  };

  const clearFilter = () => {
    onChange({ minYear: undefined, maxYear: undefined });
    setSliderValues([MIN_YEAR, MAX_YEAR]);
  };

  const hasActiveRange = filter.minYear !== undefined || filter.maxYear !== undefined;

  return (
    <div className="space-y-4">
      {/* Enable/Disable Toggle */}
      <div className="flex items-center justify-between">
        <Label htmlFor="year-enabled" className="text-sm font-medium">
          Filter by Release Year
        </Label>
        <Switch
          id="year-enabled"
          checked={filter.enabled}
          onCheckedChange={handleToggleEnabled}
          disabled={disabled}
        />
      </div>

      {filter.enabled && (
        <>
          {/* Active Range Display */}
          {hasActiveRange && (
            <div className="flex items-center justify-between">
              <Badge variant="secondary" className="text-xs">
                <Calendar className="h-3 w-3 mr-1" />
                {filter.minYear || MIN_YEAR} - {filter.maxYear || MAX_YEAR}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilter}
                className="text-xs h-6"
                disabled={disabled}
              >
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
            </div>
          )}

          {/* Year Range Slider */}
          <div className="space-y-4">
            <div className="px-2">
              <Slider
                value={sliderValues}
                onValueChange={handleSliderChange}
                min={MIN_YEAR}
                max={MAX_YEAR}
                step={1}
                className="w-full"
                disabled={disabled}
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>{MIN_YEAR}</span>
                <span>{MAX_YEAR}</span>
              </div>
            </div>

            {/* Manual Year Input */}
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">From Year</Label>
                <Input
                  type="number"
                  placeholder={MIN_YEAR.toString()}
                  value={filter.minYear?.toString() || ''}
                  onChange={(e) => handleMinYearChange(e.target.value)}
                  min={MIN_YEAR}
                  max={MAX_YEAR}
                  className="h-8 text-sm"
                  disabled={disabled}
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">To Year</Label>
                <Input
                  type="number"
                  placeholder={MAX_YEAR.toString()}
                  value={filter.maxYear?.toString() || ''}
                  onChange={(e) => handleMaxYearChange(e.target.value)}
                  min={MIN_YEAR}
                  max={MAX_YEAR}
                  className="h-8 text-sm"
                  disabled={disabled}
                />
              </div>
            </div>
          </div>

          {/* Quick Presets */}
          <div className="space-y-2">
            <Label className="text-xs text-muted-foreground">Quick Presets</Label>
            <div className="grid grid-cols-2 gap-2">
              {YEAR_PRESETS.map((preset, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handlePresetClick(preset)}
                  className="text-xs h-7 justify-start"
                  disabled={disabled}
                >
                  {preset.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Filter Description */}
          <div className="text-xs text-muted-foreground p-2 bg-muted/50 rounded">
            {filter.minYear && filter.maxYear && filter.minYear === filter.maxYear
              ? `Show games released in ${filter.minYear}`
              : filter.minYear && filter.maxYear
              ? `Show games released between ${filter.minYear} and ${filter.maxYear}`
              : filter.minYear
              ? `Show games released from ${filter.minYear} onwards`
              : filter.maxYear
              ? `Show games released up to ${filter.maxYear}`
              : 'Show games from all years'
            }
          </div>
        </>
      )}
    </div>
  );
}