import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { GameCard } from '@/components/ui/game/game-card';
import { UserGameWithDetails } from '@/types/database';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { useState } from 'react';

interface PlatformGroupProps {
  platform: string;
  games: UserGameWithDetails[];
  onGameClick: (game: UserGameWithDetails) => void;
  onUpdateStatus: (userGameId: string, newStatus: string) => void;
  onRemoveGame: (userGameId: string) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function PlatformGroup({
  platform,
  games,
  onGameClick,
  onUpdateStatus,
  onRemoveGame,
  isCollapsed = false,
  onToggleCollapse
}: PlatformGroupProps) {
  const [localCollapsed, setLocalCollapsed] = useState(isCollapsed);

  const handleToggle = () => {
    if (onToggleCollapse) {
      onToggleCollapse();
    } else {
      setLocalCollapsed(!localCollapsed);
    }
  };

  const collapsed = onToggleCollapse ? isCollapsed : localCollapsed;

  // Count games by status
  const playingCount = games.filter(g => g.status === 'playing').length;
  const completedCount = games.filter(g => g.status === 'completed').length;
  const backlogCount = games.filter(g => g.status === 'backlog').length;

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle className="text-lg">{platform}</CardTitle>
            <Badge variant="outline" className="bg-primary/5">
              {games.length} games
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggle}
            className="h-8 w-8 p-0"
          >
            {collapsed ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronUp className="h-4 w-4" />
            )}
          </Button>
        </div>
        <CardDescription className="flex items-center gap-4 text-sm">
          <span className="flex items-center gap-1">
            <span className="w-2 h-2 rounded-full bg-accent"></span>
            {playingCount} playing
          </span>
          <span className="flex items-center gap-1">
            <span className="w-2 h-2 rounded-full bg-success"></span>
            {completedCount} completed
          </span>
          <span className="flex items-center gap-1">
            <span className="w-2 h-2 rounded-full bg-primary"></span>
            {backlogCount} backlog
          </span>
        </CardDescription>
      </CardHeader>
      
      {!collapsed && (
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
            {games.map((userGame) => (
              <GameCard
                key={userGame.id}
                gameData={userGame}
                onGameClick={onGameClick}
                onUpdateStatus={onUpdateStatus}
                onRemoveGame={onRemoveGame}
              />
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}