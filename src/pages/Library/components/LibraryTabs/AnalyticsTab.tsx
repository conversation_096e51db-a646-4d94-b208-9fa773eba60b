import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base';
import { AnalyticsTabProps } from '../../types';

export function AnalyticsTab({ libraryGames, timeframe: _timeframe, onTimeframeChange: _onTimeframeChange }: AnalyticsTabProps) {
  // Calculate analytics data
  const totalHours = libraryGames.reduce((sum, g) => sum + (g.hours_played || 0), 0);
  const avgHoursPerGame = libraryGames.length > 0 ? totalHours / libraryGames.length : 0;
  const mostPlayedGame = libraryGames
    .sort((a, b) => (b.hours_played || 0) - (a.hours_played || 0))[0];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>📈 Collection Analytics</CardTitle>
          <CardDescription>
            Deep insights into your gaming habits and collection trends
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {totalHours.toFixed(1)}h
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Avg. Hours per Game</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {avgHoursPerGame.toFixed(1)}h
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Most Played</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-sm font-bold">
                    {mostPlayedGame?.game?.title || 'N/A'}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}