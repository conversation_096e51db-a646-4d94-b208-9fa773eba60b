/**
 * Artwork API Key Manager Component
 * Manages API keys for different artwork services with intelligent routing
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/base/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/base/tabs';
import { toast } from 'react-hot-toast';
import { apiKeyStore, ArtworkService, KeyType } from '@/lib/apiKeyStore';
import { rateLimitManager } from '@/lib/services/rateLimitManager';
import { artworkAPIRouter } from '@/lib/services/artworkAPIRouter';
import ServiceCard from './ServiceCard';
import RouterSettings from './RouterSettings';
// ServiceAnalytics moved to unified System Analytics Dashboard

interface ServiceKeyEntry {
  id: string;
  value: string;
  label?: string;
  source: 'user' | 'env';
  isVisible: boolean;
  usage?: {
    totalRequests: number;
    successfulRequests: number;
    averageResponseTime: number;
    lastUsed: number;
  };
}

interface ServiceInfo {
  service: ArtworkService;
  displayName: string;
  keyType: KeyType;
  keyLabel: string;
  description: string;
  costPerRequest: number;
  qualityRating: number;
  features: string[];
  isRequired: boolean;
}

const ArtworkAPIKeyManager: React.FC = () => {
  const [serviceKeys, setServiceKeys] = useState<Record<ArtworkService, ServiceKeyEntry[]>>({} as Record<ArtworkService, ServiceKeyEntry[]>);
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState<Record<ArtworkService, {
    totalRequests: number;
    successfulRequests: number;
    averageResponseTime: number;
    availableKeys: number;
    isCurrentlyLimited: boolean;
  }> | null>(null);
  // Queue status moved to unified System Analytics Dashboard
  
  
  const servicesInfo: ServiceInfo[] = useMemo(() => [
    {
      service: 'igdb',
      displayName: 'IGDB (Internet Game Database)',
      keyType: 'client_id',
      keyLabel: 'Client ID',
      description: 'Official game metadata, covers, and artwork',
      costPerRequest: 0,
      qualityRating: 5,
      features: ['Game Search', 'Official Covers', 'Artwork', 'Screenshots'],
      isRequired: true
    },
    {
      service: 'steamgriddb',
      displayName: 'SteamGridDB',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'High-quality community artwork and covers',
      costPerRequest: 0,
      qualityRating: 5,
      features: ['Custom Covers', 'High-Res Artwork', 'Community Content'],
      isRequired: false
    },
    {
      service: 'thegamesdb',
      displayName: 'TheGamesDB',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'Official game database with metadata and artwork',
      costPerRequest: 0,
      qualityRating: 4,
      features: ['Game Search', 'Official Data', 'Covers & Screenshots'],
      isRequired: false
    },

    {
      service: 'openai',
      displayName: 'OpenAI',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'AI-powered artwork search and analysis',
      costPerRequest: 0.02,
      qualityRating: 4,
      features: ['Smart Search', 'Content Analysis', 'Quality Assessment'],
      isRequired: false
    },
    {
      service: 'deepseek',
      displayName: 'DeepSeek',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'Alternative AI provider for artwork search',
      costPerRequest: 0.005,
      qualityRating: 3,
      features: ['AI Search', 'Cost Effective'],
      isRequired: false
    },
    {
      service: 'gemini',
      displayName: 'Google Gemini',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'Google\'s AI for intelligent artwork discovery',
      costPerRequest: 0.01,
      qualityRating: 4,
      features: ['Smart Analysis', 'Image Recognition'],
      isRequired: false
    }
  ], []);

  const loadServiceKeys = useCallback(async () => {
    setLoading(true);
    const allKeys: Record<ArtworkService, ServiceKeyEntry[]> = {} as Record<ArtworkService, ServiceKeyEntry[]>;

    try {
      for (const serviceInfo of servicesInfo) {
        const keys = await apiKeyStore.getAllAvailableServiceKeys(
          serviceInfo.service, 
          serviceInfo.keyType
        );

        allKeys[serviceInfo.service] = keys.map(key => ({
          ...key,
          isVisible: false,
          usage: rateLimitManager.getUsageStats(serviceInfo.service, key.id) || undefined
        }));
      }

      setServiceKeys(allKeys);
    } catch (error) {
      console.error('Failed to load service keys:', error);
      toast.error('Failed to load API keys');
    } finally {
      setLoading(false);
    }
  }, [servicesInfo]);

  const loadStatistics = useCallback(async () => {
    try {
      const stats = artworkAPIRouter.getServiceStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Failed to load statistics:', error);
      toast.error('Failed to load analytics data');
      // Set null to show error state in UI
      setStatistics(null);
    }
  }, []);

  useEffect(() => {
    loadServiceKeys();
    loadStatistics();

    // Refresh statistics every 10 seconds
    const interval = setInterval(loadStatistics, 10000);
    return () => clearInterval(interval);
  }, [loadServiceKeys, loadStatistics]);



  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold mb-2">Artwork API Management</h2>
          <p className="text-gray-600">
            Loading API key configuration...
          </p>
        </div>

        <Tabs defaultValue="services" className="space-y-4">
          <TabsList>
            <TabsTrigger value="services">Services</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="services" className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <div className="animate-pulse">
                    <div className="h-6 bg-gray-200 rounded w-1/3 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="animate-pulse space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-20 bg-gray-200 rounded"></div>
                    <div className="h-10 bg-gray-200 rounded w-full"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Artwork API Management</h2>
        <p className="text-gray-600">
          Manage API keys for different artwork services. The system will intelligently 
          route requests to optimize for quality, cost, and rate limits.
        </p>
      </div>

      <Tabs defaultValue="services" className="space-y-4">
        <TabsList>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="services" className="space-y-4">
          {servicesInfo.map(serviceInfo => {
            const keys = serviceKeys[serviceInfo.service] || [];
            const stats = statistics?.[serviceInfo.service];
            
            return (
              <ServiceCard
                key={serviceInfo.service}
                serviceInfo={serviceInfo}
                keys={keys}
                stats={stats}
                onKeyAdded={loadServiceKeys}
                onKeyDeleted={loadServiceKeys}
              />
            );
          })}
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <RouterSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ArtworkAPIKeyManager;