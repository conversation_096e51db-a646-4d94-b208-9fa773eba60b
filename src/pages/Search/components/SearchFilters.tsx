import { HorizontalFilterBar, FloatingFilterButton } from '@/components/ui/filters';
import { GameFilters } from '@/types/filters';

interface SearchFiltersProps {
  filters: GameFilters;
  onUpdateFilter: <T extends keyof GameFilters>(filterType: T, update: Partial<GameFilters[T]>) => void;
  onReset: () => void;
  activeFilterCount: number;
  isLoading: boolean;
  availablePlatforms?: string[];
}

export function SearchFilters({ 
  filters, 
  onUpdateFilter, 
  onReset, 
  activeFilterCount, 
  isLoading,
  availablePlatforms
}: SearchFiltersProps) {
  return (
    <>
      {/* Horizontal Filter Bar for Desktop */}
      <HorizontalFilterBar
        filters={filters}
        onUpdateFilter={onUpdateFilter}
        onReset={onReset}
        activeFilterCount={activeFilterCount}
        isLoading={isLoading}
        availablePlatforms={availablePlatforms}
        className="hidden md:block"
      />

      {/* Floating Filter Button for Mobile */}
      <FloatingFilterButton
        filters={filters}
        onUpdateFilter={onUpdateFilter}
        onReset={onReset}
        activeFilterCount={activeFilterCount}
        isLoading={isLoading}
        availablePlatforms={availablePlatforms}
      />
    </>
  );
}