import { cn } from "@/lib/utils"

function Skeleton({
  className,
  shimmer = true,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  shimmer?: boolean
}) {
  return (
    <div
      className={cn(
        "relative overflow-hidden rounded-md bg-muted",
        shimmer && "before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/10 before:to-transparent",
        !shimmer && "animate-pulse",
        className
      )}
      {...props}
    />
  )
}

export { Skeleton }