import React, { memo, useMemo, useCallback, useRef, useEffect, useState } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { PremiumGameCard } from '@/components/ui/game/premium-game-card';
import { useEnhancedLibrary } from '@/hooks/useEnhancedLibrary';
import { UserGameWithDetails } from '@/types/database';
import { GridViewSettings, GameCardInteractions } from '@/types/library';
import { cn } from '@/lib/utils';

// Grid density configurations
const GRID_CONFIGS = {
  compact: {
    cardWidth: 180,
    cardHeight: 280,
    gap: 12,
    minColumns: 2,
    maxColumns: 8
  },
  normal: {
    cardWidth: 220,
    cardHeight: 340,
    gap: 16,
    minColumns: 2,
    maxColumns: 6
  },
  large: {
    cardWidth: 280,
    cardHeight: 420,
    gap: 20,
    minColumns: 1,
    maxColumns: 5
  }
} as const;

// Responsive breakpoints (future use)
// const BREAKPOINTS = {
//   sm: 640,
//   md: 768,
//   lg: 1024,
//   xl: 1280,
//   '2xl': 1536
// } as const;

interface EnhancedGridViewProps {
  games: UserGameWithDetails[];
  settings: GridViewSettings;
  onSettingsChange: (settings: Partial<GridViewSettings>) => void;
  interactions?: GameCardInteractions;
  className?: string;
}

interface GridItemProps {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    games: UserGameWithDetails[];
    columnsPerRow: number;
    settings: GridViewSettings;
    interactions?: GameCardInteractions;
    selectedGames: Set<string>;
    onGameSelect: (gameId: string) => void;
  };
}

// Memoized grid item component for performance
const GridItem = memo<GridItemProps>(({ columnIndex, rowIndex, style, data }) => {
  const { games, columnsPerRow, settings, interactions, selectedGames, onGameSelect } = data;
  const gameIndex = rowIndex * columnsPerRow + columnIndex;
  const game = games[gameIndex];

  if (!game) {
    return <div style={style} />;
  }

  const config = GRID_CONFIGS[settings.density];

  return (
    <div
      style={{
        ...style,
        padding: config.gap / 2,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'flex-start'
      }}
    >
      <PremiumGameCard
        gameData={game}
        variant={settings.density === 'compact' ? 'compact' : settings.density === 'large' ? 'detailed' : 'standard'}
        style={settings.cardStyle}
        showQuickActions={true}
        enableHoverEffects={settings.enableHoverEffects}
        customArtwork={true}
        animations={{
          hoverScale: settings.animationStyle === 'premium' ? 1.05 : settings.animationStyle === 'dynamic' ? 1.03 : 1.02,
          transitionDuration: settings.animationStyle === 'premium' ? 300 : settings.animationStyle === 'dynamic' ? 250 : 200,
          parallaxEffect: settings.animationStyle === 'premium',
          glowEffect: settings.animationStyle !== 'subtle'
        }}
        interactions={{
          ...interactions,
          onSelect: () => onGameSelect(game.id)
        }}
        isSelected={selectedGames.has(game.id)}
        className="w-full max-w-none"
      />
    </div>
  );
});

GridItem.displayName = 'GridItem';

// Hook for calculating responsive grid dimensions
function useResponsiveGrid(
  containerWidth: number,
  settings: GridViewSettings
) {
  return useMemo(() => {
    const config = GRID_CONFIGS[settings.density];
    const availableWidth = containerWidth - (config.gap * 2);
    
    // Calculate optimal number of columns based on container width
    let columnsPerRow = Math.floor(availableWidth / (config.cardWidth + config.gap));
    
    // Apply min/max constraints
    columnsPerRow = Math.max(config.minColumns, Math.min(config.maxColumns, columnsPerRow));
    
    // Calculate actual card width to fill available space
    const totalGapWidth = (columnsPerRow - 1) * config.gap;
    const actualCardWidth = (availableWidth - totalGapWidth) / columnsPerRow;
    
    // Maintain aspect ratio
    const aspectRatios = {
      square: 1,
      portrait: 3/4,
      landscape: 4/3
    };
    
    const aspectRatio = aspectRatios[settings.aspectRatio];
    const actualCardHeight = actualCardWidth / aspectRatio;
    
    return {
      columnsPerRow,
      cardWidth: actualCardWidth,
      cardHeight: actualCardHeight,
      gap: config.gap
    };
  }, [containerWidth, settings.density, settings.aspectRatio]);
}

// Hook for container size observation
function useContainerSize() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setSize({ width, height });
      }
    });

    resizeObserver.observe(container);
    
    // Initial size
    const rect = container.getBoundingClientRect();
    setSize({ width: rect.width, height: rect.height });

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return { containerRef, size };
}

export const EnhancedGridView = memo<EnhancedGridViewProps>(({
  games,
  settings,
  interactions,
  className
}) => {
  const { containerRef, size } = useContainerSize();
  const { selectedGames, toggleGameSelection } = useEnhancedLibrary();
  
  const gridDimensions = useResponsiveGrid(size.width, settings);
  
  // Calculate grid layout
  const { columnsPerRow, cardWidth, cardHeight } = gridDimensions;
  const rowCount = Math.ceil(games.length / columnsPerRow);
  
  // Memoized data for grid items
  const itemData = useMemo(() => ({
    games,
    columnsPerRow,
    settings,
    interactions,
    selectedGames,
    onGameSelect: toggleGameSelection
  }), [games, columnsPerRow, settings, interactions, selectedGames, toggleGameSelection]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback(() => {
    // Add keyboard navigation logic here if needed
    // For now, we'll let the individual cards handle their own keyboard events
  }, []);

  // Style classes based on settings
  const gridClasses = cn(
    'enhanced-grid-view',
    'w-full h-full',
    settings.cardStyle === 'modern' && 'modern-grid',
    settings.cardStyle === 'classic' && 'classic-grid',
    settings.cardStyle === 'minimal' && 'minimal-grid',
    settings.colorScheme === 'vibrant' && 'vibrant-colors',
    settings.colorScheme === 'muted' && 'muted-colors',
    className
  );

  if (games.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-64', className)}>
        <div className="text-center">
          <p className="text-lg text-muted-foreground">No games found</p>
          <p className="text-sm text-muted-foreground mt-2">
            Try adjusting your filters or search criteria
          </p>
        </div>
      </div>
    );
  }

  if (size.width === 0 || size.height === 0) {
    return (
      <div ref={containerRef} className={cn('w-full h-full', className)}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-pulse">
            <div className="grid grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="w-48 h-64 bg-muted rounded-lg" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef} 
      className={gridClasses}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="grid"
      aria-label={`Game library grid with ${games.length} games`}
    >
      <Grid
        columnCount={columnsPerRow}
        columnWidth={cardWidth + gridDimensions.gap}
        height={size.height}
        rowCount={rowCount}
        rowHeight={cardHeight + gridDimensions.gap}
        width={size.width}
        itemData={itemData}
        overscanRowCount={2}
        overscanColumnCount={1}
        style={{
          overflowX: 'hidden'
        }}
      >
        {GridItem}
      </Grid>
    </div>
  );
});

EnhancedGridView.displayName = 'EnhancedGridView';

// Grid customization controls component
interface GridCustomizationControlsProps {
  settings: GridViewSettings;
  onSettingsChange: (settings: Partial<GridViewSettings>) => void;
  className?: string;
}

export const GridCustomizationControls = memo<GridCustomizationControlsProps>(({
  settings,
  onSettingsChange,
  className
}) => {
  return (
    <div className={cn('flex flex-wrap gap-4 p-4 bg-card rounded-lg border', className)}>
      {/* Density Control */}
      <div className="flex flex-col gap-2">
        <label className="text-sm font-medium">Density</label>
        <div className="flex gap-2">
          {(['compact', 'normal', 'large'] as const).map((density) => (
            <button
              key={density}
              onClick={() => onSettingsChange({ density })}
              className={cn(
                'px-3 py-1 text-xs rounded-md border transition-colors',
                settings.density === density
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-background hover:bg-muted border-border'
              )}
            >
              {density.charAt(0).toUpperCase() + density.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Aspect Ratio Control */}
      <div className="flex flex-col gap-2">
        <label className="text-sm font-medium">Aspect Ratio</label>
        <div className="flex gap-2">
          {(['square', 'portrait', 'landscape'] as const).map((ratio) => (
            <button
              key={ratio}
              onClick={() => onSettingsChange({ aspectRatio: ratio })}
              className={cn(
                'px-3 py-1 text-xs rounded-md border transition-colors',
                settings.aspectRatio === ratio
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-background hover:bg-muted border-border'
              )}
            >
              {ratio.charAt(0).toUpperCase() + ratio.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Card Style Control */}
      <div className="flex flex-col gap-2">
        <label className="text-sm font-medium">Card Style</label>
        <div className="flex gap-2">
          {(['modern', 'classic', 'minimal'] as const).map((style) => (
            <button
              key={style}
              onClick={() => onSettingsChange({ cardStyle: style })}
              className={cn(
                'px-3 py-1 text-xs rounded-md border transition-colors',
                settings.cardStyle === style
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-background hover:bg-muted border-border'
              )}
            >
              {style.charAt(0).toUpperCase() + style.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Animation Style Control */}
      <div className="flex flex-col gap-2">
        <label className="text-sm font-medium">Animations</label>
        <div className="flex gap-2">
          {(['subtle', 'dynamic', 'premium'] as const).map((animation) => (
            <button
              key={animation}
              onClick={() => onSettingsChange({ animationStyle: animation })}
              className={cn(
                'px-3 py-1 text-xs rounded-md border transition-colors',
                settings.animationStyle === animation
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-background hover:bg-muted border-border'
              )}
            >
              {animation.charAt(0).toUpperCase() + animation.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Toggle Controls */}
      <div className="flex flex-col gap-2">
        <label className="text-sm font-medium">Options</label>
        <div className="flex gap-2">
          <button
            onClick={() => onSettingsChange({ enableHoverEffects: !settings.enableHoverEffects })}
            className={cn(
              'px-3 py-1 text-xs rounded-md border transition-colors',
              settings.enableHoverEffects
                ? 'bg-primary text-primary-foreground border-primary'
                : 'bg-background hover:bg-muted border-border'
            )}
          >
            Hover Effects
          </button>
          <button
            onClick={() => onSettingsChange({ showMetadata: !settings.showMetadata })}
            className={cn(
              'px-3 py-1 text-xs rounded-md border transition-colors',
              settings.showMetadata
                ? 'bg-primary text-primary-foreground border-primary'
                : 'bg-background hover:bg-muted border-border'
            )}
          >
            Show Metadata
          </button>
        </div>
      </div>
    </div>
  );
});

GridCustomizationControls.displayName = 'GridCustomizationControls';