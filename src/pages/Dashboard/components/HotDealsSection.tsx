import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, Badge } from '@/components/ui/base';
import { LoadingSpinner } from '@/components/ui/utils';
import { useDeals } from '@/hooks/usePriceTracking';
import { Flame } from 'lucide-react';

export function HotDealsSection() {
  const { bestDeals, dealStats, isLoadingDeals } = useDeals();

  if (isLoadingDeals) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Flame className="h-5 w-5 text-orange-500" />
            Hot Deals on Your Wishlist
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!bestDeals || bestDeals.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Flame className="h-5 w-5 text-orange-500" />
          Hot Deals on Your Wishlist
        </CardTitle>
        <CardDescription>
          {bestDeals.length} deals found • Save up to {dealStats.biggestDiscount}%
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {bestDeals.slice(0, 6).map((deal, index) => (
            <div key={index} className="p-3 border rounded-lg hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-2">
                <p className="font-medium text-sm line-clamp-1">{deal.gameName}</p>
                <Badge className="bg-red-500 text-white text-xs">
                  {deal.discountPercent}% OFF
                </Badge>
              </div>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Now:</span>
                  <span className="font-semibold text-green-600">
                    ${deal.currentPrice.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Was:</span>
                  <span className="line-through text-muted-foreground">
                    ${deal.originalPrice.toFixed(2)}
                  </span>
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                {deal.shopName}
              </p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}