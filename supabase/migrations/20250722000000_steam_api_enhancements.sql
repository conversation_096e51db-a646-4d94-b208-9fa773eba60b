-- Steam API Enhancements Migration
-- Implements the database schema changes from steam-api-improvements.md

-- Enhanced user games table with achievement and session data
ALTER TABLE user_games ADD COLUMN IF NOT EXISTS achievements_unlocked INTEGER DEFAULT 0;
ALTER TABLE user_games ADD COLUMN IF NOT EXISTS achievements_total INTEGER DEFAULT 0;
ALTER TABLE user_games ADD COLUMN IF NOT EXISTS last_session_length INTEGER;
ALTER TABLE user_games ADD COLUMN IF NOT EXISTS last_session_date TIMESTAMPTZ;
ALTER TABLE user_games ADD COLUMN IF NOT EXISTS playtime_2weeks INTEGER DEFAULT 0;

-- Add constraints for achievement data
ALTER TABLE user_games ADD CONSTRAINT check_achievements_unlocked_non_negative 
  CHECK (achievements_unlocked >= 0);
ALTER TABLE user_games ADD CONSTRAINT check_achievements_total_non_negative 
  CHECK (achievements_total >= 0);
ALTER TABLE user_games ADD CONSTRAINT check_achievements_logical 
  CHECK (achievements_unlocked <= achievements_total OR achievements_total IS NULL);
ALTER TABLE user_games ADD CONSTRAINT check_session_length_non_negative 
  CHECK (last_session_length >= 0);
ALTER TABLE user_games ADD CONSTRAINT check_playtime_2weeks_non_negative 
  CHECK (playtime_2weeks >= 0);

-- New achievements table for detailed achievement tracking
CREATE TABLE IF NOT EXISTS user_game_achievements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_game_id UUID REFERENCES user_games(id) ON DELETE CASCADE NOT NULL,
  achievement_name TEXT NOT NULL,
  achievement_description TEXT,
  unlocked_at TIMESTAMPTZ,
  is_rare BOOLEAN DEFAULT FALSE,
  global_percentage DECIMAL(5,2),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT user_game_achievements_unique UNIQUE (user_game_id, achievement_name)
);

-- Steam friends table for social features
CREATE TABLE IF NOT EXISTS steam_friends (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  friend_steam_id TEXT NOT NULL,
  friend_name TEXT,
  friend_avatar TEXT,
  relationship TEXT DEFAULT 'friend' CHECK (relationship IN ('friend', 'blocked', 'ignored')),
  friend_since TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT steam_friends_unique UNIQUE (user_id, friend_steam_id)
);

-- Steam player bans table for enhanced profile data
CREATE TABLE IF NOT EXISTS steam_player_bans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  steam_id TEXT NOT NULL,
  community_banned BOOLEAN DEFAULT FALSE,
  vac_banned BOOLEAN DEFAULT FALSE,
  number_of_vac_bans INTEGER DEFAULT 0,
  days_since_last_ban INTEGER,
  number_of_game_bans INTEGER DEFAULT 0,
  economy_ban TEXT DEFAULT 'none',
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT steam_player_bans_unique UNIQUE (user_id, steam_id)
);

-- Game statistics table for detailed Steam stats
CREATE TABLE IF NOT EXISTS user_game_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_game_id UUID REFERENCES user_games(id) ON DELETE CASCADE NOT NULL,
  stat_name TEXT NOT NULL,
  stat_value DECIMAL,
  stat_display_name TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT user_game_stats_unique UNIQUE (user_game_id, stat_name)
);

-- Steam news table for game news tracking
CREATE TABLE IF NOT EXISTS steam_game_news (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  steam_app_id INTEGER NOT NULL,
  news_id TEXT NOT NULL,
  title TEXT NOT NULL,
  url TEXT,
  is_external_url BOOLEAN DEFAULT FALSE,
  author TEXT,
  contents TEXT,
  feedlabel TEXT,
  date TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT steam_game_news_unique UNIQUE (steam_app_id, news_id)
);

-- Workshop items table for Steam Workshop integration
CREATE TABLE IF NOT EXISTS steam_workshop_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  published_file_id TEXT NOT NULL,
  steam_app_id INTEGER,
  title TEXT NOT NULL,
  description TEXT,
  creator TEXT,
  time_created TIMESTAMPTZ,
  time_updated TIMESTAMPTZ,
  subscriptions INTEGER DEFAULT 0,
  favorited INTEGER DEFAULT 0,
  preview_url TEXT,
  is_subscribed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT steam_workshop_items_unique UNIQUE (user_id, published_file_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_game_achievements_user_game_id ON user_game_achievements(user_game_id);
CREATE INDEX IF NOT EXISTS idx_user_game_achievements_unlocked_at ON user_game_achievements(unlocked_at);
CREATE INDEX IF NOT EXISTS idx_steam_friends_user_id ON steam_friends(user_id);
CREATE INDEX IF NOT EXISTS idx_steam_friends_friend_steam_id ON steam_friends(friend_steam_id);
CREATE INDEX IF NOT EXISTS idx_steam_player_bans_user_id ON steam_player_bans(user_id);
CREATE INDEX IF NOT EXISTS idx_steam_player_bans_steam_id ON steam_player_bans(steam_id);
CREATE INDEX IF NOT EXISTS idx_user_game_stats_user_game_id ON user_game_stats(user_game_id);
CREATE INDEX IF NOT EXISTS idx_steam_game_news_app_id ON steam_game_news(steam_app_id);
CREATE INDEX IF NOT EXISTS idx_steam_game_news_date ON steam_game_news(date);
CREATE INDEX IF NOT EXISTS idx_steam_workshop_items_user_id ON steam_workshop_items(user_id);
CREATE INDEX IF NOT EXISTS idx_steam_workshop_items_app_id ON steam_workshop_items(steam_app_id);

-- Enhanced indexes for user_games table
CREATE INDEX IF NOT EXISTS idx_user_games_achievements_unlocked ON user_games(achievements_unlocked);
CREATE INDEX IF NOT EXISTS idx_user_games_last_session_date ON user_games(last_session_date);
CREATE INDEX IF NOT EXISTS idx_user_games_playtime_2weeks ON user_games(playtime_2weeks);

-- RLS Policies for new tables
ALTER TABLE user_game_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE steam_friends ENABLE ROW LEVEL SECURITY;
ALTER TABLE steam_player_bans ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_game_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE steam_game_news ENABLE ROW LEVEL SECURITY;
ALTER TABLE steam_workshop_items ENABLE ROW LEVEL SECURITY;

-- Policies for user_game_achievements
CREATE POLICY "Users can view their own achievements" ON user_game_achievements
  FOR SELECT USING (
    user_game_id IN (
      SELECT id FROM user_games WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert their own achievements" ON user_game_achievements
  FOR INSERT WITH CHECK (
    user_game_id IN (
      SELECT id FROM user_games WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own achievements" ON user_game_achievements
  FOR UPDATE USING (
    user_game_id IN (
      SELECT id FROM user_games WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete their own achievements" ON user_game_achievements
  FOR DELETE USING (
    user_game_id IN (
      SELECT id FROM user_games WHERE user_id = auth.uid()
    )
  );

-- Policies for steam_friends
CREATE POLICY "Users can view their own friends" ON steam_friends
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own friends" ON steam_friends
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own friends" ON steam_friends
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own friends" ON steam_friends
  FOR DELETE USING (user_id = auth.uid());

-- Policies for steam_player_bans
CREATE POLICY "Users can view their own ban data" ON steam_player_bans
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own ban data" ON steam_player_bans
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own ban data" ON steam_player_bans
  FOR UPDATE USING (user_id = auth.uid());

-- Policies for user_game_stats
CREATE POLICY "Users can view their own game stats" ON user_game_stats
  FOR SELECT USING (
    user_game_id IN (
      SELECT id FROM user_games WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert their own game stats" ON user_game_stats
  FOR INSERT WITH CHECK (
    user_game_id IN (
      SELECT id FROM user_games WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own game stats" ON user_game_stats
  FOR UPDATE USING (
    user_game_id IN (
      SELECT id FROM user_games WHERE user_id = auth.uid()
    )
  );

-- Policies for steam_game_news (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view game news" ON steam_game_news
  FOR SELECT USING (auth.role() = 'authenticated');

-- Policies for steam_workshop_items
CREATE POLICY "Users can view their own workshop items" ON steam_workshop_items
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own workshop items" ON steam_workshop_items
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own workshop items" ON steam_workshop_items
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own workshop items" ON steam_workshop_items
  FOR DELETE USING (user_id = auth.uid());

-- Update triggers for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_steam_friends_updated_at BEFORE UPDATE ON steam_friends
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_game_stats_updated_at BEFORE UPDATE ON user_game_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
