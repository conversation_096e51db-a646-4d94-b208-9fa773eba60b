/**
 * PlayStation Network Import Service
 * Handles importing user's PlayStation gaming library and trophies
 */

import { BasePlatformService, type PlatformGameData, type AuthenticationResult, type ImportProgress } from './basePlatformService';
import { type KeyType } from '@/lib/apiKeyStore';
import { type Game } from '@/types';

interface PSNAuthResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
}

interface PSNGameData {
  npCommunicationId: string;
  trophyTitleName: string;
  trophyTitleDetail?: string;
  trophyTitleIconUrl: string;
  trophyTitlePlatform: string;
  hasTrophyGroups: boolean;
  definedTrophies: {
    bronze: number;
    silver: number;
    gold: number;
    platinum: number;
  };
  earnedTrophies: {
    bronze: number;
    silver: number;
    gold: number;
    platinum: number;
  };
  progress: number;
  earnedDateTime?: string;
  lastUpdatedDateTime: string;
  hiddenFlag: boolean;
  npServiceName?: string;
  trophySetVersion?: string;
}

interface PSNProfileResponse {
  onlineId: string;
  accountId: string;
  npId: string;
  avatarUrls?: Array<{
    size: string;
    avatarUrl: string;
  }>;
  plus: number;
  aboutMe?: string;
  languagesUsed?: string[];
  trophySummary: {
    level: number;
    progress: number;
    earnedTrophies: {
      bronze: number;
      silver: number;
      gold: number;
      platinum: number;
    };
  };
}

interface PSNTrophyTitlesResponse {
  trophyTitles: PSNGameData[];
  totalItemCount: number;
  nextOffset?: number;
}

export class PlayStationImportService extends BasePlatformService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiry: Date | null = null;
  private accountId: string | null = null;

  constructor() {
    super('playstation');
  }

  protected getRequiredKeys(): KeyType[] {
    return ['client_id', 'client_secret'];
  }

  /**
   * Authenticate with PlayStation Network using OAuth2
   * Note: PSN API access requires special approval from Sony
   */
  async authenticate(): Promise<AuthenticationResult> {
    try {
      const clientId = await this.getApiKey('client_id');
      const clientSecret = await this.getApiKey('client_secret');

      if (!clientId || !clientSecret) {
        return {
          success: false,
          error: 'PlayStation Network API credentials not found. Please configure your Client ID and Client Secret in Settings.',
        };
      }

      // PlayStation OAuth2 endpoint
      const tokenUrl = 'https://ca.account.sony.com/api/authz/v3/oauth/token';
      
      const response = await this.makeRequest(tokenUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${btoa(`${clientId}:${clientSecret}`)}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          scope: 'psn:mobile.v1 psn:clientapp',
        }),
      });

      const authData: PSNAuthResponse = await response.json();

      this.accessToken = authData.access_token;
      this.refreshToken = authData.refresh_token;
      this.tokenExpiry = new Date(Date.now() + authData.expires_in * 1000);

      // Get user profile to extract account ID
      await this.getUserProfile();

      return {
        success: true,
        accessToken: this.accessToken,
        refreshToken: this.refreshToken,
        expiresAt: this.tokenExpiry,
      };
    } catch (error) {
      console.error('PlayStation Network authentication failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      };
    }
  }

  /**
   * Get user profile information
   */
  private async getUserProfile(): Promise<PSNProfileResponse | null> {
    if (!this.accessToken) return null;

    try {
      const profileUrl = 'https://us-prof.np.community.playstation.net/userProfile/v1/users/me/profile2';
      
      const response = await this.makeRequest(profileUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Accept': 'application/json',
        },
      });

      const profileData: { profile: PSNProfileResponse } = await response.json();
      this.accountId = profileData.profile.accountId;
      
      return profileData.profile;
    } catch (error) {
      console.error('Failed to get PSN profile:', error);
      return null;
    }
  }

  /**
   * Check if access token is valid and refresh if needed
   */
  private async ensureValidToken(): Promise<void> {
    if (!this.accessToken || !this.tokenExpiry || this.tokenExpiry <= new Date()) {
      const authResult = await this.authenticate();
      if (!authResult.success) {
        throw new Error(authResult.error || 'Failed to authenticate');
      }
    }
  }

  /**
   * Fetch user's PlayStation gaming library from trophy data
   */
  async fetchUserLibrary(progressCallback?: (progress: ImportProgress) => void): Promise<PlatformGameData[]> {
    await this.ensureValidToken();

    if (!this.accountId) {
      throw new Error('PlayStation account ID not available');
    }

    const games: PlatformGameData[] = [];
    let offset = 0;
    const limit = 100;
    let hasMore = true;

    progressCallback?.({
      phase: 'fetching',
      current: 20,
      total: 100,
      message: 'Fetching PlayStation trophy data...',
    });

    try {
      while (hasMore) {
        const trophyUrl = `https://us-tpy.np.community.playstation.net/trophy/v1/users/${this.accountId}/trophyTitles`;
        
        const queryParams = new URLSearchParams({
          offset: offset.toString(),
          limit: limit.toString(),
          npServiceName: 'trophy',
        });

        const response = await this.makeRequest(`${trophyUrl}?${queryParams}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Accept': 'application/json',
          },
        });

        const trophyData: PSNTrophyTitlesResponse = await response.json();

        if (trophyData.trophyTitles && trophyData.trophyTitles.length > 0) {
          for (const trophy of trophyData.trophyTitles) {
            const platformGame = await this.convertPSNGameToPlatformData(trophy);
            games.push(platformGame);
          }

          // Update progress
          progressCallback?.({
            phase: 'fetching',
            current: 20 + (games.length / trophyData.totalItemCount) * 40,
            total: 100,
            message: `Fetched ${games.length}/${trophyData.totalItemCount} games from PlayStation...`,
          });

          offset += limit;
          hasMore = trophyData.nextOffset !== undefined && games.length < trophyData.totalItemCount;
        } else {
          hasMore = false;
        }
      }

      return games;
    } catch (error) {
      console.error('Failed to fetch PlayStation library:', error);
      throw new Error(`Failed to fetch library: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get detailed trophy information for a specific game
   */
  private async getGameTrophies(npCommunicationId: string): Promise<unknown> {
    if (!this.accountId || !this.accessToken) return null;

    try {
      const trophiesUrl = `https://us-tpy.np.community.playstation.net/trophy/v1/users/${this.accountId}/npCommunicationIds/${npCommunicationId}/trophyGroups/all/trophies`;
      
      const response = await this.makeRequest(trophiesUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Accept': 'application/json',
        },
      });

      return await response.json();
    } catch (error) {
      console.error(`Failed to get trophies for ${npCommunicationId}:`, error);
      return null;
    }
  }

  /**
   * Convert PlayStation game data to our platform format
   */
  private async convertPSNGameToPlatformData(psnGame: PSNGameData): Promise<PlatformGameData> {
    // Get detailed trophy data
    const trophyData = await this.getGameTrophies(psnGame.npCommunicationId);
    
    // Determine platform based on trophyTitlePlatform
    const platforms = this.mapPSNPlatform(psnGame.trophyTitlePlatform);
    
    // Calculate total trophy count
    const totalTrophies = psnGame.definedTrophies.bronze + 
                         psnGame.definedTrophies.silver + 
                         psnGame.definedTrophies.gold + 
                         psnGame.definedTrophies.platinum;

    return {
      platformId: psnGame.npCommunicationId,
      name: psnGame.trophyTitleName,
      description: psnGame.trophyTitleDetail,
      imageUrl: psnGame.trophyTitleIconUrl,
      platforms,
      achievementCount: totalTrophies,
      lastPlayed: psnGame.earnedDateTime || psnGame.lastUpdatedDateTime,
      metadata: {
        psn_np_communication_id: psnGame.npCommunicationId,
        psn_platform: psnGame.trophyTitlePlatform,
        psn_has_trophy_groups: psnGame.hasTrophyGroups,
        psn_defined_trophies: psnGame.definedTrophies,
        psn_earned_trophies: psnGame.earnedTrophies,
        psn_progress: psnGame.progress,
        psn_hidden_flag: psnGame.hiddenFlag,
        psn_np_service_name: psnGame.npServiceName,
        psn_trophy_set_version: psnGame.trophySetVersion,
        psn_detailed_trophies: trophyData,
        psn_last_updated: psnGame.lastUpdatedDateTime,
      },
    };
  }

  /**
   * Map PlayStation platform strings to our format
   */
  private mapPSNPlatform(psnPlatform: string): string[] {
    const platformMap: Record<string, string[]> = {
      'PS3': ['PlayStation 3'],
      'PS4': ['PlayStation 4'],
      'PS5': ['PlayStation 5'],
      'PSVITA': ['PlayStation Vita'],
      'PSP': ['PlayStation Portable'],
    };

    return platformMap[psnPlatform] || [psnPlatform];
  }

  /**
   * Convert platform data to our internal Game format
   */
  protected convertToGame(platformGame: PlatformGameData): Partial<Game> {
    return {
      name: platformGame.name,
      description: platformGame.description,
      cover_image: platformGame.imageUrl,
      platforms: platformGame.platforms,
      platform_ids: {
        playstation: platformGame.platformId,
      },
      metadata: {
        import_source: 'playstation',
        achievement_count: platformGame.achievementCount,
        last_played: platformGame.lastPlayed,
        trophy_progress: platformGame.metadata?.psn_progress,
        ...platformGame.metadata,
      },
    };
  }

  /**
   * Get user's PlayStation trophy statistics
   */
  async getTrophyStats(): Promise<PSNProfileResponse['trophySummary'] | null> {
    const profile = await this.getUserProfile();
    return profile?.trophySummary || null;
  }

  /**
   * Search PlayStation Store (limited public access)
   */
  async searchPlayStationStore(): Promise<PlatformGameData[]> {
    // PlayStation Store search requires special API access
    // This is a placeholder for future implementation
    console.warn('PlayStation Store search not yet implemented');
    return [];
  }

  /**
   * Get PlayStation Store URL for a game
   */
  static getStoreUrl(gameId: string, region: string = 'US'): string {
    return `https://store.playstation.com/en-${region.toLowerCase()}/product/${gameId}`;
  }

  /**
   * Calculate trophy rarity score
   */
  static calculateTrophyRarity(psnGame: PSNGameData): number {
    const { platinum, gold, silver, bronze } = psnGame.earnedTrophies;
    
    // Weighted scoring: Platinum=10, Gold=5, Silver=3, Bronze=1
    return (platinum * 10) + (gold * 5) + (silver * 3) + bronze;
  }

  /**
   * Check if user has completed the game (earned all trophies)
   */
  static isGameCompleted(psnGame: PSNGameData): boolean {
    const defined = psnGame.definedTrophies;
    const earned = psnGame.earnedTrophies;
    
    return defined.bronze === earned.bronze &&
           defined.silver === earned.silver &&
           defined.gold === earned.gold &&
           defined.platinum === earned.platinum;
  }

  /**
   * Get games by platform
   */
  async getGamesByPlatform(platform: 'PS3' | 'PS4' | 'PS5' | 'PSVITA' | 'PSP'): Promise<PlatformGameData[]> {
    const allGames = await this.fetchUserLibrary();
    return allGames.filter(game => 
      game.metadata?.psn_platform === platform
    );
  }

  /**
   * Get user's rarest trophies
   */
  async getRarestTrophies(): Promise<unknown[]> {
    // This would require access to trophy rarity data from PSN
    // For now, this is a placeholder
    console.warn('Rare trophy data not yet implemented');
    return [];
  }
}

// Export singleton instance - DISABLED: Only Steam is supported as of July 2025
// export const playstationImportService = new PlayStationImportService();