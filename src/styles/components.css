/* Component-specific styles */

/* Header Profile Dropdown Styles */
.profile-dropdown {
  @apply absolute right-0 mt-2 w-72 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl shadow-2xl z-[9999];
  background: rgba(255, 255, 255, 0.95);
}

.dark .profile-dropdown {
  background: rgba(17, 24, 39, 0.95);
}

/* Avatar Styles */
.avatar-container {
  @apply relative h-10 w-10 rounded-full overflow-hidden;
}

.avatar-image {
  @apply h-full w-full object-cover;
}

.avatar-fallback {
  @apply flex h-full w-full items-center justify-center rounded-full bg-gradient-to-br from-primary via-secondary to-accent text-primary-foreground text-sm font-bold shadow-sm;
}

/* Profile dropdown animations */
.profile-dropdown-enter {
  animation: profileDropdownEnter 0.2s ease-out forwards;
}

/* Enhanced dropdown styling */
.dropdown-header {
  @apply px-4 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-t-xl;
}

.dropdown-menu-item {
  @apply flex items-center gap-3 w-full px-4 py-3 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100 transition-all duration-200 rounded-lg mx-1 font-medium;
}

.dropdown-menu-item-destructive {
  @apply flex items-center gap-3 w-full px-4 py-3 text-sm hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 text-red-600 dark:text-red-400 transition-all duration-200 rounded-lg mx-1 font-medium;
}

.dropdown-icon-container {
  @apply p-1.5 bg-primary/10 rounded-md group-hover:bg-primary/20 transition-colors;
}

.dropdown-icon-container-destructive {
  @apply p-1.5 bg-red-100 dark:bg-red-900/30 rounded-md group-hover:bg-red-200 dark:group-hover:bg-red-900/50 transition-colors;
}

/* Platform Family Group Animations */
.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}