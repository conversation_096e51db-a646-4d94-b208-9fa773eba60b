import { useState } from 'react';
import { Checkbox } from '@/components/ui/base/checkbox';
import { Input } from '@/components/ui/base/input';
import { But<PERSON> } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { Switch } from '@/components/ui/base/switch';
import { Label } from '@/components/ui/base/label';
import { Search, X, Plus } from 'lucide-react';
import { PublisherFilter } from '@/types/filters';

interface PublisherFilterComponentProps {
  filter: PublisherFilter;
  onChange: (update: Partial<PublisherFilter>) => void;
  disabled?: boolean;
}

// Popular game publishers - this could be fetched from API in the future
const POPULAR_PUBLISHERS = [
  'Sony Interactive Entertainment',
  'Microsoft Game Studios',
  'Nintendo',
  'Electronic Arts',
  'Activision Blizzard',
  'Ubisoft',
  'Take-Two Interactive',
  'Valve Corporation',
  'Epic Games',
  'Square Enix',
  'Capcom',
  'Bandai Namco Entertainment',
  'Konami',
  'Sega',
  'Warner Bros. Games',
  'Bethesda Softworks',
  'Devolver Digital',
  'Annapurna Interactive',
  'Team17',
  'Indie',
  'Self-Published',
  'CD Projekt',
  'Focus Entertainment',
  'Paradox Interactive',
  'Deep Silver',
  'Koch Media',
  'THQ Nordic',
  'Embracer Group',
  'Humble Games',
  'Raw Fury',
  'Coffee Stain Publishing',
  'Curve Digital',
  'Versus Evil',
  'Larian Studios',
  'Supergiant Games',
  'Grinding Gear Games',
  'Digital Extremes',
  'Riot Games',
  'Supercell',
  'King',
  'Mojang Studios'
];

export function PublisherFilterComponent({ 
  filter, 
  onChange, 
  disabled = false 
}: PublisherFilterComponentProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [customPublisher, setCustomPublisher] = useState('');

  const filteredPublishers = POPULAR_PUBLISHERS.filter(publisher => 
    publisher.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleToggleEnabled = (enabled: boolean) => {
    onChange({ enabled });
  };

  const handlePublisherToggle = (publisher: string) => {
    const isSelected = filter.publishers.includes(publisher);
    const newPublishers = isSelected
      ? filter.publishers.filter(p => p !== publisher)
      : [...filter.publishers, publisher];
    
    onChange({ publishers: newPublishers });
  };

  const handleAddCustomPublisher = () => {
    if (customPublisher.trim() && !filter.publishers.includes(customPublisher.trim())) {
      onChange({ 
        publishers: [...filter.publishers, customPublisher.trim()]
      });
      setCustomPublisher('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddCustomPublisher();
    }
  };

  const removePublisher = (publisher: string) => {
    onChange({ 
      publishers: filter.publishers.filter(p => p !== publisher)
    });
  };

  const clearAll = () => {
    onChange({ publishers: [] });
  };

  const selectMajor = () => {
    const majorPublishers = [
      'Sony Interactive Entertainment',
      'Microsoft Game Studios',
      'Nintendo',
      'Electronic Arts',
      'Activision Blizzard',
      'Ubisoft',
      'Take-Two Interactive',
      'Valve Corporation'
    ];
    const newPublishers = [...new Set([...filter.publishers, ...majorPublishers])];
    onChange({ publishers: newPublishers });
  };

  const selectIndie = () => {
    const indiePublishers = [
      'Devolver Digital',
      'Annapurna Interactive',
      'Team17',
      'Indie',
      'Self-Published',
      'Humble Games',
      'Raw Fury'
    ];
    const newPublishers = [...new Set([...filter.publishers, ...indiePublishers])];
    onChange({ publishers: newPublishers });
  };

  return (
    <div className="space-y-4">
      {/* Enable/Disable Toggle */}
      <div className="flex items-center justify-between">
        <Label htmlFor="publisher-enabled" className="text-sm font-medium">
          Filter by Publisher
        </Label>
        <Switch
          id="publisher-enabled"
          checked={filter.enabled}
          onCheckedChange={handleToggleEnabled}
          disabled={disabled}
        />
      </div>

      {filter.enabled && (
        <>
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search publishers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              disabled={disabled}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery('')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Add Custom Publisher */}
          <div className="flex gap-2">
            <Input
              placeholder="Add custom publisher..."
              value={customPublisher}
              onChange={(e) => setCustomPublisher(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
              disabled={disabled}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddCustomPublisher}
              disabled={disabled || !customPublisher.trim()}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {/* Selected Publishers */}
          {filter.publishers.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">
                  Selected Publishers ({filter.publishers.length})
                </Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAll}
                  className="text-xs h-6"
                  disabled={disabled}
                >
                  Clear All
                </Button>
              </div>
              <div className="flex flex-wrap gap-1 max-h-24 overflow-y-auto">
                {filter.publishers.map(publisher => (
                  <Badge
                    key={publisher}
                    variant="secondary"
                    className="text-xs flex items-center gap-1"
                  >
                    {publisher}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removePublisher(publisher)}
                      className="h-3 w-3 p-0 hover:bg-destructive hover:text-destructive-foreground"
                      disabled={disabled}
                    >
                      <X className="h-2 w-2" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={selectMajor}
              className="text-xs flex-1"
              disabled={disabled}
            >
              Major Publishers
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={selectIndie}
              className="text-xs flex-1"
              disabled={disabled}
            >
              Indie Publishers
            </Button>
          </div>

          {/* Publisher Selection */}
          <ScrollArea className="h-48">
            <div className="space-y-2">
              {searchQuery ? (
                // Show filtered results when searching
                <>
                  {filteredPublishers.map(publisher => (
                    <div key={publisher} className="flex items-center space-x-2">
                      <Checkbox
                        id={`publisher-${publisher}`}
                        checked={filter.publishers.includes(publisher)}
                        onCheckedChange={() => handlePublisherToggle(publisher)}
                        disabled={disabled}
                      />
                      <Label
                        htmlFor={`publisher-${publisher}`}
                        className="text-sm cursor-pointer flex-1"
                      >
                        {publisher}
                      </Label>
                    </div>
                  ))}
                  {filteredPublishers.length === 0 && (
                    <div className="text-center py-4">
                      <p className="text-sm text-muted-foreground mb-2">
                        No publishers found matching "{searchQuery}"
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setCustomPublisher(searchQuery);
                          setSearchQuery('');
                        }}
                        className="text-xs"
                        disabled={disabled}
                      >
                        Add "{searchQuery}" as custom publisher
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                // Show popular publishers when not searching
                <>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Popular Publishers
                  </Label>
                  {POPULAR_PUBLISHERS.map(publisher => (
                    <div key={publisher} className="flex items-center space-x-2">
                      <Checkbox
                        id={`publisher-${publisher}`}
                        checked={filter.publishers.includes(publisher)}
                        onCheckedChange={() => handlePublisherToggle(publisher)}
                        disabled={disabled}
                      />
                      <Label
                        htmlFor={`publisher-${publisher}`}
                        className="text-sm cursor-pointer flex-1"
                      >
                        {publisher}
                      </Label>
                    </div>
                  ))}
                </>
              )}
            </div>
          </ScrollArea>

          {/* Filter Description */}
          <div className="text-xs text-muted-foreground p-2 bg-muted/50 rounded">
            Show games published by any of the selected publishers. You can search for 
            publishers or add custom ones not in the popular list.
          </div>
        </>
      )}
    </div>
  );
}