import { supabase } from './supabase-client';
import { logError } from './supabase-utils';

// Enhanced tagging system helpers
export const userTags = {
  // Get all user tags with usage counts
  getAll: async (userId: string) => {
    const { data, error } = await supabase
      .from('user_tags')
      .select('*')
      .eq('user_id', userId)
      .order('usage_count', { ascending: false });
    
    if (error) {
      logError('getUserTags', error, { userId });
      return { data: [], error };
    }
    
    return { data: data || [], error: null };
  },

  // Create a new tag
  create: async (userId: string, tagData: { name: string; description?: string; color?: string }) => {
    const { data, error } = await supabase
      .from('user_tags')
      .insert({
        user_id: userId,
        name: tagData.name,
        description: tagData.description,
        color: tagData.color || '#3b82f6'
      })
      .select()
      .single();
    
    if (error) {
      logError('createUserTag', error, { userId, tagName: tagData.name });
      return { data: null, error };
    }
    
    return { data, error: null };
  },

  // Update an existing tag
  update: async (tagId: string, updates: Partial<{ name: string; description: string; color: string }>) => {
    const { data, error } = await supabase
      .from('user_tags')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', tagId)
      .select()
      .single();
    
    if (error) {
      logError('updateUserTag', error, { tagId });
      return { data: null, error };
    }
    
    return { data, error: null };
  },

  // Delete a tag
  delete: async (tagId: string) => {
    const { error } = await supabase
      .from('user_tags')
      .delete()
      .eq('id', tagId);
    
    if (error) {
      logError('deleteUserTag', error, { tagId });
      return { error };
    }
    
    return { error: null };
  },

  // Get popular tags across all users
  getPopular: async (limit: number = 10) => {
    const { data, error } = await supabase
      .rpc('get_popular_tags', { limit_count: limit });
    
    if (error) {
      logError('getPopularTags', error, { limit });
      return { data: [], error };
    }
    
    return { data: data || [], error: null };
  }
};

// User game tags helpers
export const userGameTags = {
  // Get all tags for a specific user game
  getByUserGame: async (userGameId: string) => {
    const { data, error } = await supabase
      .from('user_game_tags')
      .select(`
        *,
        user_tags (
          id,
          name,
          description,
          color,
          usage_count
        )
      `)
      .eq('user_game_id', userGameId);
    
    if (error) {
      logError('getUserGameTags', error, { userGameId });
      return { data: [], error };
    }
    
    return { data: data || [], error: null };
  },

  // Add a tag to a user game
  addTagToGame: async (userId: string, userGameId: string, tagId: string) => {
    const { data, error } = await supabase
      .from('user_game_tags')
      .insert({
        user_id: userId,
        user_game_id: userGameId,
        tag_id: tagId
      })
      .select()
      .single();
    
    if (error) {
      logError('addTagToGame', error, { userId, userGameId, tagId });
      return { data: null, error };
    }
    
    return { data, error: null };
  },

  // Remove a tag from a user game
  removeTagFromGame: async (userGameId: string, tagId: string) => {
    const { error } = await supabase
      .from('user_game_tags')
      .delete()
      .eq('user_game_id', userGameId)
      .eq('tag_id', tagId);
    
    if (error) {
      logError('removeTagFromGame', error, { userGameId, tagId });
      return { error };
    }
    
    return { error: null };
  },

  // Get all games with a specific tag
  getGamesByTag: async (userId: string, tagId: string) => {
    const { data, error } = await supabase
      .from('user_game_tags')
      .select(`
        *,
        user_games (
          *,
          games (*)
        )
      `)
      .eq('user_id', userId)
      .eq('tag_id', tagId);
    
    if (error) {
      logError('getGamesByTag', error, { userId, tagId });
      return { data: [], error };
    }
    
    return { data: data || [], error: null };
  },

  // Bulk tag operations
  bulkAddTags: async (userId: string, userGameId: string, tagIds: string[]) => {
    const inserts = tagIds.map(tagId => ({
      user_id: userId,
      user_game_id: userGameId,
      tag_id: tagId
    }));
    
    const { data, error } = await supabase
      .from('user_game_tags')
      .insert(inserts)
      .select();
    
    if (error) {
      logError('bulkAddTags', error, { userId, userGameId, tagCount: tagIds.length });
      return { data: [], error };
    }
    
    return { data: data || [], error: null };
  },

  // Remove all tags from a game
  removeAllTagsFromGame: async (userGameId: string) => {
    const { error } = await supabase
      .from('user_game_tags')
      .delete()
      .eq('user_game_id', userGameId);
    
    if (error) {
      logError('removeAllTagsFromGame', error, { userGameId });
      return { error };
    }
    
    return { error: null };
  }
};

// Filter presets helpers
export const filterPresets = {
  // Get all user filter presets
  getAll: async (userId: string) => {
    const { data, error } = await supabase
      .from('filter_presets')
      .select('*')
      .eq('user_id', userId)
      .order('usage_count', { ascending: false });
    
    if (error) {
      logError('getFilterPresets', error, { userId });
      return { data: [], error };
    }
    
    return { data: data || [], error: null };
  },

  // Create a new filter preset
  create: async (userId: string, presetData: { name: string; description?: string; filters: unknown; isPublic?: boolean }) => {
    const { data, error } = await supabase
      .from('filter_presets')
      .insert({
        user_id: userId,
        name: presetData.name,
        description: presetData.description,
        filters: presetData.filters,
        is_public: presetData.isPublic || false
      })
      .select()
      .single();
    
    if (error) {
      logError('createFilterPreset', error, { userId, presetName: presetData.name });
      return { data: null, error };
    }
    
    return { data, error: null };
  },

  // Update a filter preset
  update: async (presetId: string, updates: Partial<{ name: string; description: string; filters: unknown; is_public: boolean }>) => {
    const { data, error } = await supabase
      .from('filter_presets')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', presetId)
      .select()
      .single();
    
    if (error) {
      logError('updateFilterPreset', error, { presetId });
      return { data: null, error };
    }
    
    return { data, error: null };
  },

  // Delete a filter preset
  delete: async (presetId: string) => {
    const { error } = await supabase
      .from('filter_presets')
      .delete()
      .eq('id', presetId);
    
    if (error) {
      logError('deleteFilterPreset', error, { presetId });
      return { error };
    }
    
    return { error: null };
  },

  // Get public filter presets
  getPublic: async (limit: number = 20) => {
    const { data, error } = await supabase
      .from('filter_presets')
      .select('*')
      .eq('is_public', true)
      .order('usage_count', { ascending: false })
      .limit(limit);
    
    if (error) {
      logError('getPublicFilterPresets', error, { limit });
      return { data: [], error };
    }
    
    return { data: data || [], error: null };
  },

  // Increment usage count
  incrementUsage: async (presetId: string) => {
    // First get the current usage count
    const { data: currentPreset, error: fetchError } = await supabase
      .from('filter_presets')
      .select('usage_count')
      .eq('id', presetId)
      .single();
    
    if (fetchError) {
      logError('incrementPresetUsage', fetchError, { presetId });
      return { error: fetchError };
    }
    
    // Then increment it
    const { error } = await supabase
      .from('filter_presets')
      .update({ usage_count: (currentPreset?.usage_count || 0) + 1 })
      .eq('id', presetId);
    
    if (error) {
      logError('incrementPresetUsage', error, { presetId });
      return { error };
    }
    
    return { error: null };
  }
};

// Tag suggestions helpers
export const tagSuggestions = {
  // Get suggestions for a user
  getAll: async (userId: string) => {
    const { data, error } = await supabase
      .from('tag_suggestions')
      .select('*')
      .eq('user_id', userId)
      .order('confidence_score', { ascending: false });
    
    if (error) {
      logError('getTagSuggestions', error, { userId });
      return { data: [], error };
    }
    
    return { data: data || [], error: null };
  },

  // Create a new tag suggestion
  create: async (userId: string, suggestionData: { tagName: string; context?: unknown; confidenceScore?: number }) => {
    const { data, error } = await supabase
      .from('tag_suggestions')
      .insert({
        user_id: userId,
        tag_name: suggestionData.tagName,
        context: suggestionData.context,
        confidence_score: suggestionData.confidenceScore || 0.5
      })
      .select()
      .single();
    
    if (error) {
      logError('createTagSuggestion', error, { userId, tagName: suggestionData.tagName });
      return { data: null, error };
    }
    
    return { data, error: null };
  },

  // Accept a tag suggestion
  accept: async (suggestionId: string) => {
    const { data, error } = await supabase
      .from('tag_suggestions')
      .update({ is_accepted: true })
      .eq('id', suggestionId)
      .select()
      .single();
    
    if (error) {
      logError('acceptTagSuggestion', error, { suggestionId });
      return { data: null, error };
    }
    
    return { data, error: null };
  },

  // Dismiss a tag suggestion
  dismiss: async (suggestionId: string) => {
    const { error } = await supabase
      .from('tag_suggestions')
      .delete()
      .eq('id', suggestionId);
    
    if (error) {
      logError('dismissTagSuggestion', error, { suggestionId });
      return { error };
    }
    
    return { error: null };
  }
};