import React, { memo, useMemo, useCallback, useState, useRef, useEffect } from 'react';
import { FixedSizeList as List } from 'react-window';
import { useEnhancedLibrary } from '@/hooks/useEnhancedLibrary';
import { UserGameWithDetails } from '@/types/database';
import { ListViewSettings, GameCardInteractions, ListColumn } from '@/types/library';
import { cn } from '@/lib/utils';
import { 
  ChevronUp, 
  ChevronDown, 
  Settings, 
  Eye,
  Search,
  Filter
} from 'lucide-react';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent } from '@/components/ui/base/card';
import { Input } from '@/components/ui/base/input';

// List item configurations
const LIST_CONFIGS = {
  compact: {
    rowHeight: 60,
    thumbnailSize: 40,
    fontSize: 'text-sm',
    padding: 'p-2'
  },
  normal: {
    rowHeight: 80,
    thumbnailSize: 60,
    fontSize: 'text-base',
    padding: 'p-3'
  },
  comfortable: {
    rowHeight: 100,
    thumbnailSize: 80,
    fontSize: 'text-lg',
    padding: 'p-4'
  }
} as const;

interface EnhancedListViewProps {
  games: UserGameWithDetails[];
  settings: ListViewSettings;
  onSettingsChange: (settings: Partial<ListViewSettings>) => void;
  interactions?: GameCardInteractions;
  className?: string;
}

interface ListItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    games: UserGameWithDetails[];
    settings: ListViewSettings;
    interactions?: GameCardInteractions;
    visibleColumns: ListColumn[];
    sortedBy: string | null;
    sortDirection: 'asc' | 'desc';
    selectedGames: Set<string>;
    onGameSelect: (gameId: string) => void;
    onSort: (columnKey: string) => void;
  };
}

// Sorting utility functions
const sortGames = (games: UserGameWithDetails[], sortBy: string, direction: 'asc' | 'desc') => {
  return [...games].sort((a, b) => {
    let aValue: string | number | Date, bValue: string | number | Date;
    
    switch (sortBy) {
      case 'title':
        aValue = a.game?.title || '';
        bValue = b.game?.title || '';
        break;
      case 'platform':
        aValue = a.game?.platforms?.[0] || '';
        bValue = b.game?.platforms?.[0] || '';
        break;
      case 'developer':
        aValue = a.game?.developer || '';
        bValue = b.game?.developer || '';
        break;
      case 'releaseDate':
        aValue = new Date(a.game?.release_date || '1970-01-01');
        bValue = new Date(b.game?.release_date || '1970-01-01');
        break;
      case 'status':
        aValue = a.status || '';
        bValue = b.status || '';
        break;
      case 'rating':
        aValue = a.personal_rating || 0;
        bValue = b.personal_rating || 0;
        break;
      case 'metacritic':
        aValue = a.game?.metacritic_score || 0;
        bValue = b.game?.metacritic_score || 0;
        break;
      case 'hoursPlayed':
        aValue = a.hours_played || 0;
        bValue = b.hours_played || 0;
        break;
      case 'dateAdded':
        aValue = new Date(a.date_added);
        bValue = new Date(b.date_added);
        break;
      default:
        return 0;
    }
    
    if (aValue < bValue) return direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

// Status color mapping
const getStatusColor = (status: string) => {
  switch (status) {
    case 'playing': return 'bg-blue-500';
    case 'completed': return 'bg-green-500';
    case 'backlog': return 'bg-yellow-500';
    case 'wishlist': return 'bg-purple-500';
    case 'dropped': return 'bg-red-500';
    default: return 'bg-gray-500';
  }
};

// Platform icon mapping (simplified for now)
const getPlatformIcon = (platforms: string[]) => {
  if (!platforms || platforms.length === 0) return '🎮';
  const platform = platforms[0].toLowerCase();
  if (platform.includes('pc') || platform.includes('windows')) return '💻';
  if (platform.includes('playstation') || platform.includes('ps')) return '🎮';
  if (platform.includes('xbox')) return '🎮';
  if (platform.includes('nintendo') || platform.includes('switch')) return '🎮';
  return '🎮';
};

// Memoized list item component for performance
const ListItem = memo<ListItemProps>(({ index, style, data }) => {
  const { 
    games, 
    settings, 
    interactions, 
    visibleColumns, 
    selectedGames, 
    onGameSelect 
  } = data;
  
  const game = games[index];
  if (!game) return <div style={style} />;

  const config = LIST_CONFIGS[settings.compactMode ? 'compact' : 'normal'];
  const isSelected = selectedGames.has(game.id);

  return (
    <div
      style={style}
      className={cn(
        'flex items-center border-b border-border/50 hover:bg-muted/50 transition-colors',
        isSelected && 'bg-primary/10 ring-1 ring-primary/20',
        config.padding
      )}
      onClick={() => onGameSelect(game.id)}
    >
      {/* Selection checkbox */}
      <div className="flex items-center mr-3">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={() => onGameSelect(game.id)}
          className="rounded border-border"
        />
      </div>

      {/* Thumbnail */}
      {settings.showThumbnails && (
        <div className="flex-shrink-0 mr-3">
          <img
            src={game.game?.cover_image || ''}
            alt={game.game?.title || ''}
            className={cn(
              'rounded object-cover bg-muted',
              `w-${config.thumbnailSize/4} h-${config.thumbnailSize/4}`
            )}
            style={{
              width: config.thumbnailSize,
              height: config.thumbnailSize
            }}
            loading="lazy"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
            }}
          />
        </div>
      )}

      {/* Columns */}
      {visibleColumns.map((column) => {
        let content: React.ReactNode = '';
        let className = cn('px-2', config.fontSize);
        
        switch (column.key) {
          case 'title':
            content = (
              <div className="flex flex-col">
                <span className="font-medium truncate">{game.game?.title}</span>
                {!settings.compactMode && (
                  <span className="text-xs text-muted-foreground truncate">
                    {game.game?.developer}
                  </span>
                )}
              </div>
            );
            className = cn(className, 'flex-1 min-w-0');
            break;
            
          case 'platform':
            content = (
              <div className="flex items-center gap-1">
                <span>{getPlatformIcon(game.game?.platforms || [])}</span>
                <span className="truncate">
                  {game.game?.platforms?.[0] || 'Unknown'}
                </span>
              </div>
            );
            break;
            
          case 'developer':
            content = <span className="truncate">{game.game?.developer || 'Unknown'}</span>;
            break;
            
          case 'releaseDate':
            content = new Date(game.game?.release_date || '').getFullYear() || 'Unknown';
            break;
            
          case 'status':
            content = (
              <Badge 
                variant="secondary" 
                className={cn('text-white', getStatusColor(game.status))}
              >
                {game.status}
              </Badge>
            );
            break;
            
          case 'rating':
            content = game.personal_rating ? `${game.personal_rating}/10` : '-';
            break;
            
          case 'metacritic':
            content = game.game?.metacritic_score || '-';
            break;
            
          case 'hoursPlayed':
            content = game.hours_played ? `${game.hours_played}h` : '-';
            break;
            
          case 'dateAdded':
            content = new Date(game.date_added).toLocaleDateString();
            break;
            
          case 'actions':
            content = (
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    interactions?.onQuickPlay?.(game.id);
                  }}
                >
                  Play
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Open dropdown or quick actions
                  }}
                >
                  ⋯
                </Button>
              </div>
            );
            break;
        }
        
        return (
          <div
            key={column.key}
            className={className}
            style={{ 
              width: column.width === 'auto' ? undefined : column.width,
              minWidth: typeof column.width === 'number' ? column.width : undefined
            }}
          >
            {content}
          </div>
        );
      })}
    </div>
  );
});

ListItem.displayName = 'ListItem';

// Hook for list state management
function useListState(games: UserGameWithDetails[], settings: ListViewSettings) { // eslint-disable-line @typescript-eslint/no-unused-vars
  const [sortBy, setSortBy] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [searchQuery, setSearchQuery] = useState('');

  const handleSort = useCallback((columnKey: string) => {
    if (sortBy === columnKey) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(columnKey);
      setSortDirection('asc');
    }
  }, [sortBy]);

  const filteredAndSortedGames = useMemo(() => {
    let filtered = games;
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = games.filter(game => 
        game.game?.title?.toLowerCase().includes(query) ||
        game.game?.developer?.toLowerCase().includes(query) ||
        game.game?.genres?.some(genre => genre.toLowerCase().includes(query))
      );
    }
    
    // Apply sorting
    if (sortBy) {
      filtered = sortGames(filtered, sortBy, sortDirection);
    }
    
    return filtered;
  }, [games, sortBy, sortDirection, searchQuery]);

  return {
    sortBy,
    sortDirection,
    searchQuery,
    setSearchQuery,
    handleSort,
    filteredAndSortedGames
  };
}

// Hook for container size observation
function useContainerSize() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setSize({ width, height });
      }
    });

    resizeObserver.observe(container);
    
    // Initial size
    const rect = container.getBoundingClientRect();
    setSize({ width: rect.width, height: rect.height });

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return { containerRef, size };
}

// Column header component
interface ColumnHeaderProps {
  column: ListColumn;
  isActive: boolean;
  sortDirection: 'asc' | 'desc';
  onSort: (columnKey: string) => void;
  onWidthChange: (columnKey: string, width: number) => void;
}

const ColumnHeader = memo<ColumnHeaderProps>(({ 
  column, 
  isActive, 
  sortDirection, 
  onSort,
  onWidthChange 
}) => {
  const [isResizing, setIsResizing] = useState(false);
  const resizeStartX = useRef(0);
  const startWidth = useRef(0);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!column.resizable) return;
    
    setIsResizing(true);
    resizeStartX.current = e.clientX;
    startWidth.current = typeof column.width === 'number' ? column.width : 150;
    
    e.preventDefault();
  }, [column.resizable, column.width]);

  useEffect(() => {
    if (!isResizing) return;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - resizeStartX.current;
      const newWidth = Math.max(80, startWidth.current + deltaX);
      onWidthChange(column.key, newWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, onWidthChange, column.key]);

  return (
    <div
      className={cn(
        'flex items-center justify-between px-2 py-3 border-b border-border bg-muted/30',
        'text-sm font-medium text-muted-foreground',
        column.sortable && 'cursor-pointer hover:bg-muted/50',
        isActive && 'text-foreground'
      )}
      style={{ 
        width: column.width === 'auto' ? undefined : column.width,
        minWidth: typeof column.width === 'number' ? column.width : undefined
      }}
      onClick={() => column.sortable && onSort(column.key)}
    >
      <div className="flex items-center gap-2">
        <span className="truncate">{column.label}</span>
        {column.sortable && isActive && (
          sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />
        )}
      </div>
      
      {column.resizable && (
        <div
          className="w-1 h-6 cursor-col-resize hover:bg-primary/50"
          onMouseDown={handleMouseDown}
        />
      )}
    </div>
  );
});

ColumnHeader.displayName = 'ColumnHeader';

export const EnhancedListView = memo<EnhancedListViewProps>(({
  games,
  settings,
  onSettingsChange,
  interactions,
  className
}) => {
  const { containerRef, size } = useContainerSize();
  const { selectedGames, toggleGameSelection } = useEnhancedLibrary();
  const {
    sortBy,
    sortDirection,
    searchQuery,
    setSearchQuery,
    handleSort,
    filteredAndSortedGames
  } = useListState(games, settings);

  // Get visible columns sorted by order
  const visibleColumns = useMemo(() => 
    settings.columns
      .filter(col => col.visible)
      .sort((a, b) => a.order - b.order),
    [settings.columns]
  );

  const config = LIST_CONFIGS[settings.compactMode ? 'compact' : 'normal'];

  // Memoized data for list items
  const itemData = useMemo(() => ({
    games: filteredAndSortedGames,
    settings,
    interactions,
    visibleColumns,
    sortedBy: sortBy,
    sortDirection,
    selectedGames,
    onGameSelect: toggleGameSelection,
    onSort: handleSort
  }), [
    filteredAndSortedGames, 
    settings, 
    interactions, 
    visibleColumns, 
    sortBy, 
    sortDirection, 
    selectedGames, 
    toggleGameSelection, 
    handleSort
  ]);

  const handleColumnWidthChange = useCallback((columnKey: string, width: number) => {
    const updatedColumns = settings.columns.map(col =>
      col.key === columnKey ? { ...col, width } : col
    );
    onSettingsChange({ columns: updatedColumns });
  }, [settings.columns, onSettingsChange]);

  // Column visibility toggle (unused for now but ready for future use)
  // const handleColumnVisibilityToggle = useCallback((columnKey: string) => {
  //   const updatedColumns = settings.columns.map(col =>
  //     col.key === columnKey ? { ...col, visible: !col.visible } : col
  //   );
  //   onSettingsChange({ columns: updatedColumns });
  // }, [settings.columns, onSettingsChange]);

  if (games.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-64', className)}>
        <div className="text-center">
          <p className="text-lg text-muted-foreground">No games found</p>
          <p className="text-sm text-muted-foreground mt-2">
            Try adjusting your filters or search criteria
          </p>
        </div>
      </div>
    );
  }

  if (size.width === 0 || size.height === 0) {
    return (
      <div ref={containerRef} className={cn('w-full h-full', className)}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-pulse space-y-2 w-full max-w-2xl">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-16 bg-muted rounded" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef} 
      className={cn('enhanced-list-view w-full h-full flex flex-col', className)}
    >
      {/* Search and Filter Bar */}
      <div className="flex items-center gap-4 p-4 border-b border-border">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={16} />
          <Input
            placeholder="Search games..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline" size="sm">
          <Filter size={16} className="mr-2" />
          Filters
        </Button>
        <Button variant="outline" size="sm">
          <Settings size={16} className="mr-2" />
          Columns
        </Button>
      </div>

      {/* Column Headers */}
      <div className="flex bg-muted/30 sticky top-0 z-10">
        {/* Selection header */}
        <div className="flex items-center px-3 py-3 border-b border-border">
          <input
            type="checkbox"
            onChange={(e) => {
              if (e.target.checked) {
                // Select all visible games
              } else {
                // Clear selection
              }
            }}
            className="rounded border-border"
          />
        </div>

        {/* Thumbnail header */}
        {settings.showThumbnails && (
          <div className="w-16 border-b border-border"></div>
        )}

        {/* Column headers */}
        {visibleColumns.map((column) => (
          <ColumnHeader
            key={column.key}
            column={column}
            isActive={sortBy === column.key}
            sortDirection={sortDirection}
            onSort={handleSort}
            onWidthChange={handleColumnWidthChange}
          />
        ))}
      </div>

      {/* Virtual List */}
      <div className="flex-1">
        <List
          height={size.height - 120} // Account for header and search bar
          itemCount={filteredAndSortedGames.length}
          itemSize={config.rowHeight}
          itemData={itemData}
          overscanCount={5}
          className="scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent"
        >
          {ListItem}
        </List>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-2 border-t border-border bg-muted/30 text-sm text-muted-foreground">
        <span>
          {filteredAndSortedGames.length} of {games.length} games
          {selectedGames.size > 0 && ` • ${selectedGames.size} selected`}
        </span>
        <span>
          {settings.compactMode ? 'Compact' : 'Normal'} view
        </span>
      </div>
    </div>
  );
});

EnhancedListView.displayName = 'EnhancedListView';

// List customization controls component
interface ListCustomizationControlsProps {
  settings: ListViewSettings;
  onSettingsChange: (settings: Partial<ListViewSettings>) => void;
  className?: string;
}

export const ListCustomizationControls = memo<ListCustomizationControlsProps>(({
  settings,
  onSettingsChange,
  className
}) => {
  return (
    <Card className={cn('p-4', className)}>
      <CardContent className="flex flex-wrap gap-4 p-0">
        {/* View Mode Toggle */}
        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium">View Mode</label>
          <div className="flex gap-2">
            <Button
              variant={settings.compactMode ? "default" : "outline"}
              size="sm"
              onClick={() => onSettingsChange({ compactMode: !settings.compactMode })}
            >
              Compact
            </Button>
          </div>
        </div>

        {/* Thumbnail Toggle */}
        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium">Options</label>
          <div className="flex gap-2">
            <Button
              variant={settings.showThumbnails ? "default" : "outline"}
              size="sm"
              onClick={() => onSettingsChange({ showThumbnails: !settings.showThumbnails })}
            >
              <Eye size={16} className="mr-2" />
              Thumbnails
            </Button>
          </div>
        </div>

        {/* Group By */}
        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium">Group By</label>
          <div className="flex gap-2">
            {(['none', 'status', 'platform', 'genre'] as const).map((grouping) => (
              <Button
                key={grouping}
                variant={settings.groupBy === grouping ? "default" : "outline"}
                size="sm"
                onClick={() => onSettingsChange({ groupBy: grouping })}
              >
                {grouping === 'none' ? 'None' : grouping.charAt(0).toUpperCase() + grouping.slice(1)}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

ListCustomizationControls.displayName = 'ListCustomizationControls';