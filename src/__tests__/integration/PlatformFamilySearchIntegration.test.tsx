import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { PlatformFamilyView } from '@/pages/Library/components/ViewModes/PlatformFamilyView';
import { UserGameWithDetails } from '@/types/database';
import { GameFilters } from '@/types/filters';

// Mock the PlatformFamilyGroup component
vi.mock('@/components/ui/utils/PlatformFamilyGroup', () => ({
  PlatformFamilyGroup: ({ family, games, platforms, stats }: any) => (
    <div data-testid={`platform-family-group-${family}`}>
      <div data-testid="family-name">{family}</div>
      <div data-testid="game-count">{games.length}</div>
      <div data-testid="platform-count">{platforms.length}</div>
      <div data-testid="stats-total">{stats.total}</div>
      {games.map((game: any) => (
        <div key={game.id} data-testid={`game-${game.id}`}>
          {game.game?.title}
        </div>
      ))}
    </div>
  ),
}));

const createMockGame = (
  id: string,
  title: string,
  platform: string,
  genres: string[] = [],
  developer?: string
): UserGameWithDetails => ({
  id,
  user_id: 'user1',
  game_id: id,
  status: 'playing',
  personal_rating: 8,
  hours_played: 10,
  added_at: '2024-01-01',
  game: {
    id,
    title,
    platform,
    genres,
    developer,
    publisher: 'Test Publisher',
    release_date: '2024-01-01',
    description: 'Test description',
    cover_image: 'test-cover.jpg',
    igdb_id: id,
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  }
});

describe('Platform Family Search Integration', () => {
  const mockGames = [
    createMockGame('1', 'Cyberpunk 2077', 'PC', ['RPG', 'Action'], 'CD Projekt RED'),
    createMockGame('2', 'Halo Infinite', 'Xbox Series X', ['FPS', 'Action'], 'Microsoft'),
    createMockGame('3', 'The Last of Us Part II', 'PlayStation 5', ['Action', 'Adventure'], 'Naughty Dog'),
    createMockGame('4', 'Super Mario Odyssey', 'Nintendo Switch', ['Platformer', 'Adventure'], 'Nintendo'),
    createMockGame('5', 'Forza Horizon 5', 'Xbox Series X', ['Racing', 'Sports'], 'Microsoft'),
    createMockGame('6', 'God of War', 'PlayStation 5', ['Action', 'Adventure'], 'Sony'),
  ];

  const defaultProps = {
    games: mockGames,
    collapsedFamilies: new Set<import('@/lib/utils/platformFamilyUtils').PlatformFamily>(),
    onFamilyToggle: vi.fn(),
    onGameClick: vi.fn(),
    onStatusUpdate: vi.fn(),
    onRemoveGame: vi.fn(),
  };

  it('filters games by search query across all families', () => {
    render(<PlatformFamilyView {...defaultProps} searchQuery="Halo Infinite" />);

    // Should only show Xbox family with Halo game
    expect(screen.getByTestId('platform-family-group-Xbox')).toBeInTheDocument();
    expect(screen.getByTestId('game-2')).toHaveTextContent('Halo Infinite');

    // Should not show other families
    expect(screen.queryByTestId('platform-family-group-PC')).not.toBeInTheDocument();
    expect(screen.queryByTestId('platform-family-group-PlayStation')).not.toBeInTheDocument();
    expect(screen.queryByTestId('platform-family-group-Nintendo')).not.toBeInTheDocument();
  });

  it('filters games by genre across families', () => {
    const filters: GameFilters = {
      search: { enabled: false, query: '', searchType: 'smart' },
      platforms: { enabled: false, platforms: [] },
      genres: { enabled: true, genres: ['Action'], mode: 'include' },
      year: { enabled: false },
      rating: { enabled: false },
      developer: { enabled: false, developers: [] },
      publisher: { enabled: false, publishers: [] },
      customTags: { enabled: false, tags: [], mode: 'include', includeUntagged: false },
      status: { enabled: false, statuses: [], mode: 'include' },
      collection: { enabled: false, inLibrary: false, inWishlist: false, notInCollection: false },
      sort: { field: 'title', direction: 'asc' }
    };

    render(<PlatformFamilyView {...defaultProps} filters={filters} />);

    // Should show PC, Xbox, and PlayStation families (all have Action games)
    expect(screen.getByTestId('platform-family-group-PC')).toBeInTheDocument();
    expect(screen.getByTestId('platform-family-group-Xbox')).toBeInTheDocument();
    expect(screen.getByTestId('platform-family-group-PlayStation')).toBeInTheDocument();

    // Should not show Nintendo family (no Action games)
    expect(screen.queryByTestId('platform-family-group-Nintendo')).not.toBeInTheDocument();

    // Verify specific games are shown
    expect(screen.getByTestId('game-1')).toHaveTextContent('Cyberpunk 2077');
    expect(screen.getByTestId('game-2')).toHaveTextContent('Halo Infinite');
    expect(screen.getByTestId('game-3')).toHaveTextContent('The Last of Us Part II');
    expect(screen.getByTestId('game-6')).toHaveTextContent('God of War');
  });

  it('combines search and genre filters', () => {
    const filters: GameFilters = {
      search: { enabled: true, query: 'Action', searchType: 'smart' },
      platforms: { enabled: false, platforms: [] },
      genres: { enabled: true, genres: ['Adventure'], mode: 'include' },
      year: { enabled: false },
      rating: { enabled: false },
      developer: { enabled: false, developers: [] },
      publisher: { enabled: false, publishers: [] },
      customTags: { enabled: false, tags: [], mode: 'include', includeUntagged: false },
      status: { enabled: false, statuses: [], mode: 'include' },
      collection: { enabled: false, inLibrary: false, inWishlist: false, notInCollection: false },
      sort: { field: 'title', direction: 'asc' }
    };

    render(<PlatformFamilyView {...defaultProps} filters={filters} />);

    // Should show PlayStation and Nintendo families (games with both Action in title/genre and Adventure genre)
    expect(screen.getByTestId('platform-family-group-PlayStation')).toBeInTheDocument();
    expect(screen.getByTestId('platform-family-group-Nintendo')).toBeInTheDocument();

    // Should not show PC or Xbox families
    expect(screen.queryByTestId('platform-family-group-PC')).not.toBeInTheDocument();
    expect(screen.queryByTestId('platform-family-group-Xbox')).not.toBeInTheDocument();
  });

  it('sorts games within families while maintaining family grouping', () => {
    render(
      <PlatformFamilyView 
        {...defaultProps} 
        sortOptions={{ field: 'title', direction: 'desc' }}
      />
    );

    // All families should be present
    expect(screen.getByTestId('platform-family-group-PC')).toBeInTheDocument();
    expect(screen.getByTestId('platform-family-group-Xbox')).toBeInTheDocument();
    expect(screen.getByTestId('platform-family-group-PlayStation')).toBeInTheDocument();
    expect(screen.getByTestId('platform-family-group-Nintendo')).toBeInTheDocument();

    // Xbox family should have 2 games
    const xboxGroup = screen.getByTestId('platform-family-group-Xbox');
    expect(xboxGroup.querySelector('[data-testid="game-count"]')).toHaveTextContent('2');

    // PlayStation family should have 2 games
    const playstationGroup = screen.getByTestId('platform-family-group-PlayStation');
    expect(playstationGroup.querySelector('[data-testid="game-count"]')).toHaveTextContent('2');
  });

  it('shows empty state when no games match filters', () => {
    const filters: GameFilters = {
      search: { enabled: true, query: 'NonexistentGame', searchType: 'smart' },
      platforms: { enabled: false, platforms: [] },
      genres: { enabled: false, genres: [], mode: 'include' },
      year: { enabled: false },
      rating: { enabled: false },
      developer: { enabled: false, developers: [] },
      publisher: { enabled: false, publishers: [] },
      customTags: { enabled: false, tags: [], mode: 'include', includeUntagged: false },
      status: { enabled: false, statuses: [], mode: 'include' },
      collection: { enabled: false, inLibrary: false, inWishlist: false, notInCollection: false },
      sort: { field: 'title', direction: 'asc' }
    };

    render(<PlatformFamilyView {...defaultProps} filters={filters} />);

    expect(screen.getByText('No games match your current filters')).toBeInTheDocument();
    expect(screen.getByText('Try adjusting your search terms or filters to see more results.')).toBeInTheDocument();
  });

  it('filters by developer across families', () => {
    const filters: GameFilters = {
      search: { enabled: false, query: '', searchType: 'smart' },
      platforms: { enabled: false, platforms: [] },
      genres: { enabled: false, genres: [], mode: 'include' },
      year: { enabled: false },
      rating: { enabled: false },
      developer: { enabled: true, developers: ['Microsoft'] },
      publisher: { enabled: false, publishers: [] },
      customTags: { enabled: false, tags: [], mode: 'include', includeUntagged: false },
      status: { enabled: false, statuses: [], mode: 'include' },
      collection: { enabled: false, inLibrary: false, inWishlist: false, notInCollection: false },
      sort: { field: 'title', direction: 'asc' }
    };

    render(<PlatformFamilyView {...defaultProps} filters={filters} />);

    // Should only show Xbox family (Microsoft games)
    expect(screen.getByTestId('platform-family-group-Xbox')).toBeInTheDocument();
    expect(screen.getByTestId('game-2')).toHaveTextContent('Halo Infinite');
    expect(screen.getByTestId('game-5')).toHaveTextContent('Forza Horizon 5');

    // Should not show other families
    expect(screen.queryByTestId('platform-family-group-PC')).not.toBeInTheDocument();
    expect(screen.queryByTestId('platform-family-group-PlayStation')).not.toBeInTheDocument();
    expect(screen.queryByTestId('platform-family-group-Nintendo')).not.toBeInTheDocument();
  });
});