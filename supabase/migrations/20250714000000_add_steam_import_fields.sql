/*
  Add Steam-specific fields to support Steam library import functionality
  
  This migration adds optional fields to store Steam-specific data:
  - steam_app_id: Steam's unique application identifier
  - steam_url: Direct link to Steam store page
  - import_source: Track where the game was imported from
  - last_played: When the game was last played (for Steam data)
*/

-- Add Steam-specific fields to games table
ALTER TABLE games ADD COLUMN IF NOT EXISTS steam_app_id text;
ALTER TABLE games ADD COLUMN IF NOT EXISTS steam_url text;

-- Add import tracking fields to user_games table  
ALTER TABLE user_games ADD COLUMN IF NOT EXISTS import_source text;
ALTER TABLE user_games ADD COLUMN IF NOT EXISTS last_played timestamptz;

-- Create indexes for new fields to improve query performance
CREATE INDEX IF NOT EXISTS idx_games_steam_app_id ON games(steam_app_id);
CREATE INDEX IF NOT EXISTS idx_user_games_import_source ON user_games(import_source);
CREATE INDEX IF NOT EXISTS idx_user_games_last_played ON user_games(last_played);

-- Add constraint to ensure steam_app_id is unique when not null
ALTER TABLE games ADD CONSTRAINT unique_steam_app_id UNIQUE (steam_app_id);

-- Update comments for documentation
COMMENT ON COLUMN games.steam_app_id IS 'Steam application ID for games from Steam platform';
COMMENT ON COLUMN games.steam_url IS 'Direct URL to Steam store page';
COMMENT ON COLUMN user_games.import_source IS 'Source of import: steam, epic, manual, etc.';
COMMENT ON COLUMN user_games.last_played IS 'Timestamp when the game was last played (from import source)';