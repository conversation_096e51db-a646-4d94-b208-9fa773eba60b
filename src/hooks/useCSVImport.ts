import { useState, useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { csvImportService, CSVGameData, ImportResult, ExportOptions } from '../lib/csvImportService';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';

/**
 * Hook for CSV import/export functionality
 */
export const useCSVImport = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    gameCount: number;
  } | null>(null);
  const [parsedData, setParsedData] = useState<CSVGameData[]>([]);
  const [importProgress, setImportProgress] = useState(0);

  // Import CSV data mutation
  const importMutation = useMutation({
    mutationFn: async ({ 
      data, 
      options 
    }: { 
      data: CSVGameData[]; 
      options: { skipDuplicates?: boolean; updateExisting?: boolean } 
    }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // Simulate progress updates
      setImportProgress(10);
      
      const result = await csvImportService.importCSVData(user.id, data, options);
      
      setImportProgress(100);
      return result;
    },
    onSuccess: (result: ImportResult) => {
      if (result.success) {
        toast.success(
          `Import completed! ${result.imported} games imported, ${result.skipped} skipped.`
        );
        
        if (result.errors.length > 0) {
          toast.error(`${result.errors.length} errors occurred during import.`);
          console.warn('Import errors:', result.errors);
        }
        
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ['user-collection', user?.id] });
        queryClient.invalidateQueries({ queryKey: ['user-stats', user?.id] });
        queryClient.invalidateQueries({ queryKey: ['import-stats', user?.id] });
      } else {
        toast.error(`Import failed: ${result.errors.join(', ')}`);
      }

      // Reset progress after delay
      setTimeout(() => {
        setImportProgress(0);
      }, 2000);
    },
    onError: (error) => {
      console.error('CSV import error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Import failed';
      toast.error(errorMessage);
      setImportProgress(0);
    }
  });

  // Export CSV data mutation
  const exportMutation = useMutation({
    mutationFn: async (options: ExportOptions) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      return csvImportService.exportUserCollection(user.id, options);
    },
    onSuccess: (csvData: string) => {
      // Create download
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `codexa-collection-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Collection exported successfully!');
    },
    onError: (error) => {
      console.error('CSV export error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Export failed';
      toast.error(errorMessage);
    }
  });

  // Validate CSV content
  const validateCSV = useCallback((csvContent: string) => {
    try {
      const validation = csvImportService.validateCSV(csvContent);
      setValidationResult(validation);
      
      if (validation.isValid) {
        // Parse CSV data if valid
        const data = csvImportService.parseCSV(csvContent);
        setParsedData(data);
        
        toast.success(`CSV validated successfully! Found ${validation.gameCount} games.`);
        
        if (validation.warnings.length > 0) {
          toast.error(`${validation.warnings.length} warnings found.`);
        }
      } else {
        toast.error(`CSV validation failed: ${validation.errors.join(', ')}`);
        setParsedData([]);
      }
      
      return validation;
    } catch (error) {
      console.error('CSV validation error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Validation failed';
      const failedValidation = {
        isValid: false,
        errors: [errorMessage],
        warnings: [],
        gameCount: 0
      };
      setValidationResult(failedValidation);
      setParsedData([]);
      toast.error(errorMessage);
      return failedValidation;
    }
  }, []);

  // Parse CSV file
  const parseCSVFile = useCallback(async (file: File) => {
    if (!file.name.endsWith('.csv')) {
      toast.error('Please select a CSV file');
      return null;
    }

    try {
      const csvContent = await file.text();
      return validateCSV(csvContent);
    } catch (error) {
      console.error('File reading error:', error);
      toast.error('Failed to read CSV file');
      return null;
    }
  }, [validateCSV]);

  // Start import process
  const startImport = useCallback((
    data?: CSVGameData[], 
    options: { skipDuplicates?: boolean; updateExisting?: boolean } = {}
  ) => {
    const importData = data || parsedData;
    
    if (!importData.length) {
      toast.error('No data to import');
      return;
    }

    if (!user?.id) {
      toast.error('Please log in to import your data');
      return;
    }

    importMutation.mutate({ data: importData, options });
  }, [parsedData, user?.id, importMutation]);

  // Start export process
  const startExport = useCallback((options: ExportOptions = {}) => {
    if (!user?.id) {
      toast.error('Please log in to export your data');
      return;
    }

    exportMutation.mutate(options);
  }, [user?.id, exportMutation]);

  // Download template
  const downloadTemplate = useCallback(() => {
    const template = csvImportService.generateTemplate();
    const blob = new Blob([template], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'codexa-import-template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    toast.success('Template downloaded successfully!');
  }, []);

  // Clear parsed data
  const clearData = useCallback(() => {
    setParsedData([]);
    setValidationResult(null);
    setImportProgress(0);
  }, []);

  // Get import statistics
  const getImportStats = useCallback(() => {
    if (!validationResult || !parsedData.length) {
      return null;
    }

    const stats = {
      totalGames: parsedData.length,
      validGames: parsedData.filter(game => game.name.trim()).length,
      gamesWithRatings: parsedData.filter(game => game.rating !== undefined).length,
      gamesWithPlatforms: parsedData.filter(game => game.platforms && game.platforms.length > 0).length,
      gamesWithPlaytime: parsedData.filter(game => game.playtime_hours && game.playtime_hours > 0).length,
      avgRating: parsedData
        .filter(game => game.rating !== undefined)
        .reduce((sum, game, _, arr) => sum + (game.rating || 0) / arr.length, 0),
      totalPlaytime: parsedData
        .filter(game => game.playtime_hours)
        .reduce((sum, game) => sum + (game.playtime_hours || 0), 0)
    };

    return stats;
  }, [validationResult, parsedData]);

  return {
    // State
    validationResult,
    parsedData,
    importProgress,
    
    // Loading states
    isImporting: importMutation.isPending,
    isExporting: exportMutation.isPending,
    
    // Errors
    importError: importMutation.error,
    exportError: exportMutation.error,
    
    // Actions
    validateCSV,
    parseCSVFile,
    startImport,
    startExport,
    downloadTemplate,
    clearData,
    
    // Utilities
    getImportStats,
    
    // Helper functions
    isValidData: validationResult?.isValid && parsedData.length > 0,
    hasWarnings: validationResult?.warnings && validationResult.warnings.length > 0,
    hasErrors: validationResult?.errors && validationResult.errors.length > 0
  };
};

/**
 * Hook for managing data export options
 */
export const useDataExport = () => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    includeStats: true,
    includeNotes: true,
    includeMetadata: true,
    format: 'csv'
  });

  const updateExportOptions = useCallback((updates: Partial<ExportOptions>) => {
    setExportOptions(prev => ({ ...prev, ...updates }));
  }, []);

  const resetExportOptions = useCallback(() => {
    setExportOptions({
      includeStats: true,
      includeNotes: true,
      includeMetadata: true,
      format: 'csv'
    });
  }, []);

  const getExportPreview = useCallback(() => {
    const fields = ['name', 'platform', 'status', 'playtime_hours', 'user_rating', 'date_added'];
    
    if (exportOptions.includeStats) {
      fields.push('last_played', 'completion_percentage', 'purchase_price');
    }
    
    if (exportOptions.includeMetadata) {
      fields.push('developer', 'publisher', 'release_date', 'genres', 'metacritic_score');
    }
    
    if (exportOptions.includeNotes) {
      fields.push('notes');
    }
    
    return fields;
  }, [exportOptions]);

  return {
    exportOptions,
    updateExportOptions,
    resetExportOptions,
    getExportPreview
  };
};

export type { CSVGameData, ImportResult, ExportOptions };