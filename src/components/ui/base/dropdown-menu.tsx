import React, { useState, useRef, useEffect, useLayoutEffect, createContext, useContext, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { cn } from '@/lib/utils';

const DropdownContext = createContext<{
  closeDropdown: () => void;
} | null>(null);

interface DropdownMenuProps {
  children: React.ReactNode;
  trigger: React.ReactNode;
  align?: 'start' | 'end';
  className?: string;
  onOpenChange?: (open: boolean) => void;
  sideOffset?: number;
  open?: boolean;
}

// Utility function to find all scrollable ancestors
function getScrollableAncestors(element: Element): Element[] {
  const scrollableAncestors: Element[] = [];
  let parent = element.parentElement;

  while (parent && parent !== document.body) {
    const style = window.getComputedStyle(parent);
    const isScrollable = ['auto', 'scroll'].includes(style.overflow) ||
                        ['auto', 'scroll'].includes(style.overflowY) ||
                        ['auto', 'scroll'].includes(style.overflowX);
    
    if (isScrollable) {
      scrollableAncestors.push(parent);
    }
    parent = parent.parentElement;
  }
  
  scrollableAncestors.push(window as any);
  return scrollableAncestors;
}

// Debounce utility for performance
function debounce<T extends (...args: any[]) => void>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }) as T;
}

// Extract width from className or measure element
function getDropdownWidth(element: HTMLElement | null, className?: string): number {
  if (!element) return 224; // Fallback

  // Try to extract width from className
  if (className) {
    const widthMatch = className.match(/w-\[([^\]]+)\]|w-(\w+)/);
    if (widthMatch) {
      const widthValue = widthMatch[1] || widthMatch[2];
      if (widthValue.includes('rem')) {
        return parseFloat(widthValue) * 16; // Convert rem to px
      }
      if (widthValue.includes('px')) {
        return parseFloat(widthValue);
      }
      // Handle Tailwind classes like w-56, w-64
      const tailwindWidths: Record<string, number> = {
        '56': 224, '64': 256, '72': 288, '80': 320, '96': 384
      };
      if (tailwindWidths[widthValue]) {
        return tailwindWidths[widthValue];
      }
    }
  }

  // Measure actual width
  const rect = element.getBoundingClientRect();
  return rect.width || 224;
}

export function DropdownMenu({ 
  children, 
  trigger, 
  align = 'end', 
  className, 
  onOpenChange,
  sideOffset = 4,
  open: controlledOpen
}: DropdownMenuProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const isOpen = controlledOpen !== undefined ? controlledOpen : internalOpen;
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const [position, setPosition] = useState({ top: 0, left: 0, placement: 'bottom' });
  const [isPositioned, setIsPositioned] = useState(false);
  const scrollableAncestorsRef = useRef<Element[]>([]);

  const closeDropdown = useCallback(() => {
    if (controlledOpen === undefined) {
      setInternalOpen(false);
    }
    onOpenChange?.(false);
  }, [onOpenChange, controlledOpen]);

  const updatePosition = useCallback(() => {
    if (!isOpen || !triggerRef.current || !dropdownRef.current) {
      return;
    }

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const dropdownWidth = getDropdownWidth(dropdownRef.current, className);
    const dropdownHeight = dropdownRef.current.getBoundingClientRect().height || 300; // Estimate if not rendered
    
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // Calculate initial position based on alignment
    let left = align === 'end' 
      ? triggerRect.right - dropdownWidth
      : triggerRect.left;
    
    let top = triggerRect.bottom + sideOffset;
    let placement = 'bottom';

    // Collision detection and auto-flip
    
    // Horizontal collision detection
    if (left + dropdownWidth > viewportWidth - 8) {
      left = viewportWidth - dropdownWidth - 8;
    }
    if (left < 8) {
      left = 8;
    }
    
    // Vertical collision detection and flip
    if (top + dropdownHeight > viewportHeight - 8) {
      // Try flipping to top
      const topPosition = triggerRect.top - dropdownHeight - sideOffset;
      if (topPosition >= 8) {
        top = topPosition;
        placement = 'top';
      } else {
        // If both top and bottom don't fit, position for maximum visibility
        top = Math.max(8, viewportHeight - dropdownHeight - 8);
        placement = 'bottom';
      }
    }

    setPosition({ top, left, placement });
    setIsPositioned(true);
  }, [isOpen, align, sideOffset, className]);

  // Debounced version for scroll events
  const debouncedUpdatePosition = useCallback(
    debounce(updatePosition, 10),
    [updatePosition]
  );

  // Initial positioning with proper timing
  useLayoutEffect(() => {
    if (isOpen) {
      setIsPositioned(false);
      // Use requestAnimationFrame to ensure DOM is ready
      const frame1 = requestAnimationFrame(() => {
        const frame2 = requestAnimationFrame(() => {
          updatePosition();
        });
        return () => cancelAnimationFrame(frame2);
      });
      return () => cancelAnimationFrame(frame1);
    }
  }, [isOpen, updatePosition]);

  // Setup scroll listeners for all scrollable ancestors
  useEffect(() => {
    if (isOpen && triggerRef.current) {
      // Find all scrollable ancestors
      scrollableAncestorsRef.current = getScrollableAncestors(triggerRef.current);
      
      // Add scroll listeners
      scrollableAncestorsRef.current.forEach(ancestor => {
        ancestor.addEventListener('scroll', debouncedUpdatePosition, { passive: true });
      });
      
      // Add resize listener
      window.addEventListener('resize', debouncedUpdatePosition, { passive: true });
      
      return () => {
        scrollableAncestorsRef.current.forEach(ancestor => {
          ancestor.removeEventListener('scroll', debouncedUpdatePosition);
        });
        window.removeEventListener('resize', debouncedUpdatePosition);
      };
    }
  }, [isOpen, debouncedUpdatePosition]);

  // Click outside and escape key handling
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      const target = event.target as Node;
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(target) &&
        triggerRef.current &&
        !triggerRef.current.contains(target)
      ) {
        closeDropdown();
      }
    }

    function handleEscapeKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        closeDropdown();
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [isOpen, closeDropdown]);

  const handleTriggerClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const newIsOpen = !isOpen;
    if (controlledOpen === undefined) {
      setInternalOpen(newIsOpen);
    }
    onOpenChange?.(newIsOpen);
  }, [isOpen, onOpenChange, controlledOpen]);

  return (
    <>
      <div className="relative inline-block">
        {React.cloneElement(trigger as React.ReactElement, {
          ref: triggerRef,
          onClick: handleTriggerClick,
          'aria-expanded': isOpen,
          'aria-haspopup': 'menu'
        })}
      </div>
      {isOpen && createPortal(
        <DropdownContext.Provider value={{ closeDropdown }}>
          <div
            ref={dropdownRef}
            className={cn(
              'fixed z-[99999] rounded-md border bg-popover p-1 text-popover-foreground shadow-lg',
              'bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700',
              // Animation based on placement
              position.placement === 'top' 
                ? 'animate-in fade-in-0 zoom-in-95 slide-in-from-bottom-2'
                : 'animate-in fade-in-0 zoom-in-95 slide-in-from-top-2',
              // Conditional visibility for smooth positioning
              isPositioned ? 'opacity-100' : 'opacity-0',
              !className?.includes('w-') && 'w-56', // Only apply default width if no width class provided
              className
            )}
            style={{
              top: position.top,
              left: position.left,
              transition: isPositioned ? 'none' : 'opacity 0.1s ease-out',
            }}
            role="menu"
            aria-orientation="vertical"
          >
            {children}
          </div>
        </DropdownContext.Provider>,
        document.body
      )}
    </>
  );
}

interface DropdownMenuItemProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
}

export function DropdownMenuItem({ children, onClick, className, disabled }: DropdownMenuItemProps) {
  const context = useContext(DropdownContext);
  
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!disabled && onClick) {
      onClick();
      // Close dropdown after action executes
      setTimeout(() => {
        context?.closeDropdown();
      }, 0);
    }
  }, [disabled, onClick, context]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      if (!disabled && onClick) {
        onClick();
        setTimeout(() => {
          context?.closeDropdown();
        }, 0);
      }
    }
  }, [disabled, onClick, context]);

  return (
    <div
      className={cn(
        'relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors',
        'hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-slate-100',
        'focus:bg-slate-100 dark:focus:bg-slate-700 focus:text-slate-900 dark:focus:text-slate-100',
        disabled && 'pointer-events-none opacity-50',
        className
      )}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      role="menuitem"
      tabIndex={disabled ? -1 : 0}
      aria-disabled={disabled}
    >
      {children}
    </div>
  );
}

export function DropdownMenuSeparator() {
  return <div className="my-1 h-px bg-muted" />;
}