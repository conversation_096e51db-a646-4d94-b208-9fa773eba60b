/**
 * Steam Platform Adapter
 * Adapts the existing steamImportService to work with the multi-platform import system
 */

import { BasePlatformService, type ImportProgress, type ImportResult, type PlatformGameData, type AuthenticationResult } from './basePlatformService';
import { steamImportService } from '@/lib/steamImportService';
import { type Platform, type KeyType } from '@/lib/apiKeyStore';
import { type Game } from '@/types';
import { supabase } from '@/lib/supabase';

export class SteamPlatformAdapter extends BasePlatformService {
  constructor() {
    super('steam' as Platform);
  }

  protected getRequiredKeys(): KeyType[] {
    return ['api_key']; // Steam requires API key
  }

  async authenticate(): Promise<AuthenticationResult> {
    try {
      // Check if Steam API key is configured
      const isConfigured = await this.isConfigured();
      if (!isConfigured) {
        return {
          success: false,
          error: 'Steam API key not configured'
        };
      }

      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async fetchUserLibrary(progressCallback?: (progress: ImportProgress) => void): Promise<PlatformGameData[]> {
    // This method is not directly used by steamImportService
    // Return empty array as steamImportService handles its own library fetching
    return [];
  }

  protected convertToGame(platformGame: PlatformGameData): Partial<Game> {
    // This method is not directly used by steamImportService
    // steamImportService handles its own game conversion
    return {
      name: platformGame.name,
      description: platformGame.description,
      release_date: platformGame.releaseDate,
      developer: platformGame.developer,
      publisher: platformGame.publisher,
      genres: platformGame.genres || [],
      cover_image: platformGame.imageUrl
    };
  }

  /**
   * Import library using the existing steamImportService
   */
  async importLibrary(progressCallback?: (progress: ImportProgress) => void): Promise<ImportResult> {
    if (!this.userId) {
      throw new Error('User not authenticated');
    }

    // Get Steam ID from user preferences
    const { data: preferences } = await supabase
      .from('user_preferences')
      .select('steam_id')
      .eq('user_id', this.userId)
      .single();

    if (!preferences?.steam_id) {
      throw new Error('Steam ID not configured. Please add your Steam ID in Settings → API Keys.');
    }

    const importId = await this.createImportRecord('running');

    try {
      await this.updateImportRecord(importId, { status: 'running' });

      // Use the existing steamImportService
      const result = await steamImportService.importSteamLibrary(
        this.userId,
        preferences.steam_id,
        (steamProgress) => {
          // Convert steamImportService progress to multi-platform progress format
          const multiPlatformProgress: ImportProgress = {
            phase: this.mapSteamPhase(steamProgress.phase),
            current: steamProgress.gamesProcessed || 0,
            total: steamProgress.totalGames || 0,
            message: steamProgress.message
          };
          
          progressCallback?.(multiPlatformProgress);
        }
      );

      await this.updateImportRecord(importId, {
        status: result.success ? 'completed' : 'failed',
        gamesImported: result.imported,
        gamesUpdated: 0, // steamImportService doesn't track updates separately
        duplicatesFound: 0, // steamImportService doesn't track duplicates separately
        errorMessage: result.errors.length > 0 ? result.errors.join(', ') : undefined
      });

      return {
        success: result.success,
        gamesImported: result.imported,
        gamesUpdated: 0,
        duplicatesFound: 0,
        errors: result.errors,
        importId,
        message: result.message
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Import failed';
      
      await this.updateImportRecord(importId, {
        status: 'failed',
        errorMessage
      });

      return {
        success: false,
        gamesImported: 0,
        gamesUpdated: 0,
        duplicatesFound: 0,
        errors: [errorMessage],
        importId,
        message: errorMessage
      };
    }
  }

  /**
   * Map steamImportService progress phases to multi-platform phases
   */
  private mapSteamPhase(steamPhase: string): ImportProgress['phase'] {
    switch (steamPhase) {
      case 'authenticating':
      case 'fetching_profile':
        return 'authenticating';
      case 'fetching_games':
        return 'fetching';
      case 'processing':
        return 'processing';
      case 'saving':
        return 'storing';
      case 'complete':
        return 'completed';
      default:
        return 'processing';
    }
  }
}

export const steamPlatformAdapter = new SteamPlatformAdapter();