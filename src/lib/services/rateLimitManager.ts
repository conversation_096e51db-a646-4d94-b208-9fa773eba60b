/**
 * Rate Limit Manager Service
 * Tracks and manages API rate limits across different services
 */

export type ArtworkService = 'igdb' | 'steamgriddb' | 'thegamesdb' | 'openai' | 'deepseek' | 'gemini';

export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number; // Time window in milliseconds
  burstLimit?: number; // Allow burst requests up to this limit
  cooldownMs?: number; // Cooldown period after hitting limit
}

export interface RateLimitStatus {
  service: ArtworkService;
  keyId: string;
  requestCount: number;
  windowStart: number;
  isLimited: boolean;
  nextResetTime: number;
  remainingRequests: number;
}

export interface ServiceUsage {
  service: ArtworkService;
  keyId: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  lastUsed: number;
  averageResponseTime: number;
}

class RateLimitManager {
  private limits = new Map<string, RateLimitStatus>();
  private usage = new Map<string, ServiceUsage>();
  
  // Default rate limit configurations for different services
  private readonly defaultConfigs: Record<ArtworkService, RateLimitConfig> = {
    igdb: { maxRequests: 4, windowMs: 1000 }, // 4 requests per second

    steamgriddb: { maxRequests: 100, windowMs: 60 * 1000 }, // 100 requests per minute
    thegamesdb: { maxRequests: 300, windowMs: 60 * 1000 }, // 300 requests per minute
    openai: { maxRequests: 60, windowMs: 60 * 1000 }, // 60 requests per minute
    deepseek: { maxRequests: 50, windowMs: 60 * 1000 }, // 50 requests per minute
    gemini: { maxRequests: 60, windowMs: 60 * 1000 } // 60 requests per minute
  };

  /**
   * Check if a request can be made for a specific service and API key
   */
  canMakeRequest(service: ArtworkService, keyId: string): boolean {
    const limitKey = this.getLimitKey(service, keyId);
    const config = this.defaultConfigs[service];
    const now = Date.now();
    
    const status = this.limits.get(limitKey);
    
    if (!status) {
      // First request for this service/key combination
      status = {
        service,
        keyId,
        requestCount: 0,
        windowStart: now,
        isLimited: false,
        nextResetTime: now + config.windowMs,
        remainingRequests: config.maxRequests
      };
      this.limits.set(limitKey, status);
    }

    // Check if we need to reset the window
    if (now >= status.nextResetTime) {
      status.requestCount = 0;
      status.windowStart = now;
      status.nextResetTime = now + config.windowMs;
      status.isLimited = false;
      status.remainingRequests = config.maxRequests;
    }

    // Check if we're within limits
    if (status.requestCount >= config.maxRequests) {
      status.isLimited = true;
      status.remainingRequests = 0;
      return false;
    }

    return true;
  }

  /**
   * Record a request being made
   */
  recordRequest(service: ArtworkService, keyId: string, success: boolean = true, responseTimeMs: number = 0): void {
    const limitKey = this.getLimitKey(service, keyId);
    const now = Date.now();
    
    // Update rate limit status
    const status = this.limits.get(limitKey);
    if (status) {
      status.requestCount++;
      status.remainingRequests = Math.max(0, this.defaultConfigs[service].maxRequests - status.requestCount);
      
      if (status.requestCount >= this.defaultConfigs[service].maxRequests) {
        status.isLimited = true;
      }
    }

    // Update usage statistics
    let usage = this.usage.get(limitKey);
    if (!usage) {
      usage = {
        service,
        keyId,
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        lastUsed: now,
        averageResponseTime: 0
      };
      this.usage.set(limitKey, usage);
    }

    usage.totalRequests++;
    usage.lastUsed = now;
    
    if (success) {
      usage.successfulRequests++;
    } else {
      usage.failedRequests++;
    }

    // Update average response time
    if (responseTimeMs > 0) {
      usage.averageResponseTime = (usage.averageResponseTime + responseTimeMs) / 2;
    }

    console.log(`📊 API Usage - ${service}:${keyId} - ${usage.totalRequests} total (${usage.successfulRequests} success)`);
  }

  /**
   * Get rate limit status for a service/key
   */
  getRateLimitStatus(service: ArtworkService, keyId: string): RateLimitStatus | null {
    const limitKey = this.getLimitKey(service, keyId);
    const status = this.limits.get(limitKey);
    
    if (!status) return null;
    
    // Update status if window has expired
    const now = Date.now();
    if (now >= status.nextResetTime) {
      const config = this.defaultConfigs[service];
      status.requestCount = 0;
      status.windowStart = now;
      status.nextResetTime = now + config.windowMs;
      status.isLimited = false;
      status.remainingRequests = config.maxRequests;
    }
    
    return { ...status };
  }

  /**
   * Get usage statistics for a service/key
   */
  getUsageStats(service: ArtworkService, keyId: string): ServiceUsage | null {
    const limitKey = this.getLimitKey(service, keyId);
    const usage = this.usage.get(limitKey);
    return usage ? { ...usage } : null;
  }

  /**
   * Get all usage statistics
   */
  getAllUsageStats(): ServiceUsage[] {
    return Array.from(this.usage.values());
  }

  /**
   * Get available (non-limited) keys for a service
   */
  getAvailableKeys(service: ArtworkService, allKeys: string[]): string[] {
    return allKeys.filter(keyId => this.canMakeRequest(service, keyId));
  }

  /**
   * Get the best key to use based on usage statistics
   */
  getBestKey(service: ArtworkService, availableKeys: string[]): string | null {
    if (availableKeys.length === 0) return null;
    if (availableKeys.length === 1) return availableKeys[0];

    // Filter out rate-limited keys
    const usableKeys = availableKeys.filter(keyId => this.canMakeRequest(service, keyId));
    
    if (usableKeys.length === 0) return null;
    if (usableKeys.length === 1) return usableKeys[0];

    // Score keys based on usage statistics
    const keyScores = usableKeys.map(keyId => {
      const usage = this.getUsageStats(service, keyId);
      const status = this.getRateLimitStatus(service, keyId);
      
      let score = 100; // Base score
      
      if (usage) {
        // Prefer keys with better success rates
        const successRate = usage.totalRequests > 0 ? 
          (usage.successfulRequests / usage.totalRequests) * 100 : 100;
        score += successRate * 0.3;
        
        // Prefer keys with faster response times (lower is better)
        if (usage.averageResponseTime > 0) {
          score -= Math.min(usage.averageResponseTime / 1000, 20); // Cap at 20 point deduction
        }
        
        // Prefer less recently used keys for better distribution
        const timeSinceUse = Date.now() - usage.lastUsed;
        score += Math.min(timeSinceUse / (60 * 1000), 10); // Up to 10 points for keys not used in last minute
      }
      
      if (status) {
        // Prefer keys with more remaining requests
        const remainingRatio = status.remainingRequests / this.defaultConfigs[service].maxRequests;
        score += remainingRatio * 20;
      }
      
      return { keyId, score };
    });

    // Sort by score and return the best key
    keyScores.sort((a, b) => b.score - a.score);
    return keyScores[0].keyId;
  }

  /**
   * Set custom rate limit configuration for a service
   */
  setRateLimitConfig(service: ArtworkService, config: RateLimitConfig): void {
    this.defaultConfigs[service] = config;
    console.log(`🔧 Updated rate limit config for ${service}:`, config);
  }

  /**
   * Reset rate limits for a service/key (useful for testing or manual override)
   */
  resetRateLimit(service: ArtworkService, keyId: string): void {
    const limitKey = this.getLimitKey(service, keyId);
    const config = this.defaultConfigs[service];
    const now = Date.now();
    
    this.limits.set(limitKey, {
      service,
      keyId,
      requestCount: 0,
      windowStart: now,
      isLimited: false,
      nextResetTime: now + config.windowMs,
      remainingRequests: config.maxRequests
    });
    
    console.log(`🔄 Reset rate limit for ${service}:${keyId}`);
  }

  /**
   * Clean up old entries to prevent memory leaks
   */
  cleanup(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    // Clean up old rate limit entries
    for (const [key, status] of this.limits.entries()) {
      if (now - status.windowStart > maxAge) {
        this.limits.delete(key);
      }
    }
    
    // Clean up old usage entries
    for (const [key, usage] of this.usage.entries()) {
      if (now - usage.lastUsed > maxAge) {
        this.usage.delete(key);
      }
    }
    
    console.log(`🧹 Cleaned up old rate limit entries`);
  }

  /**
   * Get summary of all services and their status
   */
  getServicesSummary(): Record<ArtworkService, { totalKeys: number; availableKeys: number; totalRequests: number }> {
    const summary = {} as Record<ArtworkService, { totalKeys: number; availableKeys: number; totalRequests: number }>;
    
    // Initialize summary for all services
    for (const service of Object.keys(this.defaultConfigs) as ArtworkService[]) {
      summary[service] = { totalKeys: 0, availableKeys: 0, totalRequests: 0 };
    }
    
    // Aggregate data from usage statistics
    for (const usage of this.usage.values()) {
      if (!summary[usage.service]) continue;
      
      summary[usage.service].totalKeys++;
      summary[usage.service].totalRequests += usage.totalRequests;
      
      if (this.canMakeRequest(usage.service, usage.keyId)) {
        summary[usage.service].availableKeys++;
      }
    }
    
    return summary;
  }

  private getLimitKey(service: ArtworkService, keyId: string): string {
    return `${service}:${keyId}`;
  }
}

// Export singleton instance
export const rateLimitManager = new RateLimitManager();

// Set up periodic cleanup
setInterval(() => {
  rateLimitManager.cleanup();
}, 60 * 60 * 1000); // Clean up every hour