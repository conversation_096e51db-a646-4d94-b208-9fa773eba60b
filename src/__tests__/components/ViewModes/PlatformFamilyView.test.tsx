import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { PlatformFamilyView } from '@/pages/Library/components/ViewModes/PlatformFamilyView';
import { UserGameWithDetails } from '@/types/database';
import { PlatformFamily } from '@/lib/utils/platformFamilyUtils';

// Mock the PlatformFamilyGroup component
vi.mock('@/components/ui/utils/PlatformFamilyGroup', () => ({
  PlatformFamilyGroup: ({ family, games, platforms, stats, isCollapsed, onToggleCollapse }: any) => (
    <div data-testid={`platform-family-group-${family}`}>
      <div data-testid="family-name">{family}</div>
      <div data-testid="game-count">{games.length}</div>
      <div data-testid="platform-count">{platforms.length}</div>
      <div data-testid="stats-total">{stats.total}</div>
      <div data-testid="collapsed-state">{isCollapsed ? 'collapsed' : 'expanded'}</div>
      <button onClick={onToggleCollapse} data-testid="toggle-button">Toggle</button>
    </div>
  ),
}));

const mockGame1: UserGameWithDetails = {
  id: '1',
  user_id: 'user1',
  game_id: 1,
  status: 'playing',
  personal_rating: 8,
  hours_played: 10,
  date_added: '2024-01-01',
  game: {
    id: 1,
    title: 'Test Game 1',
    platform: 'PC',
    platforms: ['PC'],
    genre: 'Action',
    developer: 'Test Dev',
    publisher: 'Test Pub',
    release_date: '2024-01-01',
    description: 'Test description',
    cover_url: 'test-cover.jpg',
    igdb_id: 1,
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  }
};

const mockGame2: UserGameWithDetails = {
  id: '2',
  user_id: 'user1',
  game_id: 2,
  status: 'completed',
  personal_rating: 9,
  hours_played: 25,
  date_added: '2024-01-02',
  game: {
    id: 2,
    title: 'Test Game 2',
    platform: 'Xbox One',
    platforms: ['Xbox One'],
    genre: 'RPG',
    developer: 'Test Dev 2',
    publisher: 'Test Pub 2',
    release_date: '2024-01-02',
    description: 'Test description 2',
    cover_url: 'test-cover-2.jpg',
    igdb_id: 2,
    created_at: '2024-01-02',
    updated_at: '2024-01-02'
  }
};

const mockGame3: UserGameWithDetails = {
  id: '3',
  user_id: 'user1',
  game_id: 3,
  status: 'backlog',
  personal_rating: null,
  hours_played: 0,
  date_added: '2024-01-03',
  game: {
    id: 3,
    title: 'Test Game 3',
    platform: 'PlayStation 5',
    platforms: ['PlayStation 5'],
    genre: 'Adventure',
    developer: 'Test Dev 3',
    publisher: 'Test Pub 3',
    release_date: '2024-01-03',
    description: 'Test description 3',
    cover_url: 'test-cover-3.jpg',
    igdb_id: 3,
    created_at: '2024-01-03',
    updated_at: '2024-01-03'
  }
};

describe('PlatformFamilyView', () => {
  const mockProps = {
    games: [mockGame1, mockGame2, mockGame3],
    collapsedFamilies: new Set<PlatformFamily>(),
    onFamilyToggle: vi.fn(),
    onGameClick: vi.fn(),
    onStatusUpdate: vi.fn(),
    onRemoveGame: vi.fn(),
  };

  it('renders platform family groups for games', () => {
    render(<PlatformFamilyView {...mockProps} />);
    
    // Should render PC, Xbox, and PlayStation family groups
    expect(screen.getByTestId('platform-family-group-PC')).toBeInTheDocument();
    expect(screen.getByTestId('platform-family-group-Xbox')).toBeInTheDocument();
    expect(screen.getByTestId('platform-family-group-PlayStation')).toBeInTheDocument();
  });

  it('groups games correctly by platform family', () => {
    render(<PlatformFamilyView {...mockProps} />);
    
    // Each family should have 1 game
    const pcGroup = screen.getByTestId('platform-family-group-PC');
    const xboxGroup = screen.getByTestId('platform-family-group-Xbox');
    const playstationGroup = screen.getByTestId('platform-family-group-PlayStation');
    
    expect(pcGroup.querySelector('[data-testid="game-count"]')).toHaveTextContent('1');
    expect(xboxGroup.querySelector('[data-testid="game-count"]')).toHaveTextContent('1');
    expect(playstationGroup.querySelector('[data-testid="game-count"]')).toHaveTextContent('1');
  });

  it('handles collapsed families correctly', () => {
    const collapsedFamilies = new Set<PlatformFamily>(['PC', 'Xbox']);
    render(<PlatformFamilyView {...mockProps} collapsedFamilies={collapsedFamilies} />);
    
    const pcGroup = screen.getByTestId('platform-family-group-PC');
    const xboxGroup = screen.getByTestId('platform-family-group-Xbox');
    const playstationGroup = screen.getByTestId('platform-family-group-PlayStation');
    
    expect(pcGroup.querySelector('[data-testid="collapsed-state"]')).toHaveTextContent('collapsed');
    expect(xboxGroup.querySelector('[data-testid="collapsed-state"]')).toHaveTextContent('collapsed');
    expect(playstationGroup.querySelector('[data-testid="collapsed-state"]')).toHaveTextContent('expanded');
  });

  it('calls onFamilyToggle when family is toggled', () => {
    const onFamilyToggle = vi.fn();
    render(<PlatformFamilyView {...mockProps} onFamilyToggle={onFamilyToggle} />);
    
    const pcToggleButton = screen.getByTestId('platform-family-group-PC').querySelector('[data-testid="toggle-button"]');
    pcToggleButton?.click();
    
    expect(onFamilyToggle).toHaveBeenCalledWith('PC');
  });

  it('filters out empty family groups', () => {
    // Test with games that only belong to PC family
    const pcOnlyGames = [mockGame1];
    render(<PlatformFamilyView {...mockProps} games={pcOnlyGames} />);
    
    // Should only render PC family group
    expect(screen.getByTestId('platform-family-group-PC')).toBeInTheDocument();
    expect(screen.queryByTestId('platform-family-group-Xbox')).not.toBeInTheDocument();
    expect(screen.queryByTestId('platform-family-group-PlayStation')).not.toBeInTheDocument();
  });

  it('shows empty state when no games are provided', () => {
    render(<PlatformFamilyView {...mockProps} games={[]} />);
    
    expect(screen.getByText('No games found')).toBeInTheDocument();
    expect(screen.getByText('Add some games to your library to see them organized by platform family.')).toBeInTheDocument();
  });

  it('integrates with existing game interaction handlers', () => {
    const onGameClick = vi.fn();
    const onStatusUpdate = vi.fn();
    const onRemoveGame = vi.fn();
    
    render(
      <PlatformFamilyView 
        {...mockProps} 
        onGameClick={onGameClick}
        onStatusUpdate={onStatusUpdate}
        onRemoveGame={onRemoveGame}
      />
    );
    
    // Verify that the handlers are passed to the PlatformFamilyGroup components
    // This is implicitly tested by the component rendering without errors
    // and the mocked component receiving the props
    expect(screen.getByTestId('platform-family-group-PC')).toBeInTheDocument();
    expect(screen.getByTestId('platform-family-group-Xbox')).toBeInTheDocument();
    expect(screen.getByTestId('platform-family-group-PlayStation')).toBeInTheDocument();
  });

  it('handles games with no platform information', () => {
    const gameWithNoPlatform: UserGameWithDetails = {
      ...mockGame1,
      id: '4',
      game: {
        ...mockGame1.game!,
        id: 4,
        platform: undefined as any,
        platforms: undefined as any,
      }
    };
    
    render(<PlatformFamilyView {...mockProps} games={[gameWithNoPlatform]} />);
    
    // Should render in 'Other' family group
    expect(screen.getByTestId('platform-family-group-Other')).toBeInTheDocument();
  });

  it('filters games based on search query', () => {
    render(<PlatformFamilyView {...mockProps} searchQuery="Test Game 1" />);
    
    // Should only show PC family with Test Game 1
    expect(screen.getByTestId('platform-family-group-PC')).toBeInTheDocument();
    
    // Should not show Xbox or PlayStation families since no games match
    expect(screen.queryByTestId('platform-family-group-Xbox')).not.toBeInTheDocument();
    expect(screen.queryByTestId('platform-family-group-PlayStation')).not.toBeInTheDocument();
  });

  it('shows appropriate empty state when search returns no results', () => {
    render(<PlatformFamilyView {...mockProps} searchQuery="NonexistentGame" />);
    
    expect(screen.getByText('No games match your current filters')).toBeInTheDocument();
    expect(screen.getByText('Try adjusting your search terms or filters to see more results.')).toBeInTheDocument();
  });

  it('applies sorting within platform families', () => {
    const gamesWithMultipleInFamily = [
      {
        ...mockGame1,
        id: '1',
        game: {
          ...mockGame1.game!,
          id: '1',
          title: 'Zebra Game',
        }
      },
      {
        ...mockGame1,
        id: '2',
        game: {
          ...mockGame1.game!,
          id: '2',
          title: 'Alpha Game',
        }
      }
    ];

    render(
      <PlatformFamilyView 
        {...mockProps} 
        games={gamesWithMultipleInFamily}
        sortOptions={{ field: 'title', direction: 'asc' }}
      />
    );

    // Should render PC family with 2 games
    const pcGroup = screen.getByTestId('platform-family-group-PC');
    expect(pcGroup.querySelector('[data-testid="game-count"]')).toHaveTextContent('2');
  });

  it('hides family groups when all games are filtered out', () => {
    render(<PlatformFamilyView {...mockProps} searchQuery="Xbox" />);
    
    // Should only show Xbox family (since "Xbox" matches "Xbox One" platform)
    expect(screen.getByTestId('platform-family-group-Xbox')).toBeInTheDocument();
    
    // Should not show PC or PlayStation families
    expect(screen.queryByTestId('platform-family-group-PC')).not.toBeInTheDocument();
    expect(screen.queryByTestId('platform-family-group-PlayStation')).not.toBeInTheDocument();
  });
});