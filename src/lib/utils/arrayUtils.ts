/**
 * Array manipulation and utility functions
 */

/**
 * Remove duplicates from array
 */
export function unique<T>(array: T[]): T[] {
  return [...new Set(array)];
}

/**
 * Remove duplicates from array based on a key function
 */
export function uniqueBy<T, K>(array: T[], keyFn: (item: T) => K): T[] {
  const seen = new Set<K>();
  return array.filter(item => {
    const key = keyFn(item);
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

/**
 * Group array items by a key function
 */
export function groupBy<T, K extends string | number | symbol>(
  array: T[], 
  keyFn: (item: T) => K
): Record<K, T[]> {
  return array.reduce((groups, item) => {
    const key = keyFn(item);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
    return groups;
  }, {} as Record<K, T[]>);
}

/**
 * Chunk array into smaller arrays of specified size
 */
export function chunk<T>(array: T[], size: number): T[][] {
  if (size <= 0) return [];
  
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

/**
 * Flatten nested arrays
 */
export function flatten<T>(array: (T | T[])[]): T[] {
  return array.reduce<T[]>((flat, item) => {
    return flat.concat(Array.isArray(item) ? flatten(item) : item);
  }, []);
}

/**
 * Get random item from array
 */
export function randomItem<T>(array: T[]): T | undefined {
  if (array.length === 0) return undefined;
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Get multiple random items from array
 */
export function randomItems<T>(array: T[], count: number): T[] {
  if (count >= array.length) return [...array];
  
  const shuffled = shuffle([...array]);
  return shuffled.slice(0, count);
}

/**
 * Shuffle array using Fisher-Yates algorithm
 */
export function shuffle<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Sort array by multiple criteria
 */
export function sortBy<T>(
  array: T[], 
  ...sortFns: ((item: T) => string | number | Date)[]
): T[] {
  return [...array].sort((a, b) => {
    for (const sortFn of sortFns) {
      const aVal = sortFn(a);
      const bVal = sortFn(b);
      
      if (aVal < bVal) return -1;
      if (aVal > bVal) return 1;
    }
    return 0;
  });
}

/**
 * Find the intersection of multiple arrays
 */
export function intersection<T>(...arrays: T[][]): T[] {
  if (arrays.length === 0) return [];
  if (arrays.length === 1) return [...arrays[0]];
  
  return arrays.reduce((acc, current) => 
    acc.filter(item => current.includes(item))
  );
}

/**
 * Find the union of multiple arrays
 */
export function union<T>(...arrays: T[][]): T[] {
  return unique(flatten(arrays));
}

/**
 * Find the difference between two arrays (items in first but not in second)
 */
export function difference<T>(array1: T[], array2: T[]): T[] {
  return array1.filter(item => !array2.includes(item));
}

/**
 * Check if arrays are equal (shallow comparison)
 */
export function arraysEqual<T>(array1: T[], array2: T[]): boolean {
  if (array1.length !== array2.length) return false;
  return array1.every((item, index) => item === array2[index]);
}

/**
 * Check if arrays are equal (deep comparison)
 */
export function arraysDeepEqual<T>(array1: T[], array2: T[]): boolean {
  if (array1.length !== array2.length) return false;
  return array1.every((item, index) => {
    const other = array2[index];
    if (Array.isArray(item) && Array.isArray(other)) {
      return arraysDeepEqual(item, other);
    }
    if (typeof item === 'object' && typeof other === 'object' && item !== null && other !== null) {
      return JSON.stringify(item) === JSON.stringify(other);
    }
    return item === other;
  });
}

/**
 * Partition array into two arrays based on predicate
 */
export function partition<T>(array: T[], predicate: (item: T) => boolean): [T[], T[]] {
  const truthy: T[] = [];
  const falsy: T[] = [];
  
  array.forEach(item => {
    if (predicate(item)) {
      truthy.push(item);
    } else {
      falsy.push(item);
    }
  });
  
  return [truthy, falsy];
}

/**
 * Get the first n items from array
 */
export function take<T>(array: T[], count: number): T[] {
  return array.slice(0, Math.max(0, count));
}

/**
 * Get the last n items from array
 */
export function takeLast<T>(array: T[], count: number): T[] {
  return array.slice(-Math.max(0, count));
}

/**
 * Drop the first n items from array
 */
export function drop<T>(array: T[], count: number): T[] {
  return array.slice(Math.max(0, count));
}

/**
 * Drop the last n items from array
 */
export function dropLast<T>(array: T[], count: number): T[] {
  return array.slice(0, -Math.max(0, count));
}

/**
 * Find the minimum value in array
 */
export function min<T>(array: T[], getValue?: (item: T) => number): T | undefined {
  if (array.length === 0) return undefined;
  
  if (getValue) {
    return array.reduce((min, current) => 
      getValue(current) < getValue(min) ? current : min
    );
  }
  
  return array.reduce((min, current) => current < min ? current : min);
}

/**
 * Find the maximum value in array
 */
export function max<T>(array: T[], getValue?: (item: T) => number): T | undefined {
  if (array.length === 0) return undefined;
  
  if (getValue) {
    return array.reduce((max, current) => 
      getValue(current) > getValue(max) ? current : max
    );
  }
  
  return array.reduce((max, current) => current > max ? current : max);
}

/**
 * Calculate sum of array values
 */
export function sum<T>(array: T[], getValue?: (item: T) => number): number {
  if (getValue) {
    return array.reduce((sum, item) => sum + getValue(item), 0);
  }
  
  return (array as unknown as number[]).reduce((sum, item) => sum + item, 0);
}

/**
 * Calculate average of array values
 */
export function average<T>(array: T[], getValue?: (item: T) => number): number {
  if (array.length === 0) return 0;
  return sum(array, getValue) / array.length;
}

/**
 * Count occurrences of each item in array
 */
export function countBy<T, K extends string | number | symbol>(
  array: T[], 
  keyFn: (item: T) => K
): Record<K, number> {
  return array.reduce((counts, item) => {
    const key = keyFn(item);
    counts[key] = (counts[key] || 0) + 1;
    return counts;
  }, {} as Record<K, number>);
}

/**
 * Create array of specified length filled with value or function result
 */
export function createArray<T>(length: number, fillValue: T | ((index: number) => T)): T[] {
  return Array.from({ length }, (_, index) => 
    typeof fillValue === 'function' ? (fillValue as (index: number) => T)(index) : fillValue
  );
}

/**
 * Create range of numbers
 */
export function range(start: number, end?: number, step: number = 1): number[] {
  if (end === undefined) {
    end = start;
    start = 0;
  }
  
  const result: number[] = [];
  if (step > 0) {
    for (let i = start; i < end; i += step) {
      result.push(i);
    }
  } else if (step < 0) {
    for (let i = start; i > end; i += step) {
      result.push(i);
    }
  }
  
  return result;
}

/**
 * Zip multiple arrays together
 */
export function zip<T>(...arrays: T[][]): T[][] {
  if (arrays.length === 0) return [];
  
  const maxLength = Math.max(...arrays.map(arr => arr.length));
  return range(maxLength).map(i => arrays.map(arr => arr[i]));
}

/**
 * Transpose 2D array (swap rows and columns)
 */
export function transpose<T>(matrix: T[][]): T[][] {
  if (matrix.length === 0) return [];
  return matrix[0].map((_, colIndex) => matrix.map(row => row[colIndex]));
}

/**
 * Check if array is empty
 */
export function isEmpty<T>(array: T[]): boolean {
  return array.length === 0;
}

/**
 * Check if array is not empty
 */
export function isNotEmpty<T>(array: T[]): boolean {
  return array.length > 0;
}

/**
 * Compact array (remove falsy values)
 */
export function compact<T>(array: (T | null | undefined | false | 0 | '')[]): T[] {
  return array.filter(Boolean) as T[];
}

/**
 * Move item from one index to another
 */
export function moveItem<T>(array: T[], fromIndex: number, toIndex: number): T[] {
  const result = [...array];
  const [removed] = result.splice(fromIndex, 1);
  result.splice(toIndex, 0, removed);
  return result;
}

/**
 * Insert item at specific index
 */
export function insertAt<T>(array: T[], index: number, item: T): T[] {
  const result = [...array];
  result.splice(index, 0, item);
  return result;
}

/**
 * Remove item at specific index
 */
export function removeAt<T>(array: T[], index: number): T[] {
  const result = [...array];
  result.splice(index, 1);
  return result;
}

/**
 * Update item at specific index
 */
export function updateAt<T>(array: T[], index: number, item: T): T[] {
  const result = [...array];
  result[index] = item;
  return result;
}