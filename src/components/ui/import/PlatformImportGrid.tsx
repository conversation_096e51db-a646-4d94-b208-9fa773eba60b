/**
 * Platform Import Grid Component
 * Visual interface for managing multi-platform library imports
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { Alert, AlertDescription } from '@/components/ui/base/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/base/tabs';
import { 
  Download, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Play, 
  Square, 
  RefreshCw,
  AlertTriangle,
  Info,
  Gamepad2,
  History
} from 'lucide-react';
import { useMultiPlatformImport, type PlatformImportStatus } from '@/hooks/useMultiPlatformImport';
import { usePlatformInfo } from '@/hooks/useApiKeys';
import { type Platform } from '@/lib/apiKeyStore';

interface PlatformImportGridProps {
  className?: string;
}

// Platform icons mapping
const PLATFORM_ICONS: Record<Platform, React.ReactNode> = {
  steam: <Gamepad2 className="h-6 w-6 text-blue-600" />,
};

export function PlatformImportGrid({ className }: PlatformImportGridProps) {
  const {
    importStatuses,
    isAnyImportRunning,
    startImport,
    stopImport,
    resetStatuses,
    getConfiguredPlatforms,
    importHistory,
    isLoadingHistory,
    getImportSummary,
  } = useMultiPlatformImport();

  const [configuredPlatforms, setConfiguredPlatforms] = useState<Platform[]>([]);
  const [isCheckingConfig, setIsCheckingConfig] = useState(true);

  // Check platform configurations on mount
  useEffect(() => {
    const checkConfigurations = async () => {
      setIsCheckingConfig(true);
      try {
        const configured = await getConfiguredPlatforms();
        setConfiguredPlatforms(configured);
      } catch (error) {
        console.error('Failed to check platform configurations:', error);
      } finally {
        setIsCheckingConfig(false);
      }
    };

    checkConfigurations();
  }, [getConfiguredPlatforms]);

  const summary = getImportSummary();

  if (isCheckingConfig) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Checking platform configurations...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Steam Library Import
          </CardTitle>
          <CardDescription>
            Import your Steam gaming library
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Configuration Status */}
          {configuredPlatforms.length === 0 ? (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Steam is not configured. Please add your Steam API key in the{' '}
                <strong>API Keys</strong> section to enable imports.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Steam is configured and ready for library import.
              </AlertDescription>
            </Alert>
          )}

          {/* Import Controls */}
          {configuredPlatforms.length > 0 && (
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={() => startImport({ platforms: ['steam'] })}
                disabled={isAnyImportRunning}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                Import Steam Library
              </Button>
              
              {isAnyImportRunning && (
                <Button
                  variant="destructive"
                  onClick={() => stopImport()}
                  className="flex items-center gap-2"
                >
                  <Square className="h-4 w-4" />
                  Stop Import
                </Button>
              )}
              
              <Button
                variant="ghost"
                onClick={resetStatuses}
                disabled={isAnyImportRunning}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Reset
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Steam Import Card */}
      {configuredPlatforms.length > 0 && (
        <PlatformImportCard
          platform="steam"
          status={importStatuses['steam']}
          onStartImport={() => startImport({ platforms: ['steam'] })}
          onStopImport={() => stopImport('steam')}
          disabled={false}
        />
      )}

      {/* Import Summary & History */}
      <Tabs defaultValue="summary" className="w-full">
        <TabsList>
          <TabsTrigger value="summary">Import Summary</TabsTrigger>
          <TabsTrigger value="history">Import History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="summary" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Current Session Summary</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{summary.totalGames}</div>
                <div className="text-sm text-muted-foreground">Games Imported</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{summary.totalUpdated}</div>
                <div className="text-sm text-muted-foreground">Games Updated</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{summary.totalErrors}</div>
                <div className="text-sm text-muted-foreground">Errors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{summary.successRate.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Success Rate</div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="history" className="space-y-4">
          <ImportHistoryTable history={importHistory} isLoading={isLoadingHistory} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface PlatformImportCardProps {
  platform: Platform;
  status?: PlatformImportStatus;
  onStartImport: () => void;
  onStopImport: () => void;
  disabled: boolean;
}

function PlatformImportCard({
  platform,
  status,
  onStartImport,
  onStopImport,
  disabled,
}: PlatformImportCardProps) {
  const { getPlatformLabel } = usePlatformInfo();
  
  const getStatusIcon = () => {
    switch (status?.status) {
      case 'running':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return PLATFORM_ICONS[platform];
    }
  };

  const getStatusBadge = () => {
    switch (status?.status) {
      case 'running':
        return <Badge variant="default">Running</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">Ready</Badge>;
    }
  };

  const progressValue = status?.progress ? 
    Math.round((status.progress.current / status.progress.total) * 100) : 0;

  return (
    <Card className="transition-all">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            {getPlatformLabel(platform)}
          </div>
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* Progress Bar */}
        {status?.status === 'running' && status.progress && (
          <div className="space-y-2">
            <Progress value={progressValue} className="h-2" />
            <div className="text-xs text-muted-foreground">
              {status.progress.message}
            </div>
            <div className="text-xs text-muted-foreground">
              {status.progress.current}/{status.progress.total} ({progressValue}%)
            </div>
          </div>
        )}

        {/* Results */}
        {status?.result && (
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="font-medium">Imported:</span> {status.result.gamesImported}
            </div>
            <div>
              <span className="font-medium">Updated:</span> {status.result.gamesUpdated}
            </div>
          </div>
        )}

        {/* Error Message */}
        {status?.error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-3 w-3" />
            <AlertDescription className="text-xs">
              {status.error}
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          {status?.status === 'running' ? (
            <Button
              variant="destructive"
              size="sm"
              onClick={onStopImport}
              className="flex-1"
            >
              <Square className="h-3 w-3 mr-1" />
              Stop
            </Button>
          ) : (
            <Button
              variant="default"
              size="sm"
              onClick={onStartImport}
              disabled={disabled}
              className="flex-1"
            >
              <Play className="h-3 w-3 mr-1" />
              Import
            </Button>
          )}
        </div>

      </CardContent>
    </Card>
  );
}

interface ImportHistoryRecord {
  id: string;
  platform: string;
  started_at: string;
  games_imported: number;
  games_updated: number;
  status: string;
}

interface ImportHistoryTableProps {
  history: ImportHistoryRecord[];
  isLoading: boolean;
}

function ImportHistoryTable({ history, isLoading }: ImportHistoryTableProps) {
  const { getPlatformLabel } = usePlatformInfo();

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2">Loading import history...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (history.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <History className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Import History</h3>
            <p className="text-muted-foreground">
              Your Steam import history will appear here after you run your first import.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Imports</CardTitle>
        <CardDescription>
          History of your Steam library imports
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {history.slice(0, 10).map((record) => (
            <div key={record.id} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {PLATFORM_ICONS[record.platform as Platform]}
                <div>
                  <div className="font-medium">{getPlatformLabel(record.platform)}</div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(record.started_at).toLocaleDateString()} at{' '}
                    {new Date(record.started_at).toLocaleTimeString()}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <div className="text-right text-sm">
                  <div>{record.games_imported || 0} imported</div>
                  <div className="text-muted-foreground">{record.games_updated || 0} updated</div>
                </div>
                
                <Badge 
                  variant={record.status === 'completed' ? 'default' : 
                          record.status === 'failed' ? 'destructive' : 'outline'}
                >
                  {record.status}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}