import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/supabase';
import { UserGameWithDetails } from '@/types/database';
import { useMemo } from 'react';

/**
 * Consolidated hook that fetches user collection data once and provides derived data
 * This prevents multiple database calls in different hooks
 */
export function useUserCollection() {
  const { user } = useAuth();

  // Single source of truth for user collection data
  const {
    data: userCollection,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['user-collection', user?.id],
    queryFn: async (): Promise<UserGameWithDetails[]> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      try {
        const { data, error } = await db.userGames.getUserCollection(user.id);
        
        if (error) {
          console.error('Failed to fetch user collection:', error);
          return [];
        }
        
        return data || [];
      } catch (error) {
        console.error('Error fetching user collection:', error);
        return [];
      }
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes - user data changes frequently
    gcTime: 10 * 60 * 1000, // 10 minutes cache time
  });

  // Memoized derived data to prevent recalculation on every render
  const derivedData = useMemo(() => {
    const allGames = userCollection || [];
    const libraryGames = allGames.filter(game => game.status !== 'wishlist');
    const wishlistGames = allGames.filter(game => game.status === 'wishlist');

    // Calculate statistics
    const totalGames = libraryGames.length;
    const currentlyPlaying = allGames.filter(game => game.status === 'playing').length;
    const completed = allGames.filter(game => game.status === 'completed').length;
    const wishlistCount = wishlistGames.length;

    // Calculate average rating
    const ratedGames = allGames.filter(game => game.personal_rating && game.personal_rating > 0);
    const averageRating = ratedGames.length > 0
      ? ratedGames.reduce((sum, game) => sum + (game.personal_rating || 0), 0) / ratedGames.length
      : 0;

    // Recent activity (last 5 games added)
    const recentActivity = [...allGames]
      .sort((a, b) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime())
      .slice(0, 5);

    return {
      allGames,
      libraryGames,
      wishlistGames,
      stats: {
        totalGames,
        currentlyPlaying,
        completed,
        wishlistCount,
        totalPriceTracked: 0, // Price tracking data available via separate hook
        averageRating,
      },
      recentActivity,
    };
  }, [userCollection]);

  return {
    // Raw data
    userCollection: userCollection || [],
    
    // Derived data
    ...derivedData,
    
    // Loading states
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook to get user's library games (non-wishlist)
 */
export function useUserLibrary() {
  const { libraryGames, isLoading, error } = useUserCollection();
  
  return {
    data: libraryGames,
    isLoading,
    error,
  };
}

/**
 * Hook to get user's wishlist games
 */
export function useUserWishlist() {
  const { wishlistGames, isLoading, error } = useUserCollection();
  
  return {
    data: wishlistGames,
    isLoading,
    error,
  };
}

/**
 * Hook to get user statistics
 */
export function useUserStats() {
  const { stats, isLoading, error } = useUserCollection();
  
  return {
    data: stats,
    isLoading,
    error,
  };
}

/**
 * Hook to get recent activity
 */
export function useRecentActivity() {
  const { recentActivity, isLoading, error } = useUserCollection();
  
  return {
    data: recentActivity,
    isLoading,
    error,
  };
}

/**
 * Hook to get the status of a specific game in user's collection
 */
export function useUserGameStatus(gameId: string) {
  const { userCollection, isLoading, error } = useUserCollection();
  
  const userGame = useMemo(() => {
    if (!gameId || !userCollection) return null;
    return userCollection.find(game => game.game_id === gameId) || null;
  }, [userCollection, gameId]);

  return {
    data: userGame,
    isLoading,
    error,
  };
}