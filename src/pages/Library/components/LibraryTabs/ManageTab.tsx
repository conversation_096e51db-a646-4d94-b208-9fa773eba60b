import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base';
import { <PERSON><PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/base';
import { UserGameWithDetails } from '@/types/database';
import { LibraryFilters } from '../../types';
import { FiltersTab } from './FiltersTab';
import { ExportTab } from './ExportTab';
import { ArtworkTab } from './ArtworkTab';
import { 
  Filter, 
  Download, 
  ImageIcon,
  Settings
} from '@/lib/icons';

interface ManageTabProps {
  libraryGames: UserGameWithDetails[];
  isLoading: boolean;
  error: Error | null;
  filters: LibraryFilters;
  onFiltersApply: (filters: LibraryFilters) => void;
  onExport: (format: string, options: Record<string, unknown>) => Promise<void>;
  onImport: (source: string, data: unknown, options: Record<string, unknown>) => Promise<void>;
}

export function ManageTab({ 
  libraryGames, 
  isLoading, 
  error, 
  filters,
  onFiltersApply,
  onExport,
  onImport
}: ManageTabProps) {
  const [activeSubTab, setActiveSubTab] = useState('filters');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Library Management
          </CardTitle>
          <CardDescription>
            Manage your library with filters, data export/import, and artwork customization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeSubTab} onValueChange={setActiveSubTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="filters" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filters
              </TabsTrigger>
              <TabsTrigger value="export" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Data
              </TabsTrigger>
              <TabsTrigger value="artwork" className="flex items-center gap-2">
                <ImageIcon className="h-4 w-4" />
                Artwork
              </TabsTrigger>
            </TabsList>

            <TabsContent value="filters" className="mt-6">
              <FiltersTab
                libraryGames={libraryGames}
                isLoading={isLoading}
                error={error}
                filters={filters}
                onFiltersApply={onFiltersApply}
              />
            </TabsContent>

            <TabsContent value="export" className="mt-6">
              <ExportTab
                libraryGames={libraryGames}
                isLoading={isLoading}
                error={error}
                onExport={onExport}
                onImport={onImport}
              />
            </TabsContent>

            <TabsContent value="artwork" className="mt-6">
              <ArtworkTab libraryGames={libraryGames} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}