import { userGamesAPI } from './api/collections';
import { gameAPI } from './api/games';
import { UserGameWithDetails } from '../types/database';

interface GameRecommendation {
  igdb_id: string;
  gameName: string;
  reason: string;
  confidence: number; // 0-100
  categories: string[];
  similarTo: string[];
  estimatedPlayTime?: number;
  genres: string[];
  platforms: string[];
  description: string;
  tags: string[];
  cover_image?: string;
}

interface UserGameProfile {
  userId: string;
  totalGames: number;
  completedGames: number;
  currentlyPlaying: number;
  favoriteGenres: string[];
  favoritePlatforms: string[];
  topRatedGames: Array<{ title: string; rating: number }>;
  recentlyPlayedGames: string[];
  wishlistGames: string[];
  averageRating: number;
  totalPlayTime: number;
}

interface RecommendationRequest {
  count?: number;
  filters?: {
    genres?: string[];
    platforms?: string[];
    maxPlaytime?: number;
  };
}

class AIRecommendationService {
  private readonly DEEPSEEK_API_KEY: string;
  private readonly OPENAI_API_KEY: string;
  private readonly DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';
  private readonly OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

  constructor() {
    this.DEEPSEEK_API_KEY = import.meta.env.VITE_DEEPSEEK_API_KEY;
    this.OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;
    
    if (!this.DEEPSEEK_API_KEY && !this.OPENAI_API_KEY) {
      throw new Error('Either VITE_DEEPSEEK_API_KEY or VITE_OPENAI_API_KEY environment variable is required for AI recommendations');
    }
  }

  /**
   * Generate AI-powered game recommendations based on user's actual games
   */
  async generateUserBasedRecommendations(
    userId: string, 
    request: RecommendationRequest = {}
  ): Promise<GameRecommendation[]> {
    try {
      const userProfile = await this.buildUserGameProfile(userId);
      
      // Only generate recommendations if user has games
      if (userProfile.totalGames === 0) {
        return [];
      }
      
      const recommendations = await this.callAIAPI(userProfile, request);
      const validatedRecommendations = await this.validateAndEnrichRecommendations(recommendations, userId);
      
      return validatedRecommendations.slice(0, request.count || 10);
    } catch (error) {
      console.error('Error generating user-based recommendations:', error);
      return [];
    }
  }

  /**
   * Get recommendations similar to a specific game from user's collection
   */
  async getSimilarGameRecommendations(
    _gameId: string,
    gameName: string,
    userId: string
  ): Promise<GameRecommendation[]> {
    try {
      const userProfile = await this.buildUserGameProfile(userId);
      
      // Only generate if user has games to base recommendations on
      if (userProfile.totalGames === 0) {
        return [];
      }
      
      const prompt = this.buildSimilarGamePrompt(gameName, userProfile);
      const recommendations = await this.callAIAPIWithPrompt(prompt);
      
      return this.validateAndEnrichRecommendations(recommendations, userId);
    } catch (error) {
      console.error('Error getting similar game recommendations:', error);
      return [];
    }
  }

  /**
   * Build user game profile based on their actual collection
   */
  private async buildUserGameProfile(userId: string): Promise<UserGameProfile> {
    try {
      const { data: userGames, error } = await userGamesAPI.getUserCollection(userId);

      if (error) throw error;

      const games = userGames || [];
      const libraryGames = games.filter((g: UserGameWithDetails) => g.status !== 'wishlist');
      const wishlistGames = games.filter((g: UserGameWithDetails) => g.status === 'wishlist');

      // Calculate statistics
      const totalGames = libraryGames.length;
      const completedGames = libraryGames.filter((g: UserGameWithDetails) => g.status === 'completed').length;
      const currentlyPlaying = libraryGames.filter((g: UserGameWithDetails) => g.status === 'playing').length;
      
      // Extract favorite genres from user's games
      const genreCount = new Map<string, number>();
      libraryGames.forEach((game: UserGameWithDetails) => {
        if (game.game?.genres) {
          game.game.genres.forEach((genre: string) => {
            genreCount.set(genre, (genreCount.get(genre) || 0) + 1);
          });
        }
      });
      
      const favoriteGenres = Array.from(genreCount.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([genre]) => genre);

      // Extract favorite platforms
      const platformCount = new Map<string, number>();
      libraryGames.forEach((game: UserGameWithDetails) => {
        if (game.game?.platforms) {
          game.game.platforms.forEach((platform: string) => {
            platformCount.set(platform, (platformCount.get(platform) || 0) + 1);
          });
        }
      });
      
      const favoritePlatforms = Array.from(platformCount.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([platform]) => platform);

      // Get top rated games
      const topRatedGames = libraryGames
        .filter((g: UserGameWithDetails) => g.personal_rating && g.personal_rating >= 4)
        .sort((a: UserGameWithDetails, b: UserGameWithDetails) => (b.personal_rating || 0) - (a.personal_rating || 0))
        .slice(0, 10)
        .map((g: UserGameWithDetails) => ({
          title: g.game?.title || 'Unknown',
          rating: g.personal_rating || 0
        }));

      // Get recently played games
      const recentlyPlayedGames = libraryGames
        .filter((g: UserGameWithDetails) => g.status === 'completed' || g.status === 'playing')
        .sort((a: UserGameWithDetails, b: UserGameWithDetails) => new Date(b.date_added).getTime() - new Date(a.date_added).getTime())
        .slice(0, 10)
        .map((g: UserGameWithDetails) => g.game?.title || 'Unknown');

      // Calculate average rating
      const ratedGames = libraryGames.filter((g: UserGameWithDetails) => g.personal_rating);
      const averageRating = ratedGames.length > 0
        ? ratedGames.reduce((sum: number, g: UserGameWithDetails) => sum + (g.personal_rating || 0), 0) / ratedGames.length
        : 0;

      // Calculate total play time - Note: hours_played is not in the current schema
      const totalPlayTime = 0; // Will be 0 until hours_played is added to schema

      return {
        userId,
        totalGames,
        completedGames,
        currentlyPlaying,
        favoriteGenres,
        favoritePlatforms,
        topRatedGames,
        recentlyPlayedGames,
        wishlistGames: wishlistGames.map((g: UserGameWithDetails) => g.game?.title || 'Unknown'),
        averageRating,
        totalPlayTime
      };
    } catch (error) {
      console.error('Error building user game profile:', error);
      throw error;
    }
  }

  /**
   * Call AI API with user game profile
   */
  private async callAIAPI(
    userProfile: UserGameProfile,
    request: RecommendationRequest
  ): Promise<GameRecommendation[]> {
    const prompt = this.buildRecommendationPrompt(userProfile, request);
    return this.callAIAPIWithPrompt(prompt);
  }

  /**
   * Call AI API with custom prompt (DeepSeek or OpenAI)
   */
  private async callAIAPIWithPrompt(prompt: string): Promise<GameRecommendation[]> {
    // Try DeepSeek first, then fallback to OpenAI
    if (this.DEEPSEEK_API_KEY) {
      try {
        return await this.callDeepSeekAPI(prompt);
      } catch (error) {
        console.warn('DeepSeek API failed, trying OpenAI:', error);
        if (this.OPENAI_API_KEY) {
          return await this.callOpenAIAPI(prompt);
        }
        throw error;
      }
    } else if (this.OPENAI_API_KEY) {
      return await this.callOpenAIAPI(prompt);
    }
    
    throw new Error('No AI API key available');
  }

  /**
   * Call DeepSeek API
   */
  private async callDeepSeekAPI(prompt: string): Promise<GameRecommendation[]> {
    const response = await fetch(this.DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.DEEPSEEK_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [{
          role: 'user',
          content: prompt
        }],
        temperature: 0.7,
        max_tokens: 2048,
        top_p: 0.95,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`);
    }

    const data = await response.json();
    const aiResponse = data.choices?.[0]?.message?.content;
    
    if (!aiResponse) {
      throw new Error('No response from DeepSeek API');
    }

    return this.parseAIResponse(aiResponse);
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAIAPI(prompt: string): Promise<GameRecommendation[]> {
    const response = await fetch(this.OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{
          role: 'user',
          content: prompt
        }],
        temperature: 0.7,
        max_tokens: 2048,
        top_p: 0.95,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const aiResponse = data.choices?.[0]?.message?.content;
    
    if (!aiResponse) {
      throw new Error('No response from OpenAI API');
    }

    return this.parseAIResponse(aiResponse);
  }

  /**
   * Build recommendation prompt based on user's games
   */
  private buildRecommendationPrompt(userProfile: UserGameProfile, request: RecommendationRequest): string {
    const { totalGames, completedGames, favoriteGenres, favoritePlatforms, topRatedGames, recentlyPlayedGames } = userProfile;

    return `
You are a gaming AI assistant that provides personalized game recommendations based on a user's actual game collection and preferences.

USER'S GAME COLLECTION ANALYSIS:
- Total Games in Library: ${totalGames}
- Completed Games: ${completedGames}
- Completion Rate: ${Math.round((completedGames / totalGames) * 100)}%
- Average Rating: ${userProfile.averageRating.toFixed(1)}/5
- Total Play Time: ${userProfile.totalPlayTime} hours

FAVORITE GENRES (based on owned games):
${favoriteGenres.join(', ')}

FAVORITE PLATFORMS (based on owned games):
${favoritePlatforms.join(', ')}

TOP RATED GAMES IN COLLECTION:
${topRatedGames.map(g => `- ${g.title} (${g.rating}/5)`).join('\n')}

RECENTLY PLAYED GAMES:
${recentlyPlayedGames.slice(0, 8).join(', ')}

${request.filters ? `FILTERS: ${JSON.stringify(request.filters)}` : ''}

Based on this user's actual game collection and preferences, recommend ${request.count || 10} games that would fit their taste. Focus on:

1. Games similar to their top-rated games
2. Games in their favorite genres but not yet owned
3. Games from developers/publishers of their favorite titles
4. Games that match their completion patterns and play time preferences

Provide recommendations in this JSON format:
[
  {
    "gameName": "Game Title",
    "igdb_id": "12345",
    "reason": "Specific reason based on user's collection (e.g., 'Similar to your top-rated game X, same developer as Y')",
    "confidence": 85,
    "categories": ["Based on [specific game]", "Genre match", "Developer match"],
    "similarTo": ["Specific game titles from user's collection"],
    "genres": ["Action", "Adventure"],
    "platforms": ["PC", "PlayStation"],
    "description": "Brief game description",
    "tags": ["Story Rich", "Singleplayer"],
    "estimatedPlayTime": 45
  }
]

IMPORTANT: Only recommend games that are NOT already in the user's collection. Base all recommendations on their actual gaming preferences derived from their owned games.

Return only the JSON array, no additional text.
`;
  }

  /**
   * Build similar games prompt
   */
  private buildSimilarGamePrompt(gameName: string, userProfile: UserGameProfile): string {
    return `
You are a gaming AI assistant. The user owns "${gameName}" and wants similar games based on their collection preferences.

USER'S GAME PREFERENCES (from their collection):
- Favorite Genres: ${userProfile.favoriteGenres.join(', ')}
- Favorite Platforms: ${userProfile.favoritePlatforms.join(', ')}
- Top Rated Games: ${userProfile.topRatedGames.map(g => g.title).join(', ')}
- Average Rating: ${userProfile.averageRating.toFixed(1)}/5

Find 6 games similar to "${gameName}" that match this user's established preferences in JSON format:
[
  {
    "gameName": "Similar Game Title",
    "igdb_id": "12345",
    "reason": "Why this is similar to ${gameName} and fits their collection preferences",
    "confidence": 90,
    "categories": ["Similar to ${gameName}", "Genre match"],
    "similarTo": ["${gameName}"],
    "genres": ["Action", "Adventure"],
    "platforms": ["PC", "PlayStation"],
    "description": "Brief game description",
    "tags": ["Similar mechanics", "Story Rich"],
    "estimatedPlayTime": 40
  }
]

Return only the JSON array, no additional text.
`;
  }

  /**
   * Parse AI response and extract recommendations
   */
  private parseAIResponse(aiResponse: string): GameRecommendation[] {
    try {
      const cleanResponse = aiResponse.replace(/```json\n?|\n?```/g, '').trim();
      const recommendations = JSON.parse(cleanResponse);
      
      if (!Array.isArray(recommendations)) {
        throw new Error('Response is not an array');
      }
      
      return recommendations.map(rec => ({
        igdb_id: rec.igdb_id || '0',
        gameName: rec.gameName || 'Unknown Game',
        reason: rec.reason || 'Based on your game collection',
        confidence: Math.min(Math.max(rec.confidence || 70, 0), 100),
        categories: rec.categories || ['Collection-based'],
        similarTo: rec.similarTo || [],
        genres: rec.genres || ['Unknown'],
        platforms: rec.platforms || ['PC'],
        description: rec.description || 'No description available',
        tags: rec.tags || [],
        estimatedPlayTime: rec.estimatedPlayTime || 30
      }));
    } catch (error) {
      console.error('Error parsing AI response:', error);
      throw new Error('Failed to parse AI recommendations');
    }
  }

  /**
   * Validate and enrich recommendations, ensuring they're not already owned
   */
  private async validateAndEnrichRecommendations(
    recommendations: GameRecommendation[],
    userId: string
  ): Promise<GameRecommendation[]> {
    // Get user's existing games to avoid duplicates
    const { data: userGames } = await userGamesAPI.getUserCollection(userId);

    const ownedGameTitles = new Set(
      userGames?.map((ug: UserGameWithDetails) => ug.game?.title?.toLowerCase()) || []
    );
    const ownedGameIds = new Set(
      userGames?.map((ug: UserGameWithDetails) => ug.game?.igdb_id) || []
    );

    const enrichedRecommendations = await Promise.all(
      recommendations.map(async (rec) => {
        try {
          // Skip if user already owns this game
          if (ownedGameTitles.has(rec.gameName.toLowerCase()) || ownedGameIds.has(rec.igdb_id)) {
            return null;
          }

          // Search for the game to get accurate data
          const searchResults = await gameAPI.search(rec.gameName, { limit: 1 });
          const gameData = searchResults[0];

          if (gameData) {
            const convertedGame = gameAPI.convertToGame(gameData);
            const enrichedRec: GameRecommendation = {
              ...rec,
              igdb_id: gameData.id.toString(),
              gameName: gameData.name,
              genres: convertedGame.genres || rec.genres,
              platforms: gameData.platforms?.map(p => p.name) || rec.platforms,
              description: convertedGame.description || rec.description,
            };
            
            if (convertedGame.cover_image) {
              enrichedRec.cover_image = convertedGame.cover_image;
            }
            
            return enrichedRec;
          }
          return null;
        } catch (error) {
          console.error(`Error enriching recommendation for ${rec.gameName}:`, error);
          return null;
        }
      })
    );

    return enrichedRecommendations.filter((rec): rec is GameRecommendation => rec !== null);
  }
}

export const aiRecommendationService = new AIRecommendationService();
export type { GameRecommendation, UserGameProfile, RecommendationRequest };