import React, { memo, useMemo, useCallback, useState, useRef, useEffect } from 'react';
import { FixedSizeList as List } from 'react-window';
import { PremiumGameCard } from './premium-game-card';
import { useEnhancedLibrary } from '@/hooks/useEnhancedLibrary';
import { UserGameWithDetails } from '@/types/database';
import { TimelineViewSettings, GameCardInteractions } from '@/types/library';
import { cn } from '@/lib/utils';
import { 
  Calendar, 
  Clock, 
  TrendingUp, 
  Filter, 
  Settings,
  ChevronDown,
  ChevronRight,
  Play,
  Star,
  Trophy,
  Gamepad2
} from 'lucide-react';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent } from '@/components/ui/base/card';
import { Separator } from '@/components/ui/base/separator';

// Timeline configurations
const TIMELINE_CONFIGS = {
  compact: {
    itemHeight: 80,
    groupHeaderHeight: 40,
    padding: 'p-2',
    cardSize: 'w-12 h-16'
  },
  normal: {
    itemHeight: 120,
    groupHeaderHeight: 60,
    padding: 'p-3',
    cardSize: 'w-16 h-24'
  },
  comfortable: {
    itemHeight: 160,
    groupHeaderHeight: 80,
    padding: 'p-4',
    cardSize: 'w-20 h-28'
  }
} as const;

interface EnhancedTimelineViewProps {
  games: UserGameWithDetails[];
  settings: TimelineViewSettings;
  onSettingsChange: (settings: Partial<TimelineViewSettings>) => void;
  interactions?: GameCardInteractions;
  className?: string;
}

interface TimelineGroup {
  id: string;
  title: string;
  subtitle?: string;
  date: Date;
  games: UserGameWithDetails[];
  totalHours?: number;
  avgRating?: number;
  milestones?: TimelineMilestone[];
}

interface TimelineMilestone {
  id: string;
  type: 'achievement' | 'completion' | 'playtime' | 'rating';
  title: string;
  description: string;
  date: Date;
  icon: React.ReactNode;
  color: string;
}

interface TimelineItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    items: (TimelineGroup | UserGameWithDetails)[];
    settings: TimelineViewSettings;
    interactions?: GameCardInteractions;
    expandedGroups: Set<string>;
    selectedGames: Set<string>;
    onToggleGroup: (groupId: string) => void;
    onGameSelect: (gameId: string) => void;
  };
}

// Date grouping utilities
const groupGamesByDate = (
  games: UserGameWithDetails[], 
  grouping: TimelineViewSettings['grouping'],
  timelineType: TimelineViewSettings['timelineType']
): TimelineGroup[] => {
  const getDateFromGame = (game: UserGameWithDetails): Date => {
    switch (timelineType) {
      case 'added': return new Date(game.date_added);
      case 'played': return new Date(game.last_played || game.date_added);
      case 'completed': 
        return game.status === 'completed' 
          ? new Date(game.completion_date || game.date_added)
          : new Date(game.date_added);
      case 'release': return new Date(game.game?.release_date || game.date_added);
      default: return new Date(game.date_added);
    }
  };

  const getGroupKey = (date: Date): string => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const week = Math.floor(date.getDate() / 7);
    
    switch (grouping) {
      case 'day': return date.toDateString();
      case 'week': return `${year}-W${week}-${month}`;
      case 'month': return `${year}-${month}`;
      case 'quarter': return `${year}-Q${Math.floor(month / 3) + 1}`;
      case 'year': return year.toString();
      default: return year.toString();
    }
  };

  const getGroupTitle = (date: Date): { title: string; subtitle?: string } => {
    const year = date.getFullYear();
    const month = date.toLocaleDateString('default', { month: 'long' });
    const monthShort = date.toLocaleDateString('default', { month: 'short' });
    
    switch (grouping) {
      case 'day':
        return {
          title: date.toLocaleDateString('default', { 
            weekday: 'long', 
            month: 'short', 
            day: 'numeric' 
          }),
          subtitle: year.toString()
        };
      case 'week':
        return {
          title: `Week of ${date.toLocaleDateString('default', { month: 'short', day: 'numeric' })}`,
          subtitle: year.toString()
        };
      case 'month':
        return {
          title: month,
          subtitle: year.toString()
        };
      case 'quarter':
        return {
          title: `Q${Math.floor(date.getMonth() / 3) + 1} ${year}`,
          subtitle: `${monthShort} - ${new Date(year, Math.floor(date.getMonth() / 3) * 3 + 2).toLocaleDateString('default', { month: 'short' })}`
        };
      case 'year':
        return { title: year.toString() };
      default:
        return { title: year.toString() };
    }
  };

  // Group games by date
  const grouped = games.reduce((acc, game) => {
    const date = getDateFromGame(game);
    const key = getGroupKey(date);
    
    if (!acc[key]) {
      acc[key] = {
        date,
        games: []
      };
    }
    
    acc[key].games.push(game);
    return acc;
  }, {} as Record<string, { date: Date; games: UserGameWithDetails[] }>);

  // Convert to timeline groups
  return Object.entries(grouped)
    .map(([key, { date, games }]): TimelineGroup => {
      const { title, subtitle } = getGroupTitle(date, key);
      const totalHours = games.reduce((sum, game) => sum + (game.hours_played || 0), 0);
      const ratedGames = games.filter(game => game.personal_rating);
      const avgRating = ratedGames.length > 0 
        ? ratedGames.reduce((sum, game) => sum + (game.personal_rating || 0), 0) / ratedGames.length
        : undefined;

      const { title, subtitle } = getGroupTitle(date);
      
      return {
        id: key,
        title,
        subtitle,
        date,
        games: games.sort((a, b) => 
          getDateFromGame(b).getTime() - getDateFromGame(a).getTime()
        ),
        totalHours: totalHours > 0 ? totalHours : undefined,
        avgRating: avgRating ? Math.round(avgRating * 10) / 10 : undefined
      };
    })
    .sort((a, b) => b.date.getTime() - a.date.getTime());
};

// Generate milestones for a group
const generateMilestones = (group: TimelineGroup): TimelineMilestone[] => {
  const milestones: TimelineMilestone[] = [];
  
  // Completion milestone
  const completedGames = group.games.filter(game => game.status === 'completed');
  if (completedGames.length > 0) {
    milestones.push({
      id: `${group.id}-completed`,
      type: 'completion',
      title: `Completed ${completedGames.length} game${completedGames.length > 1 ? 's' : ''}`,
      description: completedGames.map(g => g.game?.title).slice(0, 3).join(', '),
      date: group.date,
      icon: <Trophy size={16} className="text-yellow-500" />,
      color: 'bg-yellow-500'
    });
  }

  // Playtime milestone
  if (group.totalHours && group.totalHours >= 10) {
    milestones.push({
      id: `${group.id}-playtime`,
      type: 'playtime',
      title: `${Math.round(group.totalHours)}h played`,
      description: `Across ${group.games.length} games`,
      date: group.date,
      icon: <Clock size={16} className="text-blue-500" />,
      color: 'bg-blue-500'
    });
  }

  // High rating milestone
  if (group.avgRating && group.avgRating >= 8) {
    milestones.push({
      id: `${group.id}-rating`,
      type: 'rating',
      title: `High rated period`,
      description: `Average rating: ${group.avgRating}/10`,
      date: group.date,
      icon: <Star size={16} className="text-purple-500" />,
      color: 'bg-purple-500'
    });
  }

  return milestones;
};

// Timeline group component
const TimelineGroupComponent = memo<{
  group: TimelineGroup;
  settings: TimelineViewSettings;
  interactions?: GameCardInteractions;
  isExpanded: boolean;
  selectedGames: Set<string>;
  onToggle: () => void;
  onGameSelect: (gameId: string) => void;
}>(({ group, settings, interactions, isExpanded, selectedGames, onToggle, onGameSelect }) => {
  const config = TIMELINE_CONFIGS.normal;
  const milestones = settings.showMilestones ? generateMilestones(group) : [];

  return (
    <div className="timeline-group">
      {/* Group Header */}
      <div 
        className={cn(
          'flex items-center justify-between py-3 px-4 bg-muted/30 border-l-4 border-primary cursor-pointer hover:bg-muted/50 transition-colors',
          config.padding
        )}
        onClick={onToggle}
      >
        <div className="flex items-center gap-3">
          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          <div>
            <h3 className="font-semibold text-foreground">{group.title}</h3>
            {group.subtitle && (
              <p className="text-sm text-muted-foreground">{group.subtitle}</p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Gamepad2 size={14} />
            <span>{group.games.length}</span>
          </div>
          
          {group.totalHours && (
            <div className="flex items-center gap-1">
              <Clock size={14} />
              <span>{Math.round(group.totalHours)}h</span>
            </div>
          )}
          
          {group.avgRating && (
            <div className="flex items-center gap-1">
              <Star size={14} />
              <span>{group.avgRating}</span>
            </div>
          )}
        </div>
      </div>

      {/* Group Content */}
      {isExpanded && (
        <div className="pl-8 py-2">
          {/* Milestones */}
          {milestones.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-2">
                {milestones.map(milestone => (
                  <div
                    key={milestone.id}
                    className="flex items-center gap-2 px-2 py-1 bg-muted rounded-md text-xs"
                  >
                    {milestone.icon}
                    <span>{milestone.title}</span>
                  </div>
                ))}
              </div>
              <Separator className="mt-2" />
            </div>
          )}

          {/* Games Grid */}
          <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-10 gap-3">
            {group.games.map(game => (
              <div key={game.id} className="timeline-game-item">
                <div
                  className={cn(
                    'relative cursor-pointer group transition-all duration-200 hover:scale-105',
                    selectedGames.has(game.id) && 'ring-2 ring-primary rounded'
                  )}
                  onClick={() => onGameSelect(game.id)}
                >
                  {/* Game Cover */}
                  <div className={cn('relative rounded overflow-hidden', config.cardSize)}>
                    <img
                      src={game.game?.cover_image || ''}
                      alt={game.game?.title || ''}
                      className="w-full h-full object-cover bg-muted"
                      loading="lazy"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                    
                    {/* Status indicator */}
                    <div className="absolute top-1 right-1">
                      <div 
                        className={cn(
                          'w-2 h-2 rounded-full',
                          game.status === 'playing' && 'bg-blue-500',
                          game.status === 'completed' && 'bg-green-500',
                          game.status === 'backlog' && 'bg-yellow-500',
                          game.status === 'wishlist' && 'bg-purple-500'
                        )}
                      />
                    </div>

                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          interactions?.onQuickPlay?.(game.id);
                        }}
                      >
                        <Play size={10} />
                      </Button>
                    </div>
                  </div>

                  {/* Game title (on hover) */}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity p-1">
                    <p className="text-xs text-white truncate">{game.game?.title}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
});

TimelineGroupComponent.displayName = 'TimelineGroupComponent';

// Timeline item renderer
const TimelineItem = memo<TimelineItemProps>(({ index, style, data }) => {
  const { items, settings, interactions, expandedGroups, selectedGames, onToggleGroup, onGameSelect } = data;
  const item = items[index];

  if (!item) return <div style={style} />;

  // Check if item is a group
  if ('games' in item) {
    const group = item as TimelineGroup;
    return (
      <div style={style}>
        <TimelineGroupComponent
          group={group}
          settings={settings}
          interactions={interactions}
          isExpanded={expandedGroups.has(group.id)}
          selectedGames={selectedGames}
          onToggle={() => onToggleGroup(group.id)}
          onGameSelect={onGameSelect}
        />
      </div>
    );
  }

  // Individual game item (for ungrouped view)
  const game = item as UserGameWithDetails;
  return (
    <div style={style} className="px-4 py-2">
      <PremiumGameCard
        gameData={game}
        variant="compact"
        interactions={{
          ...interactions,
          onSelect: () => onGameSelect(game.id)
        }}
        isSelected={selectedGames.has(game.id)}
        className="w-full max-w-none"
      />
    </div>
  );
});

TimelineItem.displayName = 'TimelineItem';

// Hook for timeline state management
function useTimelineState(
  games: UserGameWithDetails[],
  settings: TimelineViewSettings
) {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  const timelineData = useMemo(() => {
    const groups = groupGamesByDate(games, settings.grouping, settings.timelineType);
    
    // Generate milestones if enabled
    const groupsWithMilestones = groups.map(group => ({
      ...group,
      milestones: settings.showMilestones ? generateMilestones(group) : []
    }));

    return groupsWithMilestones;
  }, [games, settings.grouping, settings.timelineType, settings.showMilestones]);

  const toggleGroup = useCallback((groupId: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  }, []);

  const expandAll = useCallback(() => {
    setExpandedGroups(new Set(timelineData.map(group => group.id)));
  }, [timelineData]);

  const collapseAll = useCallback(() => {
    setExpandedGroups(new Set());
  }, []);

  return {
    timelineData,
    expandedGroups,
    toggleGroup,
    expandAll,
    collapseAll
  };
}

// Hook for container size
function useContainerSize() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setSize({ width, height });
      }
    });

    resizeObserver.observe(container);
    
    const rect = container.getBoundingClientRect();
    setSize({ width: rect.width, height: rect.height });

    return () => resizeObserver.disconnect();
  }, []);

  return { containerRef, size };
}

export const EnhancedTimelineView = memo<EnhancedTimelineViewProps>(({
  games,
  settings,
  onSettingsChange: _onSettingsChange, // eslint-disable-line @typescript-eslint/no-unused-vars
  interactions,
  className
}) => {
  const { containerRef, size } = useContainerSize();
  const { selectedGames, toggleGameSelection } = useEnhancedLibrary();
  const { timelineData, expandedGroups, toggleGroup, expandAll, collapseAll } = useTimelineState(games, settings);

  const config = TIMELINE_CONFIGS.normal;

  // Calculate items for virtual list
  const listItems = useMemo(() => {
    const items: (TimelineGroup | UserGameWithDetails)[] = [];
    
    timelineData.forEach(group => {
      items.push(group);
      if (expandedGroups.has(group.id)) {
        // Add expanded content height calculation here if needed
      }
    });
    
    return items;
  }, [timelineData, expandedGroups]);

  // Memoized data for list items
  const itemData = useMemo(() => ({
    items: listItems,
    settings,
    interactions,
    expandedGroups,
    selectedGames,
    onToggleGroup: toggleGroup,
    onGameSelect: toggleGameSelection
  }), [listItems, settings, interactions, expandedGroups, selectedGames, toggleGroup, toggleGameSelection]);

  if (games.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-64', className)}>
        <div className="text-center">
          <p className="text-lg text-muted-foreground">No games found</p>
          <p className="text-sm text-muted-foreground mt-2">
            Try adjusting your filters or search criteria
          </p>
        </div>
      </div>
    );
  }

  if (size.width === 0 || size.height === 0) {
    return (
      <div ref={containerRef} className={cn('w-full h-full', className)}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-pulse space-y-4 w-full max-w-2xl">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-8 bg-muted rounded" />
                <div className="grid grid-cols-8 gap-2">
                  {Array.from({ length: 8 }).map((_, j) => (
                    <div key={j} className="h-16 bg-muted rounded" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef} 
      className={cn('enhanced-timeline-view w-full h-full flex flex-col', className)}
    >
      {/* Timeline Controls */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Calendar size={20} />
            Timeline
          </h2>
          <Badge variant="secondary">
            {timelineData.length} {settings.grouping === 'day' ? 'days' : `${settings.grouping}s`}
          </Badge>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={expandAll}>
            Expand All
          </Button>
          <Button variant="outline" size="sm" onClick={collapseAll}>
            Collapse All
          </Button>
          <Button variant="outline" size="sm">
            <Filter size={16} className="mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Settings size={16} className="mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Timeline Stats */}
      <div className="flex items-center gap-6 px-4 py-2 bg-muted/30 text-sm">
        <div className="flex items-center gap-1">
          <TrendingUp size={14} />
          <span>{games.length} total games</span>
        </div>
        
        <div className="flex items-center gap-1">
          <Clock size={14} />
          <span>{Math.round(games.reduce((sum, game) => sum + (game.hours_played || 0), 0))}h played</span>
        </div>

        <div className="flex items-center gap-1">
          <Trophy size={14} />
          <span>{games.filter(game => game.status === 'completed').length} completed</span>
        </div>

        {selectedGames.size > 0 && (
          <div className="ml-auto">
            <Badge variant="default">
              {selectedGames.size} selected
            </Badge>
          </div>
        )}
      </div>

      {/* Virtual Timeline List */}
      <div className="flex-1">
        <List
          height={size.height - 120}
          itemCount={listItems.length}
          itemSize={({ index }) => {
            const item = listItems[index];
            if ('games' in item) {
              const group = item as TimelineGroup;
              let height = config.groupHeaderHeight;
              if (expandedGroups.has(group.id)) {
                // Calculate expanded height based on games and milestones
                const gamesPerRow = Math.floor((size.width - 100) / 80); // Approximate
                const gameRows = Math.ceil(group.games.length / gamesPerRow);
                const milestonesHeight = group.milestones?.length ? 40 : 0;
                height += gameRows * 80 + milestonesHeight + 40; // padding
              }
              return height;
            }
            return config.itemHeight;
          }}
          itemData={itemData}
          overscanCount={2}
          className="scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent"
        >
          {TimelineItem}
        </List>
      </div>
    </div>
  );
});

EnhancedTimelineView.displayName = 'EnhancedTimelineView';

// Timeline customization controls
interface TimelineCustomizationControlsProps {
  settings: TimelineViewSettings;
  onSettingsChange: (settings: Partial<TimelineViewSettings>) => void;
  className?: string;
}

export const TimelineCustomizationControls = memo<TimelineCustomizationControlsProps>(({
  settings,
  onSettingsChange,
  className
}) => {
  return (
    <Card className={cn('p-4', className)}>
      <CardContent className="flex flex-wrap gap-4 p-0">
        {/* Timeline Type */}
        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium">Timeline Type</label>
          <div className="flex gap-2">
            {(['added', 'played', 'completed', 'release'] as const).map((type) => (
              <Button
                key={type}
                variant={settings.timelineType === type ? "default" : "outline"}
                size="sm"
                onClick={() => onSettingsChange({ timelineType: type })}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Button>
            ))}
          </div>
        </div>

        {/* Grouping */}
        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium">Group By</label>
          <div className="flex gap-2">
            {(['day', 'week', 'month', 'quarter', 'year'] as const).map((grouping) => (
              <Button
                key={grouping}
                variant={settings.grouping === grouping ? "default" : "outline"}
                size="sm"
                onClick={() => onSettingsChange({ grouping })}
              >
                {grouping.charAt(0).toUpperCase() + grouping.slice(1)}
              </Button>
            ))}
          </div>
        </div>

        {/* Options */}
        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium">Options</label>
          <div className="flex gap-2">
            <Button
              variant={settings.showMilestones ? "default" : "outline"}
              size="sm"
              onClick={() => onSettingsChange({ showMilestones: !settings.showMilestones })}
            >
              Milestones
            </Button>
            <Button
              variant={settings.interactiveTimeline ? "default" : "outline"}
              size="sm"
              onClick={() => onSettingsChange({ interactiveTimeline: !settings.interactiveTimeline })}
            >
              Interactive
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

TimelineCustomizationControls.displayName = 'TimelineCustomizationControls';