import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { useUserCollection } from '@/hooks/useUserCollection';
import { useUserPreferences } from '@/hooks/useUserPreferences';
import { 
  LibraryState, 
  ViewMode, 
  GridViewSettings,
  ListViewSettings,
  CoverFlowViewSettings,
  TimelineViewSettings,
  StatsDashboardSettings,
  Collection,
  FilterPreset,
  EnhancedGameData
} from '@/types/library';
import { DEFAULT_FILTERS } from '@/types/filters';
import { UserGameWithDetails } from '@/types/database';

// Define action types
type LibraryAction =
  | { type: 'SET_VIEW_MODE'; payload: ViewMode }
  | { type: 'UPDATE_GRID_SETTINGS'; payload: Partial<GridViewSettings> }
  | { type: 'UPDATE_LIST_SETTINGS'; payload: Partial<ListViewSettings> }
  | { type: 'UPDATE_COVER_FLOW_SETTINGS'; payload: Partial<CoverFlowViewSettings> }
  | { type: 'UPDATE_TIMELINE_SETTINGS'; payload: Partial<TimelineViewSettings> }
  | { type: 'UPDATE_STATS_SETTINGS'; payload: Partial<StatsDashboardSettings> }
  | { type: 'SET_GAMES'; payload: UserGameWithDetails[] }
  | { type: 'SET_FILTERED_GAMES'; payload: UserGameWithDetails[] }
  | { type: 'UPDATE_FILTERS'; payload: typeof DEFAULT_FILTERS }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_SEARCH_RESULTS'; payload: UserGameWithDetails[] }
  | { type: 'ADD_RECENT_SEARCH'; payload: string }
  | { type: 'TOGGLE_GAME_SELECTION'; payload: string }
  | { type: 'SELECT_ALL_GAMES' }
  | { type: 'CLEAR_SELECTION' }
  | { type: 'TOGGLE_SECTION_EXPANSION'; payload: string }
  | { type: 'SET_LOADING_STATE'; payload: { key: keyof LibraryState['loadingStates']; value: boolean } }
  | { type: 'SET_ERROR'; payload: { key: string; value: string | null } }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<LibraryState['preferences']> }
  | { type: 'SET_COLLECTIONS'; payload: Collection[] }
  | { type: 'ADD_COLLECTION'; payload: Collection }
  | { type: 'UPDATE_COLLECTION'; payload: { id: string; updates: Partial<Collection> } }
  | { type: 'REMOVE_COLLECTION'; payload: string }
  | { type: 'SET_FILTER_PRESETS'; payload: FilterPreset[] }
  | { type: 'ADD_FILTER_PRESET'; payload: FilterPreset }
  | { type: 'UPDATE_FILTER_PRESET'; payload: { id: string; updates: Partial<FilterPreset> } }
  | { type: 'REMOVE_FILTER_PRESET'; payload: string };

// Default state values
const defaultGridSettings: GridViewSettings = {
  id: 'grid',
  name: 'Grid View',
  icon: 'grid',
  description: 'Display games in a customizable grid layout',
  density: 'normal',
  aspectRatio: 'portrait',
  showMetadata: true,
  enableHoverEffects: true,
  cardStyle: 'modern',
  overlayPosition: 'bottom',
  animationStyle: 'subtle',
  colorScheme: 'auto'
};

const defaultListSettings: ListViewSettings = {
  id: 'list',
  name: 'List View',
  icon: 'list',
  description: 'Display games in a detailed list with customizable columns',
  columns: [
    { key: 'title', label: 'Title', width: 'auto', sortable: true, filterable: true, visible: true, order: 0 },
    { key: 'platform', label: 'Platform', width: 150, sortable: true, filterable: true, visible: true, order: 1 },
    { key: 'developer', label: 'Developer', width: 150, sortable: true, filterable: true, visible: true, order: 2 },
    { key: 'releaseDate', label: 'Release Date', width: 120, sortable: true, filterable: true, visible: true, order: 3 },
    { key: 'status', label: 'Status', width: 100, sortable: true, filterable: true, visible: true, order: 4 },
    { key: 'rating', label: 'Rating', width: 100, sortable: true, filterable: true, visible: true, order: 5 },
    { key: 'actions', label: 'Actions', width: 120, sortable: false, filterable: false, visible: true, order: 6 }
  ],
  showThumbnails: true,
  compactMode: false,
  groupBy: 'none'
};

const defaultCoverFlowSettings: CoverFlowViewSettings = {
  id: 'coverFlow',
  name: 'Cover Flow',
  icon: 'image',
  description: 'Browse your games with an immersive cover flow experience',
  centerIndex: 0,
  visibleCount: 5,
  perspective: 800,
  autoRotate: false,
  rotationSpeed: 5000,
  touchEnabled: true,
  keyboardNavigation: true
};

const defaultTimelineSettings: TimelineViewSettings = {
  id: 'timeline',
  name: 'Timeline',
  icon: 'calendar',
  description: 'View your games organized chronologically',
  timelineType: 'added',
  grouping: 'month',
  showMilestones: true,
  interactiveTimeline: true
};

const defaultStatsDashboardSettings: StatsDashboardSettings = {
  id: 'stats',
  name: 'Stats Dashboard',
  icon: 'bar-chart',
  description: 'Visualize your gaming habits and collection statistics',
  widgets: [],
  layout: {
    columns: 12,
    rowHeight: 30,
    margin: [10, 10],
    containerPadding: [10, 10],
    isDraggable: true,
    isResizable: true,
    isBounded: true
  },
  timeRange: {
    start: null,
    end: null,
    preset: 'allTime'
  },
  comparisons: []
};

const initialState: LibraryState = {
  viewMode: 'grid',
  viewSettings: {
    grid: defaultGridSettings,
    list: defaultListSettings,
    coverFlow: defaultCoverFlowSettings,
    timeline: defaultTimelineSettings,
    stats: defaultStatsDashboardSettings
  },
  games: [],
  filteredGames: [],
  filters: DEFAULT_FILTERS,
  searchState: {
    query: '',
    isSearching: false,
    results: [],
    suggestions: [],
    recentSearches: [],
    savedSearches: []
  },
  selectedGames: new Set<string>(),
  expandedSections: new Set<string>(),
  loadingStates: {
    isLoadingGames: false,
    isLoadingFilters: false,
    isApplyingFilters: false,
    isChangingView: false,
    isLoadingCollections: false,
    errors: {}
  },
  preferences: {
    defaultViewMode: 'grid',
    rememberFilters: true,
    showTags: true,
    showRatings: true,
    showPlatformIcons: true,
    enableAnimations: true,
    cardSize: 'medium',
    theme: 'system'
  },
  customCollections: [],
  savedPresets: []
};

// Reducer function
function libraryReducer(state: LibraryState, action: LibraryAction): LibraryState {
  switch (action.type) {
    case 'SET_VIEW_MODE':
      return {
        ...state,
        viewMode: action.payload
      };
    case 'UPDATE_GRID_SETTINGS':
      return {
        ...state,
        viewSettings: {
          ...state.viewSettings,
          grid: {
            ...state.viewSettings.grid,
            ...action.payload
          }
        }
      };
    case 'UPDATE_LIST_SETTINGS':
      return {
        ...state,
        viewSettings: {
          ...state.viewSettings,
          list: {
            ...state.viewSettings.list,
            ...action.payload
          }
        }
      };
    case 'UPDATE_COVER_FLOW_SETTINGS':
      return {
        ...state,
        viewSettings: {
          ...state.viewSettings,
          coverFlow: {
            ...state.viewSettings.coverFlow,
            ...action.payload
          }
        }
      };
    case 'UPDATE_TIMELINE_SETTINGS':
      return {
        ...state,
        viewSettings: {
          ...state.viewSettings,
          timeline: {
            ...state.viewSettings.timeline,
            ...action.payload
          }
        }
      };
    case 'UPDATE_STATS_SETTINGS':
      return {
        ...state,
        viewSettings: {
          ...state.viewSettings,
          stats: {
            ...state.viewSettings.stats,
            ...action.payload
          }
        }
      };
    case 'SET_GAMES':
      return {
        ...state,
        games: action.payload,
        filteredGames: action.payload // Reset filtered games when setting new games
      };
    case 'SET_FILTERED_GAMES':
      return {
        ...state,
        filteredGames: action.payload
      };
    case 'UPDATE_FILTERS':
      return {
        ...state,
        filters: action.payload
      };
    case 'SET_SEARCH_QUERY':
      return {
        ...state,
        searchState: {
          ...state.searchState,
          query: action.payload
        }
      };
    case 'SET_SEARCH_RESULTS':
      return {
        ...state,
        searchState: {
          ...state.searchState,
          results: action.payload,
          isSearching: false
        }
      };
    case 'ADD_RECENT_SEARCH': {
      // Add to recent searches and remove duplicates
      const updatedRecentSearches = [
        action.payload,
        ...state.searchState.recentSearches.filter(search => search !== action.payload)
      ].slice(0, 10); // Keep only the 10 most recent searches
      
      return {
        ...state,
        searchState: {
          ...state.searchState,
          recentSearches: updatedRecentSearches
        }
      };
    }
    case 'TOGGLE_GAME_SELECTION': {
      const newSelectedGames = new Set(state.selectedGames);
      if (newSelectedGames.has(action.payload)) {
        newSelectedGames.delete(action.payload);
      } else {
        newSelectedGames.add(action.payload);
      }
      return {
        ...state,
        selectedGames: newSelectedGames
      };
    }
    case 'SELECT_ALL_GAMES': {
      const allGameIds = state.filteredGames.map(game => game.id);
      return {
        ...state,
        selectedGames: new Set(allGameIds)
      };
    }
    case 'CLEAR_SELECTION':
      return {
        ...state,
        selectedGames: new Set()
      };
    case 'TOGGLE_SECTION_EXPANSION': {
      const newExpandedSections = new Set(state.expandedSections);
      if (newExpandedSections.has(action.payload)) {
        newExpandedSections.delete(action.payload);
      } else {
        newExpandedSections.add(action.payload);
      }
      return {
        ...state,
        expandedSections: newExpandedSections
      };
    }
    case 'SET_LOADING_STATE':
      return {
        ...state,
        loadingStates: {
          ...state.loadingStates,
          [action.payload.key]: action.payload.value
        }
      };
    case 'SET_ERROR':
      return {
        ...state,
        loadingStates: {
          ...state.loadingStates,
          errors: {
            ...state.loadingStates.errors,
            [action.payload.key]: action.payload.value
          }
        }
      };
    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: {
          ...state.preferences,
          ...action.payload
        }
      };
    case 'SET_COLLECTIONS':
      return {
        ...state,
        customCollections: action.payload
      };
    case 'ADD_COLLECTION':
      return {
        ...state,
        customCollections: [...state.customCollections, action.payload]
      };
    case 'UPDATE_COLLECTION':
      return {
        ...state,
        customCollections: state.customCollections.map(collection => 
          collection.id === action.payload.id 
            ? { ...collection, ...action.payload.updates } 
            : collection
        )
      };
    case 'REMOVE_COLLECTION':
      return {
        ...state,
        customCollections: state.customCollections.filter(
          collection => collection.id !== action.payload
        )
      };
    case 'SET_FILTER_PRESETS':
      return {
        ...state,
        savedPresets: action.payload
      };
    case 'ADD_FILTER_PRESET':
      return {
        ...state,
        savedPresets: [...state.savedPresets, action.payload]
      };
    case 'UPDATE_FILTER_PRESET':
      return {
        ...state,
        savedPresets: state.savedPresets.map(preset => 
          preset.id === action.payload.id 
            ? { ...preset, ...action.payload.updates } 
            : preset
        )
      };
    case 'REMOVE_FILTER_PRESET':
      return {
        ...state,
        savedPresets: state.savedPresets.filter(
          preset => preset.id !== action.payload
        )
      };
    default:
      return state;
  }
}

// Create context
interface LibraryContextType {
  state: LibraryState;
  dispatch: React.Dispatch<LibraryAction>;
  enhancedGames: EnhancedGameData[];
  isLoading: boolean;
  error: unknown;
}

const LibraryContext = createContext<LibraryContextType | undefined>(undefined);

// Provider component
interface LibraryProviderProps {
  children: ReactNode;
}

export function LibraryProvider({ children }: LibraryProviderProps) {
  const [state, dispatch] = useReducer(libraryReducer, initialState);
  const { userCollection, isLoading, error } = useUserCollection();
  const { preferences } = useUserPreferences();

  // Enhance game data with additional metadata
  const enhancedGames: EnhancedGameData[] = userCollection.map(game => {
    // This is where we would add additional metadata to each game
    // For now, we're just casting to EnhancedGameData
    return game as EnhancedGameData;
  });

  // Update games when collection changes
  useEffect(() => {
    if (userCollection && !isLoading) {
      dispatch({ type: 'SET_GAMES', payload: userCollection });
      dispatch({ type: 'SET_LOADING_STATE', payload: { key: 'isLoadingGames', value: false } });
    } else {
      dispatch({ type: 'SET_LOADING_STATE', payload: { key: 'isLoadingGames', value: true } });
    }
  }, [userCollection, isLoading]);

  // Update preferences from user settings
  useEffect(() => {
    if (preferences) {
      // Map user preferences to library preferences
      dispatch({
        type: 'UPDATE_PREFERENCES',
        payload: {
          defaultViewMode: 'grid' as ViewMode, // Default to grid for now
          theme: preferences.theme || 'system',
          showTags: preferences.display?.show_metacritic || true,
          showRatings: preferences.display?.show_metacritic || true,
          showPlatformIcons: true,
          enableAnimations: true,
          cardSize: 'medium',
          rememberFilters: true,
          showScreenshots: preferences.display?.show_screenshots || false
        }
      });
    }
  }, [preferences]);

  return (
    <LibraryContext.Provider value={{ state, dispatch, enhancedGames, isLoading, error }}>
      {children}
    </LibraryContext.Provider>
  );
}

// Custom hook to use the library context
export function useLibrary() {
  const context = useContext(LibraryContext);
  if (context === undefined) {
    throw new Error('useLibrary must be used within a LibraryProvider');
  }
  return context;
}