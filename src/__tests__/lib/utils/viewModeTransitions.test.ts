import { describe, it, expect } from 'vitest';
import {
  preserveViewContext,
  shouldResetScrollOnTransition,
  getViewModeTransitionMessage,
  isValidViewModeTransition,
  ViewModeContext
} from '@/lib/utils/viewModeTransitions';
import { ViewMode } from '@/pages/Library/types';

describe('viewModeTransitions', () => {
  const mockContext: ViewModeContext = {
    searchQuery: 'test game',
    sortOptions: { field: 'title', direction: 'asc' },
    filters: { genre: 'Action' },
    selectedGameId: 'game-123',
    scrollPosition: 100,
  };

  describe('preserveViewContext', () => {
    it('should preserve all context for platform to platform-family transition', () => {
      const result = preserveViewContext('platform', 'platform-family', mockContext);
      
      expect(result.searchQuery).toBe(mockContext.searchQuery);
      expect(result.sortOptions).toEqual(mockContext.sortOptions);
      expect(result.filters).toEqual(mockContext.filters);
      expect(result.selectedGameId).toBe(mockContext.selectedGameId);
    });

    it('should preserve all context for platform-family to platform transition', () => {
      const result = preserveViewContext('platform-family', 'platform', mockContext);
      
      expect(result.searchQuery).toBe(mockContext.searchQuery);
      expect(result.sortOptions).toEqual(mockContext.sortOptions);
      expect(result.filters).toEqual(mockContext.filters);
      expect(result.selectedGameId).toBe(mockContext.selectedGameId);
    });

    it('should adjust sort options when transitioning from status to platform views', () => {
      const statusContext: ViewModeContext = {
        ...mockContext,
        sortOptions: { field: 'status', direction: 'desc' },
      };

      const result = preserveViewContext('status', 'platform', statusContext);
      
      expect(result.searchQuery).toBe(statusContext.searchQuery);
      expect(result.filters).toEqual(statusContext.filters);
      expect(result.sortOptions.field).toBe('title'); // Should be adjusted
      expect(result.sortOptions.direction).toBe('desc'); // Direction preserved
    });

    it('should adjust sort options when transitioning to status view', () => {
      const platformContext: ViewModeContext = {
        ...mockContext,
        sortOptions: { field: 'platform', direction: 'asc' },
      };

      const result = preserveViewContext('platform', 'status', platformContext);
      
      expect(result.searchQuery).toBe(platformContext.searchQuery);
      expect(result.filters).toEqual(platformContext.filters);
      expect(result.sortOptions.field).toBe('status'); // Should be adjusted
      expect(result.sortOptions.direction).toBe('asc'); // Direction preserved
    });

    it('should preserve universal sort fields across all transitions', () => {
      const universalFields = ['title', 'date_added', 'release_date', 'metacritic_score'];
      
      universalFields.forEach(field => {
        const contextWithUniversalField: ViewModeContext = {
          ...mockContext,
          sortOptions: { field, direction: 'desc' },
        };

        const result = preserveViewContext('status', 'platform', contextWithUniversalField);
        expect(result.sortOptions.field).toBe(field);
        expect(result.sortOptions.direction).toBe('desc');
      });
    });
  });

  describe('shouldResetScrollOnTransition', () => {
    it('should reset scroll when transitioning from status to platform views', () => {
      expect(shouldResetScrollOnTransition('status', 'platform')).toBe(true);
      expect(shouldResetScrollOnTransition('status', 'platform-family')).toBe(true);
    });

    it('should reset scroll when transitioning from platform views to status', () => {
      expect(shouldResetScrollOnTransition('platform', 'status')).toBe(true);
      expect(shouldResetScrollOnTransition('platform-family', 'status')).toBe(true);
    });

    it('should not reset scroll between platform and platform-family', () => {
      expect(shouldResetScrollOnTransition('platform', 'platform-family')).toBe(false);
      expect(shouldResetScrollOnTransition('platform-family', 'platform')).toBe(false);
    });

    it('should not reset scroll for same view mode', () => {
      expect(shouldResetScrollOnTransition('status', 'status')).toBe(false);
      expect(shouldResetScrollOnTransition('platform', 'platform')).toBe(false);
      expect(shouldResetScrollOnTransition('platform-family', 'platform-family')).toBe(false);
    });
  });

  describe('getViewModeTransitionMessage', () => {
    it('should return appropriate messages for view mode changes', () => {
      expect(getViewModeTransitionMessage('status', 'platform')).toBe('Switched to Platform View');
      expect(getViewModeTransitionMessage('platform', 'platform-family')).toBe('Switched to Platform Family View');
      expect(getViewModeTransitionMessage('platform-family', 'status')).toBe('Switched to Status View');
    });

    it('should return null for same view mode', () => {
      expect(getViewModeTransitionMessage('status', 'status')).toBe(null);
      expect(getViewModeTransitionMessage('platform', 'platform')).toBe(null);
      expect(getViewModeTransitionMessage('platform-family', 'platform-family')).toBe(null);
    });
  });

  describe('isValidViewModeTransition', () => {
    const validModes: ViewMode[] = ['status', 'platform', 'platform-family'];

    it('should validate all valid view mode combinations', () => {
      validModes.forEach(fromMode => {
        validModes.forEach(toMode => {
          expect(isValidViewModeTransition(fromMode, toMode)).toBe(true);
        });
      });
    });

    it('should reject invalid view modes', () => {
      expect(isValidViewModeTransition('invalid' as ViewMode, 'status')).toBe(false);
      expect(isValidViewModeTransition('status', 'invalid' as ViewMode)).toBe(false);
      expect(isValidViewModeTransition('invalid' as ViewMode, 'invalid' as ViewMode)).toBe(false);
    });
  });
});