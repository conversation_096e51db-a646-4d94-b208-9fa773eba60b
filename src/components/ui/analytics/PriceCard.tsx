import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Skeleton } from '@/components/ui/base/skeleton';
import { 
  TrendingDown, 
  ExternalLink, 
  Bell, 
  BellOff,
  RefreshCw,
  Tag
} from 'lucide-react';
import { usePriceTracking, usePriceAlerts, formatPrice, formatDiscount, getDealBadgeColor } from '@/hooks/usePriceTracking';
import { PriceAlert, PriceEntry } from '@/types/database';

interface PriceCardProps {
  gameId: string;
  gameName: string;
  showAlerts?: boolean;
  className?: string;
}

export const PriceCard: React.FC<PriceCardProps> = ({ 
  gameId, 
  gameName, 
  showAlerts = true,
  className = ''
}) => {
  const [showAllPrices, setShowAllPrices] = useState(false);
  
  const {
    currentPrices,
    lowestPrice,
    highestPrice,
    averagePrice,
    hasDeals,
    isLoadingPrices,
    isUpdatingPrices,
    updatePrices,
    pricesError
  } = usePriceTracking(gameId, gameName);

  const {
    alerts,
    isLoadingAlerts,
    createAlert,
    deleteAlert,
    isCreatingAlert
  } = usePriceAlerts();

  // Check if user has an alert for this game
  const hasAlert = alerts.some((alert: PriceAlert) => alert.gameId === gameId && alert.isActive);

  const handleToggleAlert = () => {
    if (hasAlert) {
      const alert = alerts.find((a: PriceAlert) => a.gameId === gameId && a.isActive);
      if (alert) {
        deleteAlert(alert.id!);
      }
    } else {
      // Create alert with lowest current price as target
      const targetPrice = lowestPrice ? lowestPrice.price * 0.9 : 0; // 10% below lowest
      createAlert({
        userId: '', // Will be set in the hook
        gameId,
        targetPrice,
        currency: lowestPrice?.currency || 'USD',
        isActive: true,
        notificationMethods: ['toast', 'email']
      });
    }
  };

  if (isLoadingPrices) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Price Tracking
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (pricesError) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Price Tracking
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-muted-foreground mb-4">
              Unable to load price data
            </p>
            <Button onClick={() => updatePrices()} disabled={isUpdatingPrices}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isUpdatingPrices ? 'animate-spin' : ''}`} />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (currentPrices.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Price Tracking
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-muted-foreground mb-4">
              No price data available for this game
            </p>
            <Button onClick={() => updatePrices()} disabled={isUpdatingPrices}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isUpdatingPrices ? 'animate-spin' : ''}`} />
              Check Prices
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const displayedPrices = showAllPrices ? currentPrices : currentPrices.slice(0, 3);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Price Tracking
            {hasDeals && (
              <Badge className="bg-red-500 text-white">
                <TrendingDown className="h-3 w-3 mr-1" />
                On Sale
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {showAlerts && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleToggleAlert}
                disabled={isCreatingAlert || isLoadingAlerts}
              >
                {hasAlert ? (
                  <BellOff className="h-4 w-4" />
                ) : (
                  <Bell className="h-4 w-4" />
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updatePrices()}
              disabled={isUpdatingPrices}
            >
              <RefreshCw className={`h-4 w-4 ${isUpdatingPrices ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Price Summary */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-sm text-muted-foreground">Lowest</p>
              <p className="font-semibold text-green-600">
                {lowestPrice ? formatPrice(lowestPrice.price, lowestPrice.currency) : 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Average</p>
              <p className="font-semibold">
                {averagePrice > 0 ? formatPrice(averagePrice, lowestPrice?.currency) : 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Highest</p>
              <p className="font-semibold text-red-600">
                {highestPrice ? formatPrice(highestPrice.price, highestPrice.currency) : 'N/A'}
              </p>
            </div>
          </div>

          {/* Individual Store Prices */}
          <div className="space-y-2">
            {displayedPrices.map((price: PriceEntry, index: number) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div>
                    <p className="font-medium">{price.shop_name}</p>
                    <p className="text-sm text-muted-foreground">
                      Updated: {new Date(price.last_updated).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-right">
                    <p className="font-semibold">
                      {formatPrice(price.price, price.currency)}
                    </p>
                    {/* Mock discount badge */}
                    {Math.random() > 0.7 && (
                      <Badge className={getDealBadgeColor(25)}>
                        {formatDiscount(25)}
                      </Badge>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open(price.shop_url, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* Show More/Less Button */}
          {currentPrices.length > 3 && (
            <Button
              variant="outline"
              onClick={() => setShowAllPrices(!showAllPrices)}
              className="w-full"
            >
              {showAllPrices ? 'Show Less' : `Show ${currentPrices.length - 3} More`}
            </Button>
          )}

          {/* Alert Information */}
          {showAlerts && hasAlert && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Bell className="h-4 w-4 text-blue-600" />
              <p className="text-sm text-blue-600">
                Price alert active - you'll be notified when the price drops!
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};