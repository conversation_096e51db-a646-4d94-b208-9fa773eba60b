/**
 * Base Platform Service Class
 * Shared functionality for all gaming platform import services
 */

import { apiKeyStore, type Platform, type KeyType } from '@/lib/apiKeyStore';
import { supabase } from '@/lib/supabase';
import { type Game } from '@/types';

export interface ImportProgress {
  phase: 'authenticating' | 'fetching' | 'processing' | 'storing' | 'completed' | 'error';
  current: number;
  total: number;
  message: string;
  errors?: string[];
}

export interface ImportResult {
  success: boolean;
  gamesImported: number;
  gamesUpdated: number;
  duplicatesFound: number;
  errors: string[];
  importId: string;
  message?: string;
}

export interface PlatformGameData {
  platformId: string;
  name: string;
  description?: string;
  releaseDate?: string;
  developer?: string;
  publisher?: string;
  genres?: string[];
  platforms?: string[];
  imageUrl?: string;
  achievementCount?: number;
  playtime?: number;
  lastPlayed?: string;
  metadata?: Record<string, unknown>;
}

export interface AuthenticationResult {
  success: boolean;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
  error?: string;
}

export abstract class BasePlatformService {
  protected platform: Platform;
  protected userId: string | null = null;
  
  constructor(platform: Platform) {
    this.platform = platform;
  }

  /**
   * Initialize the service with current user
   */
  async initialize(): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    this.userId = user?.id || null;
    
    if (!this.userId) {
      throw new Error('User must be authenticated for platform imports');
    }
  }

  /**
   * Get API key for the platform
   */
  protected async getApiKey(keyType: KeyType): Promise<string | null> {
    return await apiKeyStore.getKeyWithFallback(this.platform, keyType);
  }

  /**
   * Check if platform is configured with required API keys
   */
  async isConfigured(): Promise<boolean> {
    await this.initialize();
    
    const requiredKeys = this.getRequiredKeys();
    for (const keyType of requiredKeys) {
      const key = await this.getApiKey(keyType);
      if (!key) {
        return false;
      }
    }
    return true;
  }

  /**
   * Abstract method to define required API keys for the platform
   */
  protected abstract getRequiredKeys(): KeyType[];

  /**
   * Abstract method for platform authentication
   */
  abstract authenticate(): Promise<AuthenticationResult>;

  /**
   * Abstract method to fetch user's game library from platform
   */
  abstract fetchUserLibrary(progressCallback?: (progress: ImportProgress) => void): Promise<PlatformGameData[]>;

  /**
   * Convert platform-specific game data to our internal Game format
   */
  protected abstract convertToGame(platformGame: PlatformGameData): Partial<Game>;

  /**
   * Create import history record
   */
  protected async createImportRecord(status: 'pending' | 'running' | 'completed' | 'failed' = 'pending'): Promise<string> {
    const { data, error } = await supabase
      .from('import_history')
      .insert({
        user_id: this.userId,
        platform: this.platform,
        status,
        started_at: new Date().toISOString(),
      })
      .select('id')
      .single();

    if (error) {
      throw new Error(`Failed to create import record: ${error.message}`);
    }

    return data.id;
  }

  /**
   * Update import history record
   */
  protected async updateImportRecord(
    importId: string, 
    updates: {
      status?: 'pending' | 'running' | 'completed' | 'failed';
      gamesImported?: number;
      gamesUpdated?: number;
      duplicatesFound?: number;
      errorMessage?: string;
      metadata?: Record<string, unknown>;
    }
  ): Promise<void> {
    // Convert camelCase to snake_case for database columns
    const updateData: Record<string, unknown> = {};
    
    if (updates.status !== undefined) updateData.status = updates.status;
    if (updates.gamesImported !== undefined) updateData.games_imported = updates.gamesImported;
    if (updates.gamesUpdated !== undefined) updateData.games_updated = updates.gamesUpdated;
    if (updates.duplicatesFound !== undefined) updateData.duplicates_found = updates.duplicatesFound;
    if (updates.errorMessage !== undefined) updateData.error_message = updates.errorMessage;
    if (updates.metadata !== undefined) updateData.metadata = updates.metadata;
    
    if (updates.status === 'completed' || updates.status === 'failed') {
      updateData.completed_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('import_history')
      .update(updateData)
      .eq('id', importId);

    if (error) {
      console.error('Failed to update import record:', error);
    }
  }

  /**
   * Find or create game in database
   */
  protected async findOrCreateGame(gameData: Partial<Game>): Promise<{ game: Game; isNew: boolean }> {
    // First, try to find existing game by platform ID
    const platformIds = gameData.platform_ids || {};
    
    if (platformIds[this.platform]) {
      const { data: existingGame } = await supabase
        .from('games')
        .select('*')
        .contains('platform_ids', { [this.platform]: platformIds[this.platform] })
        .single();

      if (existingGame) {
        return { game: existingGame as Game, isNew: false };
      }
    }

    // If not found by platform ID, try to find by name and approximate release year
    if (gameData.name) {
      const { data: similarGames } = await supabase
        .from('games')
        .select('*')
        .ilike('name', `%${gameData.name}%`)
        .limit(5);

      // Simple fuzzy matching - could be enhanced with ML later
      const exactMatch = similarGames?.find(game => 
        game.name.toLowerCase().trim() === gameData.name!.toLowerCase().trim()
      );

      if (exactMatch) {
        // Update platform_ids for existing game
        const updatedPlatformIds = { ...exactMatch.platform_ids, ...platformIds };
        
        const { data: updatedGame } = await supabase
          .from('games')
          .update({ platform_ids: updatedPlatformIds })
          .eq('id', exactMatch.id)
          .select('*')
          .single();

        return { game: updatedGame as Game, isNew: false };
      }
    }

    // Create new game
    const { data: newGame, error } = await supabase
      .from('games')
      .insert({
        ...gameData,
        platform_ids: platformIds,
        created_at: new Date().toISOString(),
      })
      .select('*')
      .single();

    if (error) {
      throw new Error(`Failed to create game: ${error.message}`);
    }

    return { game: newGame as Game, isNew: true };
  }

  /**
   * Add game to user's library
   */
  protected async addToUserLibrary(
    gameId: string, 
    platformData: Record<string, unknown> = {},
    status: 'want_to_play' | 'playing' | 'completed' | 'dropped' = 'want_to_play'
  ): Promise<void> {
    const { error } = await supabase
      .from('user_games')
      .upsert({
        user_id: this.userId,
        game_id: gameId,
        status,
        platform_data: {
          ...platformData,
          import_platform: this.platform,
          imported_at: new Date().toISOString(),
        },
        date_added: new Date().toISOString(),
      });

    if (error && error.code !== '23505') { // Ignore duplicate key errors
      throw new Error(`Failed to add game to library: ${error.message}`);
    }
  }

  /**
   * Main import method that orchestrates the entire process
   */
  async importLibrary(progressCallback?: (progress: ImportProgress) => void): Promise<ImportResult> {
    await this.initialize();

    const importId = await this.createImportRecord('running');
    let gamesImported = 0;
    let gamesUpdated = 0;
    const duplicatesFound = 0;
    const errors: string[] = [];

    try {
      // Update progress
      progressCallback?.({
        phase: 'authenticating',
        current: 0,
        total: 100,
        message: `Authenticating with ${this.platform}...`,
      });

      // Authenticate
      const authResult = await this.authenticate();
      if (!authResult.success) {
        throw new Error(authResult.error || 'Authentication failed');
      }

      // Fetch library
      progressCallback?.({
        phase: 'fetching',
        current: 20,
        total: 100,
        message: `Fetching library from ${this.platform}...`,
      });

      const platformGames = await this.fetchUserLibrary(progressCallback);

      // Process games
      progressCallback?.({
        phase: 'processing',
        current: 60,
        total: 100,
        message: 'Processing and storing games...',
      });

      for (let i = 0; i < platformGames.length; i++) {
        try {
          const platformGame = platformGames[i];
          const gameData = this.convertToGame(platformGame);
          
          // Ensure platform ID is included
          gameData.platform_ids = {
            ...gameData.platform_ids,
            [this.platform]: platformGame.platformId,
          };

          const { game, isNew } = await this.findOrCreateGame(gameData);
          
          if (isNew) {
            gamesImported++;
          } else {
            gamesUpdated++;
          }

          // Add to user library
          await this.addToUserLibrary(game.id, platformGame.metadata);

          // Update progress
          progressCallback?.({
            phase: 'processing',
            current: 60 + ((i + 1) / platformGames.length) * 30,
            total: 100,
            message: `Processed ${i + 1}/${platformGames.length} games`,
          });

        } catch (error) {
          errors.push(`Failed to process game: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Update import record
      await this.updateImportRecord(importId, {
        status: 'completed',
        games_imported: gamesImported,
        games_updated: gamesUpdated,
        duplicates_found: duplicatesFound,
        metadata: {
          platform_games_count: platformGames.length,
          errors_count: errors.length,
        },
      });

      progressCallback?.({
        phase: 'completed',
        current: 100,
        total: 100,
        message: `Import completed: ${gamesImported} new games, ${gamesUpdated} updated`,
      });

      return {
        success: true,
        gamesImported,
        gamesUpdated,
        duplicatesFound,
        errors,
        importId,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      errors.push(errorMessage);

      await this.updateImportRecord(importId, {
        status: 'failed',
        error_message: errorMessage,
        games_imported: gamesImported,
        games_updated: gamesUpdated,
      });

      progressCallback?.({
        phase: 'error',
        current: 0,
        total: 100,
        message: `Import failed: ${errorMessage}`,
        errors,
      });

      return {
        success: false,
        gamesImported,
        gamesUpdated,
        duplicatesFound,
        errors,
        importId,
      };
    }
  }

  /**
   * Get import history for this platform
   */
  async getImportHistory(): Promise<unknown[]> {
    await this.initialize();

    const { data, error } = await supabase
      .from('import_history')
      .select('*')
      .eq('user_id', this.userId)
      .eq('platform', this.platform)
      .order('started_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch import history: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Utility method for making HTTP requests with retry logic
   */
  protected async makeRequest(
    url: string, 
    options: RequestInit = {},
    retries: number = 3
  ): Promise<Response> {
    let lastError: Error;

    for (let i = 0; i <= retries; i++) {
      try {
        const response = await fetch(url, {
          ...options,
          headers: {
            'Content-Type': 'application/json',
            ...options.headers,
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown request error');
        
        if (i < retries) {
          // Exponential backoff
          const delay = Math.pow(2, i) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }
}