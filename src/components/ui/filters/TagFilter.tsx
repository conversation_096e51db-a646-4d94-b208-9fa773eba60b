import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/base/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/base/command';
import { Switch } from '@/components/ui/base/switch';
import { Label } from '@/components/ui/base/label';
import { Separator } from '@/components/ui/base/separator';
import { useUserTags } from '@/hooks/useUserTags';
import { CustomTagsFilter } from '@/types/filters';
import { Hash, Plus, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TagFilterProps {
  filter: CustomTagsFilter;
  onFilterChange: (filter: CustomTagsFilter) => void;
  className?: string;
}

export function TagFilter({ filter, onFilterChange, className }: TagFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  
  const { tags, popularTags, isLoading } = useUserTags();

  // Get selected tag objects
  const selectedTags = tags.filter(tag => filter.tags.includes(tag.id));

  // Filter available tags
  const filteredTags = tags.filter(tag => 
    tag.name.toLowerCase().includes(searchValue.toLowerCase()) &&
    !filter.tags.includes(tag.id)
  );

  // Handle tag selection
  const handleTagSelect = (tagId: string) => {
    const newTags = [...filter.tags, tagId];
    onFilterChange({
      ...filter,
      tags: newTags,
      enabled: newTags.length > 0 || filter.includeUntagged
    });
  };

  // Handle tag removal
  const handleTagRemove = (tagId: string) => {
    const newTags = filter.tags.filter(id => id !== tagId);
    onFilterChange({
      ...filter,
      tags: newTags,
      enabled: newTags.length > 0 || filter.includeUntagged
    });
  };

  // Handle mode change
  const handleModeChange = (mode: 'include' | 'exclude') => {
    onFilterChange({ ...filter, mode });
  };

  // Handle include untagged toggle
  const handleIncludeUntaggedChange = (includeUntagged: boolean) => {
    onFilterChange({
      ...filter,
      includeUntagged,
      enabled: filter.tags.length > 0 || includeUntagged
    });
  };

  // Clear all filters
  const handleClear = () => {
    onFilterChange({
      enabled: false,
      tags: [],
      mode: 'include',
      includeUntagged: false
    });
  };

  const activeCount = filter.tags.length + (filter.includeUntagged ? 1 : 0);

  return (
    <div className={cn('space-y-2', className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={filter.enabled ? 'default' : 'outline'}
            size="sm"
            className="h-8 border-dashed"
          >
            <Hash className="mr-2 h-4 w-4" />
            Tags
            {activeCount > 0 && (
              <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
                {activeCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <div className="p-4 space-y-4">
            {/* Filter Mode */}
            <div className="flex items-center space-x-2">
              <Label className="text-sm font-medium">Mode:</Label>
              <Button
                variant={filter.mode === 'include' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleModeChange('include')}
              >
                Include
              </Button>
              <Button
                variant={filter.mode === 'exclude' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleModeChange('exclude')}
              >
                Exclude
              </Button>
            </div>

            {/* Include Untagged Option */}
            <div className="flex items-center space-x-2">
              <Switch
                checked={filter.includeUntagged}
                onCheckedChange={handleIncludeUntaggedChange}
              />
              <Label className="text-sm">Include games without tags</Label>
            </div>

            <Separator />

            {/* Selected Tags */}
            {selectedTags.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Selected Tags:</Label>
                <div className="flex flex-wrap gap-1">
                  {selectedTags.map(tag => (
                    <Badge
                      key={tag.id}
                      variant="secondary"
                      className="flex items-center gap-1 text-xs"
                      style={{ backgroundColor: `${tag.color}20`, borderColor: tag.color }}
                    >
                      <span>{tag.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 hover:bg-transparent"
                        onClick={() => handleTagRemove(tag.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Tag Search */}
            <Command>
              <CommandInput
                placeholder="Search tags..."
                value={searchValue}
                onValueChange={setSearchValue}
              />
              <CommandList className="max-h-32">
                <CommandEmpty>
                  {isLoading ? 'Loading tags...' : 'No tags found'}
                </CommandEmpty>
                
                {filteredTags.length > 0 && (
                  <CommandGroup>
                    {filteredTags.map(tag => (
                      <CommandItem
                        key={tag.id}
                        value={tag.name}
                        onSelect={() => handleTagSelect(tag.id)}
                        className="cursor-pointer"
                      >
                        <div className="flex items-center gap-2 w-full">
                          <div
                            className="h-3 w-3 rounded-full"
                            style={{ backgroundColor: tag.color }}
                          />
                          <span className="flex-1">{tag.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {tag.usage_count}
                          </span>
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}
              </CommandList>
            </Command>

            {/* Popular Tags */}
            {popularTags.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Popular Tags:</Label>
                <div className="flex flex-wrap gap-1">
                  {popularTags.slice(0, 6).map(tag => (
                    <Button
                      key={tag.name}
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={() => {
                        const matchingTag = tags.find(t => t.name === tag.name);
                        if (matchingTag && !filter.tags.includes(matchingTag.id)) {
                          handleTagSelect(matchingTag.id);
                        }
                      }}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      {tag.name}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClear}
                disabled={!filter.enabled}
                className="flex-1"
              >
                Clear
              </Button>
              <Button
                size="sm"
                onClick={() => setIsOpen(false)}
                className="flex-1"
              >
                Done
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Active Filter Display */}
      {filter.enabled && (
        <div className="flex flex-wrap gap-1 text-xs text-muted-foreground">
          <span>
            {filter.mode === 'include' ? 'Including' : 'Excluding'} tags:
          </span>
          {selectedTags.map(tag => (
            <Badge
              key={tag.id}
              variant="outline"
              className="h-5 px-1.5 text-xs"
              style={{ borderColor: tag.color }}
            >
              {tag.name}
            </Badge>
          ))}
          {filter.includeUntagged && (
            <Badge variant="outline" className="h-5 px-1.5 text-xs">
              Untagged
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}