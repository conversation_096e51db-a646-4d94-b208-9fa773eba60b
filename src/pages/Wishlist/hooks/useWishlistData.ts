import { useMemo } from 'react';
import { useUserWishlist } from '@/hooks/useUserLibrary';
import { WishlistStats, WishlistData } from '../types';

export function useWishlistData(): WishlistData {
  const { data: wishlist, isLoading, error } = useUserWishlist();
  
  const wishlistGames = useMemo(() => wishlist || [], [wishlist]);
  
  const stats = useMemo((): WishlistStats => {
    const total = wishlistGames.length;
    
    // Calculate average rating from games that have ratings
    const ratedGames = wishlistGames.filter(g => g.game?.metacritic_score);
    const averageMetacriticScore = ratedGames.length > 0 
      ? ratedGames.reduce((sum, g) => sum + (g.game?.metacritic_score || 0), 0) / ratedGames.length
      : 0;
    
    // Calculate average personal rating
    const personallyRatedGames = wishlistGames.filter(g => g.personal_rating);
    const averageRating = personallyRatedGames.length > 0 
      ? personallyRatedGames.reduce((sum, g) => sum + (g.personal_rating || 0), 0) / personallyRatedGames.length
      : 0;
    
    // Count price tracked games (placeholder - would need actual price tracking data)
    const priceTracked = wishlistGames.filter(g => g.price_tracking_enabled).length;
    
    // Calculate total estimated value (placeholder - would need actual pricing data)
    const totalValue = 0; // This would be calculated from actual price data
    
    // Get top genres
    const genreCounts: Record<string, number> = {};
    wishlistGames.forEach(game => {
      game.game?.genres?.forEach(genre => {
        genreCounts[genre] = (genreCounts[genre] || 0) + 1;
      });
    });
    const topGenres = Object.entries(genreCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([genre, count]) => ({ genre, count }));
    
    // Get top developers
    const developerCounts: Record<string, number> = {};
    wishlistGames.forEach(game => {
      if (game.game?.developer) {
        developerCounts[game.game.developer] = (developerCounts[game.game.developer] || 0) + 1;
      }
    });
    const topDevelopers = Object.entries(developerCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([developer, count]) => ({ developer, count }));
    
    return {
      total,
      averageRating,
      totalValue,
      priceTracked,
      averageMetacriticScore,
      topGenres,
      topDevelopers
    };
  }, [wishlistGames]);
  
  return {
    wishlistGames,
    stats,
    isLoading,
    error
  };
}