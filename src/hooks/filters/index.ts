// Main composed hook (replacement for useSearchFilters)
export { useSearchFiltersComposed } from './useSearchFiltersComposed';

// Individual focused hooks
export { useFilterState } from './useFilterState';
export { useFilterPersistence, parseFiltersFromURL } from './useFilterPersistence';
export { useFilterActions } from './useFilterActions';

// Types re-exported for convenience
export type { UseFilterStateOptions } from './useFilterState';