/**
 * SteamGridDB API Service - Game artwork and metadata
 * Provides high-quality game artwork including covers, icons, and banners
 */

// SteamGridDB API types
export interface SteamGridDBGame {
  id: number;
  name: string;
  types: string[];
  verified: boolean;
}

export interface SteamGridDBGrid {
  id: number;
  score: number;
  style: string;
  width: number;
  height: number;
  nsfw: boolean;
  humor: boolean;
  epilepsy: boolean;
  untagged: boolean;
  url: string;
  thumb: string;
  tags: string[];
  author: {
    name: string;
    steam64: string;
    avatar: string;
  };
}

export interface SteamGridDBSearchOptions {
  term?: string;
  type?: 'game';
  limit?: number;
}

export interface SteamGridDBGridOptions {
  styles?: string[];
  dimensions?: string[];
  formats?: string[];
  types?: string[];
  nsfw?: 'any' | 'false' | 'true';
  humor?: 'any' | 'false' | 'true';
  epilepsy?: 'any' | 'false' | 'true';
  untagged?: 'any' | 'false' | 'true';
  page?: number;
}

/**
 * SteamGridDB API Service for game artwork
 */
export class SteamGridDBAPIService {
  private readonly baseUrl = 'https://www.steamgriddb.com/api/v2';
  private readonly apiKey: string;

  constructor() {
    this.apiKey = import.meta.env.VITE_STEAMGRIDDB_API_KEY || 'e8fd2c09196e52744b3e838ea4fbfa95';
    console.log('🎨 SteamGridDB API Service initialized');
  }

  isConfigured(): boolean {
    return !!this.apiKey;
  }

  private async makeRequest<T>(endpoint: string, params: Record<string, unknown> = {}): Promise<T> {
    if (!this.isConfigured()) {
      throw new Error('SteamGridDB API key not configured');
    }

    try {
      const url = new URL(`${this.baseUrl}${endpoint}`);
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            url.searchParams.append(key, value.join(','));
          } else {
            url.searchParams.append(key, value.toString());
          }
        }
      });

      console.log(`🔍 SteamGridDB request: ${url.toString()}`);

      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`SteamGridDB API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(`SteamGridDB error: ${data.errors?.join(', ') || 'Unknown error'}`);
      }

      console.log(`✅ SteamGridDB request successful - ${Array.isArray(data.data) ? data.data.length : 1} results`);
      return data.data;
    } catch (error) {
      console.error('❌ SteamGridDB API error:', error);
      throw error;
    }
  }

  /**
   * Search for games by name
   * Note: Search endpoint may not be available, this is a fallback implementation
   */
  async searchGames(term: string, options: SteamGridDBSearchOptions = {}): Promise<SteamGridDBGame[]> {
    try {
      const params = {
        term,
        type: options.type || 'game',
        ...options
      };

      return await this.makeRequest<SteamGridDBGame[]>('/search/autocomplete', params);
    } catch {
      console.warn('⚠️ SteamGridDB search not available, returning empty results');
      return [];
    }
  }

  /**
   * Get game by Steam ID (more reliable than search)
   */
  async getGameBySteamId(steamId: string): Promise<SteamGridDBGame | null> {
    try {
      return await this.makeRequest<SteamGridDBGame>(`/games/steam/${steamId}`);
    } catch (error) {
      console.warn(`⚠️ Failed to get game by Steam ID ${steamId}:`, error);
      return null;
    }
  }

  /**
   * Get grids (cover art) for a game by ID
   */
  async getGrids(gameId: number, options: SteamGridDBGridOptions = {}): Promise<SteamGridDBGrid[]> {
    const params = {
      styles: options.styles || ['alternate', 'blurred', 'white_logo', 'material', 'no_logo'],
      dimensions: options.dimensions || ['460x215', '920x430'],
      formats: options.formats || ['png', 'jpg'],
      nsfw: options.nsfw || 'false',
      humor: options.humor || 'any',
      epilepsy: options.epilepsy || 'false',
      untagged: options.untagged || 'any',
      page: options.page || 0,
      ...options
    };

    return this.makeRequest<SteamGridDBGrid[]>(`/grids/game/${gameId}`, params);
  }

  /**
   * Get heroes (wide banners) for a game by ID
   */
  async getHeroes(gameId: number, options: SteamGridDBGridOptions = {}): Promise<SteamGridDBGrid[]> {
    const params = {
      styles: options.styles || ['alternate', 'blurred', 'material'],
      dimensions: options.dimensions || ['1920x620', '3840x1240'],
      formats: options.formats || ['png', 'jpg'],
      nsfw: options.nsfw || 'false',
      humor: options.humor || 'any',
      epilepsy: options.epilepsy || 'false',
      untagged: options.untagged || 'any',
      page: options.page || 0,
      ...options
    };

    return this.makeRequest<SteamGridDBGrid[]>(`/heroes/game/${gameId}`, params);
  }

  /**
   * Get logos for a game by ID
   */
  async getLogos(gameId: number, options: SteamGridDBGridOptions = {}): Promise<SteamGridDBGrid[]> {
    const params = {
      styles: options.styles || ['official', 'white', 'black'],
      formats: options.formats || ['png'],
      nsfw: options.nsfw || 'false',
      humor: options.humor || 'any',
      epilepsy: options.epilepsy || 'false',
      untagged: options.untagged || 'any',
      page: options.page || 0,
      ...options
    };

    return this.makeRequest<SteamGridDBGrid[]>(`/logos/game/${gameId}`, params);
  }

  /**
   * Get icons for a game by ID
   */
  async getIcons(gameId: number, options: SteamGridDBGridOptions = {}): Promise<SteamGridDBGrid[]> {
    const params = {
      styles: options.styles || ['official', 'custom'],
      dimensions: options.dimensions || ['32', '64', '128', '256'],
      formats: options.formats || ['png', 'ico'],
      nsfw: options.nsfw || 'false',
      humor: options.humor || 'any',
      epilepsy: options.epilepsy || 'false',
      untagged: options.untagged || 'any',
      page: options.page || 0,
      ...options
    };

    return this.makeRequest<SteamGridDBGrid[]>(`/icons/game/${gameId}`, params);
  }

  /**
   * Enhanced search that returns games with their best artwork
   */
  async searchWithArtwork(term: string): Promise<Array<SteamGridDBGame & { artwork?: SteamGridDBGrid }>> {
    try {
      const games = await this.searchGames(term, { limit: 10 });
      
      const gamesWithArtwork = await Promise.all(
        games.map(async (game) => {
          try {
            const grids = await this.getGrids(game.id, { 
              styles: ['alternate', 'blurred', 'white_logo'],
              dimensions: ['460x215'],
              formats: ['png', 'jpg']
            });
            
            // Get the highest scored artwork
            const bestArtwork = grids.sort((a, b) => b.score - a.score)[0];
            
            return {
              ...game,
              artwork: bestArtwork
            };
          } catch (error) {
            console.warn(`Failed to get artwork for game ${game.name}:`, error);
            return game;
          }
        })
      );

      return gamesWithArtwork;
    } catch (error) {
      console.error('❌ SteamGridDB search with artwork failed:', error);
      return [];
    }
  }
}

// Export singleton instance
export const steamGridDBAPI = new SteamGridDBAPIService();