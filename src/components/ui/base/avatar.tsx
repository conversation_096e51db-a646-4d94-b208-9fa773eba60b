import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface AvatarProps {
  src?: string;
  alt?: string;
  fallback: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const sizeClasses = {
  sm: 'h-8 w-8 text-xs',
  md: 'h-10 w-10 text-sm',
  lg: 'h-12 w-12 text-base'
};

export function Avatar({ src, alt, fallback, className, size = 'md' }: AvatarProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Reset states when src changes
  useState(() => {
    setImageError(false);
    setImageLoaded(false);
  }, [src]);

  const handleImageError = () => {
    setImageError(true);
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const showImage = src && !imageError && imageLoaded;
  const showFallback = !src || imageError || !imageLoaded;

  return (
    <div className={cn(
      'relative inline-flex items-center justify-center rounded-full bg-muted overflow-hidden',
      sizeClasses[size], 
      className
    )}>
      {src && !imageError && (
        <img
          src={src}
          alt={alt || 'Avatar'}
          className={cn(
            'h-full w-full object-cover transition-opacity duration-200',
            imageLoaded ? 'opacity-100' : 'opacity-0'
          )}
          onError={handleImageError}
          onLoad={handleImageLoad}
        />
      )}
      {showFallback && (
        <div className={cn(
          'flex h-full w-full items-center justify-center bg-muted text-muted-foreground font-medium transition-opacity duration-200',
          !imageLoaded && src ? 'opacity-100' : showImage ? 'opacity-0' : 'opacity-100'
        )}>
          {fallback}
        </div>
      )}
    </div>
  );
}