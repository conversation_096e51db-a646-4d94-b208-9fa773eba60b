import { gameService } from './gameService';
import { userService } from './userService';
import { collectionService } from './collectionService';
import { aiRecommendationService } from '../aiRecommendationService';
import { collectionInsightsService } from '../collectionInsightsService';
import { Game, Platform } from '../../types';

/**
 * Orchestration Service - Coordinates complex workflows across multiple services
 * Implements high-level business processes that involve multiple domains
 */
export class OrchestrationService {
  /**
   * Complete onboarding workflow for new users
   */
  async completeUserOnboarding(userData: {
    email: string;
    password: string;
    username?: string;
    preferences?: {
      favoriteGenres?: string[];
      favoritePlatforms?: Platform[];
      gamingExperience?: 'beginner' | 'casual' | 'hardcore';
    };
    initialGames?: Array<{
      title: string;
      platform?: Platform;
      status?: 'owned' | 'playing' | 'completed' | 'wishlist';
    }>;
  }) {
    try {
      // Step 1: Register user
      const registrationResult = await userService.registerUser({
        email: userData.email,
        password: userData.password,
        username: userData.username
      });

      const userId = registrationResult.user.id;

      // Step 2: Complete profile with preferences
      if (userData.preferences) {
        await userService.completeUserProfile(userId, {
          favorite_genres: userData.preferences.favoriteGenres,
          favorite_platforms: userData.preferences.favoritePlatforms?.map(p => p.toString())
        });
      }

      // Step 3: Add initial games if provided
      if (userData.initialGames && userData.initialGames.length > 0) {
        const gameAddResults = [];
        
        for (const gameData of userData.initialGames) {
          try {
            // Search for the game
            const searchResults = await gameService.searchGames(gameData.title, {
              platforms: gameData.platform ? [gameData.platform] : undefined,
              limit: 1
            });

            if (searchResults.length > 0) {
              const game = searchResults[0];
              const result = await collectionService.addGameToCollection(userId, game.id, {
                status: gameData.status || 'owned',
                platform: gameData.platform,
                autoDetectStatus: true
              });
              gameAddResults.push({ title: gameData.title, success: true, result });
            } else {
              gameAddResults.push({ title: gameData.title, success: false, error: 'Game not found' });
            }
          } catch (error) {
            gameAddResults.push({ 
              title: gameData.title, 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        return {
          user: registrationResult.user,
          session: registrationResult.session,
          gamesAdded: gameAddResults,
          onboardingComplete: true
        };
      }

      return {
        user: registrationResult.user,
        session: registrationResult.session,
        onboardingComplete: true
      };
    } catch (error) {
      console.error('Error in user onboarding workflow:', error);
      throw new Error('Failed to complete user onboarding');
    }
  }

  /**
   * Smart game discovery workflow
   */
  async discoverGamesForUser(userId: string, options: {
    discoveryType?: 'recommendations' | 'similar' | 'trending' | 'mixed';
    referenceGameId?: string;
    platforms?: Platform[];
    genres?: string[];
    limit?: number;
  } = {}) {
    const { discoveryType = 'mixed', referenceGameId, platforms, genres, limit = 20 } = options;

    try {
      let discoveredGames: Game[] = [];

      switch (discoveryType) {
        case 'recommendations':
          // Get AI-powered recommendations
          const recommendations = await aiRecommendationService.generateUserBasedRecommendations(userId, {
            count: limit,
            filters: { platforms, genres }
          });
          
          // Convert recommendations to games
          for (const rec of recommendations) {
            const gameDetails = await gameService.getGameDetails(rec.igdb_id, userId);
            if (gameDetails) {
              discoveredGames.push({
                ...gameDetails,
                recommendationContext: {
                  reason: rec.reason,
                  confidence: rec.confidence,
                  categories: rec.categories
                }
              });
            }
          }
          break;

        case 'similar':
          if (referenceGameId) {
            discoveredGames = await gameService.getSimilarGames(referenceGameId, {
              limit,
              userId
            });
          }
          break;

        case 'trending':
          discoveredGames = await gameService.getPopularGames({
            platform: platforms?.[0],
            genre: genres?.[0],
            limit,
            userId
          });
          break;

        case 'mixed':
          // Combine multiple discovery methods
          const [aiRecs, popular, newReleases] = await Promise.allSettled([
            aiRecommendationService.generateUserBasedRecommendations(userId, {
              count: Math.ceil(limit / 3),
              filters: { platforms, genres }
            }),
            gameService.getPopularGames({
              platform: platforms?.[0],
              limit: Math.ceil(limit / 3),
              userId
            }),
            gameService.getNewReleases({
              platform: platforms?.[0],
              limit: Math.ceil(limit / 3),
              userId
            })
          ]);

          // Process AI recommendations
          if (aiRecs.status === 'fulfilled') {
            for (const rec of aiRecs.value.slice(0, Math.ceil(limit / 3))) {
              const gameDetails = await gameService.getGameDetails(rec.igdb_id, userId);
              if (gameDetails) {
                discoveredGames.push({
                  ...gameDetails,
                  recommendationContext: {
                    reason: rec.reason,
                    confidence: rec.confidence,
                    categories: rec.categories,
                    source: 'ai_recommendation'
                  }
                });
              }
            }
          }

          // Add popular games
          if (popular.status === 'fulfilled') {
            discoveredGames.push(...popular.value.map(game => ({
              ...game,
              recommendationContext: {
                reason: 'Popular game in your preferred genres/platforms',
                confidence: 75,
                categories: ['Popular'],
                source: 'trending'
              }
            })));
          }

          // Add new releases
          if (newReleases.status === 'fulfilled') {
            discoveredGames.push(...newReleases.value.map(game => ({
              ...game,
              recommendationContext: {
                reason: 'Recent release matching your preferences',
                confidence: 70,
                categories: ['New Release'],
                source: 'new_release'
              }
            })));
          }
          break;
      }

      // Remove duplicates and limit results
      const uniqueGames = discoveredGames.filter((game, index, self) => 
        index === self.findIndex(g => g.id === game.id)
      ).slice(0, limit);

      return {
        games: uniqueGames,
        discoveryType,
        totalFound: uniqueGames.length,
        metadata: {
          platforms,
          genres,
          userId,
          generatedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error in game discovery workflow:', error);
      throw new Error('Failed to discover games for user');
    }
  }

  /**
   * Comprehensive collection analysis workflow
   */
  async analyzeUserCollection(userId: string) {
    try {
      const [
        collectionStats,
        insights,
        smartCollections,
        userStats,
        recommendations
      ] = await Promise.allSettled([
        collectionInsightsService.analyzeCollection(userId),
        collectionInsightsService.generateInsights(userId),
        collectionService.getSmartCollections(userId),
        userService.generateUserStatsSummary(userId),
        aiRecommendationService.generateUserBasedRecommendations(userId, { count: 10 })
      ]);

      return {
        collectionStats: collectionStats.status === 'fulfilled' ? collectionStats.value : null,
        insights: insights.status === 'fulfilled' ? insights.value : [],
        smartCollections: smartCollections.status === 'fulfilled' ? smartCollections.value : null,
        userStats: userStats.status === 'fulfilled' ? userStats.value : null,
        recommendations: recommendations.status === 'fulfilled' ? recommendations.value : [],
        analysisDate: new Date().toISOString(),
        errors: [
          ...(collectionStats.status === 'rejected' ? [`Collection stats: ${collectionStats.reason}`] : []),
          ...(insights.status === 'rejected' ? [`Insights: ${insights.reason}`] : []),
          ...(smartCollections.status === 'rejected' ? [`Smart collections: ${smartCollections.reason}`] : []),
          ...(userStats.status === 'rejected' ? [`User stats: ${userStats.reason}`] : []),
          ...(recommendations.status === 'rejected' ? [`Recommendations: ${recommendations.reason}`] : [])
        ]
      };
    } catch (error) {
      console.error('Error in collection analysis workflow:', error);
      throw new Error('Failed to analyze user collection');
    }
  }

  /**
   * Game addition workflow with smart categorization
   */
  async addGameWithSmartCategorization(userId: string, gameIdentifier: string, options: {
    searchQuery?: string;
    platform?: Platform;
    userIntent?: 'own' | 'want' | 'playing' | 'completed';
    autoDetectStatus?: boolean;
  } = {}) {
    const { searchQuery, platform, userIntent = 'own', autoDetectStatus = true } = options;

    try {
      let game: Game | null = null;

      // Try to get game by ID first
      if (gameIdentifier.includes('_')) {
        game = await gameService.getGameDetails(gameIdentifier, userId);
      }

      // If not found by ID, search by query
      if (!game && searchQuery) {
        const searchResults = await gameService.searchGames(searchQuery, {
          platforms: platform ? [platform] : undefined,
          limit: 1,
          includeUserContext: true,
          userId
        });

        if (searchResults.length > 0) {
          game = searchResults[0];
        }
      }

      if (!game) {
        throw new Error('Game not found');
      }

      // Check if user already owns this game
      if (game.userContext?.owned) {
        return {
          success: false,
          error: 'Game already in collection',
          existingStatus: game.userContext.status,
          game
        };
      }

      // Map user intent to status
      const statusMap = {
        'own': 'owned' as const,
        'want': 'wishlist' as const,
        'playing': 'playing' as const,
        'completed': 'completed' as const
      };

      const status = statusMap[userIntent];

      // Add game to collection
      const result = await collectionService.addGameToCollection(userId, game.id, {
        status,
        platform,
        autoDetectStatus
      });

      // Generate follow-up recommendations
      const similarGames = await gameService.getSimilarGames(game.id, {
        limit: 5,
        userId
      });

      return {
        success: true,
        game,
        addedStatus: status,
        result: result.data,
        similarGames: similarGames.filter(g => !g.userContext?.owned),
        recommendations: {
          message: `Since you added ${game.title}, you might also like these games`,
          games: similarGames.slice(0, 3)
        }
      };
    } catch (error) {
      console.error('Error in smart game addition workflow:', error);
      throw new Error('Failed to add game with smart categorization');
    }
  }

  /**
   * Batch import workflow for multiple games
   */
  async batchImportGames(userId: string, games: Array<{
    title: string;
    platform?: Platform;
    status?: 'owned' | 'playing' | 'completed' | 'wishlist';
    rating?: number;
    notes?: string;
  }>) {
    const results = [];
    const errors = [];

    for (const gameData of games) {
      try {
        const result = await this.addGameWithSmartCategorization(userId, '', {
          searchQuery: gameData.title,
          platform: gameData.platform,
          userIntent: gameData.status === 'wishlist' ? 'want' : 'own',
          autoDetectStatus: !gameData.status
        });

        if (result.success && gameData.rating) {
          // Update rating if provided
          await collectionService.moveGameStatus(userId, result.game.id, result.addedStatus, {
            rating: gameData.rating
          });
        }

        results.push({
          title: gameData.title,
          success: result.success,
          game: result.game,
          status: result.addedStatus
        });
      } catch (error) {
        errors.push({
          title: gameData.title,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return {
      totalProcessed: games.length,
      successful: results.filter(r => r.success).length,
      failed: errors.length,
      results,
      errors
    };
  }
}

export const orchestrationService = new OrchestrationService();