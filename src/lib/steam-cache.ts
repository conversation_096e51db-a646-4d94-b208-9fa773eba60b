/**
 * Steam API Caching System
 * Implements intelligent caching with TTL and memory management
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

interface CacheStats {
  hits: number;
  misses: number;
  entries: number;
  memoryUsage: number;
  hitRate: number;
}

export class SteamCache {
  private cache = new Map<string, CacheEntry<any>>();
  private stats = { hits: 0, misses: 0 };
  private readonly maxEntries: number;
  private readonly defaultTtl: number;

  constructor(maxEntries = 1000, defaultTtl = 300000) { // 5 minutes default TTL
    this.maxEntries = maxEntries;
    this.defaultTtl = defaultTtl;
    
    // Cleanup expired entries every 5 minutes
    setInterval(() => this.cleanup(), 300000);
  }

  /**
   * Get cached data
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;

    return entry.data;
  }

  /**
   * Set cached data
   */
  set<T>(key: string, data: T, ttl?: number): void {
    // Ensure we don't exceed max entries
    if (this.cache.size >= this.maxEntries) {
      this.evictLeastUsed();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTtl,
      accessCount: 1,
      lastAccessed: Date.now()
    };

    this.cache.set(key, entry);
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Delete specific key
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0 };
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const memoryUsage = this.estimateMemoryUsage();
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;

    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      entries: this.cache.size,
      memoryUsage,
      hitRate: Math.round(hitRate * 100) / 100
    };
  }

  /**
   * Generate cache key for Steam API requests
   */
  static generateKey(endpoint: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    
    return `steam:${endpoint}:${sortedParams}`;
  }

  /**
   * Get cached data with fallback function
   */
  async getOrSet<T>(
    key: string, 
    fallback: () => Promise<T>, 
    ttl?: number
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const data = await fallback();
    this.set(key, data, ttl);
    return data;
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Steam cache cleanup: removed ${cleaned} expired entries`);
    }
  }

  /**
   * Evict least recently used entries when cache is full
   */
  private evictLeastUsed(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`🗑️ Steam cache evicted LRU entry: ${oldestKey}`);
    }
  }

  /**
   * Estimate memory usage (rough calculation)
   */
  private estimateMemoryUsage(): number {
    let size = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      // Rough estimation: key size + JSON string size + metadata
      size += key.length * 2; // UTF-16 characters
      size += JSON.stringify(entry.data).length * 2;
      size += 64; // Metadata overhead
    }
    
    return size;
  }
}

// Cache configurations for different data types
export const CACHE_CONFIGS = {
  PROFILE: { ttl: 3600000, key: 'profile' }, // 1 hour
  LIBRARY: { ttl: 1800000, key: 'library' }, // 30 minutes
  ACHIEVEMENTS: { ttl: 3600000, key: 'achievements' }, // 1 hour
  GAME_DETAILS: { ttl: 86400000, key: 'game-details' }, // 24 hours
  FRIENDS: { ttl: 1800000, key: 'friends' }, // 30 minutes
  RECENTLY_PLAYED: { ttl: 300000, key: 'recently-played' }, // 5 minutes
  PLAYER_BANS: { ttl: 3600000, key: 'player-bans' }, // 1 hour
  GLOBAL_ACHIEVEMENTS: { ttl: 86400000, key: 'global-achievements' } // 24 hours
};

// Global cache instance
export const steamCache = new SteamCache();

/**
 * Batch processing utility for Steam API calls
 */
export class SteamBatchProcessor {
  private queue: Array<{
    operation: () => Promise<any>;
    resolve: (value: any) => void;
    reject: (error: any) => void;
    priority: number;
  }> = [];
  
  private processing = false;
  private readonly batchSize: number;
  private readonly batchDelay: number;

  constructor(batchSize = 5, batchDelay = 1000) {
    this.batchSize = batchSize;
    this.batchDelay = batchDelay;
  }

  /**
   * Add operation to batch queue
   */
  async add<T>(operation: () => Promise<T>, priority = 0): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push({ operation, resolve, reject, priority });
      
      // Sort by priority (higher priority first)
      this.queue.sort((a, b) => b.priority - a.priority);
      
      if (!this.processing) {
        this.processBatch();
      }
    });
  }

  /**
   * Process queued operations in batches
   */
  private async processBatch(): Promise<void> {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0) {
      const batch = this.queue.splice(0, this.batchSize);
      
      console.log(`🔄 Processing Steam API batch: ${batch.length} operations`);

      // Process batch concurrently
      const promises = batch.map(async ({ operation, resolve, reject }) => {
        try {
          const result = await operation();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      await Promise.allSettled(promises);

      // Delay between batches to respect rate limits
      if (this.queue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, this.batchDelay));
      }
    }

    this.processing = false;
  }

  /**
   * Get queue status
   */
  getStatus(): { queueLength: number; processing: boolean } {
    return {
      queueLength: this.queue.length,
      processing: this.processing
    };
  }
}

// Global batch processor instance
export const steamBatchProcessor = new SteamBatchProcessor();
