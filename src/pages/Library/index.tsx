import { useState, useEffect, useRef } from 'react';
import { LoadingSpinner } from '@/components/ui/utils';
import { EnhancedGameDetailModal } from '@/components/ui/game/enhanced-game-detail-modal';
import { useUpdateGameStatus, useRemoveFromCollection } from '@/hooks/useGameActions';
import { useViewModeState } from '@/hooks/useViewModeState';
import { Game } from '@/types';
import { UserGameWithDetails } from '@/types/database';
import { LibraryHeader } from './components/LibraryHeader';
import { LibraryStats } from './components/LibraryStats';
import { LibraryTabs, TabContent } from './components/LibraryTabs';
import { LibraryTab } from './components/LibraryTabs/LibraryTab';
import { CollectionsStatsTab } from './components/LibraryTabs/CollectionsStatsTab';
import { AnalyticsTab } from './components/LibraryTabs/AnalyticsTab';
import { ManageTab } from './components/LibraryTabs/ManageTab';
import { useLibraryData } from './hooks/useLibraryData';
import { LibraryFilters, ViewMode } from './types';
import { 
  preserveViewContext, 
  shouldResetScrollOnTransition,
  isValidViewModeTransition,
  ViewModeContext 
} from '@/lib/utils/viewModeTransitions';

export default function Library() {
  const { libraryGames, stats, isLoading, error } = useLibraryData();
  const { 
    viewMode, 
    collapsedPlatforms, 
    collapsedFamilies, 
    setViewMode: setViewModeInternal, 
    togglePlatform, 
    toggleFamily,
    isLoading: viewModeLoading 
  } = useViewModeState();
  const updateGameStatus = useUpdateGameStatus();
  const removeFromCollection = useRemoveFromCollection();
  
  const [selectedGame, setSelectedGame] = useState<UserGameWithDetails | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [sortOptions, setSortOptions] = useState<{ field: string; direction: 'asc' | 'desc' }>({
    field: 'added_at',
    direction: 'desc'
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('library');
  const [analyticsTimeframe, setAnalyticsTimeframe] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [filters, setFilters] = useState<LibraryFilters>({});
  
  // Ref to track previous view mode for smooth transitions
  const previousViewMode = useRef<ViewMode>(viewMode);
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle view mode changes with context preservation
  const handleViewModeChange = (newMode: ViewMode) => {
    const currentMode = previousViewMode.current;
    
    // Validate the transition
    if (!isValidViewModeTransition(currentMode, newMode)) {
      console.warn(`Invalid view mode transition from ${currentMode} to ${newMode}`);
      return;
    }

    // Create current context
    const currentContext: ViewModeContext = {
      searchQuery,
      sortOptions,
      filters,
      selectedGameId: selectedGame?.id,
      scrollPosition: containerRef.current?.scrollTop || 0,
    };

    // Preserve context during transition
    const preservedContext = preserveViewContext(currentMode, newMode, currentContext);

    // Apply preserved context
    setSearchQuery(preservedContext.searchQuery);
    setSortOptions(preservedContext.sortOptions);
    setFilters(preservedContext.filters);

    // Handle scroll position
    if (shouldResetScrollOnTransition(currentMode, newMode)) {
      // Reset scroll to top for major transitions
      setTimeout(() => {
        containerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100);
    }

    // Update view mode
    setViewModeInternal(newMode);
    previousViewMode.current = newMode;
  };

  // Update previous view mode ref when view mode changes
  useEffect(() => {
    previousViewMode.current = viewMode;
  }, [viewMode]);

  const handleGameClick = (gameData: UserGameWithDetails) => {
    setSelectedGame(gameData);
    setIsModalOpen(true);
  };

  const handleStatusUpdate = (userGameId: string, newStatus: string) => {
    updateGameStatus.mutate({
      userGameId,
      status: newStatus
    });
  };

  const handleRemoveFromCollection = (userGameId: string) => {
    removeFromCollection.mutate(userGameId);
  };

  const handlePlatformToggle = togglePlatform;
  const handleFamilyToggle = toggleFamily;

  const handleExport = async (format: string, options: Record<string, unknown>) => {
    console.log('Exporting library:', format, options);
    // Implementation will be added
  };
  
  const handleImport = async (source: string, data: unknown, options: Record<string, unknown>) => {
    console.log('Importing from:', source, options);
    // Implementation will be added
  };

  const handleFiltersApply = (newFilters: LibraryFilters) => {
    setFilters(newFilters);
    console.log('Applying filters:', newFilters);
    // Implementation will be added
  };

  const handleCollectionCreate = (collection: Record<string, unknown>) => {
    console.log('Creating collection:', collection);
    // Implementation will be added
  };

  const handleCollectionUpdate = (id: string, updates: Record<string, unknown>) => {
    console.log('Updating collection:', id, updates);
    // Implementation will be added
  };

  const handleCollectionDelete = (id: string) => {
    console.log('Deleting collection:', id);
    // Implementation will be added
  };

  const handleCollectionDuplicate = (id: string) => {
    console.log('Duplicating collection:', id);
    // Implementation will be added
  };

  if (isLoading || viewModeLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="text-center py-8">
          <p className="text-destructive">Failed to load library</p>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className="container mx-auto p-6 space-y-6">
      <LibraryHeader
        gameCount={libraryGames.length}
      />
      
      <LibraryTabs activeTab={activeTab} onTabChange={setActiveTab}>
        <TabContent value="library">
          <LibraryStats stats={stats} isLoading={isLoading} />
          <LibraryTab
            libraryGames={libraryGames}
            isLoading={isLoading}
            error={error}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            sortOptions={sortOptions}
            setSortOptions={setSortOptions}
            onGameClick={handleGameClick}
            onStatusUpdate={handleStatusUpdate}
            onRemoveGame={handleRemoveFromCollection}
          />
        </TabContent>
        
        <TabContent value="collections">
          <CollectionsStatsTab
            libraryGames={libraryGames}
            isLoading={isLoading}
            error={error}
            stats={stats}
            onCollectionCreate={handleCollectionCreate}
            onCollectionUpdate={handleCollectionUpdate}
            onCollectionDelete={handleCollectionDelete}
            onCollectionDuplicate={handleCollectionDuplicate}
          />
        </TabContent>
        
        <TabContent value="analytics">
          <AnalyticsTab
            libraryGames={libraryGames}
            isLoading={isLoading}
            error={error}
            timeframe={analyticsTimeframe}
            onTimeframeChange={setAnalyticsTimeframe}
          />
        </TabContent>
        
        <TabContent value="manage">
          <ManageTab
            libraryGames={libraryGames}
            isLoading={isLoading}
            error={error}
            filters={filters}
            onFiltersApply={handleFiltersApply}
            onExport={handleExport}
            onImport={handleImport}
          />
        </TabContent>
      </LibraryTabs>

      <EnhancedGameDetailModal
        gameData={selectedGame}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onStatusUpdate={handleStatusUpdate}
        onQuickPlay={(gameData) => {
          console.log('Quick play:', gameData.game?.title);
          // Add quick play functionality here
        }}
        onAddToWishlist={(gameData) => {
          handleStatusUpdate(gameData.id, 'wishlist');
        }}
      />
    </div>
  );
}