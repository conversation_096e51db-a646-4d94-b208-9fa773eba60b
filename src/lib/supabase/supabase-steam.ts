import { supabase } from "./supabase-client";

// Steam-specific database operations for enhanced Steam API features

// User Game Achievements operations
export const userGameAchievements = {
  getByUserGame: (userGameId: string) =>
    supabase
      .from("user_game_achievements")
      .select("*")
      .eq("user_game_id", userGameId)
      .order("unlocked_at", { ascending: false }),

  getUnlockedCount: (userGameId: string) =>
    supabase
      .from("user_game_achievements")
      .select("id", { count: "exact" })
      .eq("user_game_id", userGameId)
      .not("unlocked_at", "is", null),

  getRareAchievements: (userGameId: string) =>
    supabase
      .from("user_game_achievements")
      .select("*")
      .eq("user_game_id", userGameId)
      .eq("is_rare", true)
      .not("unlocked_at", "is", null)
      .order("global_percentage", { ascending: true }),

  create: (achievementData: {
    user_game_id: string;
    achievement_name: string;
    achievement_description?: string;
    unlocked_at?: string;
    is_rare?: boolean;
    global_percentage?: number;
  }) =>
    supabase
      .from("user_game_achievements")
      .insert(achievementData)
      .select()
      .single(),

  upsert: (achievementData: {
    user_game_id: string;
    achievement_name: string;
    achievement_description?: string;
    unlocked_at?: string;
    is_rare?: boolean;
    global_percentage?: number;
  }) =>
    supabase
      .from("user_game_achievements")
      .upsert(achievementData, { onConflict: "user_game_id,achievement_name" })
      .select()
      .single(),

  delete: (id: string) =>
    supabase.from("user_game_achievements").delete().eq("id", id),

  deleteByUserGame: (userGameId: string) =>
    supabase.from("user_game_achievements").delete().eq("user_game_id", userGameId),
};

// Steam Friends operations
export const steamFriends = {
  getByUser: (userId: string) =>
    supabase
      .from("steam_friends")
      .select("*")
      .eq("user_id", userId)
      .order("friend_since", { ascending: false }),

  getByUserAndFriend: (userId: string, friendSteamId: string) =>
    supabase
      .from("steam_friends")
      .select("*")
      .eq("user_id", userId)
      .eq("friend_steam_id", friendSteamId)
      .single(),

  create: (friendData: {
    user_id: string;
    friend_steam_id: string;
    friend_name?: string;
    friend_avatar?: string;
    relationship?: string;
    friend_since?: string;
  }) =>
    supabase.from("steam_friends").insert(friendData).select().single(),

  upsert: (friendData: {
    user_id: string;
    friend_steam_id: string;
    friend_name?: string;
    friend_avatar?: string;
    relationship?: string;
    friend_since?: string;
  }) =>
    supabase
      .from("steam_friends")
      .upsert(friendData, { onConflict: "user_id,friend_steam_id" })
      .select()
      .single(),

  update: (id: string, updates: {
    friend_name?: string;
    friend_avatar?: string;
    relationship?: string;
  }) =>
    supabase
      .from("steam_friends")
      .update(updates)
      .eq("id", id)
      .select()
      .single(),

  delete: (id: string) =>
    supabase.from("steam_friends").delete().eq("id", id),

  deleteByUser: (userId: string) =>
    supabase.from("steam_friends").delete().eq("user_id", userId),
};

// Steam Player Bans operations
export const steamPlayerBans = {
  getByUser: (userId: string) =>
    supabase
      .from("steam_player_bans")
      .select("*")
      .eq("user_id", userId)
      .single(),

  getBySteamId: (steamId: string) =>
    supabase
      .from("steam_player_bans")
      .select("*")
      .eq("steam_id", steamId)
      .single(),

  create: (banData: {
    user_id: string;
    steam_id: string;
    community_banned?: boolean;
    vac_banned?: boolean;
    number_of_vac_bans?: number;
    days_since_last_ban?: number;
    number_of_game_bans?: number;
    economy_ban?: string;
  }) =>
    supabase.from("steam_player_bans").insert(banData).select().single(),

  upsert: (banData: {
    user_id: string;
    steam_id: string;
    community_banned?: boolean;
    vac_banned?: boolean;
    number_of_vac_bans?: number;
    days_since_last_ban?: number;
    number_of_game_bans?: number;
    economy_ban?: string;
  }) =>
    supabase
      .from("steam_player_bans")
      .upsert(banData, { onConflict: "user_id,steam_id" })
      .select()
      .single(),

  update: (id: string, updates: {
    community_banned?: boolean;
    vac_banned?: boolean;
    number_of_vac_bans?: number;
    days_since_last_ban?: number;
    number_of_game_bans?: number;
    economy_ban?: string;
  }) =>
    supabase
      .from("steam_player_bans")
      .update({ ...updates, last_updated: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single(),

  delete: (id: string) =>
    supabase.from("steam_player_bans").delete().eq("id", id),
};

// User Game Stats operations
export const userGameStats = {
  getByUserGame: (userGameId: string) =>
    supabase
      .from("user_game_stats")
      .select("*")
      .eq("user_game_id", userGameId)
      .order("stat_name", { ascending: true }),

  getByStat: (userGameId: string, statName: string) =>
    supabase
      .from("user_game_stats")
      .select("*")
      .eq("user_game_id", userGameId)
      .eq("stat_name", statName)
      .single(),

  create: (statData: {
    user_game_id: string;
    stat_name: string;
    stat_value?: number;
    stat_display_name?: string;
  }) =>
    supabase.from("user_game_stats").insert(statData).select().single(),

  upsert: (statData: {
    user_game_id: string;
    stat_name: string;
    stat_value?: number;
    stat_display_name?: string;
  }) =>
    supabase
      .from("user_game_stats")
      .upsert(statData, { onConflict: "user_game_id,stat_name" })
      .select()
      .single(),

  update: (id: string, updates: {
    stat_value?: number;
    stat_display_name?: string;
  }) =>
    supabase
      .from("user_game_stats")
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single(),

  delete: (id: string) =>
    supabase.from("user_game_stats").delete().eq("id", id),

  deleteByUserGame: (userGameId: string) =>
    supabase.from("user_game_stats").delete().eq("user_game_id", userGameId),
};

// Steam Game News operations
export const steamGameNews = {
  getByAppId: (steamAppId: number, limit: number = 10) =>
    supabase
      .from("steam_game_news")
      .select("*")
      .eq("steam_app_id", steamAppId)
      .order("date", { ascending: false })
      .limit(limit),

  getRecent: (limit: number = 20) =>
    supabase
      .from("steam_game_news")
      .select("*")
      .order("date", { ascending: false })
      .limit(limit),

  create: (newsData: {
    steam_app_id: number;
    news_id: string;
    title: string;
    url?: string;
    is_external_url?: boolean;
    author?: string;
    contents?: string;
    feedlabel?: string;
    date?: string;
  }) =>
    supabase.from("steam_game_news").insert(newsData).select().single(),

  upsert: (newsData: {
    steam_app_id: number;
    news_id: string;
    title: string;
    url?: string;
    is_external_url?: boolean;
    author?: string;
    contents?: string;
    feedlabel?: string;
    date?: string;
  }) =>
    supabase
      .from("steam_game_news")
      .upsert(newsData, { onConflict: "steam_app_id,news_id" })
      .select()
      .single(),

  delete: (id: string) =>
    supabase.from("steam_game_news").delete().eq("id", id),

  deleteByAppId: (steamAppId: number) =>
    supabase.from("steam_game_news").delete().eq("steam_app_id", steamAppId),
};

// Steam Workshop Items operations
export const steamWorkshopItems = {
  getByUser: (userId: string) =>
    supabase
      .from("steam_workshop_items")
      .select("*")
      .eq("user_id", userId)
      .order("time_updated", { ascending: false }),

  getByAppId: (userId: string, steamAppId: number) =>
    supabase
      .from("steam_workshop_items")
      .select("*")
      .eq("user_id", userId)
      .eq("steam_app_id", steamAppId)
      .order("time_updated", { ascending: false }),

  getSubscribed: (userId: string) =>
    supabase
      .from("steam_workshop_items")
      .select("*")
      .eq("user_id", userId)
      .eq("is_subscribed", true)
      .order("time_updated", { ascending: false }),

  create: (workshopData: {
    user_id: string;
    published_file_id: string;
    steam_app_id?: number;
    title: string;
    description?: string;
    creator?: string;
    time_created?: string;
    time_updated?: string;
    subscriptions?: number;
    favorited?: number;
    preview_url?: string;
    is_subscribed?: boolean;
  }) =>
    supabase.from("steam_workshop_items").insert(workshopData).select().single(),

  upsert: (workshopData: {
    user_id: string;
    published_file_id: string;
    steam_app_id?: number;
    title: string;
    description?: string;
    creator?: string;
    time_created?: string;
    time_updated?: string;
    subscriptions?: number;
    favorited?: number;
    preview_url?: string;
    is_subscribed?: boolean;
  }) =>
    supabase
      .from("steam_workshop_items")
      .upsert(workshopData, { onConflict: "user_id,published_file_id" })
      .select()
      .single(),

  update: (id: string, updates: {
    title?: string;
    description?: string;
    subscriptions?: number;
    favorited?: number;
    is_subscribed?: boolean;
  }) =>
    supabase
      .from("steam_workshop_items")
      .update(updates)
      .eq("id", id)
      .select()
      .single(),

  delete: (id: string) =>
    supabase.from("steam_workshop_items").delete().eq("id", id),

  deleteByUser: (userId: string) =>
    supabase.from("steam_workshop_items").delete().eq("user_id", userId),
};
