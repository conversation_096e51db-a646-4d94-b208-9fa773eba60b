import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { PlatformFamilyGroup } from '@/components/ui/utils/PlatformFamilyGroup';
import { UserGameWithDetails } from '@/types/database';
import { PlatformFamilyStats } from '@/lib/utils/platformFamilyUtils';

// Mock the game card component
vi.mock('@/components/ui/game/game-card', () => ({
  GameCard: ({ gameData }: { gameData: UserGameWithDetails }) => (
    <div data-testid={`game-card-${gameData.id}`}>
      {gameData.game?.title}
    </div>
  )
}));

const mockGame: UserGameWithDetails = {
  id: '1',
  user_id: 'user1',
  game_id: 'game1',
  status: 'playing',
  personal_rating: 8,
  hours_played: 10,
  date_added: '2024-01-01',
  game: {
    id: 'game1',
    title: 'Test Game',
    platform: 'PC',
    developer: 'Test Developer',
    publisher: 'Test Publisher',
    release_date: '2024-01-01',
    genres: ['Action'],
    cover_image: 'test-image.jpg',
    metacritic_score: 85,
    description: 'Test description'
  }
};

const mockStats: PlatformFamilyStats = {
  total: 1,
  playing: 1,
  completed: 0,
  backlog: 0,
  wishlist: 0,
  owned: 0,
  completionRate: 0,
  averageRating: 8,
  totalHours: 10
};

describe('PlatformFamilyGroup', () => {
  const defaultProps = {
    family: 'PC' as const,
    games: [mockGame],
    platforms: ['PC', 'Steam Deck'],
    stats: mockStats,
    isCollapsed: false,
    onToggleCollapse: vi.fn(),
    onGameClick: vi.fn(),
    onStatusUpdate: vi.fn(),
    onRemoveGame: vi.fn()
  };

  it('renders platform family header with icon and name', () => {
    render(<PlatformFamilyGroup {...defaultProps} />);
    
    expect(screen.getByText('PC')).toBeInTheDocument();
    expect(screen.getByText('1 games')).toBeInTheDocument();
  });

  it('displays platform list in header', () => {
    render(<PlatformFamilyGroup {...defaultProps} />);
    
    expect(screen.getByText('PC, Steam Deck')).toBeInTheDocument();
  });

  it('shows statistics breakdown', () => {
    render(<PlatformFamilyGroup {...defaultProps} />);
    
    expect(screen.getByText('1 playing')).toBeInTheDocument();
    expect(screen.getByText('• ⭐ 8/10 avg')).toBeInTheDocument();
    expect(screen.getByText('• 10h played')).toBeInTheDocument();
  });

  it('renders games when not collapsed', () => {
    render(<PlatformFamilyGroup {...defaultProps} />);
    
    expect(screen.getByTestId('game-card-1')).toBeInTheDocument();
    expect(screen.getByText('Test Game')).toBeInTheDocument();
  });

  it('hides games when collapsed', () => {
    render(<PlatformFamilyGroup {...defaultProps} isCollapsed={true} />);
    
    expect(screen.queryByTestId('game-card-1')).not.toBeInTheDocument();
  });

  it('calls onToggleCollapse when collapse button is clicked', () => {
    const onToggleCollapse = vi.fn();
    render(<PlatformFamilyGroup {...defaultProps} onToggleCollapse={onToggleCollapse} />);
    
    const collapseButton = screen.getByRole('button');
    fireEvent.click(collapseButton);
    
    expect(onToggleCollapse).toHaveBeenCalledTimes(1);
  });

  it('handles multiple platforms correctly', () => {
    const props = {
      ...defaultProps,
      platforms: ['PC', 'Mac', 'Linux', 'Steam Deck', 'Windows']
    };
    
    render(<PlatformFamilyGroup {...props} />);
    
    expect(screen.getByText('PC, Mac, Linux +2 more')).toBeInTheDocument();
  });

  it('shows only non-zero status counts', () => {
    const statsWithMultipleStatuses: PlatformFamilyStats = {
      total: 5,
      playing: 2,
      completed: 1,
      backlog: 1,
      wishlist: 1,
      owned: 0,
      completionRate: 20,
      averageRating: 7.5,
      totalHours: 25
    };

    const props = {
      ...defaultProps,
      stats: statsWithMultipleStatuses
    };
    
    render(<PlatformFamilyGroup {...props} />);
    
    expect(screen.getByText('2 playing')).toBeInTheDocument();
    expect(screen.getByText('1 completed')).toBeInTheDocument();
    expect(screen.getByText('1 backlog')).toBeInTheDocument();
    expect(screen.getByText('1 wishlist')).toBeInTheDocument();
    expect(screen.queryByText('0 owned')).not.toBeInTheDocument();
  });
});