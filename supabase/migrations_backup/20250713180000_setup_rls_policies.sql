-- Enable Row Level Security on the tables if not already enabled.
ALTER TABLE public.games ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_games ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to ensure a clean slate and avoid conflicts.
DROP POLICY IF EXISTS "Allow authenticated users to read games" ON public.games;
DROP POLICY IF EXISTS "Allow users to manage their own game records" ON public.user_games;

-- Create a policy to allow any authenticated user to read from the 'games' table.
-- This is necessary for the join operation in the failing queries.
CREATE POLICY "Allow authenticated users to read games"
ON public.games
FOR SELECT
TO authenticated
USING (true);

-- Create a policy that allows users to view, add, update, and delete their own entries
-- in the 'user_games' table, but not anyone else's.
CREATE POLICY "Allow users to manage their own game records"
ON public.user_games
FOR ALL
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);