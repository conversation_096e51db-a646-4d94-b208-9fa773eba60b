import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base';
import { FiltersTabProps } from '../../types';

export function FiltersTab(_: FiltersTabProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🔍 Advanced Smart Filters</CardTitle>
          <CardDescription>
            Create complex filters with multiple conditions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <label className="text-sm font-medium">Filter by Status</label>
                <select className="w-full mt-1 p-2 border rounded-md">
                  <option>All Statuses</option>
                  <option>Playing</option>
                  <option>Completed</option>
                  <option>Backlog</option>
                  <option>Wishlist</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">Filter by Rating</label>
                <select className="w-full mt-1 p-2 border rounded-md">
                  <option>All Ratings</option>
                  <option>8+ Stars</option>
                  <option>6-8 Stars</option>
                  <option>Below 6 Stars</option>
                </select>
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              Advanced filtering features coming soon...
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}