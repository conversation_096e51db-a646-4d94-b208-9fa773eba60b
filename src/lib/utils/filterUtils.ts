import { Game } from '@/types';
import { GameFilt<PERSON>, FilterResult, SortOptions, CustomTagsFilter, StatusFilter, CollectionFilter } from '@/types/filters';

/**
 * Apply all active filters to a list of games
 */
export function applyFilters<T extends Game>(
  games: T[], 
  filters: GameFilters
): FilterResult<T> {
  const startTime = performance.now();
  const originalCount = games.length;
  
  let filteredGames = [...games];
  const appliedFilters: string[] = [];

  // Apply search filter
  if (filters.search.enabled && filters.search.query.trim()) {
    filteredGames = applySearchFilter(filteredGames, filters.search.query, filters.search.searchType);
    appliedFilters.push('search');
  }

  // Apply platform filter
  if (filters.platforms.enabled && filters.platforms.platforms.length > 0) {
    filteredGames = applyPlatformFilter(filteredGames, filters.platforms.platforms);
    appliedFilters.push('platforms');
  }

  // Apply genre filter
  if (filters.genres.enabled && filters.genres.genres.length > 0) {
    filteredGames = applyGenreFilter(filteredGames, filters.genres.genres, filters.genres.mode);
    appliedFilters.push('genres');
  }

  // Apply year filter
  if (filters.year.enabled && (filters.year.minYear || filters.year.maxYear)) {
    filteredGames = applyYearFilter(filteredGames, filters.year.minYear, filters.year.maxYear);
    appliedFilters.push('year');
  }

  // Apply rating filter
  if (filters.rating.enabled && (filters.rating.minRating || filters.rating.maxRating)) {
    filteredGames = applyRatingFilter(filteredGames, filters.rating.minRating, filters.rating.maxRating);
    appliedFilters.push('rating');
  }

  // Apply developer filter
  if (filters.developer.enabled && filters.developer.developers.length > 0) {
    filteredGames = applyDeveloperFilter(filteredGames, filters.developer.developers);
    appliedFilters.push('developer');
  }

  // Apply publisher filter
  if (filters.publisher.enabled && filters.publisher.publishers.length > 0) {
    filteredGames = applyPublisherFilter(filteredGames, filters.publisher.publishers);
    appliedFilters.push('publisher');
  }

  // Apply custom tags filter
  if (filters.customTags.enabled && (filters.customTags.tags.length > 0 || filters.customTags.includeUntagged)) {
    filteredGames = applyCustomTagsFilter(filteredGames, filters.customTags);
    appliedFilters.push('customTags');
  }

  // Apply status filter
  if (filters.status.enabled && filters.status.statuses.length > 0) {
    filteredGames = applyStatusFilter(filteredGames, filters.status);
    appliedFilters.push('status');
  }

  // Apply collection filter
  if (filters.collection.enabled) {
    filteredGames = applyCollectionFilter(filteredGames, filters.collection);
    appliedFilters.push('collection');
  }

  // Apply sorting
  filteredGames = applySorting(filteredGames, filters.sort);

  const endTime = performance.now();
  const searchTime = Math.round(endTime - startTime);

  return {
    data: filteredGames,
    totalCount: originalCount,
    filteredCount: filteredGames.length,
    appliedFilters,
    searchTime
  };
}

/**
 * Apply search filter to games
 */
function applySearchFilter<T extends Game>(
  games: T[], 
  query: string, 
  searchType: 'exact' | 'fuzzy' | 'smart'
): T[] {
  const searchTerm = query.toLowerCase().trim();
  
  if (!searchTerm) return games;

  return games.filter(game => {
    const searchableText = [
      game.title,
      game.description || '',
      game.developer || '',
      game.publisher || '',
      ...(game.genres || []),
      ...(game.platforms || [])
    ].join(' ').toLowerCase();

    switch (searchType) {
      case 'exact':
        return searchableText.includes(searchTerm);
      
      case 'fuzzy':
        return fuzzyMatch(searchableText, searchTerm);
      
      case 'smart':
      default:
        // Smart search: try exact first, then fuzzy
        return searchableText.includes(searchTerm) || fuzzyMatch(searchableText, searchTerm);
    }
  });
}

/**
 * Apply platform filter to games
 */
function applyPlatformFilter<T extends Game>(games: T[], platforms: string[]): T[] {
  return games.filter(game => 
    game.platforms && game.platforms.some(platform => platforms.includes(platform))
  );
}

/**
 * Apply genre filter to games
 */
function applyGenreFilter<T extends Game>(
  games: T[], 
  genres: string[], 
  mode: 'include' | 'exclude'
): T[] {
  return games.filter(game => {
    if (!game.genres || game.genres.length === 0) {
      return mode === 'exclude'; // Include games with no genres when excluding
    }

    const hasAnyGenre = game.genres.some(genre => genres.includes(genre));
    
    return mode === 'include' ? hasAnyGenre : !hasAnyGenre;
  });
}

/**
 * Apply year filter to games
 */
function applyYearFilter<T extends Game>(
  games: T[], 
  minYear?: number, 
  maxYear?: number
): T[] {
  return games.filter(game => {
    if (!game.release_date) {
      return false; // Exclude games with no release date
    }

    const gameYear = new Date(game.release_date).getFullYear();
    
    if (minYear && gameYear < minYear) return false;
    if (maxYear && gameYear > maxYear) return false;
    
    return true;
  });
}

/**
 * Apply rating filter to games
 */
function applyRatingFilter<T extends Game>(
  games: T[], 
  minRating?: number, 
  maxRating?: number
): T[] {
  return games.filter(game => {
    if (typeof game.metacritic_score !== 'number') {
      return false; // Exclude games with no rating
    }

    if (minRating && game.metacritic_score < minRating) return false;
    if (maxRating && game.metacritic_score > maxRating) return false;
    
    return true;
  });
}

/**
 * Apply developer filter to games
 */
function applyDeveloperFilter<T extends Game>(games: T[], developers: string[]): T[] {
  return games.filter(game => 
    game.developer && developers.includes(game.developer)
  );
}

/**
 * Apply publisher filter to games
 */
function applyPublisherFilter<T extends Game>(games: T[], publishers: string[]): T[] {
  return games.filter(game => 
    game.publisher && publishers.includes(game.publisher)
  );
}

/**
 * Apply sorting to games
 */
function applySorting<T extends Game>(games: T[], sort: SortOptions): T[] {
  return [...games].sort((a, b) => {
    let comparison = 0;
    
    switch (sort.field) {
      case 'title':
        comparison = a.title.localeCompare(b.title);
        break;
        
      case 'release_date': {
        const dateA = a.release_date ? new Date(a.release_date).getTime() : 0;
        const dateB = b.release_date ? new Date(b.release_date).getTime() : 0;
        comparison = dateA - dateB;
        break;
      }
      
      case 'metacritic_score': {
        const scoreA = a.metacritic_score || 0;
        const scoreB = b.metacritic_score || 0;
        comparison = scoreA - scoreB;
        break;
      }
      
      case 'popularity': {
        // For now, use a simple heuristic: games with higher ratings are more popular
        const popA = (a.metacritic_score || 0) * (a.platforms?.length || 1);
        const popB = (b.metacritic_score || 0) * (b.platforms?.length || 1);
        comparison = popA - popB;
        break;
      }
      
      case 'added_at': {
        // This would be used for user library sorting
        // For now, default to title sorting
        comparison = a.title.localeCompare(b.title);
        break;
      }
      
      default:
        comparison = 0;
    }
    
    return sort.direction === 'asc' ? comparison : -comparison;
  });
}

/**
 * Simple fuzzy matching algorithm
 */
function fuzzyMatch(text: string, pattern: string): boolean {
  const patternChars = pattern.split('');
  let textIndex = 0;
  
  for (const char of patternChars) {
    const foundIndex = text.indexOf(char, textIndex);
    if (foundIndex === -1) {
      return false;
    }
    textIndex = foundIndex + 1;
  }
  
  return true;
}

/**
 * Extract available filter options from a list of games
 */
export function extractFilterOptions(games: Game[]) {
  const platforms = new Set<string>();
  const genres = new Set<string>();
  const developers = new Set<string>();
  const publishers = new Set<string>();
  const years = new Set<number>();

  games.forEach(game => {
    // Extract platforms
    if (game.platforms) {
      game.platforms.forEach(platform => platforms.add(platform));
    }

    // Extract genres
    if (game.genres) {
      game.genres.forEach(genre => genres.add(genre));
    }

    // Extract developers
    if (game.developer) {
      developers.add(game.developer);
    }

    // Extract publishers
    if (game.publisher) {
      publishers.add(game.publisher);
    }

    // Extract years
    if (game.release_date) {
      const year = new Date(game.release_date).getFullYear();
      years.add(year);
    }
  });

  return {
    platforms: Array.from(platforms).sort(),
    genres: Array.from(genres).sort(),
    developers: Array.from(developers).sort(),
    publishers: Array.from(publishers).sort(),
    years: Array.from(years).sort((a, b) => b - a) // Newest first
  };
}

/**
 * Create filter summary text
 */
export function createFilterSummary(filters: GameFilters): string {
  const parts: string[] = [];

  if (filters.search.enabled && filters.search.query.trim()) {
    parts.push(`search: "${filters.search.query}"`);
  }

  if (filters.platforms.enabled && filters.platforms.platforms.length > 0) {
    parts.push(`platforms: ${filters.platforms.platforms.length} selected`);
  }

  if (filters.genres.enabled && filters.genres.genres.length > 0) {
    const mode = filters.genres.mode === 'include' ? 'including' : 'excluding';
    parts.push(`genres: ${mode} ${filters.genres.genres.length} selected`);
  }

  if (filters.year.enabled && (filters.year.minYear || filters.year.maxYear)) {
    const min = filters.year.minYear || '?';
    const max = filters.year.maxYear || '?';
    parts.push(`year: ${min}-${max}`);
  }

  if (filters.rating.enabled && (filters.rating.minRating || filters.rating.maxRating)) {
    const min = filters.rating.minRating || 0;
    const max = filters.rating.maxRating || 100;
    parts.push(`rating: ${min}-${max}`);
  }

  if (filters.developer.enabled && filters.developer.developers.length > 0) {
    parts.push(`developers: ${filters.developer.developers.length} selected`);
  }

  if (filters.publisher.enabled && filters.publisher.publishers.length > 0) {
    parts.push(`publishers: ${filters.publisher.publishers.length} selected`);
  }

  if (filters.customTags.enabled && (filters.customTags.tags.length > 0 || filters.customTags.includeUntagged)) {
    const mode = filters.customTags.mode === 'include' ? 'including' : 'excluding';
    const tagCount = filters.customTags.tags.length;
    const untaggedText = filters.customTags.includeUntagged ? ' + untagged' : '';
    parts.push(`tags: ${mode} ${tagCount} selected${untaggedText}`);
  }

  if (filters.status.enabled && filters.status.statuses.length > 0) {
    const mode = filters.status.mode === 'include' ? 'including' : 'excluding';
    parts.push(`status: ${mode} ${filters.status.statuses.length} selected`);
  }

  if (filters.collection.enabled) {
    const collectionParts = [];
    if (filters.collection.inLibrary) collectionParts.push('library');
    if (filters.collection.inWishlist) collectionParts.push('wishlist');
    if (filters.collection.notInCollection) collectionParts.push('not collected');
    if (collectionParts.length > 0) {
      parts.push(`collection: ${collectionParts.join(', ')}`);
    }
  }

  return parts.length > 0 ? parts.join(', ') : 'no filters applied';
}

/**
 * Apply custom tags filter to games
 * Note: This assumes games have a 'tags' property with tag IDs
 */
function applyCustomTagsFilter<T extends Game>(games: T[], filter: CustomTagsFilter): T[] {
  return games.filter(game => {
    // Type assertion to access potential tags property
    const gameWithTags = game as T & { tags?: string[] };
    const gameTags = gameWithTags.tags || [];
    const hasNoTags = gameTags.length === 0;

    // Handle untagged games
    if (hasNoTags) {
      return filter.includeUntagged;
    }

    // If no specific tags are selected, only filter by untagged preference
    if (filter.tags.length === 0) {
      return !filter.includeUntagged; // Only show tagged games if untagged is false
    }

    // Check if game has any of the selected tags
    const hasAnySelectedTag = gameTags.some(tagId => filter.tags.includes(tagId));

    if (filter.mode === 'include') {
      // Include games that have any of the selected tags OR untagged games if includeUntagged is true
      return hasAnySelectedTag || (hasNoTags && filter.includeUntagged);
    } else {
      // Exclude games that have any of the selected tags, but include untagged if specified
      return !hasAnySelectedTag || (hasNoTags && filter.includeUntagged);
    }
  });
}

/**
 * Apply status filter to games
 * Note: This assumes games have a 'status' property
 */
function applyStatusFilter<T extends Game>(games: T[], filter: StatusFilter): T[] {
  return games.filter(game => {
    // Type assertion to access potential status property
    const gameWithStatus = game as T & { status?: 'playing' | 'completed' | 'backlog' | 'wishlist' };
    const gameStatus = gameWithStatus.status;

    // If game has no status, exclude it unless we're excluding all statuses
    if (!gameStatus) {
      return filter.mode === 'exclude';
    }

    const hasSelectedStatus = filter.statuses.includes(gameStatus);

    return filter.mode === 'include' ? hasSelectedStatus : !hasSelectedStatus;
  });
}

/**
 * Apply collection filter to games
 * Note: This assumes games have properties indicating collection status
 */
function applyCollectionFilter<T extends Game>(games: T[], filter: CollectionFilter): T[] {
  return games.filter(game => {
    // Type assertion to access potential collection properties
    const gameWithCollection = game as T & { 
      inLibrary?: boolean;
      inWishlist?: boolean;
      isInCollection?: boolean;
    };

    const inLibrary = gameWithCollection.inLibrary || gameWithCollection.isInCollection || false;
    const inWishlist = gameWithCollection.inWishlist || false;
    const notInCollection = !inLibrary && !inWishlist;

    // Check if game matches any of the selected collection states
    const matchesLibrary = filter.inLibrary && inLibrary;
    const matchesWishlist = filter.inWishlist && inWishlist;
    const matchesNotInCollection = filter.notInCollection && notInCollection;

    return matchesLibrary || matchesWishlist || matchesNotInCollection;
  });
}

/**
 * Get count of active filters
 */
export function getActiveFilterCount(filters: GameFilters): number {
  let count = 0;

  if (filters.search.enabled && filters.search.query.trim()) count++;
  if (filters.platforms.enabled && filters.platforms.platforms.length > 0) count++;
  if (filters.genres.enabled && filters.genres.genres.length > 0) count++;
  if (filters.year.enabled && (filters.year.minYear || filters.year.maxYear)) count++;
  if (filters.rating.enabled && (filters.rating.minRating || filters.rating.maxRating)) count++;
  if (filters.developer.enabled && filters.developer.developers.length > 0) count++;
  if (filters.publisher.enabled && filters.publisher.publishers.length > 0) count++;
  if (filters.customTags.enabled && (filters.customTags.tags.length > 0 || filters.customTags.includeUntagged)) count++;
  if (filters.status.enabled && filters.status.statuses.length > 0) count++;
  if (filters.collection.enabled && (filters.collection.inLibrary || filters.collection.inWishlist || filters.collection.notInCollection)) count++;

  return count;
}

/**
 * Reset all filters to default state
 */
export function resetAllFilters(): GameFilters {
  return {
    search: {
      enabled: false,
      query: '',
      searchType: 'smart'
    },
    platforms: {
      enabled: false,
      platforms: []
    },
    genres: {
      enabled: false,
      genres: [],
      mode: 'include'
    },
    year: {
      enabled: false,
      minYear: undefined,
      maxYear: undefined
    },
    rating: {
      enabled: false,
      minRating: undefined,
      maxRating: undefined
    },
    developer: {
      enabled: false,
      developers: []
    },
    publisher: {
      enabled: false,
      publishers: []
    },
    customTags: {
      enabled: false,
      tags: [],
      mode: 'include',
      includeUntagged: false
    },
    status: {
      enabled: false,
      statuses: [],
      mode: 'include'
    },
    collection: {
      enabled: false,
      inLibrary: false,
      inWishlist: false,
      notInCollection: false
    },
    sort: {
      field: 'title',
      direction: 'asc'
    }
  };
}

/**
 * Check if any filters are active
 */
export function hasActiveFilters(filters: GameFilters): boolean {
  return getActiveFilterCount(filters) > 0;
}