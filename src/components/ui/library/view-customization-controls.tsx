import { memo, useState } from 'react';
import { Button } from '@/components/ui/base/button';
import { DropdownMenu } from '@/components/ui/base/dropdown-menu';
import { Badge } from '@/components/ui/base/badge';
import { Separator } from '@/components/ui/base/separator';
import { cn } from '@/lib/utils';
import {
  Grid3X3,
  List,
  Settings,
  Sparkles,
  Zap,
  Eye,
  EyeOff,
  RotateCcw,
  ChevronDown
} from '@/lib/icons';

export interface ViewSettings {
  variant: 'compact' | 'standard' | 'detailed';
  style: 'modern' | 'classic' | 'minimal';
  animationLevel: 'subtle' | 'dynamic' | 'premium';
  gridDensity: 'tight' | 'normal' | 'spacious';
  showMetadata: boolean;
  enableHoverEffects: boolean;
}

interface ViewCustomizationControlsProps {
  settings: ViewSettings;
  onSettingsChange: (settings: Partial<ViewSettings>) => void;
  className?: string;
}

const defaultSettings: ViewSettings = {
  variant: 'standard',
  style: 'modern',
  animationLevel: 'dynamic',
  gridDensity: 'normal',
  showMetadata: true,
  enableHoverEffects: true,
};

export const ViewCustomizationControls = memo<ViewCustomizationControlsProps>(({
  settings,
  onSettingsChange,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleReset = () => {
    onSettingsChange(defaultSettings);
  };

  const variantConfig = {
    compact: { icon: Grid3X3, label: 'Compact', description: 'Small cards, more games visible' },
    standard: { icon: Grid3X3, label: 'Standard', description: 'Balanced size and detail' },
    detailed: { icon: List, label: 'Detailed', description: 'Large cards with full metadata' },
  };

  const styleConfig = {
    modern: { label: 'Modern', description: 'Gradients and premium effects' },
    classic: { label: 'Classic', description: 'Traditional card design' },
    minimal: { label: 'Minimal', description: 'Clean and simple' },
  };

  const animationConfig = {
    subtle: { label: 'Subtle', description: 'Minimal animations' },
    dynamic: { label: 'Dynamic', description: 'Smooth transitions' },
    premium: { label: 'Premium', description: 'Advanced 3D effects' },
  };

  const densityConfig = {
    tight: { label: 'Tight', description: 'Maximum games per row' },
    normal: { label: 'Normal', description: 'Comfortable spacing' },
    spacious: { label: 'Spacious', description: 'Generous spacing' },
  };


  return (
    <div className={cn('flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-2', className)}>
      {/* Mobile-first layout with responsive adjustments */}
      <div className="flex items-center justify-between sm:justify-start gap-2">
        {/* Quick variant toggles */}
        <div className="flex items-center gap-1 p-1 bg-muted rounded-lg">
          {Object.entries(variantConfig).map(([variant, config]) => {
            const Icon = config.icon;
            return (
              <Button
                key={variant}
                variant={settings.variant === variant ? 'default' : 'ghost'}
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => onSettingsChange({ variant: variant as ViewSettings['variant'] })}
                title={`${config.label}: ${config.description}`}
              >
                <Icon className="h-4 w-4" />
              </Button>
            );
          })}
        </div>

        {/* Advanced settings dropdown */}
        <DropdownMenu
          open={isOpen}
          onOpenChange={setIsOpen}
          trigger={
            <Button variant="outline" size="sm" className="gap-2 hover:bg-muted/80 transition-colors">
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">View Options</span>
              <ChevronDown className={cn("h-3 w-3 transition-transform duration-200", isOpen && "rotate-180")} />
            </Button>
          }
          align="end"
          sideOffset={8}
          className="w-full sm:w-[22rem] max-w-[95vw] sm:max-w-[85vw]"
        >
          <div className="p-6 space-y-7">
          {/* Card Style Section */}
          <div>
            <div className="flex items-center gap-2.5 mb-4">
              <div className="p-1.5 rounded-lg bg-primary/10 transition-colors duration-200">
                <Sparkles className="h-4 w-4 text-primary transition-colors duration-200" />
              </div>
              <span className="font-semibold text-base transition-colors duration-200">Card Style</span>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              {Object.entries(styleConfig).map(([style, config]) => (
                <button
                  key={style}
                  onClick={() => onSettingsChange({ style: style as ViewSettings['style'] })}
                  className={cn(
                    'group relative p-4 text-left rounded-xl border-2 transition-all duration-200',
                    'hover:shadow-sm active:scale-[0.98]',
                    settings.style === style
                      ? 'bg-primary text-primary-foreground border-primary shadow-md scale-[1.02]'
                      : 'bg-card hover:bg-muted/50 border-border hover:border-muted-foreground/20'
                  )}
                >
                  <div className="font-semibold text-sm">{config.label}</div>
                  <div className={cn(
                    "text-xs mt-1.5 leading-relaxed",
                    settings.style === style
                      ? "opacity-90"
                      : "text-muted-foreground group-hover:text-foreground/80"
                  )}>{config.description}</div>
                  {settings.style === style && (
                    <div className="absolute top-2 right-2 animate-in fade-in-0 zoom-in-0 duration-200">
                      <div className="h-2 w-2 bg-primary-foreground rounded-full animate-pulse" />
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Animation Level Section */}
          <div>
            <div className="flex items-center gap-2.5 mb-4">
              <div className="p-1.5 rounded-lg bg-primary/10 transition-colors duration-200">
                <Zap className="h-4 w-4 text-primary transition-colors duration-200" />
              </div>
              <span className="font-semibold text-base transition-colors duration-200">Animations</span>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              {Object.entries(animationConfig).map(([level, config]) => (
                <button
                  key={level}
                  onClick={() => onSettingsChange({ animationLevel: level as ViewSettings['animationLevel'] })}
                  className={cn(
                    'group relative p-4 text-left rounded-xl border-2 transition-all duration-200',
                    'hover:shadow-sm active:scale-[0.98]',
                    settings.animationLevel === level
                      ? 'bg-primary text-primary-foreground border-primary shadow-md scale-[1.02]'
                      : 'bg-card hover:bg-muted/50 border-border hover:border-muted-foreground/20'
                  )}
                >
                  <div className="font-semibold text-sm">{config.label}</div>
                  <div className={cn(
                    "text-xs mt-1.5 leading-relaxed",
                    settings.animationLevel === level
                      ? "opacity-90"
                      : "text-muted-foreground group-hover:text-foreground/80"
                  )}>{config.description}</div>
                  {settings.animationLevel === level && (
                    <div className="absolute top-2 right-2 animate-in fade-in-0 zoom-in-0 duration-200">
                      <div className="h-2 w-2 bg-primary-foreground rounded-full animate-pulse" />
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Grid Density Section */}
          <div>
            <div className="flex items-center gap-2.5 mb-4">
              <div className="p-1.5 rounded-lg bg-primary/10 transition-colors duration-200">
                <Grid3X3 className="h-4 w-4 text-primary transition-colors duration-200" />
              </div>
              <span className="font-semibold text-base transition-colors duration-200">Grid Density</span>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              {Object.entries(densityConfig).map(([density, config]) => (
                <button
                  key={density}
                  onClick={() => onSettingsChange({ gridDensity: density as ViewSettings['gridDensity'] })}
                  className={cn(
                    'group relative p-4 text-left rounded-xl border-2 transition-all duration-200',
                    'hover:shadow-sm active:scale-[0.98]',
                    settings.gridDensity === density
                      ? 'bg-primary text-primary-foreground border-primary shadow-md scale-[1.02]'
                      : 'bg-card hover:bg-muted/50 border-border hover:border-muted-foreground/20'
                  )}
                >
                  <div className="font-semibold text-sm">{config.label}</div>
                  <div className={cn(
                    "text-xs mt-1.5 leading-relaxed",
                    settings.gridDensity === density
                      ? "opacity-90"
                      : "text-muted-foreground group-hover:text-foreground/80"
                  )}>{config.description}</div>
                  {settings.gridDensity === density && (
                    <div className="absolute top-2 right-2 animate-in fade-in-0 zoom-in-0 duration-200">
                      <div className="h-2 w-2 bg-primary-foreground rounded-full animate-pulse" />
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>

          <Separator className="my-6" />

          {/* Toggle Options */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                <span className="font-medium">Hover Effects</span>
              </div>
              <Button
                variant={settings.enableHoverEffects ? 'default' : 'outline'}
                size="sm"
                onClick={() => onSettingsChange({ enableHoverEffects: !settings.enableHoverEffects })}
                className="h-8"
              >
                {settings.enableHoverEffects ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
              </Button>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <List className="h-4 w-4" />
                <span className="font-medium">Show Metadata</span>
              </div>
              <Button
                variant={settings.showMetadata ? 'default' : 'outline'}
                size="sm"
                onClick={() => onSettingsChange({ showMetadata: !settings.showMetadata })}
                className="h-8"
              >
                {settings.showMetadata ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
              </Button>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Reset Button */}
          <div className="flex justify-between items-center p-4 rounded-xl bg-muted/30 border border-border/50">
            <div>
              <div className="font-semibold text-sm">Reset to Defaults</div>
              <div className="text-xs text-muted-foreground mt-0.5">Restore original settings</div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              className="gap-2"
            >
              <RotateCcw className="h-3 w-3" />
              Reset
            </Button>
          </div>
          </div>
        </DropdownMenu>
      </div>

      {/* Current settings indicator - hidden on mobile to save space */}
      <div className="hidden md:flex items-center gap-1">
        <Badge variant="outline" className="text-xs">
          {variantConfig[settings.variant].label}
        </Badge>
        <Badge variant="outline" className="text-xs">
          {styleConfig[settings.style].label}
        </Badge>
      </div>
    </div>
  );
});

ViewCustomizationControls.displayName = 'ViewCustomizationControls';

// Helper function to get grid classes based on settings
export const getGridClasses = (settings: ViewSettings): string => {
  const densityClasses = {
    tight: 'gap-2 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 3xl:grid-cols-8',
    normal: 'gap-3 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 3xl:grid-cols-7',
    spacious: 'gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6',
  };

  return cn('grid', densityClasses[settings.gridDensity]);
};