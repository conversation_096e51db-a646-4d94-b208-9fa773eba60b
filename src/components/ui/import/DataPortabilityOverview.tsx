/**
 * Data Portability Overview Component
 * Provides data export and backup functionality overview
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Alert, AlertDescription } from '@/components/ui/base/alert';
import { 
  Download, 
  Upload, 
  Shield, 
  Clock, 
  Database, 
  FileJson,
  FileSpreadsheet,
  Archive,
  CloudDownload,
  Info,
  CheckCircle,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserStats } from '@/hooks/useUserStats';
import { toast } from 'react-hot-toast';
import { cn } from '@/lib/utils';

interface DataPortabilityOverviewProps {
  className?: string;
}

interface ExportFormat {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  supported: boolean;
  recommended: boolean;
}

export const DataPortabilityOverview: React.FC<DataPortabilityOverviewProps> = ({ 
  className 
}) => {
  const { user } = useAuth();
  const { data: userStats } = useUserStats();
  
  const [isExporting, setIsExporting] = useState(false);
  const [exportFormat, setExportFormat] = useState<string>('csv');
  const [lastExportDate] = useState<Date | null>(null);

  const exportFormats: ExportFormat[] = [
    {
      id: 'csv',
      name: 'CSV',
      description: 'Comma-separated values for spreadsheet applications',
      icon: <FileSpreadsheet className="h-4 w-4" />,
      supported: true,
      recommended: true
    },
    {
      id: 'json',
      name: 'JSON',
      description: 'JavaScript Object Notation for developers',
      icon: <FileJson className="h-4 w-4" />,
      supported: true,
      recommended: false
    },
    {
      id: 'backup',
      name: 'Full Backup',
      description: 'Complete data backup with all metadata',
      icon: <Archive className="h-4 w-4" />,
      supported: false,
      recommended: false
    }
  ];

  const handleExport = async () => {
    if (!user) {
      toast.error('Must be logged in to export data');
      return;
    }

    setIsExporting(true);
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (exportFormat === 'csv') {
        toast.success('CSV export completed successfully');
      } else if (exportFormat === 'json') {
        toast.success('JSON export completed successfully');
      }
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const dataSize = userStats?.totalGames || 0;
  const estimatedFileSize = Math.max(1, Math.round(dataSize * 0.5)); // Rough estimate in KB

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CloudDownload className="h-5 w-5" />
          Data Portability
        </CardTitle>
        <CardDescription>
          Export your game library data for backup or migration
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Data Overview */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Database className="h-5 w-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{dataSize}</div>
            <div className="text-xs text-gray-600">Games in Library</div>
          </div>
          
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Archive className="h-5 w-5 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">~{estimatedFileSize}KB</div>
            <div className="text-xs text-gray-600">Estimated Size</div>
          </div>
        </div>

        {/* Export Formats */}
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <FileSpreadsheet className="h-4 w-4" />
            Export Formats
          </h4>
          
          <div className="space-y-2">
            {exportFormats.map((format) => (
              <div 
                key={format.id}
                className={cn(
                  "flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-colors",
                  format.supported 
                    ? "hover:bg-gray-50 border-gray-200" 
                    : "opacity-50 cursor-not-allowed bg-gray-50 border-gray-100",
                  exportFormat === format.id && format.supported && "border-primary bg-primary/5"
                )}
                onClick={() => format.supported && setExportFormat(format.id)}
              >
                <div className="flex items-center gap-3">
                  {format.icon}
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{format.name}</span>
                      {format.recommended && (
                        <Badge variant="secondary" className="text-xs">Recommended</Badge>
                      )}
                      {!format.supported && (
                        <Badge variant="outline" className="text-xs">Coming Soon</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{format.description}</p>
                  </div>
                </div>
                {exportFormat === format.id && format.supported && (
                  <CheckCircle className="h-5 w-5 text-primary" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Data Privacy Info */}
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Your exported data remains private and is not shared with third parties. 
            Exports include only your game library data and preferences.
          </AlertDescription>
        </Alert>

        {/* Last Export Info */}
        {lastExportDate && (
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Clock className="h-4 w-4 text-gray-600" />
            <span className="text-sm text-gray-700">
              Last export: {lastExportDate.toLocaleDateString()}
            </span>
          </div>
        )}

        {/* Data Included Info */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Data Included in Export:</h4>
          <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-3 w-3 text-green-500" />
              Game titles & metadata
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-3 w-3 text-green-500" />
              Platform information
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-3 w-3 text-green-500" />
              Play status & dates
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-3 w-3 text-green-500" />
              Personal ratings
            </div>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-3 w-3 text-orange-500" />
              Account info (limited)
            </div>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-3 w-3 text-orange-500" />
              API keys (excluded)
            </div>
          </div>
        </div>

        {/* Export Actions */}
        <div className="space-y-3 pt-2 border-t">
          <Button 
            onClick={handleExport}
            disabled={isExporting || !exportFormats.find(f => f.id === exportFormat)?.supported}
            className="w-full"
          >
            {isExporting ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export as {exportFormats.find(f => f.id === exportFormat)?.name}
              </>
            )}
          </Button>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex-1">
              <Info className="h-4 w-4 mr-2" />
              Export Guide
            </Button>
            <Button variant="outline" size="sm" className="flex-1">
              <Upload className="h-4 w-4 mr-2" />
              Import Data
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DataPortabilityOverview;