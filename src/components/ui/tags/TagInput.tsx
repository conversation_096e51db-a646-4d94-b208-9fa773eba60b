import { useState, useRef } from 'react';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/base/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/base/command';
import { useUserTags } from '@/hooks/useUserTags';
import { UserTag } from '@/types/filters';
import { Plus, X, Tag, Hash } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TagInputProps {
  selectedTags: UserTag[];
  onTagsChange: (tags: UserTag[]) => void;
  placeholder?: string;
  className?: string;
  maxTags?: number;
  allowCreate?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function TagInput({
  selectedTags,
  onTagsChange,
  placeholder = 'Add tags...',
  className,
  maxTags = 20,
  allowCreate = true,
  size = 'md'
}: TagInputProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const { tags, createTag, isCreating: isCreatingTag } = useUserTags();

  // Filter available tags (exclude already selected)
  const availableTags = tags.filter(tag => 
    !selectedTags.some(selected => selected.id === tag.id)
  );

  // Filter tags based on search
  const filteredTags = availableTags.filter(tag =>
    tag.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  // Handle tag selection
  const handleTagSelect = (tag: UserTag) => {
    if (selectedTags.length < maxTags) {
      onTagsChange([...selectedTags, tag]);
    }
    setSearchValue('');
    setIsOpen(false);
  };

  // Handle tag removal
  const handleTagRemove = (tagId: string) => {
    onTagsChange(selectedTags.filter(tag => tag.id !== tagId));
  };

  // Handle creating new tag
  const handleCreateTag = async () => {
    if (!searchValue.trim() || isCreatingTag) return;

    setIsCreating(true);
    try {
      await createTag({
        name: searchValue.trim(),
        color: '#3b82f6'
      });
      setSearchValue('');
      setIsOpen(false);
    } catch (error) {
      console.error('Error creating tag:', error);
    } finally {
      setIsCreating(false);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
      setSearchValue('');
    } else if (e.key === 'Enter' && allowCreate && searchValue.trim()) {
      e.preventDefault();
      handleCreateTag();
    }
  };

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1.5',
    lg: 'text-base px-4 py-2'
  };

  return (
    <div className={cn('space-y-3', className)}>
      {/* Selected Tags */}
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedTags.map(tag => (
            <Badge
              key={tag.id}
              variant="secondary"
              className={cn(
                'flex items-center gap-1 transition-colors',
                sizeClasses[size]
              )}
              style={{ backgroundColor: `${tag.color}20`, borderColor: tag.color }}
            >
              <Hash className="h-3 w-3" style={{ color: tag.color }} />
              <span>{tag.name}</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 hover:bg-transparent"
                onClick={() => handleTagRemove(tag.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}

      {/* Tag Input */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isOpen}
            className={cn(
              'w-full justify-start text-left font-normal',
              selectedTags.length === 0 && 'text-muted-foreground'
            )}
            disabled={selectedTags.length >= maxTags}
          >
            <Tag className="mr-2 h-4 w-4" />
            {selectedTags.length === 0 ? placeholder : `${selectedTags.length} tag${selectedTags.length === 1 ? '' : 's'} selected`}
            {selectedTags.length >= maxTags && (
              <span className="ml-auto text-xs text-muted-foreground">
                Max {maxTags} tags
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              ref={inputRef}
              placeholder="Search tags..."
              value={searchValue}
              onValueChange={setSearchValue}
              onKeyDown={handleKeyDown}
            />
            <CommandList>
              <CommandEmpty>
                {allowCreate && searchValue.trim() ? (
                  <div className="p-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={handleCreateTag}
                      disabled={isCreating || isCreatingTag}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Create "{searchValue}"
                    </Button>
                  </div>
                ) : (
                  'No tags found'
                )}
              </CommandEmpty>
              
              {filteredTags.length > 0 && (
                <CommandGroup heading="Available Tags">
                  {filteredTags.map(tag => (
                    <CommandItem
                      key={tag.id}
                      value={tag.name}
                      onSelect={() => handleTagSelect(tag)}
                      className="cursor-pointer"
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: tag.color }}
                        />
                        <span>{tag.name}</span>
                        {tag.description && (
                          <span className="text-xs text-muted-foreground">
                            {tag.description}
                          </span>
                        )}
                        <span className="ml-auto text-xs text-muted-foreground">
                          {tag.usage_count}
                        </span>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
              
              {allowCreate && searchValue.trim() && !filteredTags.some(tag => tag.name.toLowerCase() === searchValue.toLowerCase()) && (
                <CommandGroup heading="Create New">
                  <CommandItem
                    value={`create-${searchValue}`}
                    onSelect={handleCreateTag}
                    className="cursor-pointer"
                    disabled={isCreating || isCreatingTag}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create "{searchValue}"
                  </CommandItem>
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}