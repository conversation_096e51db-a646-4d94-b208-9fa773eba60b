-- Add missing tables for advanced features
-- This migration addresses gaps in Supabase integration identified in codebase audit

-- User Tags Table (for custom tagging system)
CREATE TABLE IF NOT EXISTS user_tags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  color TEXT DEFAULT '#6366f1',
  description TEXT,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT user_tags_name_user_unique UNIQUE (user_id, name)
);

-- User Game Tags Junction Table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS user_game_tags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  user_game_id UUID NOT NULL REFERENCES user_games(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES user_tags(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT user_game_tags_unique UNIQUE (user_game_id, tag_id)
);

-- Filter Presets Table (saved filter combinations)
CREATE TABLE IF NOT EXISTS filter_presets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  filters JSONB NOT NULL DEFAULT '{}',
  is_default BOOLEAN DEFAULT FALSE,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT filter_presets_name_user_unique UNIQUE (user_id, name)
);

-- Tag Suggestions Table (AI-generated tag suggestions)
CREATE TABLE IF NOT EXISTS tag_suggestions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  suggested_tags TEXT[] NOT NULL DEFAULT '{}',
  confidence_scores JSONB DEFAULT '{}',
  is_applied BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  applied_at TIMESTAMPTZ,
  
  CONSTRAINT tag_suggestions_user_game_unique UNIQUE (user_id, game_id)
);

-- User Recommendations Table (AI recommendations storage)
CREATE TABLE IF NOT EXISTS user_recommendations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  recommendation_type TEXT NOT NULL DEFAULT 'ai_based',
  confidence_score DECIMAL(3,2) DEFAULT 0.0,
  reasoning TEXT,
  metadata JSONB DEFAULT '{}',
  is_dismissed BOOLEAN DEFAULT FALSE,
  is_accepted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT user_recommendations_user_game_type_unique UNIQUE (user_id, game_id, recommendation_type)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_tags_user_id ON user_tags(user_id);
CREATE INDEX IF NOT EXISTS idx_user_tags_name ON user_tags(name);
CREATE INDEX IF NOT EXISTS idx_user_game_tags_user_id ON user_game_tags(user_id);
CREATE INDEX IF NOT EXISTS idx_user_game_tags_user_game_id ON user_game_tags(user_game_id);
CREATE INDEX IF NOT EXISTS idx_user_game_tags_tag_id ON user_game_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_filter_presets_user_id ON filter_presets(user_id);
CREATE INDEX IF NOT EXISTS idx_filter_presets_is_default ON filter_presets(user_id, is_default) WHERE is_default = TRUE;
CREATE INDEX IF NOT EXISTS idx_tag_suggestions_user_id ON tag_suggestions(user_id);
CREATE INDEX IF NOT EXISTS idx_tag_suggestions_game_id ON tag_suggestions(game_id);
CREATE INDEX IF NOT EXISTS idx_tag_suggestions_is_applied ON tag_suggestions(user_id, is_applied);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_user_id ON user_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_game_id ON user_recommendations(game_id);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_type ON user_recommendations(user_id, recommendation_type);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_active ON user_recommendations(user_id, is_dismissed, is_accepted) WHERE is_dismissed = FALSE;

-- Add RLS policies
ALTER TABLE user_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_game_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE filter_presets ENABLE ROW LEVEL SECURITY;
ALTER TABLE tag_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_recommendations ENABLE ROW LEVEL SECURITY;

-- User Tags RLS Policies
CREATE POLICY "Users can view their own tags"
  ON user_tags FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own tags"
  ON user_tags FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own tags"
  ON user_tags FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own tags"
  ON user_tags FOR DELETE
  USING (auth.uid() = user_id);

-- User Game Tags RLS Policies
CREATE POLICY "Users can view their own game tags"
  ON user_game_tags FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own game tags"
  ON user_game_tags FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own game tags"
  ON user_game_tags FOR DELETE
  USING (auth.uid() = user_id);

-- Filter Presets RLS Policies
CREATE POLICY "Users can view their own filter presets"
  ON filter_presets FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own filter presets"
  ON filter_presets FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own filter presets"
  ON filter_presets FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own filter presets"
  ON filter_presets FOR DELETE
  USING (auth.uid() = user_id);

-- Tag Suggestions RLS Policies
CREATE POLICY "Users can view their own tag suggestions"
  ON tag_suggestions FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own tag suggestions"
  ON tag_suggestions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own tag suggestions"
  ON tag_suggestions FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own tag suggestions"
  ON tag_suggestions FOR DELETE
  USING (auth.uid() = user_id);

-- User Recommendations RLS Policies
CREATE POLICY "Users can view their own recommendations"
  ON user_recommendations FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own recommendations"
  ON user_recommendations FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own recommendations"
  ON user_recommendations FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own recommendations"
  ON user_recommendations FOR DELETE
  USING (auth.uid() = user_id);

-- Add triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_tags_updated_at 
  BEFORE UPDATE ON user_tags 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_filter_presets_updated_at 
  BEFORE UPDATE ON filter_presets 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_recommendations_updated_at 
  BEFORE UPDATE ON user_recommendations 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add helpful RPC functions for advanced features

-- Function to get user tags with usage counts
CREATE OR REPLACE FUNCTION get_user_tags_with_counts(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  color TEXT,
  description TEXT,
  usage_count BIGINT,
  created_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ut.id,
    ut.name,
    ut.color,
    ut.description,
    COUNT(ugt.id) as usage_count,
    ut.created_at
  FROM user_tags ut
  LEFT JOIN user_game_tags ugt ON ut.id = ugt.tag_id
  WHERE ut.user_id = p_user_id
  GROUP BY ut.id, ut.name, ut.color, ut.description, ut.created_at
  ORDER BY COUNT(ugt.id) DESC, ut.name;
END;
$$;

-- Function to get filter presets with metadata
CREATE OR REPLACE FUNCTION get_user_filter_presets(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  description TEXT,
  filters JSONB,
  is_default BOOLEAN,
  usage_count INTEGER,
  created_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    fp.id,
    fp.name,
    fp.description,
    fp.filters,
    fp.is_default,
    fp.usage_count,
    fp.created_at
  FROM filter_presets fp
  WHERE fp.user_id = p_user_id
  ORDER BY fp.is_default DESC, fp.usage_count DESC, fp.name;
END;
$$;

-- Function to get AI recommendations for user
CREATE OR REPLACE FUNCTION get_user_recommendations(p_user_id UUID, p_limit INTEGER DEFAULT 10)
RETURNS TABLE (
  id UUID,
  game_id UUID,
  game_title TEXT,
  game_cover_image TEXT,
  recommendation_type TEXT,
  confidence_score DECIMAL,
  reasoning TEXT,
  created_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ur.id,
    ur.game_id,
    g.title as game_title,
    g.cover_image as game_cover_image,
    ur.recommendation_type,
    ur.confidence_score,
    ur.reasoning,
    ur.created_at
  FROM user_recommendations ur
  JOIN games g ON ur.game_id = g.id
  WHERE ur.user_id = p_user_id 
    AND ur.is_dismissed = FALSE 
    AND ur.is_accepted = FALSE
  ORDER BY ur.confidence_score DESC, ur.created_at DESC
  LIMIT p_limit;
END;
$$;

-- Grant necessary permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON user_tags TO authenticated;
GRANT ALL ON user_game_tags TO authenticated;
GRANT ALL ON filter_presets TO authenticated;
GRANT ALL ON tag_suggestions TO authenticated;
GRANT ALL ON user_recommendations TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_tags_with_counts(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_filter_presets(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_recommendations(UUID, INTEGER) TO authenticated;