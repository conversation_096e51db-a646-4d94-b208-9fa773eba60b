/**
 * DEMO COMPONENT - Steam Library Style Showcase
 * 
 * This component demonstrates the new Steam-inspired library cards and grid layout
 * with hover effects and enhanced detail modals. Contains hardcoded sample data
 * for demonstration purposes only.
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { But<PERSON> } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { 
  SteamLibraryCard,
  SteamLibraryGrid,
  EnhancedGameDetailModal
} from '@/components/ui/game';
import { UserGameWithDetails } from '@/types/database';
import {
  Grid3X3,
  List,
  Play,
  Settings,
  Gamepad2,
  Star
} from '@/lib/icons';

// DEMO DATA - Hardcoded sample data for demonstration purposes only
const sampleLibraryGames: UserGameWithDetails[] = [
  {
    id: 'demo-1',
    user_id: 'demo-user',
    game_id: 'cyberpunk-2077',
    status: 'playing',
    personal_rating: 5,
    hours_played: 47.5,
    personal_notes: 'Amazing graphics and story!',
    date_added: '2024-01-01T00:00:00Z',
    game: {
      id: 'cyberpunk-2077',
      title: 'Cyberpunk 2077',
      developer: 'CD Projekt RED',
      publisher: 'CD Projekt',
      release_date: '2020-12-10',
      genres: ['Action', 'RPG', 'Open World'],
      platform: 'PC',
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co2nv8.webp',
      description: 'An open-world, action-adventure story set in Night City.',
      metacritic_score: 86,
      screenshots: []
    }
  },
  {
    id: 'demo-2',
    user_id: 'demo-user',
    game_id: 'witcher-3',
    status: 'completed',
    personal_rating: 5,
    hours_played: 127.3,
    personal_notes: 'One of the best RPGs ever made!',
    date_added: '2024-01-01T00:00:00Z',
    game: {
      id: 'witcher-3',
      title: 'The Witcher 3: Wild Hunt',
      developer: 'CD Projekt RED',
      publisher: 'CD Projekt',
      release_date: '2015-05-19',
      genres: ['RPG', 'Fantasy', 'Open World'],
      platform: 'PC',
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co1wyy.webp',
      description: 'A story-driven, next-generation open world role-playing game.',
      metacritic_score: 93,
      screenshots: []
    }
  },
  {
    id: 'demo-3',
    user_id: 'demo-user',
    game_id: 'elden-ring',
    status: 'backlog',
    personal_rating: 4,
    hours_played: 23.7,
    personal_notes: 'Challenging but rewarding',
    date_added: '2024-01-01T00:00:00Z',
    game: {
      id: 'elden-ring',
      title: 'Elden Ring',
      developer: 'FromSoftware',
      publisher: 'Bandai Namco',
      release_date: '2022-02-25',
      genres: ['Action', 'RPG', 'Souls-like'],
      platform: 'PC',
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co4jni.webp',
      description: 'A fantasy action-RPG adventure set within a world created by Hidetaka Miyazaki.',
      metacritic_score: 96,
      screenshots: []
    }
  },
  {
    id: 'demo-4',
    user_id: 'demo-user',
    game_id: 'hades',
    status: 'completed',
    personal_rating: 5,
    hours_played: 89.2,
    personal_notes: 'Perfect roguelike!',
    date_added: '2024-01-01T00:00:00Z',
    game: {
      id: 'hades',
      title: 'Hades',
      developer: 'Supergiant Games',
      publisher: 'Supergiant Games',
      release_date: '2020-09-17',
      genres: ['Action', 'Roguelike', 'Indie'],
      platform: 'PC',
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co2145.webp',
      description: 'A rogue-like dungeon crawler where you defy the god of the dead.',
      metacritic_score: 93,
      screenshots: []
    }
  },
  {
    id: 'demo-5',
    user_id: 'demo-user',
    game_id: 'doom-eternal',
    status: 'wishlist',
    date_added: '2024-01-01T00:00:00Z',
    game: {
      id: 'doom-eternal',
      title: 'DOOM Eternal',
      developer: 'id Software',
      publisher: 'Bethesda Softworks',
      release_date: '2020-03-20',
      genres: ['Action', 'FPS', 'Shooter'],
      platform: 'PC',
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co1tmu.webp',
      description: 'Hell\'s armies have invaded Earth. Become the Slayer in an epic single-player campaign.',
      metacritic_score: 88,
      screenshots: []
    }
  },
  {
    id: 'demo-6',
    user_id: 'demo-user',
    game_id: 'hollow-knight',
    status: 'backlog',
    personal_rating: 3,
    hours_played: 12.1,
    personal_notes: 'Too difficult for me',
    date_added: '2024-01-01T00:00:00Z',
    game: {
      id: 'hollow-knight',
      title: 'Hollow Knight',
      developer: 'Team Cherry',
      publisher: 'Team Cherry',
      release_date: '2017-02-24',
      genres: ['Metroidvania', 'Indie', 'Platformer'],
      platform: 'PC',
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co1rgi.webp',
      description: 'A challenging 2D action-adventure through a vast ruined kingdom.',
      metacritic_score: 90,
      screenshots: []
    }
  }
];

export const SteamLibraryDemo: React.FC = () => {
  const [selectedDemo, setSelectedDemo] = useState<'individual' | 'grid'>('grid');
  const [selectedGame, setSelectedGame] = useState<UserGameWithDetails | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleGameClick = (gameData: UserGameWithDetails) => {
    setSelectedGame(gameData);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedGame(null);
  };

  const handleQuickPlay = (gameData: UserGameWithDetails) => {
    console.log('Quick play:', gameData.game.title);
  };

  const handleStatusUpdate = (userGameId: string, status: string) => {
    console.log('Status update:', userGameId, status);
  };

  const handleAddToWishlist = (gameData: UserGameWithDetails) => {
    console.log('Add to wishlist:', gameData.game.title);
  };

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-blue-600/10 rounded-2xl">
            <Gamepad2 className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Steam Library Demo
            </h1>
            <p className="text-muted-foreground mt-2">
              Showcase of Steam-inspired game library cards with hover effects and detailed modals
            </p>
          </div>
        </div>

        {/* Demo Mode Toggle */}
        <div className="flex items-center justify-center gap-2">
          <Button
            variant={selectedDemo === 'grid' ? 'default' : 'secondary'}
            onClick={() => setSelectedDemo('grid')}
            className="px-4"
          >
            <Grid3X3 className="h-4 w-4 mr-2" />
            Full Grid Demo
          </Button>
          <Button
            variant={selectedDemo === 'individual' ? 'default' : 'secondary'}
            onClick={() => setSelectedDemo('individual')}
            className="px-4"
          >
            <List className="h-4 w-4 mr-2" />
            Individual Cards
          </Button>
        </div>
      </div>

      {/* Demo Content */}
      {selectedDemo === 'grid' ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Grid3X3 className="h-5 w-5" />
              Steam Library Grid
            </CardTitle>
            <p className="text-muted-foreground">
              Complete library experience with search, filters, and sorting
            </p>
          </CardHeader>
          <CardContent>
            <SteamLibraryGrid
              games={sampleLibraryGames}
              onStatusUpdate={handleStatusUpdate}
              onQuickPlay={handleQuickPlay}
              onAddToWishlist={handleAddToWishlist}
              showFilters={true}
              showSearch={true}
              defaultView="grid"
            />
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* Individual Card Examples */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Individual Steam Cards
              </CardTitle>
              <p className="text-muted-foreground">
                Hover over cards to see the Steam-style external hover card with detailed game information
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {sampleLibraryGames.slice(0, 3).map((gameData) => (
                  <SteamLibraryCard
                    key={gameData.id}
                    gameData={gameData}
                    onGameClick={handleGameClick}
                    onStatusUpdate={handleStatusUpdate}
                    onQuickPlay={handleQuickPlay}
                    showHoverDetails={true}
                    enableQuickActions={true}
                    showSteamHoverCard={true}
                  />
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Features Showcase */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Play className="h-5 w-5" />
                  Key Features
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">✨</Badge>
                  <span className="text-sm">Steam-style external hover card</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">🎮</Badge>
                  <span className="text-sm">Quick action buttons</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">📊</Badge>
                  <span className="text-sm">Play time tracking</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">⭐</Badge>
                  <span className="text-sm">Personal ratings</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">🏷️</Badge>
                  <span className="text-sm">Status indicators</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Interaction Guide
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div>
                  <strong>Hover:</strong> See game details and quick actions
                </div>
                <div>
                  <strong>Click:</strong> Open detailed game modal
                </div>
                <div>
                  <strong>Play Button:</strong> Quick play action
                </div>
                <div>
                  <strong>Heart Icon:</strong> Add to wishlist
                </div>
                <div>
                  <strong>More Options:</strong> Additional actions menu
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Enhanced Game Detail Modal */}
      <EnhancedGameDetailModal
        gameData={selectedGame}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onStatusUpdate={handleStatusUpdate}
        onQuickPlay={handleQuickPlay}
        onAddToWishlist={handleAddToWishlist}
      />
    </div>
  );
};
