import { ViewMode } from '@/pages/Library/types';

/**
 * Utility functions for migrating from old platform view preferences
 * to the new view mode system
 */

interface LegacyPreferences {
  default_platform?: string;
  default_sort?: string;
  [key: string]: any;
}

interface ModernPreferences {
  default_view_mode: ViewMode;
  default_platform: string;
  [key: string]: any;
}

/**
 * Migrates legacy platform preferences to modern view mode preferences
 */
export function migrateLegacyViewPreferences(
  legacyPrefs: LegacyPreferences
): Partial<ModernPreferences> {
  const migrated: Partial<ModernPreferences> = {};

  // Handle legacy default_platform field
  if (legacyPrefs.default_platform) {
    const legacyValue = legacyPrefs.default_platform;
    
    // Map legacy values to new view modes
    switch (legacyValue) {
      case 'platform':
        migrated.default_view_mode = 'platform';
        migrated.default_platform = 'platform';
        break;
      case 'platform-family':
        migrated.default_view_mode = 'platform-family';
        migrated.default_platform = 'platform-family';
        break;
      case 'status':
      default:
        migrated.default_view_mode = 'status';
        migrated.default_platform = 'status';
        break;
    }
  }

  // Handle legacy default_sort field (from display preferences)
  if (legacyPrefs.default_sort) {
    const legacySort = legacyPrefs.default_sort;
    
    // If default_sort was used for view mode, migrate it
    if (['platform', 'platform-family', 'status'].includes(legacySort)) {
      migrated.default_view_mode = legacySort as ViewMode;
      migrated.default_platform = legacySort;
    }
  }

  return migrated;
}

/**
 * Checks if preferences need migration from legacy format
 */
export function needsViewPreferenceMigration(preferences: any): boolean {
  // Check if we have legacy fields but no modern view mode field
  const hasLegacyFields = !!(preferences.default_platform || preferences.display?.default_sort);
  const hasModernFields = !!(preferences.default_view_mode || preferences.display?.default_view_mode);
  
  return hasLegacyFields && !hasModernFields;
}

/**
 * Migrates localStorage view mode preferences
 */
export function migrateLocalStorageViewPreferences(): void {
  try {
    // Check for legacy localStorage keys
    const legacyViewMode = localStorage.getItem('library-view-mode');
    const legacyPlatformView = localStorage.getItem('library-platform-view');
    
    // If we have a valid modern view mode, no migration needed
    if (legacyViewMode && ['status', 'platform', 'platform-family'].includes(legacyViewMode)) {
      return;
    }

    // Migrate from legacy platform view setting
    if (legacyPlatformView) {
      let migratedMode: ViewMode = 'status';
      
      try {
        const parsed = JSON.parse(legacyPlatformView);
        if (parsed === 'platform') {
          migratedMode = 'platform';
        } else if (parsed === 'platform-family') {
          migratedMode = 'platform-family';
        }
      } catch {
        // If parsing fails, check string value
        if (legacyPlatformView === 'platform') {
          migratedMode = 'platform';
        } else if (legacyPlatformView === 'platform-family') {
          migratedMode = 'platform-family';
        }
      }
      
      // Set the migrated value
      localStorage.setItem('library-view-mode', migratedMode);
      
      // Clean up legacy key
      localStorage.removeItem('library-platform-view');
    }

    // Migrate collapsed platform states if they exist with old keys
    const legacyCollapsedPlatforms = localStorage.getItem('collapsed-platforms');
    if (legacyCollapsedPlatforms && !localStorage.getItem('library-collapsed-platforms')) {
      localStorage.setItem('library-collapsed-platforms', legacyCollapsedPlatforms);
      localStorage.removeItem('collapsed-platforms');
    }

    // Migrate collapsed family states if they exist with old keys
    const legacyCollapsedFamilies = localStorage.getItem('collapsed-families');
    if (legacyCollapsedFamilies && !localStorage.getItem('library-collapsed-families')) {
      localStorage.setItem('library-collapsed-families', legacyCollapsedFamilies);
      localStorage.removeItem('collapsed-families');
    }

  } catch (error) {
    console.warn('Failed to migrate localStorage view preferences:', error);
  }
}

/**
 * Gets the default view mode based on legacy preferences
 */
export function getDefaultViewModeFromLegacy(legacyPrefs: any): ViewMode {
  // Check various legacy preference locations
  const legacyPlatform = legacyPrefs?.default_platform;
  const legacySort = legacyPrefs?.display?.default_sort;
  const legacyViewMode = legacyPrefs?.display?.default_view_mode;

  // Priority order: modern field > legacy sort > legacy platform > default
  if (legacyViewMode && ['status', 'platform', 'platform-family'].includes(legacyViewMode)) {
    return legacyViewMode as ViewMode;
  }

  if (legacySort && ['status', 'platform', 'platform-family'].includes(legacySort)) {
    return legacySort as ViewMode;
  }

  if (legacyPlatform && ['status', 'platform', 'platform-family'].includes(legacyPlatform)) {
    return legacyPlatform as ViewMode;
  }

  return 'status'; // Safe default
}

/**
 * Validates that a migrated view mode is valid
 */
export function validateMigratedViewMode(mode: unknown): ViewMode {
  if (typeof mode === 'string' && ['status', 'platform', 'platform-family'].includes(mode)) {
    return mode as ViewMode;
  }
  return 'status';
}