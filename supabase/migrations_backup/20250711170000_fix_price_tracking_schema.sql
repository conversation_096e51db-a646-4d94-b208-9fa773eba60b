/*
  # Fix Price Tracking Schema Migration
  
  This migration adds missing columns to the price_tracking table
  to match the TypeScript interface definitions:
  - Rename shop_name to store_name for consistency
  - Add is_on_sale boolean column
  - Add original_price for tracking discounts
  - Add discount_percentage for calculating savings
  - Add url column for store links
*/

-- Add missing columns to price_tracking table
ALTER TABLE price_tracking 
ADD COLUMN IF NOT EXISTS store_name text,
ADD COLUMN IF NOT EXISTS url text,
ADD COLUMN IF NOT EXISTS is_on_sale boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS original_price decimal,
ADD COLUMN IF NOT EXISTS discount_percentage integer;

-- Copy data from shop_name to store_name if shop_name exists
UPDATE price_tracking 
SET store_name = shop_name 
WHERE store_name IS NULL AND shop_name IS NOT NULL;

-- Update store_name to be NOT NULL after copying data
ALTER TABLE price_tracking 
ALTER COLUMN store_name SET NOT NULL;

-- Drop the old shop_name column if it exists
ALTER TABLE price_tracking 
DROP COLUMN IF EXISTS shop_name;

-- Rename shop_url to url if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'price_tracking' AND column_name = 'shop_url') THEN
        ALTER TABLE price_tracking RENAME COLUMN shop_url TO url;
    END IF;
END $$;

-- Add constraints for data validation
ALTER TABLE price_tracking 
ADD CONSTRAINT IF NOT EXISTS check_original_price_positive 
CHECK (original_price IS NULL OR original_price >= 0);

ALTER TABLE price_tracking 
ADD CONSTRAINT IF NOT EXISTS check_discount_percentage_valid 
CHECK (discount_percentage IS NULL OR (discount_percentage >= 0 AND discount_percentage <= 100));

-- Add indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_price_tracking_store_name ON price_tracking(store_name);
CREATE INDEX IF NOT EXISTS idx_price_tracking_is_on_sale ON price_tracking(is_on_sale);
CREATE INDEX IF NOT EXISTS idx_price_tracking_last_updated ON price_tracking(last_updated);

-- Update the existing view if it references old column names
DROP VIEW IF EXISTS price_alerts_with_games;
CREATE OR REPLACE VIEW price_alerts_with_games AS
SELECT 
  pa.*,
  g.title,
  g.cover_image,
  g.platform
FROM price_alerts pa
JOIN games g ON pa.game_id = g.id;

-- Grant necessary permissions
GRANT SELECT ON price_alerts_with_games TO authenticated;

-- Add a function to automatically calculate discount percentage
CREATE OR REPLACE FUNCTION calculate_discount_percentage()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.original_price IS NOT NULL AND NEW.original_price > 0 AND NEW.price < NEW.original_price THEN
    NEW.discount_percentage = ROUND(((NEW.original_price - NEW.price) / NEW.original_price * 100)::numeric, 0)::integer;
    NEW.is_on_sale = true;
  ELSE
    NEW.discount_percentage = NULL;
    NEW.is_on_sale = false;
  END IF;
  
  RETURN NEW;
END;
$$ language plpgsql;

-- Create trigger to automatically calculate discount percentage
DROP TRIGGER IF EXISTS calculate_discount_on_price_update ON price_tracking;
CREATE TRIGGER calculate_discount_on_price_update
  BEFORE INSERT OR UPDATE ON price_tracking
  FOR EACH ROW
  EXECUTE FUNCTION calculate_discount_percentage();