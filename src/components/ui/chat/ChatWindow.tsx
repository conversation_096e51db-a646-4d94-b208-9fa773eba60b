import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { Badge } from '@/components/ui/base/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/base/select';
import { 
  Bot, 
  Minimize2, 
  Maximize2, 
  X, 
  Settings,
  MessageCircle,
  Sparkles,
  Image as ImageIcon,
  RefreshCw
} from 'lucide-react';
import { MessageBubble } from './MessageBubble';
import { ChatInput } from './ChatInput';
import { ChatMessage, AIProvider } from '@/lib/enhancedAIService';
import { cn } from '@/lib/utils';

interface ChatWindowProps {
  messages: ChatMessage[];
  isLoading?: boolean;
  isMinimized?: boolean;
  onSendMessage: (message: string) => void;
  onMinimize?: () => void;
  onMaximize?: () => void;
  onClose?: () => void;
  onClearChat?: () => void;
  currentProvider?: { provider: AIProvider; model: string };
  onProviderChange?: (provider: AIProvider) => void;
  className?: string;
}

export const ChatWindow: React.FC<ChatWindowProps> = ({
  messages,
  isLoading = false,
  isMinimized = false,
  onSendMessage,
  onMinimize,
  onMaximize,
  onClose,
  onClearChat,
  currentProvider,
  onProviderChange,
  className = ''
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  if (isMinimized) {
    return (
      <Card className={cn("fixed bottom-4 right-4 w-80 z-50 shadow-lg", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Bot className="h-4 w-4 text-blue-500" />
              <span>AI Gaming Assistant</span>
              <Badge variant="outline" className="text-xs">
                {currentProvider?.provider || 'AI'}
              </Badge>
            </div>
            <div className="flex items-center gap-1">
              <Button variant="ghost" size="sm" onClick={onMaximize}>
                <Maximize2 className="h-3 w-3" />
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-3 w-3" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-sm text-muted-foreground">
            {messages.length > 0 ? (
              <div className="flex items-center gap-2">
                <MessageCircle className="h-3 w-3" />
                <span>{messages.length} messages</span>
              </div>
            ) : (
              "Click to expand and start chatting!"
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("fixed bottom-4 right-4 w-[600px] h-[700px] z-[100] shadow-xl flex flex-col", className)}>
      <CardHeader className="pb-3 border-b flex-shrink-0">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Bot className="h-4 w-4 text-blue-500" />
            <span>AI Gaming Assistant</span>
            <Sparkles className="h-3 w-3 text-yellow-500" />
          </div>
          <div className="flex items-center gap-1">
            <Badge variant="outline" className="text-xs">
              {currentProvider?.provider?.toUpperCase() || 'AI'}
            </Badge>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-3 w-3" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onMinimize}>
              <Minimize2 className="h-3 w-3" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-3 w-3" />
            </Button>
          </div>
        </CardTitle>
        
        {showSettings && (
          <div className="mt-2 p-3 bg-muted rounded-lg text-xs space-y-3">
            <div>
              <label className="font-medium block mb-1">AI Provider</label>
              <Select
                value={currentProvider?.provider}
                onValueChange={(value: AIProvider) => onProviderChange?.(value)}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select AI provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="openai">OpenAI (GPT-4)</SelectItem>
                  <SelectItem value="gemini">Google Gemini</SelectItem>
                  <SelectItem value="deepseek">DeepSeek</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Current Model:</span>
              <span className="text-muted-foreground">{currentProvider?.model || 'Unknown'}</span>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onClearChat}
                className="text-xs flex-1"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Clear Chat
              </Button>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="flex-1 p-0 flex flex-col min-h-0">
        <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
          {messages.length === 0 ? (
            <div className="text-center py-8">
              <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="font-medium mb-2">Welcome to your AI Gaming Assistant!</h3>
              <p className="text-sm text-muted-foreground mb-4">
                I can help you discover games, find recommendations, search for box art, and discuss your favorite titles.
              </p>
              <div className="grid gap-2 text-xs">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <MessageCircle className="h-3 w-3" />
                  <span>Ask about game recommendations</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <ImageIcon className="h-3 w-3" />
                  <span>Search for game box art</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Sparkles className="h-3 w-3" />
                  <span>Discuss gaming topics</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message, index) => (
                <MessageBubble
                  key={message.id || index}
                  message={message}
                  isLast={index === messages.length - 1}
                />
              ))}
              {isLoading && (
                <MessageBubble
                  message={{
                    id: 'loading',
                    role: 'assistant',
                    content: 'Thinking...',
                    timestamp: new Date()
                  }}
                  isLoading={true}
                />
              )}
              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>

        <div className="border-t p-4 flex-shrink-0 bg-background relative z-[110]">
          <ChatInput
            onSendMessage={onSendMessage}
            disabled={isLoading}
            placeholder="Ask about games, search for box art, or get recommendations..."
          />
        </div>
      </CardContent>
    </Card>
  );
};