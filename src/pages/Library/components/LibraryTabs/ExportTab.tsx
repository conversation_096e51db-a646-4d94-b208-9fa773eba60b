import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button } from '@/components/ui/base';
import { Download, Upload } from '@/lib/icons';
import { ExportTabProps } from '../../types';

export function ExportTab({ onExport, onImport }: ExportTabProps) {
  const handleExportJSON = () => {
    onExport('json', { includeMetadata: true, includeRatings: true });
  };

  const handleExportCSV = () => {
    onExport('csv', { includeMetadata: true, includeRatings: true });
  };

  const handleImportSteam = () => {
    onImport('steam', null, {});
  };

  const handleImportCSV = () => {
    onImport('csv', null, {});
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>📤 Export & Import</CardTitle>
          <CardDescription>
            Backup your collection and import from other platforms
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="font-medium mb-2">Export Options</h3>
                <div className="space-y-2">
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={handleExportJSON}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export as JSON
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={handleExportCSV}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export as CSV
                  </Button>
                </div>
              </div>
              <div>
                <h3 className="font-medium mb-2">Import Options</h3>
                <div className="space-y-2">
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={handleImportSteam}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Import from Steam
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={handleImportCSV}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Import CSV File
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}