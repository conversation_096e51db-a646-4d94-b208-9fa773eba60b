import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { aiRecommendationService, RecommendationRequest } from '../lib/aiRecommendationService';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';

/**
 * Hook for AI-powered game recommendations based on user's collection
 */
export const useAIRecommendations = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isGenerating, setIsGenerating] = useState(false);

  // Get user-based recommendations
  const {
    data: recommendations,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['ai-recommendations', user?.id],
    queryFn: () => aiRecommendationService.generateUserBasedRecommendations(user?.id || '', {
      count: 12
    }),
    enabled: !!user?.id,
    staleTime: 30 * 60 * 1000, // 30 minutes
    retry: 2
  });

  // Generate custom recommendations mutation
  const generateRecommendationsMutation = useMutation({
    mutationFn: (request: RecommendationRequest) =>
      aiRecommendationService.generateUserBasedRecommendations(user?.id || '', request),
    onMutate: () => {
      setIsGenerating(true);
    },
    onSuccess: (data) => {
      queryClient.setQueryData(['ai-recommendations', user?.id], data);
      toast.success('New recommendations generated successfully');
    },
    onError: (error) => {
      console.error('❌ AI Recommendation Error:', error);
      toast.error('Failed to generate recommendations. Please try again.');
    },
    onSettled: () => {
      setIsGenerating(false);
    }
  });

  const generateRecommendations = useCallback((request: RecommendationRequest) => {
    if (!user?.id) {
      toast.error('Please log in to get recommendations');
      return;
    }
    return generateRecommendationsMutation.mutate(request);
  }, [user?.id, generateRecommendationsMutation]);

  return {
    recommendations: recommendations || [],
    isLoading,
    isGenerating: isGenerating || generateRecommendationsMutation.isPending,
    error,
    refetch,
    generateRecommendations
  };
};

/**
 * Hook for similar game recommendations
 */
export const useSimilarGames = (gameId?: string, gameName?: string) => {
  const { user } = useAuth();
  const [isGenerating, setIsGenerating] = useState(false);

  const {
    data: similarGames,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['similar-games', gameId, user?.id],
    queryFn: () => aiRecommendationService.getSimilarGameRecommendations(
      gameId || '',
      gameName || '',
      user?.id || ''
    ),
    enabled: !!gameId && !!gameName && !!user?.id,
    staleTime: 60 * 60 * 1000, // 1 hour
    retry: 2
  });

  // Generate new similar games
  const generateSimilarMutation = useMutation({
    mutationFn: ({ gameId, gameName }: { gameId: string; gameName: string }) =>
      aiRecommendationService.getSimilarGameRecommendations(gameId, gameName, user?.id || ''),
    onMutate: () => {
      setIsGenerating(true);
    },
    onSuccess: () => {
      toast.success('Found similar games successfully');
    },
    onError: (error) => {
      console.error('❌ Similar Games Error:', error);
      toast.error('Failed to find similar games. Please try again.');
    },
    onSettled: () => {
      setIsGenerating(false);
    }
  });

  const generateSimilar = useCallback((gameId: string, gameName: string) => {
    if (!user?.id) {
      toast.error('Please log in to get recommendations');
      return;
    }
    return generateSimilarMutation.mutate({ gameId, gameName });
  }, [user?.id, generateSimilarMutation]);

  return {
    similarGames: similarGames || [],
    isLoading,
    isGenerating: isGenerating || generateSimilarMutation.isPending,
    error,
    refetch,
    generateSimilar
  };
};

/**
 * Simplified hook that combines user-based and similar game recommendations
 */
export const useUserBasedRecommendations = () => {
  const { user } = useAuth();
  const userRecs = useAIRecommendations();

  // Get recommendation stats
  const recommendationStats = {
    totalRecommendations: userRecs.recommendations.length,
    averageConfidence: (() => {
      const allRecs = userRecs.recommendations;
      if (allRecs.length === 0) return 0;
      return allRecs.reduce((sum, rec) => sum + rec.confidence, 0) / allRecs.length;
    })(),
    topCategories: (() => {
      const allRecs = userRecs.recommendations;
      const categoryCount = new Map<string, number>();
      
      allRecs.forEach(rec => {
        rec.categories.forEach(category => {
          categoryCount.set(category, (categoryCount.get(category) || 0) + 1);
        });
      });
      
      return Array.from(categoryCount.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([category, count]) => ({ category, count }));
    })(),
    isLoading: userRecs.isLoading
  };

  // Generate all recommendations
  const generateAllRecommendations = useCallback(() => {
    if (!user?.id) {
      toast.error('Please log in to get recommendations');
      return;
    }

    userRecs.refetch().then(() => {
      toast.success('Recommendations refreshed successfully');
    }).catch((error) => {
      console.error('Error refreshing recommendations:', error);
      toast.error('Failed to refresh recommendations');
    });
  }, [user?.id, userRecs]);

  return {
    recommendations: userRecs.recommendations,
    recommendationStats,
    isLoading: recommendationStats.isLoading,
    isGenerating: userRecs.isGenerating,
    generateAllRecommendations,
    generateRecommendations: userRecs.generateRecommendations
  };
};

// Helper functions for recommendation display
export const getRecommendationBadgeColor = (confidence: number): string => {
  if (confidence >= 85) return 'bg-green-500';
  if (confidence >= 70) return 'bg-blue-500';
  if (confidence >= 55) return 'bg-yellow-500';
  return 'bg-gray-500';
};

export const getRecommendationIcon = (): string => {
  return '';
};