import React, { useState, useRef, useCallback, useMemo } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Star,
  FileImage,
  Zap,
  Sparkles,
  RotateCcw,
  Trash2,
  Eye,
  Grid3X3,
  List,
  Filter,
  SortAsc,
  SortDesc,
  Clock,
  FileType,
  Maximize2,
  Search
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/base/select';
import { Switch } from '@/components/ui/base/switch';
import { Label } from '@/components/ui/base/label';
import { toast } from 'react-hot-toast';
import { customArtworkService, ArtworkType, UploadProgress } from '@/lib/customArtworkService';
import { cn } from '@/lib/utils';
import { BoxArtSearch } from './BoxArtSearch';
import { enhancedAIService } from '@/lib/enhancedAIService';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/base/tabs';

interface UploadFile {
  id: string;
  file: File;
  artworkType: ArtworkType;
  isPrimary: boolean;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
  preview?: string;
  dimensions?: { width: number; height: number };
  optimizedSize?: number;
}

type ViewMode = 'grid' | 'list';
type SortBy = 'name' | 'type' | 'size' | 'status' | 'date';
type SortOrder = 'asc' | 'desc';

interface CustomArtworkUploadProps {
  userGameId: string;
  onUploadComplete?: () => void;
  className?: string;
  showAdvancedFeatures?: boolean;
  gameTitle?: string;
}

export const CustomArtworkUpload: React.FC<CustomArtworkUploadProps> = ({
  userGameId,
  onUploadComplete,
  className = '',
  showAdvancedFeatures = true,
  gameTitle = ''
}) => {
  const queryClient = useQueryClient();
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortBy>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [filterType, setFilterType] = useState<ArtworkType | 'all'>('all');
  const [showPreview, setShowPreview] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'upload' | 'search'>('upload');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const artworkTypes = customArtworkService.getArtworkTypes();

  // Function to invalidate artwork queries broadly
  const invalidateArtworkQueries = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['custom-artwork'] });
    queryClient.invalidateQueries({ queryKey: ['user-games'] });
    queryClient.invalidateQueries({ queryKey: ['games'] });
  }, [queryClient]);

  // Generate preview URL and extract image dimensions
  const generatePreview = useCallback((file: File): Promise<{ preview: string; dimensions: { width: number; height: number } }> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => {
        const img = new Image();
        img.onload = () => {
          resolve({
            preview: reader.result as string,
            dimensions: { width: img.width, height: img.height }
          });
        };
        img.src = reader.result as string;
      };
      reader.readAsDataURL(file);
    });
  }, []);

  // Smart artwork type detection based on image dimensions
  const detectArtworkType = useCallback((dimensions: { width: number; height: number }): ArtworkType => {
    const aspectRatio = dimensions.width / dimensions.height;
    
    if (aspectRatio >= 0.6 && aspectRatio <= 0.8) {
      return 'front';
    }
    if (aspectRatio <= 0.3) {
      return 'spine';
    }
    if (aspectRatio >= 1.2) {
      return 'manual';
    }
    return 'front';
  }, []);

  // Add files to upload queue with enhanced metadata
  const addFiles = useCallback(async (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const newUploadFiles: UploadFile[] = [];

    for (const file of fileArray) {
      if (!file.type.startsWith('image/')) {
        toast.error(`${file.name} is not an image file`);
        continue;
      }

      if (file.size > 5 * 1024 * 1024) {
        toast.error(`${file.name} is too large. Maximum size is 5MB`);
        continue;
      }

      try {
        const { preview, dimensions } = await generatePreview(file);
        const detectedType = detectArtworkType(dimensions);
        
        const uploadFile: UploadFile = {
          id: `${Date.now()}-${Math.random()}`,
          file,
          artworkType: detectedType,
          isPrimary: false,
          progress: 0,
          status: 'pending',
          preview,
          dimensions,
          optimizedSize: file.size
        };

        newUploadFiles.push(uploadFile);
      } catch {
        toast.error(`Failed to process ${file.name}`);
      }
    }

    setUploadFiles(prev => [...prev, ...newUploadFiles]);
    
    if (newUploadFiles.length > 0) {
      toast.success(`Added ${newUploadFiles.length} file${newUploadFiles.length > 1 ? 's' : ''} to upload queue`);
    }
  }, [generatePreview, detectArtworkType]);

  // Enhanced drag and drop with better visual feedback
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    const items = Array.from(e.dataTransfer.items);
    
    const hasNonFiles = items.some(item => item.kind !== 'file');
    if (hasNonFiles) {
      toast.error('Please drop image files only');
      return;
    }

    if (files.length > 0) {
      addFiles(files);
    }
  }, [addFiles]);

  // Handle file input change
  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      addFiles(files);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [addFiles]);

  // Update file properties
  const updateFile = useCallback((id: string, updates: Partial<UploadFile>) => {
    setUploadFiles(prev => prev.map(file => 
      file.id === id ? { ...file, ...updates } : file
    ));
  }, []);

  // Remove file from queue
  const removeFile = useCallback((id: string) => {
    setUploadFiles(prev => prev.filter(file => file.id !== id));
  }, []);

  // Upload single file
  const uploadSingleFile = useCallback(async (uploadFile: UploadFile) => {
    updateFile(uploadFile.id, { status: 'uploading', progress: 0 });

    try {
      const result = await customArtworkService.uploadArtwork(
        userGameId,
        uploadFile.artworkType,
        uploadFile.file,
        uploadFile.isPrimary,
        (progress: UploadProgress) => {
          updateFile(uploadFile.id, { progress: progress.percentage });
        }
      );

      if (result.success && result.artwork) {
        updateFile(uploadFile.id, { status: 'success', progress: 100 });
        toast.success(`${uploadFile.artworkType} artwork uploaded successfully`);
        invalidateArtworkQueries();
        onUploadComplete?.();
      } else {
        updateFile(uploadFile.id, { 
          status: 'error', 
          error: result.error || 'Upload failed' 
        });
        toast.error(result.error || 'Upload failed');
      }
    } catch {
      updateFile(uploadFile.id, { 
        status: 'error', 
        error: 'Unexpected error during upload' 
      });
      toast.error('Unexpected error during upload');
    }
  }, [userGameId, updateFile, onUploadComplete, invalidateArtworkQueries]);

  // Upload all pending files
  const uploadAllFiles = useCallback(async () => {
    const pendingFiles = uploadFiles.filter(file => file.status === 'pending');
    
    for (const file of pendingFiles) {
      await uploadSingleFile(file);
    }
  }, [uploadFiles, uploadSingleFile]);

  // Clear completed/error files
  const clearCompleted = useCallback(() => {
    setUploadFiles(prev => prev.filter(file => 
      file.status === 'pending' || file.status === 'uploading'
    ));
    setSelectedFiles([]);
  }, []);

  // Batch operations
  const handleSelectAll = useCallback(() => {
    const allFileIds = uploadFiles.map(file => file.id);
    setSelectedFiles(allFileIds);
  }, [uploadFiles]);

  const handleDeselectAll = useCallback(() => {
    setSelectedFiles([]);
  }, []);

  const handleSelectFile = useCallback((fileId: string) => {
    setSelectedFiles(prev => 
      prev.includes(fileId) 
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  }, []);

  const handleBatchDelete = useCallback(() => {
    setUploadFiles(prev => prev.filter(file => !selectedFiles.includes(file.id)));
    setSelectedFiles([]);
    toast.success(`Removed ${selectedFiles.length} file${selectedFiles.length > 1 ? 's' : ''} from queue`);
  }, [selectedFiles]);

  const handleBatchChangeType = useCallback((artworkType: ArtworkType) => {
    setUploadFiles(prev => prev.map(file => 
      selectedFiles.includes(file.id) 
        ? { ...file, artworkType }
        : file
    ));
    toast.success(`Changed artwork type for ${selectedFiles.length} file${selectedFiles.length > 1 ? 's' : ''}`);
  }, [selectedFiles]);

  const handleBatchUpload = useCallback(async () => {
    const selectedFilesToUpload = uploadFiles.filter(file => 
      selectedFiles.includes(file.id) && file.status === 'pending'
    );
    
    for (const file of selectedFilesToUpload) {
      await uploadSingleFile(file);
    }
    
    setSelectedFiles([]);
  }, [uploadFiles, selectedFiles, uploadSingleFile]);

  // Search functionality handlers
  const handleImageSearch = useCallback(async (query: string) => {
    try {
      const results = await enhancedAIService.searchGameBoxArt(query);
      return results;
    } catch (error) {
      console.error('Search error:', error);
      throw new Error('Failed to search for images');
    }
  }, []);

  const detectArtworkTypeFromTitle = useCallback((title: string): ArtworkType => {
    const titleLower = title.toLowerCase();
    
    if (titleLower.includes('back') || titleLower.includes('rear')) {
      return 'back';
    }
    if (titleLower.includes('spine') || titleLower.includes('side')) {
      return 'spine';
    }
    if (titleLower.includes('disc') || titleLower.includes('cd') || titleLower.includes('dvd')) {
      return 'disc';
    }
    if (titleLower.includes('manual') || titleLower.includes('instruction')) {
      return 'manual';
    }
    if (titleLower.includes('3d') || titleLower.includes('perspective')) {
      return '3d';
    }
    
    return 'front';
  }, []);

  const handleAddToCollectionFromSearch = useCallback(async (url: string, title: string) => {
    try {
      const artworkType = detectArtworkTypeFromTitle(title);
      
      const result = await customArtworkService.uploadFromUrl(
        userGameId,
        artworkType,
        url,
        title,
        false,
        (progress) => {
          console.log(`Upload progress: ${progress.percentage}%`);
        }
      );

      if (result.success) {
        invalidateArtworkQueries();
        onUploadComplete?.();
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload from search error:', error);
      throw error;
    }
  }, [userGameId, invalidateArtworkQueries, onUploadComplete, detectArtworkTypeFromTitle]);

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <ImageIcon className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: UploadFile['status']) => {
    switch (status) {
      case 'uploading':
        return 'border-blue-200 bg-blue-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  // Sorting and filtering
  const sortedAndFilteredFiles = useMemo(() => {
    let filtered = uploadFiles;
    
    if (filterType !== 'all') {
      filtered = filtered.filter(file => file.artworkType === filterType);
    }
    
    filtered.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;
      
      switch (sortBy) {
        case 'name':
          aValue = a.file.name.toLowerCase();
          bValue = b.file.name.toLowerCase();
          break;
        case 'type':
          aValue = a.artworkType;
          bValue = b.artworkType;
          break;
        case 'size':
          aValue = a.file.size;
          bValue = b.file.size;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'date':
        default:
          aValue = a.id;
          bValue = b.id;
          break;
      }
      
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
    
    return filtered;
  }, [uploadFiles, filterType, sortBy, sortOrder]);

  const pendingCount = uploadFiles.filter(f => f.status === 'pending').length;
  const completedCount = uploadFiles.filter(f => f.status === 'success').length;
  const selectedCount = selectedFiles.length;
  const hasSelection = selectedCount > 0;

  return (
    <div className={cn('w-full h-full flex flex-col', className)}>
      {/* Progress Indicator */}
      {uploadFiles.length > 0 && (
        <div className="mb-6 p-4 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-xl border border-primary/10">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-medium text-foreground">Upload Progress</div>
            <div className="text-xs text-muted-foreground">
              {completedCount} of {uploadFiles.length} completed
            </div>
          </div>
          <div className="w-full bg-muted/30 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-primary to-secondary h-2 rounded-full transition-all duration-500"
              style={{ width: `${uploadFiles.length > 0 ? (completedCount / uploadFiles.length) * 100 : 0}%` }}
            />
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 space-y-6">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'upload' | 'search')}>
          <TabsList className="grid w-full grid-cols-2 bg-gradient-to-r from-muted/50 to-muted/30  border border-muted/20 shadow-lg rounded-xl p-1 h-12">
            <TabsTrigger 
              value="upload" 
              className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/90 data-[state=active]:to-primary data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-primary/25 transition-all duration-300 rounded-lg font-medium"
            >
              <Upload className="h-4 w-4" />
              <span className="hidden sm:inline">Upload Files</span>
              <span className="sm:hidden">Upload</span>
            </TabsTrigger>
            <TabsTrigger 
              value="search" 
              className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/90 data-[state=active]:to-primary data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-primary/25 transition-all duration-300 rounded-lg font-medium"
            >
              <Search className="h-4 w-4" />
              <span className="hidden sm:inline">Search Images</span>
              <span className="sm:hidden">Search</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6 mt-6 flex-1 h-full">
            {/* Upload Controls Header */}
            {showAdvancedFeatures && uploadFiles.length > 0 && (
              <div className="flex items-center justify-between p-4 bg-muted/20 rounded-xl border border-muted/30">
                <div className="flex items-center gap-3">
                  <div className="text-sm font-medium">View Options</div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                    className="bg-white/80  border-muted/30 hover:bg-primary/10 hover:border-primary/30 transition-all duration-200 rounded-lg"
                  >
                    {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                    className="bg-white/80  border-muted/30 hover:bg-primary/10 hover:border-primary/30 transition-all duration-200 rounded-lg"
                  >
                    {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                  </Button>
                </div>
                
                <div className="text-xs text-muted-foreground">
                  {uploadFiles.length} files • {pendingCount} pending • {completedCount} completed
                </div>
              </div>
            )}

            {/* Enhanced Modern Drop Zone */}
            <div
              className={cn(
                'group relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-500 cursor-pointer overflow-hidden ',
                isDragOver 
                  ? 'border-primary/60 bg-gradient-to-br from-primary/15 via-primary/8 to-transparent shadow-2xl scale-[1.03] ring-4 ring-primary/25 shadow-primary/20' 
                  : 'border-muted-foreground/30 hover:border-primary/40 hover:bg-gradient-to-br hover:from-primary/8 hover:via-primary/4 hover:to-transparent hover:shadow-xl hover:scale-[1.01] hover:ring-2 hover:ring-primary/15 bg-gradient-to-br from-background/80 to-muted/20'
              )}
              onDragEnter={handleDragEnter}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              {/* Animated background layers */}
              <div className="absolute inset-0 opacity-30">
                <div className={cn(
                  'absolute inset-0 bg-gradient-to-r from-primary/20 via-secondary/15 to-accent/20 transition-all duration-700',
                  isDragOver ? 'animate-pulse opacity-100 scale-110' : 'opacity-0'
                )} />
                <div className={cn(
                  'absolute inset-0 bg-gradient-to-br from-transparent via-primary/10 to-secondary/10 transition-all duration-500',
                  isDragOver ? 'animate-pulse opacity-100' : 'opacity-0 group-hover:opacity-60'
                )} />
              </div>

              {/* Floating particles effect */}
              <div className={cn(
                'absolute inset-0 transition-opacity duration-300',
                isDragOver ? 'opacity-100' : 'opacity-0'
              )}>
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className={cn(
                      'absolute w-1 h-1 bg-primary/40 rounded-full',
                      'animate-float'
                    )}
                    style={{
                      left: `${20 + i * 15}%`,
                      top: `${30 + (i % 2) * 20}%`,
                      animationDelay: `${i * 0.5}s`,
                      animationDuration: '3s'
                    }}
                  />
                ))}
              </div>
              
              <div className="relative z-10">
                {/* Enhanced icon container */}
                <div className="relative mb-8">
                  <div className={cn(
                    'absolute inset-0 rounded-full transition-all duration-500 animate-float',
                    isDragOver 
                      ? 'bg-gradient-to-br from-primary/25 to-secondary/20 scale-125 shadow-lg shadow-primary/25' 
                      : 'bg-gradient-to-br from-muted/30 to-background/50 group-hover:scale-110 group-hover:shadow-md group-hover:from-primary/15 group-hover:to-secondary/10'
                  )} />
                  <div className="relative p-6">
                    {isDragOver ? (
                      <div className="relative">
                        <Sparkles className="h-16 w-16 mx-auto text-primary animate-bounce drop-shadow-lg" />
                        <div className="absolute inset-0 animate-ping">
                          <Sparkles className="h-16 w-16 mx-auto text-primary/30" />
                        </div>
                      </div>
                    ) : (
                      <Upload className={cn(
                        'h-16 w-16 mx-auto transition-all duration-300 drop-shadow-sm',
                        'text-muted-foreground group-hover:text-primary group-hover:scale-110'
                      )} />
                    )}
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className={cn(
                    'text-xl font-bold transition-all duration-300 tracking-tight',
                    isDragOver 
                      ? 'text-primary scale-105 drop-shadow-sm' 
                      : 'text-foreground group-hover:text-primary'
                  )}>
                    {isDragOver ? 'Drop your artwork here!' : 'Drop images here or click to browse'}
                  </h3>
                  
                  <p className={cn(
                    'text-sm transition-all duration-300',
                    isDragOver 
                      ? 'text-primary/80 font-medium' 
                      : 'text-muted-foreground group-hover:text-foreground/80'
                  )}>
                    {isDragOver 
                      ? 'Release to add files to your upload queue'
                      : 'Upload custom box art, covers, and artwork for your games'
                    }
                  </p>
                  
                  {/* Enhanced format badges */}
                  <div className="flex flex-wrap justify-center gap-3 text-xs">
                    <Badge variant="outline" className="bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200/60 shadow-sm hover:shadow-md transition-shadow ">
                      <CheckCircle className="h-3 w-3 mr-1.5" />
                      JPEG
                    </Badge>
                    <Badge variant="outline" className="bg-gradient-to-r from-blue-50 to-sky-50 text-blue-700 border-blue-200/60 shadow-sm hover:shadow-md transition-shadow ">
                      <CheckCircle className="h-3 w-3 mr-1.5" />
                      PNG
                    </Badge>
                    <Badge variant="outline" className="bg-gradient-to-r from-purple-50 to-violet-50 text-purple-700 border-purple-200/60 shadow-sm hover:shadow-md transition-shadow ">
                      <CheckCircle className="h-3 w-3 mr-1.5" />
                      WebP
                    </Badge>
                    <Badge variant="outline" className="bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border-orange-200/60 shadow-sm hover:shadow-md transition-shadow ">
                      <Zap className="h-3 w-3 mr-1.5" />
                      Max 5MB
                    </Badge>
                  </div>
                  
                  {showAdvancedFeatures && (
                    <div className={cn(
                      'flex items-center justify-center gap-3 text-xs transition-all duration-300 mt-6 px-4 py-2 rounded-full ',
                      'text-muted-foreground/80 group-hover:text-foreground/70 bg-background/30 border border-muted/30'
                    )}>
                      <div className="flex items-center gap-1">
                        <FileImage className="h-3.5 w-3.5" />
                        <span className="font-medium">Smart detection</span>
                      </div>
                      <div className="w-1 h-1 rounded-full bg-muted-foreground/40" />
                      <div className="flex items-center gap-1">
                        <Sparkles className="h-3.5 w-3.5" />
                        <span className="font-medium">Auto-optimization</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileInput}
              className="hidden"
            />

            {/* Enhanced Upload Queue */}
            {uploadFiles.length > 0 && (
              <div className="space-y-6">
                {/* Enhanced Queue Header with Bulk Actions */}
                <div className="p-4 bg-gradient-to-r from-muted/30 to-muted/20 rounded-xl border border-muted/20">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <h4 className="font-bold text-lg">
                        Upload Queue 
                        <span className="text-sm font-normal text-muted-foreground ml-2">
                          ({sortedAndFilteredFiles.length} of {uploadFiles.length} files)
                        </span>
                      </h4>
                      
                      {hasSelection && (
                        <Badge variant="secondary" className="bg-gradient-to-r from-primary/10 to-secondary/10 text-primary border border-primary/20">
                          {selectedCount} selected
                        </Badge>
                      )}
                    </div>
              
                    <div className="flex items-center gap-2">
                      {/* Selection Controls */}
                      {showAdvancedFeatures && uploadFiles.length > 0 && (
                        <div className="flex items-center gap-2 mr-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={selectedCount === uploadFiles.length ? handleDeselectAll : handleSelectAll}
                          >
                            {selectedCount === uploadFiles.length ? 'Deselect All' : 'Select All'}
                          </Button>
                        </div>
                      )}
                      
                      {/* Batch Actions */}
                      {hasSelection && (
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleBatchUpload}
                            disabled={!selectedFiles.some(id => 
                              uploadFiles.find(f => f.id === id)?.status === 'pending'
                            )}
                          >
                            <Upload className="h-4 w-4 mr-1" />
                            Upload Selected
                          </Button>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleBatchDelete}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Remove Selected
                          </Button>
                          
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">Change type:</span>
                            <select
                              onChange={(e) => handleBatchChangeType(e.target.value as ArtworkType)}
                              className="text-sm border rounded px-2 py-1"
                              defaultValue=""
                            >
                              <option value="" disabled>Select type</option>
                              {artworkTypes.map(type => (
                                <option key={type.value} value={type.value}>
                                  {type.label}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>
                      )}
                      
                      {/* Regular Actions */}
                      <div className="flex gap-2">
                        {pendingCount > 0 && (
                          <Button 
                            onClick={uploadAllFiles} 
                            size="sm"
                            className="bg-primary hover:bg-primary/90"
                          >
                            <Upload className="h-4 w-4 mr-1" />
                            Upload All ({pendingCount})
                          </Button>
                        )}
                        
                        {(completedCount > 0 || uploadFiles.some(f => f.status === 'error')) && (
                          <Button 
                            onClick={clearCompleted} 
                            variant="outline" 
                            size="sm"
                          >
                            <RotateCcw className="h-4 w-4 mr-1" />
                            Clear Completed
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Quick Stats */}
                  <div className="flex items-center gap-6 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <span className="text-muted-foreground">{pendingCount} pending</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <span className="text-muted-foreground">{uploadFiles.filter(f => f.status === 'uploading').length} uploading</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-muted-foreground">{completedCount} completed</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-muted-foreground">{uploadFiles.filter(f => f.status === 'error').length} errors</span>
                    </div>
                  </div>
                </div>
                
                {/* Filters and Sorting */}
                {showAdvancedFeatures && uploadFiles.length > 0 && (
                  <div className="flex items-center gap-4 p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Filter className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Filter:</span>
                      <select
                        value={filterType}
                        onChange={(e) => setFilterType(e.target.value as ArtworkType | 'all')}
                        className="text-sm border rounded px-2 py-1"
                      >
                        <option value="all">All Types</option>
                        {artworkTypes.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Sort by:</span>
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value as SortBy)}
                        className="text-sm border rounded px-2 py-1"
                      >
                        <option value="date">Date Added</option>
                        <option value="name">File Name</option>
                        <option value="type">Artwork Type</option>
                        <option value="size">File Size</option>
                        <option value="status">Status</option>
                      </select>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>
                        {pendingCount} pending • {completedCount} completed • {uploadFiles.filter(f => f.status === 'error').length} errors
                      </span>
                    </div>
                  </div>
                )}

                <div className={cn(
                  viewMode === 'grid' 
                    ? 'grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5' 
                    : 'space-y-4'
                )}>
                  {sortedAndFilteredFiles.map(uploadFile => (
                    <Card 
                      key={uploadFile.id} 
                      className={cn(
                        'group relative transition-all duration-300 cursor-pointer  border-0',
                        'hover:shadow-2xl hover:shadow-primary/10 hover:scale-[1.02] hover:-translate-y-1',
                        'bg-gradient-to-br from-background/95 to-muted/30',
                        getStatusColor(uploadFile.status),
                        selectedFiles.includes(uploadFile.id) && 'ring-2 ring-primary shadow-lg shadow-primary/20 scale-[1.01]',
                        viewMode === 'list' && 'flex items-center p-4',
                        uploadFile.status === 'uploading' && 'animate-pulse',
                        uploadFile.status === 'success' && 'shadow-green-500/20 border-green-200/30',
                        uploadFile.status === 'error' && 'shadow-red-500/20 border-red-200/30'
                      )}
                    >
                      <CardContent className={cn(
                        viewMode === 'grid' ? 'p-4' : 'p-0 flex-1 flex items-center gap-4'
                      )}>
                        {/* Selection Checkbox */}
                        {showAdvancedFeatures && (
                          <div className={cn(
                            'absolute z-10',
                            viewMode === 'grid' ? 'top-2 left-2' : 'relative'
                          )}>
                            <input
                              type="checkbox"
                              checked={selectedFiles.includes(uploadFile.id)}
                              onChange={() => handleSelectFile(uploadFile.id)}
                              className="w-4 h-4 accent-primary"
                            />
                          </div>
                        )}
                        
                        {/* Enhanced Preview Image */}
                        <div className={cn(
                          'rounded-xl overflow-hidden bg-gradient-to-br from-muted/50 to-muted/80 relative group cursor-pointer shadow-inner',
                          viewMode === 'grid' ? 'aspect-[2/3] mb-4' : 'w-16 h-20 flex-shrink-0',
                          'ring-1 ring-black/5 hover:ring-primary/20 transition-all duration-300'
                        )}>
                          {uploadFile.preview && (
                            <>
                              <img 
                                src={uploadFile.preview}
                                alt="Preview"
                                className="w-full h-full object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"
                                onClick={() => setShowPreview(uploadFile.id)}
                              />
                              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                                <Button
                                  size="sm"
                                  variant="secondary"
                                  className="bg-white/20 hover:bg-white/30 text-white border-white/30  shadow-2xl rounded-full p-3 h-10 w-10 hover:scale-110 transition-all duration-200"
                                >
                                  <Maximize2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </>
                          )}
                          
                          {/* Enhanced Status overlay */}
                          {uploadFile.status === 'uploading' && (
                            <div className="absolute inset-0 bg-black/60  flex flex-col items-center justify-center">
                              <div className="relative mb-2">
                                <Loader2 className="h-8 w-8 text-white animate-spin" />
                                <div className="absolute inset-0 border-2 border-white/20 rounded-full animate-ping" />
                              </div>
                              <div className="text-white text-xs font-medium bg-black/40 px-2 py-1 rounded-full ">
                                {uploadFile.progress}%
                              </div>
                            </div>
                          )}

                          {/* Success overlay */}
                          {uploadFile.status === 'success' && (
                            <div className="absolute top-2 right-2">
                              <div className="bg-green-500 text-white rounded-full p-1.5 shadow-lg animate-bounce">
                                <CheckCircle className="h-3 w-3" />
                              </div>
                            </div>
                          )}

                          {/* Error overlay */}
                          {uploadFile.status === 'error' && (
                            <div className="absolute top-2 right-2">
                              <div className="bg-red-500 text-white rounded-full p-1.5 shadow-lg animate-pulse">
                                <AlertCircle className="h-3 w-3" />
                              </div>
                            </div>
                          )}
                        </div>

                        {/* File Info */}
                        <div className={cn(
                          'space-y-3 flex-1',
                          viewMode === 'list' && 'space-y-2'
                        )}>
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <span className="text-sm font-medium truncate block">
                                {uploadFile.file.name}
                              </span>
                              {viewMode === 'list' && uploadFile.dimensions && (
                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                  <span>{uploadFile.dimensions.width}×{uploadFile.dimensions.height}px</span>
                                  <span>•</span>
                                  <span>{(uploadFile.file.size / 1024 / 1024).toFixed(1)}MB</span>
                                </div>
                              )}
                            </div>
                            
                            <div className="flex items-center gap-1">
                              {viewMode === 'list' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setShowPreview(uploadFile.id)}
                                  className="h-6 w-6 p-0"
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(uploadFile.id)}
                                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          {/* Artwork Type Selector */}
                          {viewMode === 'grid' && (
                            <div className="space-y-2">
                              <Label className="text-xs font-medium">Artwork Type</Label>
                              <Select
                                value={uploadFile.artworkType}
                                onValueChange={(value: ArtworkType) => 
                                  updateFile(uploadFile.id, { artworkType: value })
                                }
                                disabled={uploadFile.status === 'uploading'}
                              >
                                <SelectTrigger className="w-full h-8 text-xs">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {artworkTypes.map(type => (
                                    <SelectItem key={type.value} value={type.value}>
                                      <div className="flex items-center gap-2">
                                        <FileType className="h-3 w-3" />
                                        {type.label}
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}

                          {/* List View Type Display */}
                          {viewMode === 'list' && (
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {artworkTypes.find(t => t.value === uploadFile.artworkType)?.label}
                              </Badge>
                              {uploadFile.isPrimary && (
                                <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">
                                  <Star className="h-3 w-3 mr-1 fill-current" />
                                  Primary
                                </Badge>
                              )}
                            </div>
                          )}

                          {/* Primary Toggle */}
                          {viewMode === 'grid' && (
                            <div className="flex items-center space-x-2">
                              <Switch
                                id={`primary-${uploadFile.id}`}
                                checked={uploadFile.isPrimary}
                                onCheckedChange={(checked) => 
                                  updateFile(uploadFile.id, { isPrimary: checked })
                                }
                                disabled={uploadFile.status === 'uploading'}
                              />
                              <Label htmlFor={`primary-${uploadFile.id}`} className="text-xs">
                                Set as primary
                              </Label>
                              {uploadFile.isPrimary && (
                                <Star className="h-3 w-3 text-yellow-500 fill-current" />
                              )}
                            </div>
                          )}

                          {/* Enhanced Status and Progress */}
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                {getStatusIcon(uploadFile.status)}
                                <span className="text-xs capitalize font-medium">
                                  {uploadFile.status}
                                </span>
                                {uploadFile.status === 'uploading' && (
                                  <span className="text-xs text-muted-foreground">
                                    {uploadFile.progress}%
                                  </span>
                                )}
                              </div>
                              
                              <div className="flex items-center gap-1">
                                {uploadFile.status === 'pending' && (
                                  <Button
                                    size="sm"
                                    onClick={() => uploadSingleFile(uploadFile)}
                                    className="h-7 px-3 text-xs bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-white shadow-sm"
                                  >
                                    <Upload className="h-3 w-3 mr-1" />
                                    Upload
                                  </Button>
                                )}
                                
                                {uploadFile.status === 'success' && viewMode === 'grid' && (
                                  <>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => setShowPreview(uploadFile.id)}
                                      className="h-7 w-7 p-0 hover:bg-primary/10 rounded-lg"
                                      title="Preview"
                                    >
                                      <Eye className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => {
                                        updateFile(uploadFile.id, { isPrimary: !uploadFile.isPrimary })
                                      }}
                                      className={cn(
                                        "h-7 w-7 p-0 rounded-lg transition-colors",
                                        uploadFile.isPrimary 
                                          ? "bg-yellow-100 hover:bg-yellow-200 text-yellow-600" 
                                          : "hover:bg-yellow-50 text-muted-foreground hover:text-yellow-600"
                                      )}
                                      title={uploadFile.isPrimary ? "Remove as primary" : "Set as primary"}
                                    >
                                      <Star className={cn("h-3 w-3", uploadFile.isPrimary && "fill-current")} />
                                    </Button>
                                  </>
                                )}
                              </div>
                            </div>

                            {uploadFile.status === 'uploading' && (
                              <div className="space-y-2">
                                <Progress 
                                  value={uploadFile.progress} 
                                  className="h-2 bg-muted/50 overflow-hidden" 
                                />
                                <div className="flex justify-between text-xs text-muted-foreground">
                                  <span>Uploading...</span>
                                  <span className="font-medium">{uploadFile.progress}%</span>
                                </div>
                              </div>
                            )}

                            {uploadFile.error && (
                              <div className="bg-red-50 border border-red-200 rounded p-2">
                                <p className="text-xs text-red-600 font-medium">
                                  {uploadFile.error}
                                </p>
                              </div>
                            )}
                            
                            {/* File Details for Grid View */}
                            {viewMode === 'grid' && uploadFile.dimensions && (
                              <div className="text-xs text-muted-foreground space-y-1">
                                <div className="flex items-center justify-between">
                                  <span>Dimensions:</span>
                                  <span>{uploadFile.dimensions.width}×{uploadFile.dimensions.height}px</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span>Size:</span>
                                  <span>{(uploadFile.file.size / 1024 / 1024).toFixed(1)}MB</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span>Type:</span>
                                  <span>{uploadFile.file.type.split('/')[1].toUpperCase()}</span>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Enhanced Info Section */}
            <div className="bg-gradient-to-br from-blue-50/80 via-purple-50/60 to-indigo-50/80  rounded-2xl p-6 border border-blue-200/40 shadow-lg">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl">
                  <Sparkles className="h-6 w-6 text-blue-600" />
                </div>
                <div className="space-y-3 text-sm flex-1">
                  <div className="font-bold text-blue-900 text-lg">Enhanced Features</div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="flex items-center gap-2 text-blue-700">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                      <span>Smart artwork type detection</span>
                    </div>
                    <div className="flex items-center gap-2 text-blue-700">
                      <div className="w-1.5 h-1.5 bg-purple-500 rounded-full" />
                      <span>Automatic image optimization</span>
                    </div>
                    <div className="flex items-center gap-2 text-blue-700">
                      <div className="w-1.5 h-1.5 bg-indigo-500 rounded-full" />
                      <span>Batch operations support</span>
                    </div>
                    <div className="flex items-center gap-2 text-blue-700">
                      <div className="w-1.5 h-1.5 bg-violet-500 rounded-full" />
                      <span>Responsive grid & list views</span>
                    </div>
                    <div className="flex items-center gap-2 text-blue-700">
                      <div className="w-1.5 h-1.5 bg-pink-500 rounded-full" />
                      <span>Advanced sorting & filtering</span>
                    </div>
                    <div className="flex items-center gap-2 text-blue-700">
                      <div className="w-1.5 h-1.5 bg-cyan-500 rounded-full" />
                      <span>Enhanced preview modal</span>
                    </div>
                  </div>
                  <div className="bg-blue-100/60  rounded-lg px-3 py-2 border border-blue-200/30">
                    <div className="text-xs text-blue-600 font-medium">
                      📁 Supports JPEG, PNG, and WebP formats • ⚡ Max 5MB per file • 🔒 Secure upload processing
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Preview Modal */}
            {showPreview && (
              <div className="fixed inset-0 bg-black/90  flex items-center justify-center z-50 p-4 animate-in fade-in duration-300">
                <div className="bg-white/95  rounded-2xl max-w-5xl max-h-[95vh] overflow-hidden shadow-2xl border border-white/20 animate-in zoom-in-95 duration-300">
                  {/* Enhanced Header */}
                  <div className="flex items-center justify-between p-6 border-b border-muted/20 bg-gradient-to-r from-background/80 to-muted/40">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-full">
                        <ImageIcon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">
                          {uploadFiles.find(f => f.id === showPreview)?.file.name}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {uploadFiles.find(f => f.id === showPreview)?.artworkType} artwork
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowPreview(null)}
                      className="h-10 w-10 rounded-full hover:bg-muted/50 transition-colors"
                    >
                      <X className="h-5 w-5" />
                    </Button>
                  </div>
                  
                  {/* Enhanced Image Display */}
                  <div className="p-6 bg-gradient-to-br from-muted/20 to-muted/40">
                    <div className="relative bg-white/80  rounded-xl p-4 shadow-inner">
                      <img
                        src={uploadFiles.find(f => f.id === showPreview)?.preview}
                        alt="Preview"
                        className="max-w-full max-h-[60vh] object-contain mx-auto rounded-lg shadow-lg transition-transform duration-300 hover:scale-105"
                      />
                    </div>
                    
                    {/* Enhanced Metadata */}
                    <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                      {uploadFiles.find(f => f.id === showPreview)?.dimensions && (
                        <div className="bg-white/60  rounded-lg p-3 border border-white/30">
                          <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Dimensions</div>
                          <div className="text-sm font-semibold mt-1">
                            {uploadFiles.find(f => f.id === showPreview)?.dimensions?.width}×{uploadFiles.find(f => f.id === showPreview)?.dimensions?.height}px
                          </div>
                        </div>
                      )}
                      <div className="bg-white/60  rounded-lg p-3 border border-white/30">
                        <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">File Size</div>
                        <div className="text-sm font-semibold mt-1">
                          {((uploadFiles.find(f => f.id === showPreview)?.file.size || 0) / 1024 / 1024).toFixed(1)}MB
                        </div>
                      </div>
                      <div className="bg-white/60  rounded-lg p-3 border border-white/30">
                        <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Format</div>
                        <div className="text-sm font-semibold mt-1">
                          {uploadFiles.find(f => f.id === showPreview)?.file.type.split('/')[1].toUpperCase()}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="search" className="space-y-6 mt-6 flex-1 h-full">
            <div className="h-full flex flex-col">
              <div className="mb-4 p-4 bg-gradient-to-r from-blue-50/80 to-purple-50/80 rounded-xl border border-blue-200/40">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/10 rounded-lg">
                    <Search className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-sm font-semibold text-blue-900">Search Online Artwork</div>
                    <div className="text-xs text-blue-600">Find high-quality box art and covers from across the web</div>
                  </div>
                </div>
              </div>
              
              <div className="flex-1">
                <BoxArtSearch
                  onSearch={handleImageSearch}
                  onAddToCollection={handleAddToCollectionFromSearch}
                  initialSearchQuery={gameTitle ? `${gameTitle} box art` : ''}
                  className="w-full h-full"
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CustomArtworkUpload;