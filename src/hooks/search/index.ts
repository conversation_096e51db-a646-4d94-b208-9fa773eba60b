// Main composed hook (replacement for useEnhancedSearch)
export { useEnhancedSearchComposed } from './useEnhancedSearchComposed';

// Individual focused hooks
export { useSearchState } from './useSearchState';
export { useSearchActions } from './useSearchActions';
export { useVoiceSearch } from './useVoiceSearch';
export { useSearchUtils } from './useSearchUtils';
export { useSearchHistory } from './useSearchHistory';
export { useSearchAnalytics } from './useSearchAnalytics';

// Types
export type { SearchFilters, VoiceSearchState } from './useSearchState';