/**
 * Example component demonstrating the new image display improvements
 * Shows different modes and configurations
 */

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { But<PERSON> } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { 
  EnhancedGameImage, 
  SteamGameImage, 
  FlexibleGameImage, 
  ImprovedGameImage 
} from '@/components/ui/game/enhanced-game-image';
import { useSmartImageDisplay } from '@/hooks/useSmartImageDisplay';
import { getOptimalAspectRatio, getSmartObjectFit } from '@/lib/utils/imageDisplayUtils';

// Example game data with different aspect ratios
const exampleGames = [
  {
    id: '1',
    title: 'Cyberpunk 2077',
    cover: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co2lbd.jpg',
    dimensions: { width: 264, height: 352 }, // Portrait
    description: 'Standard portrait game cover (2:3 ratio)'
  },
  {
    id: '2', 
    title: 'Steam Game',
    cover: 'https://cdn.akamai.steamstatic.com/steam/apps/1091500/header.jpg',
    dimensions: { width: 460, height: 215 }, // Steam capsule
    description: 'Steam capsule format (wide landscape)'
  },
  {
    id: '3',
    title: 'Square Game',
    cover: 'https://via.placeholder.com/400x400/4a90e2/ffffff?text=Square+Game',
    dimensions: { width: 400, height: 400 }, // Square
    description: 'Square format game cover'
  },
  {
    id: '4',
    title: 'Wide Game',
    cover: 'https://via.placeholder.com/800x450/50c878/ffffff?text=Wide+Game',
    dimensions: { width: 800, height: 450 }, // 16:9
    description: 'Wide landscape format (16:9 ratio)'
  }
];

const displayModes = [
  { key: 'adaptive', label: 'Adaptive', description: 'Smart switching based on image ratio' },
  { key: 'steam', label: 'Steam', description: 'Steam-optimized display' },
  { key: 'flexible', label: 'Flexible', description: 'Prioritizes showing full image' },
  { key: 'traditional', label: 'Traditional', description: 'Always fills container' }
] as const;

export const ImageDisplayExample: React.FC = () => {
  const [selectedMode, setSelectedMode] = useState<'adaptive' | 'steam' | 'flexible' | 'traditional'>('adaptive');
  const [showDimensions, setShowDimensions] = useState(true);

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Game Image Display Improvements Demo</CardTitle>
          <p className="text-muted-foreground">
            Demonstrating the new smart image display system with dynamic aspect ratios and object fitting.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Mode Selection */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Display Mode</h3>
            <div className="flex flex-wrap gap-2">
              {displayModes.map((mode) => (
                <Button
                  key={mode.key}
                  variant={selectedMode === mode.key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedMode(mode.key)}
                >
                  {mode.label}
                </Button>
              ))}
            </div>
            <p className="text-xs text-muted-foreground">
              {displayModes.find(m => m.key === selectedMode)?.description}
            </p>
          </div>

          {/* Options */}
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={showDimensions}
                onChange={(e) => setShowDimensions(e.target.checked)}
                className="rounded"
              />
              Show image dimensions
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Game Examples Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {exampleGames.map((game) => {
          const optimalAspectRatio = getOptimalAspectRatio(game.dimensions.width, game.dimensions.height);
          const smartObjectFit = getSmartObjectFit(
            game.dimensions.width / game.dimensions.height,
            2/3, // Standard container ratio
            selectedMode
          );

          return (
            <Card key={game.id} className="group">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">{game.title}</CardTitle>
                <p className="text-xs text-muted-foreground">{game.description}</p>
                {showDimensions && (
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="outline" className="text-xs">
                      {game.dimensions.width}×{game.dimensions.height}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {optimalAspectRatio}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {smartObjectFit}
                    </Badge>
                  </div>
                )}
              </CardHeader>
              <CardContent>
                <EnhancedGameImage
                  src={game.cover}
                  alt={game.title}
                  gameName={game.title}
                  mode={selectedMode}
                  enableHover={true}
                  showFallback={true}
                  loading="lazy"
                  quality="medium"
                />
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Component Variants Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Component Variants</CardTitle>
          <p className="text-muted-foreground">
            Different specialized components for specific use cases.
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Enhanced (Adaptive)</h4>
              <EnhancedGameImage
                src={exampleGames[0].cover}
                alt={exampleGames[0].title}
                gameName={exampleGames[0].title}
                mode="adaptive"
              />
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Steam Optimized</h4>
              <SteamGameImage
                src={exampleGames[1].cover}
                alt={exampleGames[1].title}
                gameName={exampleGames[1].title}
              />
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Flexible Display</h4>
              <FlexibleGameImage
                src={exampleGames[2].cover}
                alt={exampleGames[2].title}
                gameName={exampleGames[2].title}
              />
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Improved Traditional</h4>
              <ImprovedGameImage
                src={exampleGames[3].cover}
                alt={exampleGames[3].title}
                gameName={exampleGames[3].title}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Hook Usage Example */}
      <Card>
        <CardHeader>
          <CardTitle>Hook Usage Example</CardTitle>
          <p className="text-muted-foreground">
            Using the useSmartImageDisplay hook directly for custom implementations.
          </p>
        </CardHeader>
        <CardContent>
          <HookUsageDemo />
        </CardContent>
      </Card>
    </div>
  );
};

// Separate component to demonstrate hook usage
const HookUsageDemo: React.FC = () => {
  const { containerClasses, imageClasses, config, imageDimensions } = useSmartImageDisplay({
    src: exampleGames[0].cover,
    mode: 'adaptive',
    enableHover: true
  });

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Generated Classes</h4>
          <div className="text-xs space-y-1">
            <div>
              <strong>Container:</strong>
              <code className="block bg-muted p-2 rounded text-xs mt-1 break-all">
                {containerClasses}
              </code>
            </div>
            <div>
              <strong>Image:</strong>
              <code className="block bg-muted p-2 rounded text-xs mt-1 break-all">
                {imageClasses}
              </code>
            </div>
          </div>
        </div>
        
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Configuration</h4>
          <div className="text-xs space-y-1">
            <div><strong>Aspect Ratio:</strong> {config.aspectRatio}</div>
            <div><strong>Object Fit:</strong> {config.objectFit}</div>
            <div><strong>Padding:</strong> {config.containerPadding}</div>
            {imageDimensions && (
              <div><strong>Dimensions:</strong> {imageDimensions.width}×{imageDimensions.height}</div>
            )}
          </div>
        </div>
      </div>
      
      <div className="w-48">
        <div className={containerClasses}>
          <img
            src={exampleGames[0].cover}
            alt="Hook Demo"
            className={imageClasses}
            loading="lazy"
          />
        </div>
      </div>
    </div>
  );
};

export default ImageDisplayExample;
