import { <PERSON><PERSON><PERSON>ircle, <PERSON>fresh<PERSON><PERSON>, Lightbulb, WifiOff, Clock, Shield } from 'lucide-react';
import { Button } from '@/components/ui/base/button';
import { Card, CardContent } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { SearchError } from '@/lib/utils/searchErrorHandler';

interface SearchErrorDisplayProps {
  error: SearchError;
  searchQuery?: string;
  onRetry?: () => void;
  onClose?: () => void;
  className?: string;
}

const ERROR_ICONS = {
  network: WifiOff,
  api_error: Shield,
  no_results: AlertCircle,
  invalid_query: AlertCircle,
  rate_limit: Clock,
  timeout: Clock,
  unknown: AlertCircle
};

const ERROR_COLORS = {
  network: 'destructive',
  api_error: 'destructive',
  no_results: 'secondary',
  invalid_query: 'secondary',
  rate_limit: 'outline',
  timeout: 'outline',
  unknown: 'destructive'
} as const;

export function SearchErrorDisplay({ 
  error, 
  searchQuery, 
  onRetry, 
  onClose,
  className = ''
}: SearchErrorDisplayProps) {
  const Icon = ERROR_ICONS[error.type];
  const colorVariant = ERROR_COLORS[error.type];
  
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
  };

  return (
    <Card className={`border-2 ${
      error.type === 'no_results' 
        ? 'border-muted-foreground/20 bg-muted/20' 
        : 'border-destructive/20 bg-destructive/5'
    } ${className}`}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Error Header */}
          <div className="flex items-start gap-4">
            <div className={`p-2 rounded-full ${
              error.type === 'no_results' 
                ? 'bg-muted text-muted-foreground' 
                : 'bg-destructive/10 text-destructive'
            }`}>
              <Icon className="h-5 w-5" />
            </div>
            
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2 flex-wrap">
                <h3 className={`font-semibold ${
                  error.type === 'no_results' 
                    ? 'text-muted-foreground' 
                    : 'text-destructive'
                }`}>
                  {error.type === 'no_results' ? 'No Results Found' : 'Search Error'}
                </h3>
                
                <Badge variant={colorVariant} className="text-xs">
                  {error.type.replace('_', ' ').toUpperCase()}
                </Badge>
                
                {error.retryable && (
                  <Badge variant="outline" className="text-xs">
                    RETRYABLE
                  </Badge>
                )}
              </div>
              
              <p className="text-foreground/80">
                {error.userMessage}
              </p>
              
              {searchQuery && (
                <p className="text-sm text-muted-foreground">
                  Search query: <span className="font-mono bg-muted px-1 py-0.5 rounded text-xs">
                    "{searchQuery}"
                  </span>
                </p>
              )}
            </div>
          </div>

          {/* Suggestions */}
          {error.suggestions.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <Lightbulb className="h-4 w-4" />
                Suggestions:
              </div>
              
              <ul className="space-y-1 text-sm text-muted-foreground ml-6">
                {error.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-primary mt-1">•</span>
                    <span>{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-3 pt-2">
            {error.retryable && onRetry && (
              <Button 
                onClick={handleRetry}
                size="sm"
                variant="default"
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
            )}
            
            {onClose && (
              <Button 
                onClick={onClose}
                size="sm"
                variant="ghost"
              >
                Dismiss
              </Button>
            )}
            
            {error.type === 'no_results' && searchQuery && (
              <Button 
                size="sm"
                variant="outline"
                onClick={() => {
                  // Clear search query and suggest new search
                  const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement;
                  if (searchInput) {
                    searchInput.value = '';
                    searchInput.focus();
                  }
                }}
              >
                New Search
              </Button>
            )}
          </div>

          {/* Technical Details (Development Mode) */}
          {process.env.NODE_ENV === 'development' && error.technicalDetails && (
            <details className="mt-4">
              <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
                Technical Details (Dev Mode)
              </summary>
              <pre className="mt-2 text-xs text-muted-foreground bg-muted/50 p-2 rounded overflow-x-auto">
                {error.technicalDetails}
              </pre>
            </details>
          )}
        </div>
      </CardContent>
    </Card>
  );
}