import React from 'react';
import { StatusBadge } from './status-badge';
import { GameImage } from './game-image';
import { getStatusIconElement } from '@/lib/gameStatusUtils';

// Test component to verify enhanced components work
export const EnhancedTest = () => {
  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Enhanced Components Test</h2>
      
      <div className="flex gap-2">
        <StatusBadge status="playing" />
        <StatusBadge status="completed" />
        <StatusBadge status="backlog" />
      </div>
      
      <div className="flex gap-2">
        {getStatusIconElement('playing')}
        {getStatusIconElement('completed')}
        {getStatusIconElement('backlog')}
      </div>
      
      <div className="w-48">
        <GameImage 
          src={null} 
          alt="Test Game" 
          aspectRatio="portrait"
          showFallback={true}
        />
      </div>
    </div>
  );
};