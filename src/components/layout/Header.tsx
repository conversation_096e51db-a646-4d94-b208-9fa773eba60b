import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { LogOut, User, Settings, Database, Zap, Bell, Search } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/base/tooltip';
import { Avatar } from '@/components/ui/base/avatar';
import { ThemeToggle } from '../ui/theme-toggle';
import { useAuth } from '@/contexts/AuthContext';
import { useQuery } from '@tanstack/react-query';
import { db } from '@/lib/supabase';

export function Header() {
  const { user, signOut } = useAuth();
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Fetch user profile to get avatar URL
  const { data: profile } = useQuery({
    queryKey: ['user-profile', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;
      
      const { data, error } = await db.userProfiles.getProfile(user.id);
      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error fetching user profile:', error);
        return null;
      }
      
      return data;
    },
    enabled: !!user?.id,
  });

  const handleSignOut = async () => {
    await signOut();
    setIsUserMenuOpen(false);
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Get user initials for avatar
  const getUserInitials = () => {
    const email = user?.email || '';
    const username = user?.user_metadata?.username || '';
    const name = username || email;
    
    if (name.includes('@')) {
      return name.split('@')[0].substring(0, 2).toUpperCase();
    }
    
    return name.substring(0, 2).toUpperCase();
  };

  // Get user avatar URL
  const getUserAvatarUrl = () => {
    // First check the user profile from database (uploaded avatar)
    if (profile?.avatar_url) {
      return profile.avatar_url;
    }
    // Fallback to user metadata (OAuth providers like Google, GitHub)
    return user?.user_metadata?.avatar_url || user?.user_metadata?.picture || null;
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <header className="sticky top-0 z-40 flex h-16 items-center gap-4 border-b border-border bg-background px-4 lg:px-6 shadow-lg">
      <div className="flex flex-1 items-center gap-4">
        {/* Welcome Message */}
        <div className="hidden md:block">
          <div className="text-sm">
            <span className="text-muted-foreground">{getGreeting()}, </span>
            <span className="text-foreground font-semibold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              {profile?.display_name || profile?.username || user?.user_metadata?.username || user?.email?.split('@')[0] || 'User'}
            </span>
          </div>
        </div>
        
        {/* Status Badges */}
        <div className="flex items-center gap-2 ml-auto mr-4">
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200 hidden sm:flex hover:bg-blue-100 transition-colors cursor-help">
                <Database className="h-3 w-3 mr-1" />
                IGDB API
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>Powered by Internet Game Database</p>
            </TooltipContent>
          </Tooltip>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="secondary" className="bg-emerald-50 text-emerald-700 border-emerald-200 hidden lg:flex hover:bg-emerald-100 transition-colors cursor-help">
                <Zap className="h-3 w-3 mr-1" />
                AI Enhanced
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>AI-powered search and recommendations</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          {/* Quick Search */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9 hover:bg-accent/50 transition-all duration-200 hover:scale-105"
                asChild
              >
                <Link to="/search">
                  <Search className="h-4 w-4" />
                  <span className="sr-only">Quick search</span>
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Quick search games</p>
            </TooltipContent>
          </Tooltip>

          {/* Notifications */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9 hover:bg-accent/50 transition-all duration-200 hover:scale-105 relative"
              >
                <Bell className="h-4 w-4" />
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-[10px] text-white flex items-center justify-center">
                  3
                </span>
                <span className="sr-only">Notifications</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>3 new notifications</p>
            </TooltipContent>
          </Tooltip>

          {/* Theme Toggle */}
          <ThemeToggle size="md" />
        </div>
      </div>
      
      {/* User Menu */}
      <div className="relative" ref={menuRef}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              className="relative h-10 w-10 rounded-full p-0 border border-primary/30 hover:border-primary/50 transition-all duration-300 hover:scale-105 hover:shadow-lg"
              onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
            >
              <Avatar
                src={getUserAvatarUrl()}
                alt={user?.user_metadata?.username || user?.email || 'User'}
                fallback={getUserInitials()}
                className="h-8 w-8"
              />
              <span className="sr-only">Toggle user menu</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Account menu</p>
          </TooltipContent>
        </Tooltip>
        
        {isUserMenuOpen && (
          <div className="profile-dropdown profile-dropdown-enter">
            <div className="p-1">
              {/* User Info Header */}
              <div className="dropdown-header">
                <div className="flex items-center gap-3">
                  <Avatar
                    src={getUserAvatarUrl()}
                    alt={user?.user_metadata?.username || user?.email || 'User'}
                    fallback={getUserInitials()}
                    size="lg"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="text-base font-semibold text-foreground truncate">
                      {profile?.display_name || profile?.username || user?.user_metadata?.username || user?.email?.split('@')[0] || 'User'}
                    </div>
                    <div className="text-sm text-muted-foreground truncate">
                      {user?.email}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Member since {new Date().getFullYear()}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Menu Items */}
              <div className="py-2">
                <Link
                  to="/profile"
                  className="dropdown-menu-item group"
                  onClick={() => setIsUserMenuOpen(false)}
                >
                  <div className="dropdown-icon-container">
                    <User className="h-4 w-4 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">Profile</div>
                    <div className="text-xs text-muted-foreground">Manage your account</div>
                  </div>
                </Link>
                
                <Link
                  to="/settings"
                  className="dropdown-menu-item group"
                  onClick={() => setIsUserMenuOpen(false)}
                >
                  <div className="dropdown-icon-container">
                    <Settings className="h-4 w-4 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">Settings</div>
                    <div className="text-xs text-muted-foreground">App preferences</div>
                  </div>
                </Link>
                
                {/* Divider */}
                <div className="my-2 h-px bg-gray-200 dark:bg-gray-700 mx-3" />
                
                <button
                  className="dropdown-menu-item-destructive group"
                  onClick={handleSignOut}
                >
                  <div className="dropdown-icon-container-destructive">
                    <LogOut className="h-4 w-4" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">Sign out</div>
                    <div className="text-xs text-muted-foreground">End your session</div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}