/**
 * Test utilities and helpers for consistent test setup
 */

import { UserGameWithDetails, GameRecord, UserGameRecord } from '@/types/database';

/**
 * Creates a mock GameRecord for testing
 */
export const createMockGameRecord = (
  id: string,
  overrides: Partial<GameRecord> = {}
): GameRecord => ({
  id,
  title: `Test Game ${id}`,
  platform: 'PC',
  description: 'Test description',
  cover_image: undefined,
  screenshots: undefined,
  release_date: '2023-01-01',
  metacritic_score: undefined,
  developer: 'Test Developer',
  publisher: 'Test Publisher',
  genres: ['Action'],
  igdb_id: undefined,
  youtube_links: undefined,
  steam_app_id: undefined,
  steam_url: undefined,
  tgdb_id: undefined,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

/**
 * Creates a mock UserGameRecord for testing
 */
export const createMockUserGameRecord = (
  id: string,
  gameId: string,
  overrides: Partial<UserGameRecord> = {}
): UserGameRecord => ({
  id,
  user_id: 'test-user',
  game_id: gameId,
  status: 'owned',
  personal_rating: undefined,
  personal_notes: undefined,
  hours_played: undefined,
  date_added: new Date().toISOString(),
  date_completed: undefined,
  is_wishlist: false,
  import_source: undefined,
  last_played: undefined,
  ...overrides,
});

/**
 * Creates a mock UserGameWithDetails for testing
 */
export const createMockUserGameWithDetails = (
  id: string,
  platform: string,
  status: 'playing' | 'completed' | 'backlog' | 'wishlist' | 'owned' = 'owned',
  personalRating?: number,
  hoursPlayed?: number,
  gameOverrides: Partial<GameRecord> = {},
  userGameOverrides: Partial<UserGameRecord> = {}
): UserGameWithDetails => {
  const game = createMockGameRecord(id, { platform, ...gameOverrides });
  const userGame = createMockUserGameRecord(id, id, {
    status,
    personal_rating: personalRating,
    hours_played: hoursPlayed,
    is_wishlist: status === 'wishlist',
    ...userGameOverrides,
  });

  return {
    ...userGame,
    game,
  };
};

/**
 * Creates multiple mock games for testing
 */
export const createMockGameCollection = (
  configs: Array<{
    id: string;
    platform: string;
    status?: 'playing' | 'completed' | 'backlog' | 'wishlist' | 'owned';
    personalRating?: number;
    hoursPlayed?: number;
    gameOverrides?: Partial<GameRecord>;
    userGameOverrides?: Partial<UserGameRecord>;
  }>
): UserGameWithDetails[] => {
  return configs.map(config =>
    createMockUserGameWithDetails(
      config.id,
      config.platform,
      config.status,
      config.personalRating,
      config.hoursPlayed,
      config.gameOverrides,
      config.userGameOverrides
    )
  );
};

/**
 * Test data constants for consistent testing
 */
export const TEST_PLATFORMS = {
  PC: ['PC', 'Mac', 'Linux', 'Steam Deck', 'Windows', 'macOS'],
  XBOX: ['Xbox', 'Xbox 360', 'Xbox One', 'Xbox Series X/S', 'Xbox Series X', 'Xbox Series S'],
  PLAYSTATION: ['PlayStation', 'PlayStation 2', 'PlayStation 3', 'PlayStation 4', 'PlayStation 5', 'PS4', 'PS5', 'PSP', 'PS Vita'],
  NINTENDO: ['Nintendo Switch', 'Nintendo 3DS', 'Nintendo DS', 'Wii U', 'Wii', 'GameCube', 'Nintendo 64', 'N64', 'SNES', 'NES'],
  MOBILE: ['iOS', 'Android', 'iPhone', 'iPad'],
  OTHER: ['Atari', 'Sega Genesis', 'Dreamcast', '3DO'],
} as const;

/**
 * Test user ID constant
 */
export const TEST_USER_ID = 'test-user-123';

/**
 * Common test assertions for platform family utils
 */
export const assertPlatformFamilyStats = (
  stats: any,
  expected: {
    total: number;
    playing?: number;
    completed?: number;
    backlog?: number;
    wishlist?: number;
    owned?: number;
    completionRate?: number;
    averageRating?: number;
    totalHours?: number;
  }
) => {
  expect(stats.total).toBe(expected.total);
  if (expected.playing !== undefined) expect(stats.playing).toBe(expected.playing);
  if (expected.completed !== undefined) expect(stats.completed).toBe(expected.completed);
  if (expected.backlog !== undefined) expect(stats.backlog).toBe(expected.backlog);
  if (expected.wishlist !== undefined) expect(stats.wishlist).toBe(expected.wishlist);
  if (expected.owned !== undefined) expect(stats.owned).toBe(expected.owned);
  if (expected.completionRate !== undefined) expect(stats.completionRate).toBe(expected.completionRate);
  if (expected.averageRating !== undefined) expect(stats.averageRating).toBe(expected.averageRating);
  if (expected.totalHours !== undefined) expect(stats.totalHours).toBe(expected.totalHours);
};