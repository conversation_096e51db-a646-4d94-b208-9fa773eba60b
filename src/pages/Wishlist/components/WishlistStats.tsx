import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/base';
import { WishlistStats as WishlistStatsType } from '../types';

interface WishlistStatsProps {
  stats: WishlistStatsType;
}

export function WishlistStats({ stats }: WishlistStatsProps) {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Total Wishlist</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-secondary">{stats.total}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-primary">
            {stats.averageRating > 0 ? stats.averageRating.toFixed(1) : 'N/A'}
          </div>
          <p className="text-xs text-muted-foreground">Personal ratings</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Price Tracked</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-accent">{stats.priceTracked}</div>
          <p className="text-xs text-muted-foreground">Games monitored</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Avg Metacritic</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-success">
            {stats.averageMetacriticScore > 0 ? Math.round(stats.averageMetacriticScore) : 'N/A'}
          </div>
          <p className="text-xs text-muted-foreground">Quality score</p>
        </CardContent>
      </Card>
    </div>
  );
}