import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/base';
import {
  Gamepad2,
  ImageIcon
} from '@/lib/icons';

interface LibraryTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  children: React.ReactNode;
}

export function LibraryTabs({ activeTab, onTabChange, children }: LibraryTabsProps) {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="library" className="flex items-center gap-2">
          <Gamepad2 className="h-4 w-4" />
          Library
        </TabsTrigger>
        <TabsTrigger value="artwork" className="flex items-center gap-2">
          <ImageIcon className="h-4 w-4" />
          Artwork
        </TabsTrigger>
      </TabsList>
      {children}
    </Tabs>
  );
}

export interface BaseTabProps {
  isActive: boolean;
}

export function TabContent({ 
  value, 
  children, 
  className = "space-y-6" 
}: { 
  value: string; 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <TabsContent value={value} className={className}>
      {children}
    </TabsContent>
  );
}