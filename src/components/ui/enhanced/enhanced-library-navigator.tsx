import React, { memo, useCallback, useState, useMemo } from 'react';
import { useEnhancedLibrary } from '@/hooks/useEnhancedLibrary';
import { EnhancedGridView, GridCustomizationControls } from './enhanced-grid-view';
import { EnhancedListView, ListCustomizationControls } from './enhanced-list-view';
import { EnhancedCoverFlow, CoverFlowCustomizationControls } from './enhanced-cover-flow';
import { EnhancedTimelineView, TimelineCustomizationControls } from './enhanced-timeline-view';
import { ViewMode, GameCardInteractions } from '@/types/library';
import { cn } from '@/lib/utils';
import { 
  Grid3X3, 
  List, 
  Image, 
  Calendar, 
  BarChart3,
  Settings,
  Filter,
  Search,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2,
  MoreHorizontal,
  Layout
} from 'lucide-react';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent } from '@/components/ui/base/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/base/tabs';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/base/dropdown-menu';
import { Separator } from '@/components/ui/base/separator';
import { Input } from '@/components/ui/base/input';

// View mode configuration
const VIEW_MODES = {
  grid: {
    id: 'grid' as const,
    name: 'Grid View',
    icon: Grid3X3,
    description: 'Display games in a customizable grid layout',
    component: EnhancedGridView,
    controls: GridCustomizationControls
  },
  list: {
    id: 'list' as const,
    name: 'List View',
    icon: List,
    description: 'Display games in a detailed list with sortable columns',
    component: EnhancedListView,
    controls: ListCustomizationControls
  },
  coverFlow: {
    id: 'coverFlow' as const,
    name: 'Cover Flow',
    icon: Image,
    description: 'Browse games with an immersive 3D cover flow experience',
    component: EnhancedCoverFlow,
    controls: CoverFlowCustomizationControls
  },
  timeline: {
    id: 'timeline' as const,
    name: 'Timeline',
    icon: Calendar,
    description: 'View games organized chronologically',
    component: EnhancedTimelineView,
    controls: TimelineCustomizationControls
  },
  stats: {
    id: 'stats' as const,
    name: 'Statistics',
    icon: BarChart3,
    description: 'Visualize your gaming habits and collection statistics',
    component: () => <div>Statistics Dashboard (Coming Soon)</div>,
    controls: () => <div>Stats Controls</div>
  }
} as const;

interface EnhancedLibraryNavigatorProps {
  className?: string;
  showControls?: boolean;
  fullscreen?: boolean;
  onFullscreenToggle?: () => void;
}

interface ViewControlsProps {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
  showCustomizationControls: boolean;
  onToggleCustomization: () => void;
  isFullscreen: boolean;
  onFullscreenToggle?: () => void;
}

// View controls component
const ViewControls = memo<ViewControlsProps>(({
  currentView,
  onViewChange,
  showCustomizationControls,
  onToggleCustomization,
  isFullscreen,
  onFullscreenToggle
}) => {
  return (
    <div className="flex items-center justify-between p-4 border-b border-border bg-background">
      {/* View Mode Tabs */}
      <Tabs value={currentView} onValueChange={(value) => onViewChange(value as ViewMode)}>
        <TabsList className="grid grid-cols-4 w-auto">
          {Object.entries(VIEW_MODES).slice(0, 4).map(([key, mode]) => {
            const Icon = mode.icon;
            return (
              <TabsTrigger 
                key={key} 
                value={key} 
                className="flex items-center gap-2 px-3"
                title={mode.description}
              >
                <Icon size={16} />
                <span className="hidden sm:inline">{mode.name}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>
      </Tabs>

      {/* Control Actions */}
      <div className="flex items-center gap-2">
        {/* Current View Info */}
        <div className="hidden md:flex items-center gap-2 text-sm text-muted-foreground">
          <span>{VIEW_MODES[currentView].description}</span>
        </div>

        <Separator orientation="vertical" className="h-6 hidden md:block" />

        {/* View Options Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Layout size={16} className="mr-2" />
              <span className="hidden sm:inline">View Options</span>
              <MoreHorizontal size={16} className="ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>View Modes</DropdownMenuLabel>
            {Object.entries(VIEW_MODES).map(([key, mode]) => {
              const Icon = mode.icon;
              return (
                <DropdownMenuItem
                  key={key}
                  onClick={() => onViewChange(key as ViewMode)}
                  className={cn(
                    'flex items-center gap-2',
                    currentView === key && 'bg-accent'
                  )}
                >
                  <Icon size={16} />
                  <div className="flex-1">
                    <div className="font-medium">{mode.name}</div>
                    <div className="text-xs text-muted-foreground">{mode.description}</div>
                  </div>
                </DropdownMenuItem>
              );
            })}
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem
              onClick={onToggleCustomization}
              className="flex items-center gap-2"
            >
              {showCustomizationControls ? <EyeOff size={16} /> : <Eye size={16} />}
              {showCustomizationControls ? 'Hide' : 'Show'} Customization
            </DropdownMenuItem>

            {onFullscreenToggle && (
              <DropdownMenuItem
                onClick={onFullscreenToggle}
                className="flex items-center gap-2"
              >
                {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
                {isFullscreen ? 'Exit' : 'Enter'} Fullscreen
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Customization Toggle */}
        <Button
          variant={showCustomizationControls ? "default" : "outline"}
          size="sm"
          onClick={onToggleCustomization}
          title="Toggle customization controls"
        >
          <Settings size={16} className="mr-2" />
          <span className="hidden sm:inline">Customize</span>
        </Button>

        {/* Fullscreen Toggle */}
        {onFullscreenToggle && (
          <Button
            variant="outline"
            size="sm"
            onClick={onFullscreenToggle}
            title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
          >
            {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </Button>
        )}
      </div>
    </div>
  );
});

ViewControls.displayName = 'ViewControls';

// Library stats component
interface LibraryStatsProps {
  games: UserGameWithDetails[];
  selectedCount: number;
  activeFilterCount: number;
  currentView: ViewMode;
}

const LibraryStats = memo<LibraryStatsProps>(({ 
  games, 
  selectedCount, 
  activeFilterCount, 
  currentView 
}) => {
  const totalGames = games.length;
  const completedGames = games.filter(game => game.status === 'completed').length;
  const playingGames = games.filter(game => game.status === 'playing').length;
  const totalHours = games.reduce((sum, game) => sum + (game.hours_played || 0), 0);

  return (
    <div className="flex items-center gap-4 px-4 py-2 bg-muted/30 text-sm border-b border-border">
      <div className="flex items-center gap-1">
        <span className="font-medium">{totalGames}</span>
        <span className="text-muted-foreground">
          {totalGames === 1 ? 'game' : 'games'}
        </span>
      </div>

      {completedGames > 0 && (
        <div className="flex items-center gap-1">
          <span className="font-medium text-green-600">{completedGames}</span>
          <span className="text-muted-foreground">completed</span>
        </div>
      )}

      {playingGames > 0 && (
        <div className="flex items-center gap-1">
          <span className="font-medium text-blue-600">{playingGames}</span>
          <span className="text-muted-foreground">playing</span>
        </div>
      )}

      {totalHours > 0 && (
        <div className="flex items-center gap-1">
          <span className="font-medium">{Math.round(totalHours)}h</span>
          <span className="text-muted-foreground">played</span>
        </div>
      )}

      <div className="flex items-center gap-2 ml-auto">
        {activeFilterCount > 0 && (
          <Badge variant="secondary" className="text-xs">
            <Filter size={10} className="mr-1" />
            {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''}
          </Badge>
        )}

        {selectedCount > 0 && (
          <Badge variant="default" className="text-xs">
            {selectedCount} selected
          </Badge>
        )}

        <Badge variant="outline" className="text-xs">
          {VIEW_MODES[currentView].name}
        </Badge>
      </div>
    </div>
  );
});

LibraryStats.displayName = 'LibraryStats';

// Search and filter bar component
interface SearchFilterBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onFilterToggle: () => void;
  hasActiveFilters: boolean;
  activeFilterCount: number;
}

const SearchFilterBar = memo<SearchFilterBarProps>(({
  searchQuery,
  onSearchChange,
  onFilterToggle,
  hasActiveFilters,
  activeFilterCount
}) => {
  return (
    <div className="flex items-center gap-4 p-4 bg-background">
      {/* Search Input */}
      <div className="relative flex-1 max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={16} />
        <Input
          placeholder="Search your library..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10 pr-4"
        />
      </div>

      {/* Filter Toggle */}
      <Button
        variant={hasActiveFilters ? "default" : "outline"}
        size="sm"
        onClick={onFilterToggle}
        className="flex items-center gap-2"
      >
        <Filter size={16} />
        <span>Filters</span>
        {activeFilterCount > 0 && (
          <Badge variant="secondary" className="ml-1 text-xs">
            {activeFilterCount}
          </Badge>
        )}
      </Button>

      {/* Quick Actions */}
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <MoreHorizontal size={16} />
        </Button>
      </div>
    </div>
  );
});

SearchFilterBar.displayName = 'SearchFilterBar';

export const EnhancedLibraryNavigator = memo<EnhancedLibraryNavigatorProps>(({
  className,
  showControls = true,
  fullscreen = false,
  onFullscreenToggle
}) => {
  const {
    state,
    processedGames,
    setViewMode,
    updateGridSettings,
    updateListSettings,
    updateCoverFlowSettings,
    updateTimelineSettings,
    updateStatsSettings,
    setSearchQuery,
    hasActiveFilters,
    activeFilterCount,
    getSelectedGames
  } = useEnhancedLibrary();

  const [showCustomizationControls, setShowCustomizationControls] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // Current view configuration
  const currentViewConfig = VIEW_MODES[state.viewMode];
  const ViewComponent = currentViewConfig.component;
  const ControlsComponent = currentViewConfig.controls;

  // Current view settings
  const currentViewSettings = state.viewSettings[state.viewMode];

  // Settings change handler
  const handleSettingsChange = useCallback((settings: Partial<typeof currentViewSettings>) => {
    switch (state.viewMode) {
      case 'grid':
        updateGridSettings(settings);
        break;
      case 'list':
        updateListSettings(settings);
        break;
      case 'coverFlow':
        updateCoverFlowSettings(settings);
        break;
      case 'timeline':
        updateTimelineSettings(settings);
        break;
      case 'stats':
        updateStatsSettings(settings);
        break;
    }
  }, [
    state.viewMode,
    updateGridSettings,
    updateListSettings,
    updateCoverFlowSettings,
    updateTimelineSettings,
    updateStatsSettings
  ]);

  // Game interactions
  const gameInteractions: GameCardInteractions = useMemo(() => ({
    onStatusChange: (gameId: string, status: string) => {
      console.log('Status change:', gameId, status);
      // Handle status change
    },
    onRatingChange: (gameId: string, rating: number) => {
      console.log('Rating change:', gameId, rating);
      // Handle rating change
    },
    onAddToCollection: (gameId: string, collectionId: string) => {
      console.log('Add to collection:', gameId, collectionId);
      // Handle collection addition
    },
    onShare: (gameId: string, method: string) => {
      console.log('Share game:', gameId, method);
      // Handle sharing
    },
    onCustomize: (gameId: string) => {
      console.log('Customize game:', gameId);
      // Handle customization
    },
    onSelect: (gameId: string) => {
      console.log('Select game:', gameId);
      // Handle selection
    },
    onQuickPlay: (gameId: string) => {
      console.log('Quick play:', gameId);
      // Handle quick play
    }
  }), []);

  const selectedGames = getSelectedGames();

  return (
    <div className={cn(
      'enhanced-library-navigator flex flex-col h-full bg-background',
      fullscreen && 'fixed inset-0 z-50',
      className
    )}>
      {/* View Controls */}
      {showControls && (
        <ViewControls
          currentView={state.viewMode}
          onViewChange={setViewMode}
          showCustomizationControls={showCustomizationControls}
          onToggleCustomization={() => setShowCustomizationControls(!showCustomizationControls)}
          isFullscreen={fullscreen}
          onFullscreenToggle={onFullscreenToggle}
        />
      )}

      {/* Search and Filter Bar */}
      <SearchFilterBar
        searchQuery={state.searchState.query}
        onSearchChange={setSearchQuery}
        onFilterToggle={() => setShowFilters(!showFilters)}
        hasActiveFilters={hasActiveFilters}
        activeFilterCount={activeFilterCount}
      />

      {/* Customization Controls */}
      {showCustomizationControls && (
        <div className="border-b border-border bg-muted/30">
          <div className="p-4">
            <ControlsComponent
              settings={currentViewSettings}
              onSettingsChange={handleSettingsChange as (settings: unknown) => void}
            />
          </div>
        </div>
      )}

      {/* Library Stats */}
      <LibraryStats
        games={processedGames}
        selectedCount={selectedGames.length}
        activeFilterCount={activeFilterCount}
        currentView={state.viewMode}
      />

      {/* Main View Content */}
      <div className="flex-1 relative overflow-hidden">
        <ViewComponent
          games={processedGames}
          settings={currentViewSettings}
          onSettingsChange={handleSettingsChange as (settings: unknown) => void}
          interactions={gameInteractions}
          className="h-full"
        />
      </div>

      {/* Filter Panel Overlay */}
      {showFilters && (
        <div className="absolute inset-0 bg-black/50 z-40 flex justify-end">
          <Card className="w-80 h-full rounded-none border-l border-t-0 border-b-0 border-r-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Filters</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(false)}
                >
                  Close
                </Button>
              </div>
              
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  Filter panel content would go here...
                </div>
                
                {hasActiveFilters && (
                  <div className="pt-4 border-t">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full"
                    >
                      Clear All Filters
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
});

EnhancedLibraryNavigator.displayName = 'EnhancedLibraryNavigator';