import { memo, useCallback, useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/base/dropdown-menu';
import { 
  Play, 
  CheckCircle, 
  Pause, 
  Gamepad2, 
  Calendar,
  Star,
  MoreVertical,
  ImageIcon,
  Upload,
  Clock,
  Heart,
  Share2,
  Edit3,
  Plus,
  Eye,
  EyeOff
} from 'lucide-react';
import { UserGameWithDetails } from '@/types/database';
import { GameCardInteractions, AnimationConfig } from '@/types/library';
import { useCustomArtwork } from '@/hooks/useCustomArtwork';
import { cn } from '@/lib/utils';

// Card variant types
export type CardVariant = 'compact' | 'standard' | 'detailed';
export type CardStyle = 'modern' | 'classic' | 'minimal';
export type InteractionMode = 'click' | 'hover' | 'focus';

interface PremiumGameCardProps {
  gameData: UserGameWithDetails;
  variant?: CardVariant;
  style?: CardStyle;
  interactionMode?: InteractionMode;
  showQuickActions?: boolean;
  enableHoverEffects?: boolean;
  customArtwork?: boolean;
  animations?: Partial<AnimationConfig>;
  interactions?: GameCardInteractions;
  isSelected?: boolean;
  className?: string;
}

const defaultAnimations: AnimationConfig = {
  hoverScale: 1.02,
  transitionDuration: 400,
  staggerDelay: 100,
  parallaxEffect: true,
  glowEffect: true,
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'playing':
      return <Play className="h-4 w-4 text-green-500" />;
    case 'completed':
      return <CheckCircle className="h-4 w-4 text-blue-500" />;
    case 'backlog':
      return <Pause className="h-4 w-4 text-orange-500" />;
    case 'wishlist':
      return <Heart className="h-4 w-4 text-pink-500" />;
    default:
      return <Gamepad2 className="h-4 w-4 text-gray-500" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'playing':
      return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800';
    case 'completed':
      return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800';
    case 'backlog':
      return 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800';
    case 'wishlist':
      return 'bg-pink-100 text-pink-800 border-pink-200 dark:bg-pink-900/20 dark:text-pink-400 dark:border-pink-800';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800';
  }
};

export const PremiumGameCard = memo<PremiumGameCardProps>(({
  gameData,
  variant = 'standard',
  style = 'modern',
  interactionMode = 'hover',
  showQuickActions = true,
  enableHoverEffects = true,
  customArtwork = true,
  animations = {},
  interactions = {},
  isSelected = false,
  className,
}) => {
  const game = gameData.game;
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const cardRef = useRef<HTMLDivElement>(null);
  
  // Merge animations with defaults
  const animConfig = { ...defaultAnimations, ...animations };
  
  // Get custom artwork for this user game
  const { getBestCoverImage, hasCustomArtwork } = useCustomArtwork(gameData.id);

  const handleCardClick = useCallback(() => {
    if (interactionMode === 'click' && interactions.onSelect) {
      interactions.onSelect(!isSelected);
    }
  }, [interactionMode, interactions, isSelected]);

  const handleStatusChange = useCallback((status: string) => {
    interactions.onStatusChange?.(status);
  }, [interactions]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoaded(true);
  }, []);

  const handleMouseEnter = useCallback(() => {
    if (enableHoverEffects) {
      setIsHovered(true);
    }
  }, [enableHoverEffects]);

  const handleMouseLeave = useCallback(() => {
    if (enableHoverEffects) {
      setIsHovered(false);
      setMousePosition({ x: 0, y: 0 });
    }
  }, [enableHoverEffects]);

  const handleMouseDown = useCallback(() => {
    setIsPressed(true);
  }, []);

  const handleMouseUp = useCallback(() => {
    setIsPressed(false);
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!enableHoverEffects || !cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    setMousePosition({ x, y });
  }, [enableHoverEffects]);

  // Parallax effect on mouse move
  useEffect(() => {
    if (!animConfig.parallaxEffect || !cardRef.current) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isHovered) return;
      
      const rect = cardRef.current!.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const rotateX = (y - centerY) / 10;
      const rotateY = (centerX - x) / 10;
      
      cardRef.current!.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(${animConfig.hoverScale})`;
    };

    const handleMouseLeave = () => {
      if (cardRef.current) {
        cardRef.current.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1)';
      }
    };

    if (isHovered) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [isHovered, animConfig.parallaxEffect, animConfig.hoverScale]);

  if (!game) return null;

  // Variant-specific classes
  const variantClasses = {
    compact: 'w-full max-w-[200px]',
    standard: 'w-full max-w-[280px]',
    detailed: 'w-full max-w-[320px]',
  };

  // Style-specific classes
  const styleClasses = {
    modern: 'rounded-xl border-0 shadow-lg bg-gradient-to-br from-card via-card to-card/95',
    classic: 'rounded-lg border shadow-md bg-card',
    minimal: 'rounded-md border-0 shadow-sm bg-card',
  };

  // Animation classes
  const animationClasses = cn(
    'transition-all duration-300 ease-out',
    enableHoverEffects && [
      'hover:shadow-2xl hover:shadow-primary/10',
      animConfig.glowEffect && 'hover:ring-1 hover:ring-primary/20',
      'hover:-translate-y-1',
    ],
    isSelected && 'ring-2 ring-primary shadow-lg shadow-primary/20',
    className
  );

  return (
    <Card 
      ref={cardRef}
      className={cn(
        'group cursor-pointer overflow-hidden relative',
        variantClasses[variant],
        styleClasses[style],
        animationClasses,
        isPressed && 'scale-95',
        'transform-gpu will-change-transform'
      )}
      onClick={handleCardClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseMove={handleMouseMove}
      style={{
        transitionDuration: `${animConfig.transitionDuration}ms`,
        '--mouse-x': `${mousePosition.x}%`,
        '--mouse-y': `${mousePosition.y}%`,
      } as React.CSSProperties}
    >
      {/* Premium animated background gradient */}
      {enableHoverEffects && style === 'modern' && (
        <div 
          className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
          style={{
            background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
              hsl(var(--primary) / 0.05) 0%, 
              hsl(var(--secondary) / 0.03) 50%, 
              transparent 100%)`
          }}
        />
      )}
      
      {/* Premium glow effect */}
      {enableHoverEffects && animConfig.glowEffect && (
        <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10" />
      )}
      
      {/* Sparkle effects for premium interactions */}
      {isHovered && enableHoverEffects && (
        <>
          <div className="absolute top-4 right-4 w-1 h-1 bg-primary rounded-full animate-ping opacity-75" />
          <div className="absolute top-8 right-8 w-0.5 h-0.5 bg-secondary rounded-full animate-pulse delay-300" />
          <div className="absolute bottom-8 left-4 w-0.5 h-0.5 bg-accent rounded-full animate-pulse delay-700" />
        </>
      )}
      {/* Card Header - varies by variant */}
      {variant !== 'compact' && (
        <CardHeader className={cn(
          'pb-2',
          variant === 'detailed' ? 'pb-3' : 'pb-2'
        )}>
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className={cn(
                'truncate group-hover:text-primary transition-colors',
                variant === 'compact' ? 'text-sm' : variant === 'detailed' ? 'text-lg' : 'text-base'
              )}>
                {game.title}
              </CardTitle>
              <CardDescription className="flex items-center gap-2 mt-1">
                {getStatusIcon(gameData.status)}
                <span className="capitalize text-xs">{gameData.status}</span>
                {variant === 'detailed' && game.developer && (
                  <>
                    <span className="text-muted-foreground">•</span>
                    <span className="truncate text-xs">{game.developer}</span>
                  </>
                )}
              </CardDescription>
            </div>
            
            {showQuickActions && (
              <DropdownMenu
                trigger={
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                }
                align="end"
              >
                <DropdownMenuItem onClick={() => handleStatusChange('playing')}>
                  <Play className="h-4 w-4 mr-2" />
                  Mark as Playing
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('completed')}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Mark as Completed
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('backlog')}>
                  <Pause className="h-4 w-4 mr-2" />
                  Move to Backlog
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('wishlist')}>
                  <Heart className="h-4 w-4 mr-2" />
                  Add to Wishlist
                </DropdownMenuItem>
                {interactions.onAddToCollection && (
                  <DropdownMenuItem onClick={() => interactions.onAddToCollection?.('default')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add to Collection
                  </DropdownMenuItem>
                )}
                {interactions.onShare && (
                  <DropdownMenuItem onClick={() => interactions.onShare?.('default')}>
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </DropdownMenuItem>
                )}
                {interactions.onCustomize && (
                  <DropdownMenuItem onClick={() => interactions.onCustomize?.()}>
                    <Edit3 className="h-4 w-4 mr-2" />
                    Customize
                  </DropdownMenuItem>
                )}
              </DropdownMenu>
            )}
          </div>
        </CardHeader>
      )}

      <CardContent className={cn(
        variant === 'compact' ? 'p-2' : 'p-4'
      )}>
        <div className="space-y-3">
          {/* Enhanced Image Container */}
          <div className={cn(
            'relative overflow-hidden rounded-lg bg-gradient-to-br from-muted via-muted to-muted/80',
            'group-hover:shadow-2xl group-hover:shadow-primary/20 transition-premium box-art-glow',
            variant === 'compact' ? 'aspect-[3/4]' : 'aspect-[2/3]'
          )}>
            <div className="relative w-full h-full">
              {(getBestCoverImage() || game.cover_image) && !imageError ? (
                <>
                  {/* Loading shimmer effect */}
                  {!imageLoaded && (
                    <div className="absolute inset-0 box-art-shimmer rounded-lg">
                      <div className="w-full h-full bg-gradient-to-br from-muted/80 via-muted/60 to-muted/40 animate-pulse" />
                    </div>
                  )}
                  
                  {/* Main image with enhanced effects */}
                  <img
                    src={getBestCoverImage() || game.cover_image}
                    alt={game.title}
                    className={cn(
                      'object-cover w-full h-full transition-premium',
                      imageLoaded 
                        ? 'opacity-100 group-hover:scale-110 group-hover:brightness-110 group-hover:saturate-110' 
                        : 'opacity-0 scale-105'
                    )}
                    loading="lazy"
                    decoding="async"
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                    style={{ imageRendering: 'crisp-edges' }}
                  />
                  
                  {/* Enhanced glass morphism overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/10 opacity-0 group-hover:opacity-100 transition-premium glass-morphism" />
                  
                  {/* Multi-layered reflection effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/15 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-premium" />
                  <div className="absolute top-0 left-0 w-1/3 h-1/3 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-premium" />
                  
                  {/* Premium glow border */}
                  <div className="absolute inset-0 rounded-lg ring-1 ring-white/20 ring-inset opacity-0 group-hover:opacity-100 transition-premium" />
                  <div className="absolute inset-0 rounded-lg ring-1 ring-primary/20 ring-inset opacity-0 group-hover:opacity-100 transition-premium delay-75" />
                </>
              ) : (
                /* Enhanced fallback with premium animated placeholder */
                <div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-muted via-muted/90 to-muted/70 group-hover:from-muted/80 group-hover:to-muted/60 transition-premium">
                  <div className="relative mb-3">
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-full animate-glow" />
                    <ImageIcon className={cn(
                      'text-muted-foreground/60 animate-pulse relative z-10',
                      variant === 'compact' ? 'h-8 w-8' : 'h-16 w-16'
                    )} />
                    <div className="absolute inset-0 box-art-shimmer rounded-full" />
                  </div>
                  <p className="text-xs text-muted-foreground font-medium tracking-wide">No Cover Art</p>
                  <div className="mt-2 w-16 h-0.5 bg-gradient-to-r from-transparent via-muted-foreground/20 to-transparent animate-pulse" />
                </div>
              )}
            </div>
            
            {/* Premium hover effects */}
            <div className="absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-premium" />
            <div className="absolute top-2 right-2 w-2 h-2 bg-primary rounded-full opacity-0 group-hover:opacity-100 animate-glow transition-premium" />
            
            {/* Status indicator */}
            <div className="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-premium">
              <div className="w-8 h-8 rounded-full bg-black/40 flex items-center justify-center">
                {getStatusIcon(gameData.status)}
              </div>
            </div>

            {/* Custom artwork indicator */}
            {customArtwork && hasCustomArtwork && (
              <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-premium">
                <Badge variant="secondary" className="bg-primary text-primary-foreground border-0 text-xs">
                  <Upload className="h-3 w-3 mr-1" />
                  Custom
                </Badge>
              </div>
            )}

            {/* Quick action buttons for detailed variant */}
            {variant === 'detailed' && showQuickActions && (
              <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-premium">
                <div className="flex gap-1">
                  {interactions.onQuickPlay && (
                    <Button
                      size="sm"
                      variant="secondary"
                      className="h-8 w-8 p-0 bg-black/20  border-0 hover:bg-primary/80"
                      onClick={(e) => {
                        e.stopPropagation();
                        interactions.onQuickPlay?.();
                      }}
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    size="sm"
                    variant="secondary"
                    className="h-8 w-8 p-0 bg-black/20  border-0 hover:bg-primary/80"
                    onClick={(e) => {
                      e.stopPropagation();
                      interactions.onSelect?.(!isSelected);
                    }}
                  >
                    {isSelected ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Compact variant shows minimal info */}
          {variant === 'compact' && (
            <div className="space-y-1">
              <h3 className="text-sm font-medium truncate group-hover:text-primary transition-colors">
                {game.title}
              </h3>
              <Badge className={cn('text-xs', getStatusColor(gameData.status))}>
                {gameData.status}
              </Badge>
            </div>
          )}

          {/* Standard and detailed variants show more info */}
          {variant !== 'compact' && (
            <>
              <div className="flex flex-wrap gap-2">
                <Badge className={getStatusColor(gameData.status)}>
                  {gameData.status}
                </Badge>

                {game.metacritic_score && (
                  <Badge variant="secondary">
                    <Star className="h-3 w-3 mr-1" />
                    {game.metacritic_score}
                  </Badge>
                )}

                {variant === 'detailed' && gameData.personal_rating && (
                  <Badge variant="outline" className="text-yellow-600 border-yellow-200">
                    <Star className="h-3 w-3 mr-1 fill-current" />
                    {gameData.personal_rating}/10
                  </Badge>
                )}
              </div>

              {game.genres && game.genres.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {game.genres.slice(0, variant === 'detailed' ? 4 : 3).map((genre, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {genre}
                    </Badge>
                  ))}
                  {game.genres.length > (variant === 'detailed' ? 4 : 3) && (
                    <Badge variant="outline" className="text-xs">
                      +{game.genres.length - (variant === 'detailed' ? 4 : 3)} more
                    </Badge>
                  )}
                </div>
              )}

              {/* Detailed variant shows additional info */}
              {variant === 'detailed' && (
                <div className="space-y-2 text-sm text-muted-foreground">
                  {game.release_date && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(game.release_date).getFullYear()}</span>
                    </div>
                  )}
                  
                  {gameData.hours_played && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{Math.round(gameData.hours_played)}h played</span>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
});

PremiumGameCard.displayName = 'PremiumGameCard';