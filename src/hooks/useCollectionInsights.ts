import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  collectionInsightsService, 
  GamingPersonality
} from '../lib/collectionInsightsService';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';

/**
 * Hook for collection statistics and analysis
 */
export const useCollectionStats = () => {
  const { user } = useAuth();

  const {
    data: stats,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['collection-stats', user?.id],
    queryFn: () => collectionInsightsService.analyzeCollection(user?.id || ''),
    enabled: !!user?.id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2
  });

  // Calculate additional derived stats
  const derivedStats = {
    backlogPercentage: stats ? (stats.gamesInBacklog / stats.totalGames) * 100 : 0,
    playedPercentage: stats ? (stats.gamesPlayed / stats.totalGames) * 100 : 0,
    averageGamesPerGenre: stats ? stats.totalGames / Math.max(stats.topGenres.length, 1) : 0,
    diversityScore: stats ? (stats.topGenres.length / 10) * 100 : 0, // Out of 10 possible genres
    productivityScore: stats ? Math.min((stats.gamesCompleted / stats.totalGames) * 100, 100) : 0
  };

  return {
    stats: stats || null,
    derivedStats,
    isLoading,
    error,
    refetch
  };
};

/**
 * Hook for backlog optimization recommendations
 */
export const useBacklogOptimization = () => {
  const { user } = useAuth();
  const [filterPriority, setFilterPriority] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [sortBy, setSortBy] = useState<'priority' | 'playtime' | 'likelihood'>('priority');

  const {
    data: optimizations,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['backlog-optimization', user?.id],
    queryFn: () => collectionInsightsService.optimizeBacklog(user?.id || ''),
    enabled: !!user?.id,
    staleTime: 15 * 60 * 1000, // 15 minutes
    retry: 2
  });

  // Filter and sort optimizations
  const filteredOptimizations = optimizations?.filter(opt => 
    filterPriority === 'all' || opt.priority === filterPriority
  ) || [];

  const sortedOptimizations = [...filteredOptimizations].sort((a, b) => {
    switch (sortBy) {
      case 'priority': {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      case 'playtime':
        return a.estimatedPlayTime - b.estimatedPlayTime;
      case 'likelihood':
        return b.completionLikelihood - a.completionLikelihood;
      default:
        return 0;
    }
  });

  // Get quick stats
  const quickStats = {
    totalBacklogGames: optimizations?.length || 0,
    highPriorityGames: optimizations?.filter(opt => opt.priority === 'high').length || 0,
    quickCompletions: optimizations?.filter(opt => opt.estimatedPlayTime < 20).length || 0,
    highLikelihood: optimizations?.filter(opt => opt.completionLikelihood > 80).length || 0
  };

  return {
    optimizations: sortedOptimizations,
    quickStats,
    isLoading,
    error,
    refetch,
    filterPriority,
    setFilterPriority,
    sortBy,
    setSortBy
  };
};

/**
 * Hook for discovery challenges
 */
export const useDiscoveryChallenges = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const {
    data: challenges,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['discovery-challenges', user?.id],
    queryFn: () => collectionInsightsService.generateDiscoveryChallenges(user?.id || ''),
    enabled: !!user?.id,
    staleTime: 30 * 60 * 1000, // 30 minutes
    retry: 2
  });

  // Update challenge progress (mock implementation)
  const updateChallengeMutation = useMutation({
    mutationFn: async ({ challengeId, progress }: { challengeId: string; progress: number }) => {
      // In a real implementation, this would update the backend
      await new Promise(resolve => setTimeout(resolve, 500));
      return { challengeId, progress };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['discovery-challenges', user?.id] });
      toast.success('Challenge progress updated!');
    },
    onError: (error) => {
      console.error('Error updating challenge:', error);
      toast.error('Failed to update challenge progress');
    }
  });

  // Complete challenge
  const completeChallengeMutation = useMutation({
    mutationFn: async (challengeId: string) => {
      // In a real implementation, this would mark the challenge as completed
      await new Promise(resolve => setTimeout(resolve, 500));
      return challengeId;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['discovery-challenges', user?.id] });
      toast.success('Challenge completed successfully');
    },
    onError: (error) => {
      console.error('Error completing challenge:', error);
      toast.error('Failed to complete challenge');
    }
  });

  // Get challenge statistics
  const challengeStats = {
    totalChallenges: challenges?.length || 0,
    activeChallenges: challenges?.filter(c => c.isActive).length || 0,
    completedChallenges: challenges?.filter(c => c.progress >= c.maxProgress).length || 0,
    overallProgress: challenges?.reduce((sum, c) => sum + (c.progress / c.maxProgress), 0) || 0
  };

  return {
    challenges: challenges || [],
    challengeStats,
    isLoading,
    error,
    refetch,
    updateChallenge: updateChallengeMutation.mutate,
    completeChallenge: completeChallengeMutation.mutate,
    isUpdating: updateChallengeMutation.isPending,
    isCompleting: completeChallengeMutation.isPending
  };
};

/**
 * Hook for gaming personality analysis
 */
export const useGamingPersonality = () => {
  const { user } = useAuth();

  const {
    data: personality,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['gaming-personality', user?.id],
    queryFn: () => collectionInsightsService.analyzeGamingPersonality(user?.id || ''),
    enabled: !!user?.id,
    staleTime: 60 * 60 * 1000, // 1 hour
    retry: 2
  });

  // Get personality insights
  const personalityInsights = {
    strengthScore: personality?.score || 0,
    primaryTrait: personality?.traits[0] || 'Unknown',
    personalityColor: getPersonalityColor(personality?.type),
    personalityIcon: getPersonalityIcon(personality?.type),
    topRecommendation: personality?.recommendations[0] || 'Keep exploring!'
  };

  return {
    personality: personality || null,
    personalityInsights,
    isLoading,
    error,
    refetch
  };
};

/**
 * Hook for comprehensive game insights
 */
export const useGameInsights = () => {
  const { user } = useAuth();
  const [insightType, setInsightType] = useState<'all' | 'backlog_priority' | 'completion_prediction' | 'genre_recommendation' | 'mood_match'>('all');

  const {
    data: insights,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['game-insights', user?.id],
    queryFn: () => collectionInsightsService.generateInsights(user?.id || ''),
    enabled: !!user?.id,
    staleTime: 20 * 60 * 1000, // 20 minutes
    retry: 2
  });

  // Filter insights by type
  const filteredInsights = insights?.filter(insight => 
    insightType === 'all' || insight.insightType === insightType
  ) || [];

  // Get insights by category
  const insightsByCategory = {
    backlogPriority: insights?.filter(i => i.insightType === 'backlog_priority') || [],
    completionPrediction: insights?.filter(i => i.insightType === 'completion_prediction') || [],
    genreRecommendation: insights?.filter(i => i.insightType === 'genre_recommendation') || [],
    moodMatch: insights?.filter(i => i.insightType === 'mood_match') || []
  };

  // Get insight statistics
  const insightStats = {
    totalInsights: insights?.length || 0,
    highConfidenceInsights: insights?.filter(i => i.score > 80).length || 0,
    averageConfidence: insights ? insights.reduce((sum, i) => sum + i.score, 0) / (insights.length || 1) : 0,
    topInsight: insights?.[0] || null
  };

  return {
    insights: filteredInsights,
    insightsByCategory,
    insightStats,
    isLoading,
    error,
    refetch,
    insightType,
    setInsightType
  };
};

/**
 * Combined hook for dashboard insights
 */
export const useDashboardInsights = () => {
  const collectionStats = useCollectionStats();
  const backlogOptimization = useBacklogOptimization();
  const discoveryChallenges = useDiscoveryChallenges();
  const gamingPersonality = useGamingPersonality();
  const gameInsights = useGameInsights();

  // Calculate overall insights score
  const overallInsightsScore = {
    collectionHealth: collectionStats.derivedStats.productivityScore,
    backlogEfficiency: backlogOptimization.quickStats.highLikelihood > 0 ? 85 : 45,
    discoveryProgress: discoveryChallenges.challengeStats.overallProgress * 20,
    personalityAlignment: gamingPersonality.personalityInsights.strengthScore
  };

  const averageScore = Object.values(overallInsightsScore).reduce((sum, score) => sum + score, 0) / 4;

  return {
    collectionStats,
    backlogOptimization,
    discoveryChallenges,
    gamingPersonality,
    gameInsights,
    overallInsightsScore,
    averageScore: Math.round(averageScore),
    isLoading: collectionStats.isLoading || backlogOptimization.isLoading || 
               discoveryChallenges.isLoading || gamingPersonality.isLoading || 
               gameInsights.isLoading
  };
};

// Helper functions
function getPersonalityColor(type?: GamingPersonality['type']): string {
  const colors = {
    completionist: 'text-green-600',
    explorer: 'text-blue-600',
    casual: 'text-yellow-600',
    hardcore: 'text-red-600',
    social: 'text-purple-600',
    collector: 'text-orange-600'
  };
  return colors[type || 'casual'];
}

function getPersonalityIcon(): string {
  // Icons removed for clean, modern interface
  return '';
}

// Export helper functions for use in components
export { getPersonalityColor, getPersonalityIcon };