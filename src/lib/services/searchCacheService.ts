import { SearchResult, SearchIntent } from '@/lib/enhancedSearchService';
import { generateUUID } from '@/lib/utils/baseUtils';

// IndexedDB configuration
const DB_NAME = 'CodexaSearchCache';
const DB_VERSION = 1;
const CACHE_STORE = 'searchCache';
const ANALYTICS_STORE = 'cacheAnalytics';
const WARMUP_STORE = 'warmupQueries';

// Cache configuration
const DEFAULT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes base TTL
const MAX_CACHE_ENTRIES = 1000;
const CACHE_CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
const WARMUP_QUERIES = [
  'popular games',
  'best rpg games',
  'indie games',
  'action games',
  'strategy games',
  'puzzle games',
  'multiplayer games',
  'single player games'
];

interface CachedSearchResult {
  id: string;
  queryHash: string;
  query: string;
  intent: SearchIntent;
  result: SearchResult;
  cachedAt: number;
  expiresAt: number;
  hitCount: number;
  lastAccessed: number;
  userId?: string;
}

interface CacheAnalytics {
  totalHits: number;
  totalMisses: number;
  totalQueries: number;
  averageResponseTime: number;
  cacheSize: number;
  lastCleanup: number;
  popularQueries: Record<string, number>;
}

interface CacheStats {
  hitRate: number;
  missRate: number;
  totalQueries: number;
  cacheSize: number;
  averageResponseTime: number;
  popularQueries: Array<{ query: string; count: number }>;
}

class SearchCacheService {
  private db: IDBDatabase | null = null;
  private initialized = false;
  private cleanupTimer: number | null = null;
  private analytics: CacheAnalytics = {
    totalHits: 0,
    totalMisses: 0,
    totalQueries: 0,
    averageResponseTime: 0,
    cacheSize: 0,
    lastCleanup: Date.now(),
    popularQueries: {}
  };

  constructor() {
    this.initializeDB();
    this.startCleanupTimer();
  }

  /**
   * Initialize IndexedDB database
   */
  private async initializeDB(): Promise<void> {
    if (typeof window === 'undefined' || !window.indexedDB) {
      console.warn('IndexedDB not available, search caching disabled');
      return;
    }

    try {
      const request = indexedDB.open(DB_NAME, DB_VERSION);
      
      request.onerror = () => {
        console.error('Failed to open IndexedDB:', request.error);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create search cache store
        if (!db.objectStoreNames.contains(CACHE_STORE)) {
          const cacheStore = db.createObjectStore(CACHE_STORE, { keyPath: 'id' });
          cacheStore.createIndex('queryHash', 'queryHash', { unique: false });
          cacheStore.createIndex('userId', 'userId', { unique: false });
          cacheStore.createIndex('expiresAt', 'expiresAt', { unique: false });
          cacheStore.createIndex('lastAccessed', 'lastAccessed', { unique: false });
        }

        // Create analytics store
        if (!db.objectStoreNames.contains(ANALYTICS_STORE)) {
          db.createObjectStore(ANALYTICS_STORE, { keyPath: 'id' });
        }

        // Create warmup queries store
        if (!db.objectStoreNames.contains(WARMUP_STORE)) {
          const warmupStore = db.createObjectStore(WARMUP_STORE, { keyPath: 'query' });
          warmupStore.createIndex('priority', 'priority', { unique: false });
        }
      };

      request.onsuccess = async (event) => {
        this.db = (event.target as IDBOpenDBRequest).result;
        this.initialized = true;
        
        // Load analytics
        await this.loadAnalytics();
        
        // Initialize warmup queries if empty
        await this.initializeWarmupQueries();
        
        console.log('SearchCacheService initialized successfully');
      };

    } catch (error) {
      console.error('Error initializing SearchCacheService:', error);
    }
  }

  /**
   * Get dynamic TTL based on query characteristics (as per search-improvements.md)
   */
  private getDynamicCacheTTL(query: string, intent?: SearchIntent): number {
    const normalizedQuery = query.toLowerCase().trim();
    
    // Popular searches cache longer (10 minutes)
    if (this.isPopularSearch(normalizedQuery)) {
      console.log(`🏆 Popular query "${query}" - Extended TTL: 10 minutes`);
      return 10 * 60 * 1000;
    }
    
    // New releases and trending searches cache shorter (2 minutes)
    if (normalizedQuery.includes('new') || 
        normalizedQuery.includes('upcoming') ||
        normalizedQuery.includes('latest') ||
        normalizedQuery.includes('2024') ||
        normalizedQuery.includes('2025') ||
        normalizedQuery.includes('trending')) {
      console.log(`🆕 Time-sensitive query "${query}" - Reduced TTL: 2 minutes`);
      return 2 * 60 * 1000;
    }
    
    // Genre and platform searches cache medium time (7 minutes)
    if (intent?.type === 'genre' || intent?.type === 'platform') {
      console.log(`🎮 Genre/Platform query "${query}" - Medium TTL: 7 minutes`);
      return 7 * 60 * 1000;
    }
    
    // Mood-based searches cache longer as they're more stable (8 minutes)
    if (intent?.type === 'mood') {
      console.log(`😊 Mood query "${query}" - Extended TTL: 8 minutes`);
      return 8 * 60 * 1000;
    }
    
    // AI recommendation queries cache shorter as they should be fresh (3 minutes)
    if (intent?.type === 'recommendation') {
      console.log(`🤖 Recommendation query "${query}" - Short TTL: 3 minutes`);
      return 3 * 60 * 1000;
    }
    
    // Default TTL for all other queries
    console.log(`📋 Standard query "${query}" - Default TTL: 5 minutes`);
    return DEFAULT_CACHE_TTL;
  }

  /**
   * Check if a search query is popular based on analytics
   */
  private isPopularSearch(query: string): boolean {
    const normalizedQuery = query.toLowerCase().trim();
    
    // Check against known popular queries
    const popularQueries = [
      'minecraft', 'fortnite', 'call of duty', 'grand theft auto', 'gta',
      'world of warcraft', 'wow', 'league of legends', 'valorant', 'apex legends',
      'cyberpunk', 'witcher', 'elder scrolls', 'skyrim', 'fallout',
      'fifa', 'nba', 'madden', 'assassin\'s creed', 'ac',
      'halo', 'doom', 'battlefield', 'overwatch', 'counter-strike',
      'popular games', 'best games', 'top games', 'indie games',
      'rpg games', 'action games', 'strategy games', 'puzzle games'
    ];
    
    // Check if query matches popular patterns
    const isPopularPattern = popularQueries.some(popular => 
      normalizedQuery.includes(popular) || popular.includes(normalizedQuery)
    );
    
    // Check analytics data
    const queryCount = this.analytics.popularQueries[query] || 0;
    const isAnalyticsPopular = queryCount >= 5; // 5+ searches = popular
    
    return isPopularPattern || isAnalyticsPopular;
  }

  /**
   * Generate cache key from query and filters
   */
  private generateQueryHash(query: string, userId?: string, additionalParams?: Record<string, unknown>): string {
    const normalizedQuery = query.toLowerCase().trim();
    const params = additionalParams ? JSON.stringify(additionalParams) : '';
    const userKey = userId || 'anonymous';
    
    // Simple hash function for cache key
    const str = `${normalizedQuery}:${userKey}:${params}`;
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Get cached search result
   */
  async getCachedResult(
    query: string, 
    userId?: string, 
    additionalParams?: Record<string, unknown>
  ): Promise<SearchResult | null> {
    if (!this.initialized || !this.db) {
      return null;
    }

    const queryHash = this.generateQueryHash(query, userId, additionalParams);
    const startTime = Date.now();

    try {
      const transaction = this.db.transaction([CACHE_STORE], 'readonly');
      const store = transaction.objectStore(CACHE_STORE);
      const index = store.index('queryHash');
      const request = index.get(queryHash);

      return new Promise((resolve) => {
        request.onsuccess = () => {
          const cached = request.result as CachedSearchResult | undefined;
          
          if (cached && cached.expiresAt > Date.now()) {
            // Update hit count and last accessed
            this.updateCacheEntry(cached.id, {
              hitCount: cached.hitCount + 1,
              lastAccessed: Date.now()
            });
            
            // Update analytics
            this.analytics.totalHits++;
            this.analytics.totalQueries++;
            this.analytics.popularQueries[query] = (this.analytics.popularQueries[query] || 0) + 1;
            this.updateAverageResponseTime(Date.now() - startTime);
            
            console.log(`Cache HIT for query: "${query}"`);
            resolve(cached.result);
          } else {
            // Cache miss
            this.analytics.totalMisses++;
            this.analytics.totalQueries++;
            
            if (cached && cached.expiresAt <= Date.now()) {
              // Remove expired entry
              this.deleteCacheEntry(cached.id);
            }
            
            console.log(`Cache MISS for query: "${query}"`);
            resolve(null);
          }
        };

        request.onerror = () => {
          console.error('Error retrieving cached result:', request.error);
          resolve(null);
        };
      });

    } catch (error) {
      console.error('Error getting cached result:', error);
      return null;
    }
  }

  /**
   * Cache search result with dynamic TTL based on query characteristics
   */
  async cacheResult(
    query: string,
    intent: SearchIntent,
    result: SearchResult,
    userId?: string,
    customTtl?: number,
    additionalParams?: Record<string, unknown>
  ): Promise<void> {
    if (!this.initialized || !this.db) {
      return;
    }

    const queryHash = this.generateQueryHash(query, userId, additionalParams);
    const now = Date.now();
    
    // Use dynamic TTL unless custom TTL is explicitly provided
    const ttl = customTtl || this.getDynamicCacheTTL(query, intent);

    const cachedResult: CachedSearchResult = {
      id: generateUUID(),
      queryHash,
      query,
      intent,
      result,
      cachedAt: now,
      expiresAt: now + ttl,
      hitCount: 0,
      lastAccessed: now,
      userId
    };

    try {
      const transaction = this.db.transaction([CACHE_STORE], 'readwrite');
      const store = transaction.objectStore(CACHE_STORE);
      
      // Check if we need to cleanup before adding
      await this.cleanupIfNeeded();
      
      const request = store.add(cachedResult);
      
      request.onsuccess = () => {
        this.analytics.cacheSize++;
        console.log(`Cached search result for query: "${query}"`);
      };

      request.onerror = () => {
        console.error('Error caching search result:', request.error);
      };

    } catch (error) {
      console.error('Error caching result:', error);
    }
  }

  /**
   * Warm up cache with popular queries
   */
  async warmupCache(customQueries?: string[]): Promise<void> {
    const queries = customQueries || WARMUP_QUERIES;
    
    console.log('Starting cache warmup with queries:', queries);
    
    for (const query of queries) {
      try {
        // Check if already cached
        const cached = await this.getCachedResult(query);
        if (!cached) {
          // This would be called by the enhanced search service
          // We'll mark it for warmup instead of actually executing
          await this.markForWarmup(query, 'high');
        }
      } catch (error) {
        console.error(`Error warming up cache for query "${query}":`, error);
      }
    }
  }

  /**
   * Mark query for warmup
   */
  private async markForWarmup(query: string, priority: 'high' | 'medium' | 'low'): Promise<void> {
    if (!this.initialized || !this.db) return;

    try {
      const transaction = this.db.transaction([WARMUP_STORE], 'readwrite');
      const store = transaction.objectStore(WARMUP_STORE);
      
      const warmupEntry = {
        query,
        priority,
        markedAt: Date.now(),
        attempts: 0
      };
      
      await store.put(warmupEntry);
    } catch (error) {
      console.error('Error marking query for warmup:', error);
    }
  }

  /**
   * Get queries marked for warmup
   */
  async getWarmupQueries(): Promise<Array<{ query: string; priority: string }>> {
    if (!this.initialized || !this.db) return [];

    try {
      const transaction = this.db.transaction([WARMUP_STORE], 'readonly');
      const store = transaction.objectStore(WARMUP_STORE);
      const request = store.getAll();

      return new Promise((resolve) => {
        request.onsuccess = () => {
          resolve(request.result || []);
        };
        request.onerror = () => {
          console.error('Error getting warmup queries:', request.error);
          resolve([]);
        };
      });
    } catch (error) {
      console.error('Error getting warmup queries:', error);
      return [];
    }
  }

  /**
   * Clear cache
   */
  async clearCache(userId?: string): Promise<void> {
    if (!this.initialized || !this.db) return;

    try {
      const transaction = this.db.transaction([CACHE_STORE], 'readwrite');
      const store = transaction.objectStore(CACHE_STORE);

      if (userId) {
        // Clear only user's cache
        const index = store.index('userId');
        const request = index.openCursor(IDBKeyRange.only(userId));
        
        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            cursor.delete();
            cursor.continue();
          }
        };
      } else {
        // Clear all cache
        await store.clear();
        this.analytics.cacheSize = 0;
      }

      console.log(`Cache cleared${userId ? ` for user ${userId}` : ' (all entries)'}`);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): CacheStats {
    const totalQueries = this.analytics.totalQueries || 1; // Avoid division by zero
    const popularQueries = Object.entries(this.analytics.popularQueries)
      .map(([query, count]) => ({ query, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      hitRate: (this.analytics.totalHits / totalQueries) * 100,
      missRate: (this.analytics.totalMisses / totalQueries) * 100,
      totalQueries: this.analytics.totalQueries,
      cacheSize: this.analytics.cacheSize,
      averageResponseTime: this.analytics.averageResponseTime,
      popularQueries
    };
  }

  /**
   * Cleanup expired entries
   */
  private async cleanup(): Promise<void> {
    if (!this.initialized || !this.db) return;

    try {
      const now = Date.now();
      const transaction = this.db.transaction([CACHE_STORE], 'readwrite');
      const store = transaction.objectStore(CACHE_STORE);
      const index = store.index('expiresAt');
      
      const request = index.openCursor(IDBKeyRange.upperBound(now));
      let deletedCount = 0;

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          deletedCount++;
          cursor.continue();
        } else {
          if (deletedCount > 0) {
            this.analytics.cacheSize -= deletedCount;
            console.log(`Cleaned up ${deletedCount} expired cache entries`);
          }
          this.analytics.lastCleanup = now;
        }
      };

    } catch (error) {
      console.error('Error during cache cleanup:', error);
    }
  }

  /**
   * Cleanup if cache size exceeds limit
   */
  private async cleanupIfNeeded(): Promise<void> {
    if (this.analytics.cacheSize >= MAX_CACHE_ENTRIES) {
      await this.cleanupOldEntries();
    }
  }

  /**
   * Cleanup old entries based on LRU
   */
  private async cleanupOldEntries(): Promise<void> {
    if (!this.initialized || !this.db) return;

    try {
      const transaction = this.db.transaction([CACHE_STORE], 'readwrite');
      const store = transaction.objectStore(CACHE_STORE);
      const index = store.index('lastAccessed');
      
      const request = index.openCursor();
      let deleteCount = 0;
      const maxDelete = Math.floor(MAX_CACHE_ENTRIES * 0.2); // Delete 20% of oldest entries

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor && deleteCount < maxDelete) {
          cursor.delete();
          deleteCount++;
          cursor.continue();
        } else {
          if (deleteCount > 0) {
            this.analytics.cacheSize -= deleteCount;
            console.log(`Cleaned up ${deleteCount} old cache entries (LRU)`);
          }
        }
      };

    } catch (error) {
      console.error('Error cleaning up old entries:', error);
    }
  }

  /**
   * Update cache entry
   */
  private async updateCacheEntry(id: string, updates: Partial<CachedSearchResult>): Promise<void> {
    if (!this.initialized || !this.db) return;

    try {
      const transaction = this.db.transaction([CACHE_STORE], 'readwrite');
      const store = transaction.objectStore(CACHE_STORE);
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const entry = getRequest.result;
        if (entry) {
          const updatedEntry = { ...entry, ...updates };
          store.put(updatedEntry);
        }
      };
    } catch (error) {
      console.error('Error updating cache entry:', error);
    }
  }

  /**
   * Delete cache entry
   */
  private async deleteCacheEntry(id: string): Promise<void> {
    if (!this.initialized || !this.db) return;

    try {
      const transaction = this.db.transaction([CACHE_STORE], 'readwrite');
      const store = transaction.objectStore(CACHE_STORE);
      await store.delete(id);
      this.analytics.cacheSize--;
    } catch (error) {
      console.error('Error deleting cache entry:', error);
    }
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = window.setInterval(() => {
      this.cleanup();
      this.saveAnalytics();
    }, CACHE_CLEANUP_INTERVAL);
  }

  /**
   * Update average response time
   */
  private updateAverageResponseTime(responseTime: number): void {
    const count = this.analytics.totalQueries;
    const currentAvg = this.analytics.averageResponseTime;
    this.analytics.averageResponseTime = ((currentAvg * (count - 1)) + responseTime) / count;
  }

  /**
   * Load analytics from storage
   */
  private async loadAnalytics(): Promise<void> {
    if (!this.initialized || !this.db) return;

    try {
      const transaction = this.db.transaction([ANALYTICS_STORE], 'readonly');
      const store = transaction.objectStore(ANALYTICS_STORE);
      const request = store.get('analytics');

      request.onsuccess = () => {
        if (request.result) {
          this.analytics = { ...this.analytics, ...request.result.data };
        }
      };
    } catch (error) {
      console.error('Error loading analytics:', error);
    }
  }

  /**
   * Save analytics to storage
   */
  private async saveAnalytics(): Promise<void> {
    if (!this.initialized || !this.db) return;

    try {
      const transaction = this.db.transaction([ANALYTICS_STORE], 'readwrite');
      const store = transaction.objectStore(ANALYTICS_STORE);
      
      await store.put({
        id: 'analytics',
        data: this.analytics,
        updatedAt: Date.now()
      });
    } catch (error) {
      console.error('Error saving analytics:', error);
    }
  }

  /**
   * Initialize warmup queries
   */
  private async initializeWarmupQueries(): Promise<void> {
    if (!this.initialized || !this.db) return;

    try {
      const warmupQueries = await this.getWarmupQueries();
      
      if (warmupQueries.length === 0) {
        // Initialize with default warmup queries
        for (const query of WARMUP_QUERIES) {
          await this.markForWarmup(query, 'medium');
        }
      }
    } catch (error) {
      console.error('Error initializing warmup queries:', error);
    }
  }

  /**
   * Destroy service and cleanup resources
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    if (this.db) {
      this.db.close();
      this.db = null;
    }

    this.initialized = false;
  }
}

// Export singleton instance
export const searchCacheService = new SearchCacheService();
export type { CacheStats, CachedSearchResult };