import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { userTags } from '@/lib/supabase';
import { UserTag } from '@/types/filters';

export function useUserTags() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const {
    data: tags = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['user-tags', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      try {
        const { data, error } = await userTags.getAll(user.id);
        if (error) {
          // Handle table not found or other DB errors gracefully
          if (error.code === '42P01' || error.message?.includes('does not exist')) {
            console.warn('User tags table not found, returning empty array');
            return [];
          }
          throw error;
        }
        return data.map(tag => ({
          ...tag,
          created_at: new Date(tag.created_at)
        })) as UserTag[];
      } catch (err) {
        console.error('Error fetching user tags:', err);
        return [];
      }
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: unknown) => {
      // Don't retry if table doesn't exist
      if ((error as any)?.code === '42P01' || (error as any)?.message?.includes('does not exist')) {
        return false;
      }
      return failureCount < 2;
    }
  });

  const createTagMutation = useMutation({
    mutationFn: async (tagData: { name: string; description?: string; color?: string }) => {
      if (!user?.id) throw new Error('User not authenticated');
      try {
        const { data, error } = await userTags.create(user.id, tagData);
        if (error) {
          if (error.code === '42P01' || error.message?.includes('does not exist')) {
            throw new Error('Tag system not available yet. Please try again later.');
          }
          throw error;
        }
        return data;
      } catch (err) {
        console.error('Error creating tag:', err);
        throw err;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
    }
  });

  const updateTagMutation = useMutation({
    mutationFn: async ({ tagId, updates }: { tagId: string; updates: Partial<{ name: string; description: string; color: string }> }) => {
      const { data, error } = await userTags.update(tagId, updates);
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
    }
  });

  const deleteTagMutation = useMutation({
    mutationFn: async (tagId: string) => {
      const { error } = await userTags.delete(tagId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
    }
  });

  const getPopularTagsQuery = useQuery({
    queryKey: ['popular-tags'],
    queryFn: async () => {
      const { data, error } = await userTags.getPopular(20);
      if (error) throw error;
      return data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000 // 30 minutes
  });

  return {
    tags,
    isLoading,
    error,
    refetch,
    createTag: createTagMutation.mutate,
    updateTag: updateTagMutation.mutate,
    deleteTag: deleteTagMutation.mutate,
    isCreating: createTagMutation.isPending,
    isUpdating: updateTagMutation.isPending,
    isDeleting: deleteTagMutation.isPending,
    popularTags: getPopularTagsQuery.data || [],
    isLoadingPopular: getPopularTagsQuery.isLoading
  };
}

export function useTagSuggestions() {
  const { user } = useAuth();
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const generateSuggestions = async (gameTitle: string, genres: string[] = []) => {
    if (!user?.id) return;
    
    setIsLoading(true);
    try {
      // Simple tag suggestions based on game data
      const baseSuggestions = new Set<string>();
      
      // Genre-based suggestions
      genres.forEach(genre => {
        baseSuggestions.add(genre.toLowerCase());
      });
      
      // Title-based suggestions
      const titleWords = gameTitle.toLowerCase().split(/\s+/);
      titleWords.forEach(word => {
        if (word.length > 3) {
          baseSuggestions.add(word);
        }
      });
      
      // Common gaming tags
      const commonTags = [
        'favorites', 'multiplayer', 'single-player', 'co-op', 'indie', 'AAA',
        'retro', 'modern', 'casual', 'hardcore', 'story-rich', 'open-world',
        'linear', 'atmospheric', 'challenging', 'relaxing', 'replay-value',
        'short', 'long', 'beautiful', 'soundtrack'
      ];
      
      // Add relevant common tags based on context
      commonTags.forEach(tag => {
        if (Math.random() > 0.7) { // Random selection for variety
          baseSuggestions.add(tag);
        }
      });
      
      setSuggestions(Array.from(baseSuggestions).slice(0, 8));
    } catch (error) {
      console.error('Error generating tag suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    suggestions,
    isLoading,
    generateSuggestions,
    clearSuggestions: () => setSuggestions([])
  };
}