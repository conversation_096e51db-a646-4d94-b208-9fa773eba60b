/**
 * Validation utility functions
 */

/**
 * Check if value is null or undefined
 */
export function isNullOrUndefined(value: unknown): value is null | undefined {
  return value === null || value === undefined;
}

/**
 * Check if value is not null or undefined
 */
export function isNotNullOrUndefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Check if string is empty or only whitespace
 */
export function isEmptyString(value: string | null | undefined): boolean {
  return !value || value.trim().length === 0;
}

/**
 * Check if string is not empty
 */
export function isNotEmptyString(value: string | null | undefined): value is string {
  return Boolean(value && value.trim().length > 0);
}

/**
 * Check if value is a valid email address
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Check if value is a valid URL
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Check if value is a valid phone number (basic validation)
 */
export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-()]{10,}$/;
  return phoneRegex.test(phone);
}

/**
 * Check if value is a valid UUID
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Check if value is a valid JSON string
 */
export function isValidJSON(jsonString: string): boolean {
  try {
    JSON.parse(jsonString);
    return true;
  } catch {
    return false;
  }
}

/**
 * Check if value is a valid date
 */
export function isValidDate(date: unknown): date is Date {
  return date instanceof Date && !isNaN(date.getTime());
}

/**
 * Check if string represents a valid date
 */
export function isValidDateString(dateString: string): boolean {
  const date = new Date(dateString);
  return isValidDate(date);
}

/**
 * Check if value is a number (including string numbers)
 */
export function isNumeric(value: unknown): boolean {
  if (typeof value === 'number') return !isNaN(value);
  if (typeof value === 'string') return !isNaN(Number(value)) && value.trim() !== '';
  return false;
}

/**
 * Check if value is an integer
 */
export function isInteger(value: unknown): boolean {
  return isNumeric(value) && Number.isInteger(Number(value));
}

/**
 * Check if value is a positive number
 */
export function isPositiveNumber(value: unknown): boolean {
  return isNumeric(value) && Number(value) > 0;
}

/**
 * Check if value is a non-negative number (>= 0)
 */
export function isNonNegativeNumber(value: unknown): boolean {
  return isNumeric(value) && Number(value) >= 0;
}

/**
 * Check if value is within a range
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return value >= min && value <= max;
}

/**
 * Check if string length is within range
 */
export function isValidLength(str: string, min: number, max?: number): boolean {
  const length = str.length;
  if (max === undefined) {
    return length >= min;
  }
  return length >= min && length <= max;
}

/**
 * Check if password meets basic requirements
 */
export function isValidPassword(password: string, options: {
  minLength?: number;
  requireUppercase?: boolean;
  requireLowercase?: boolean;
  requireNumbers?: boolean;
  requireSpecialChars?: boolean;
} = {}): boolean {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = false
  } = options;

  if (password.length < minLength) return false;
  if (requireUppercase && !/[A-Z]/.test(password)) return false;
  if (requireLowercase && !/[a-z]/.test(password)) return false;
  if (requireNumbers && !/\d/.test(password)) return false;
  if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) return false;

  return true;
}

/**
 * Check if value is a valid hex color
 */
export function isValidHexColor(color: string): boolean {
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexColorRegex.test(color);
}

/**
 * Check if value is a valid RGB color
 */
export function isValidRGBColor(color: string): boolean {
  const rgbRegex = /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/;
  const match = color.match(rgbRegex);
  
  if (!match) return false;
  
  const [, r, g, b] = match;
  return [r, g, b].every(val => {
    const num = parseInt(val, 10);
    return num >= 0 && num <= 255;
  });
}

/**
 * Check if value is a valid credit card number (Luhn algorithm)
 */
export function isValidCreditCard(cardNumber: string): boolean {
  const cleaned = cardNumber.replace(/\D/g, '');
  
  if (cleaned.length < 13 || cleaned.length > 19) return false;
  
  let sum = 0;
  let isEven = false;
  
  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned[i], 10);
    
    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    isEven = !isEven;
  }
  
  return sum % 10 === 0;
}

/**
 * Check if value is a valid IP address (IPv4)
 */
export function isValidIPv4(ip: string): boolean {
  const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
  const match = ip.match(ipv4Regex);
  
  if (!match) return false;
  
  return match.slice(1).every(octet => {
    const num = parseInt(octet, 10);
    return num >= 0 && num <= 255;
  });
}

/**
 * Check if value is a valid MAC address
 */
export function isValidMACAddress(mac: string): boolean {
  const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
  return macRegex.test(mac);
}

/**
 * Check if value is a valid slug (URL-friendly string)
 */
export function isValidSlug(slug: string): boolean {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
}

/**
 * Check if array has minimum length
 */
export function hasMinLength<T>(array: T[], minLength: number): boolean {
  return array.length >= minLength;
}

/**
 * Check if array has maximum length
 */
export function hasMaxLength<T>(array: T[], maxLength: number): boolean {
  return array.length <= maxLength;
}

/**
 * Check if object has required properties
 */
export function hasRequiredProperties<T extends Record<string, unknown>>(
  obj: T,
  requiredProps: (keyof T)[]
): boolean {
  return requiredProps.every(prop => prop in obj && obj[prop] !== undefined);
}

/**
 * Validate form data against schema
 */
export function validateFormData<T extends Record<string, unknown>>(
  data: T,
  schema: {
    [K in keyof T]?: {
      required?: boolean;
      type?: 'string' | 'number' | 'boolean' | 'email' | 'url';
      minLength?: number;
      maxLength?: number;
      min?: number;
      max?: number;
      pattern?: RegExp;
      custom?: (value: T[K]) => boolean;
    };
  }
): { isValid: boolean; errors: Partial<Record<keyof T, string>> } {
  const errors: Partial<Record<keyof T, string>> = {};

  for (const [key, rules] of Object.entries(schema) as [keyof T, NonNullable<typeof schema[keyof T]>][]) {
    const value = data[key];

    // Check required
    if (rules.required && (value === undefined || value === null || value === '')) {
      errors[key] = `${String(key)} is required`;
      continue;
    }

    // Skip validation if value is empty and not required
    if (!rules.required && (value === undefined || value === null || value === '')) {
      continue;
    }

    // Type validation
    if (rules.type) {
      switch (rules.type) {
        case 'string':
          if (typeof value !== 'string') {
            errors[key] = `${String(key)} must be a string`;
            continue;
          }
          break;
        case 'number':
          if (!isNumeric(value)) {
            errors[key] = `${String(key)} must be a number`;
            continue;
          }
          break;
        case 'boolean':
          if (typeof value !== 'boolean') {
            errors[key] = `${String(key)} must be a boolean`;
            continue;
          }
          break;
        case 'email':
          if (typeof value !== 'string' || !isValidEmail(value)) {
            errors[key] = `${String(key)} must be a valid email`;
            continue;
          }
          break;
        case 'url':
          if (typeof value !== 'string' || !isValidUrl(value)) {
            errors[key] = `${String(key)} must be a valid URL`;
            continue;
          }
          break;
      }
    }

    // Length validation for strings
    if (typeof value === 'string') {
      if (rules.minLength && value.length < rules.minLength) {
        errors[key] = `${String(key)} must be at least ${rules.minLength} characters`;
        continue;
      }
      if (rules.maxLength && value.length > rules.maxLength) {
        errors[key] = `${String(key)} must be no more than ${rules.maxLength} characters`;
        continue;
      }
    }

    // Range validation for numbers
    if (isNumeric(value)) {
      const numValue = Number(value);
      if (rules.min !== undefined && numValue < rules.min) {
        errors[key] = `${String(key)} must be at least ${rules.min}`;
        continue;
      }
      if (rules.max !== undefined && numValue > rules.max) {
        errors[key] = `${String(key)} must be no more than ${rules.max}`;
        continue;
      }
    }

    // Pattern validation
    if (rules.pattern && typeof value === 'string' && !rules.pattern.test(value)) {
      errors[key] = `${String(key)} format is invalid`;
      continue;
    }

    // Custom validation
    if (rules.custom && !rules.custom(value)) {
      errors[key] = `${String(key)} is invalid`;
      continue;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Sanitize string by removing potentially dangerous characters
 */
export function sanitizeString(str: string): string {
  return str
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Check if value is safe for HTML output
 */
export function isSafeForHTML(value: string): boolean {
  const dangerousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+=/i,
    /<iframe/i,
    /<object/i,
    /<embed/i
  ];
  
  return !dangerousPatterns.some(pattern => pattern.test(value));
}