import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';

export interface ArtworkRouterSettings {
  useEnvironmentKeys: boolean;
  enableHighQuality: boolean;
  enableCostOptimization: boolean;
}

const DEFAULT_SETTINGS: ArtworkRouterSettings = {
  useEnvironmentKeys: true,
  enableHighQuality: false,
  enableCostOptimization: true,
};

const STORAGE_KEY = 'artwork-router-settings';

export function useArtworkRouterSettings() {
  const { user } = useAuth();
  const [settings, setSettings] = useState<ArtworkRouterSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load settings from localStorage
  const loadSettings = useCallback(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedSettings = JSON.parse(stored);
        setSettings({ ...DEFAULT_SETTINGS, ...parsedSettings });
      }
    } catch (error) {
      console.warn('Failed to load artwork router settings:', error);
      setSettings(DEFAULT_SETTINGS);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save settings to localStorage
  const saveSettings = useCallback(async (newSettings: Partial<ArtworkRouterSettings>) => {
    if (!user) {
      toast.error('Must be logged in to save settings');
      return;
    }

    setIsSaving(true);
    try {
      const updatedSettings = { ...settings, ...newSettings };
      
      // Save to localStorage
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSettings));
      setSettings(updatedSettings);
      
      toast.success('Router settings saved');
    } catch (error) {
      console.error('Failed to save artwork router settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  }, [settings, user]);

  // Update individual setting
  const updateSetting = useCallback(async (key: keyof ArtworkRouterSettings, value: boolean) => {
    await saveSettings({ [key]: value });
  }, [saveSettings]);

  // Initialize on mount
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    settings,
    isLoading,
    isSaving,
    updateSetting,
    saveSettings,
  };
}