import { supabase } from '../supabase';
import { Session, AuthChangeEvent } from '@supabase/supabase-js';
import {
  UserProfileRecord,
  UserProfileUpdateData,
  UserPreferencesRecord,
  UserPreferencesUpdateData,
  UserRecommendationRecord,
  UserRecommendationUpdateData,
} from '../../types/database';

/**
 * User Authentication API
 * Handles user authentication, registration, and session management
 */
export class UserAuthAPI {
  /**
   * Register a new user
   */
  async signUp(email: string, password: string, username?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: { username }
      }
    });
    
    if (error) {
      // Enhanced error logging for signup
      const errorDetails = {
        message: error?.message || 'Unknown signup error',
        status: error?.status || 'unknown',
        name: error?.name || 'AuthError',
        timestamp: new Date().toISOString(),
        action: 'signUp',
        context: 'authentication',
        email: email ? email.substring(0, 3) + '***' : 'unknown'
      };
      
      console.error('❌ SignUp Error:', errorDetails);
      
      // Log to external service if available
      if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
        try {
          window.navigator.sendBeacon('/api/log-error', JSON.stringify(errorDetails));
        } catch (logError) {
          console.warn('Failed to send error log:', logError);
        }
      }
    } else if (data.user) {
      // Ensure user records are created after successful signup
      await this.ensureUserRecords(data.user.id);
    }
    
    return { data, error };
  }

  /**
   * Sign in an existing user
   */
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) {
      // Enhanced error logging for signin
      const errorDetails = {
        message: error?.message || 'Unknown signin error',
        status: error?.status || 'unknown',
        name: error?.name || 'AuthError',
        timestamp: new Date().toISOString(),
        action: 'signIn',
        context: 'authentication',
        email: email ? email.substring(0, 3) + '***' : 'unknown'
      };
      
      console.error('❌ SignIn Error:', errorDetails);
      
      // Log to external service if available
      if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
        try {
          window.navigator.sendBeacon('/api/log-error', JSON.stringify(errorDetails));
        } catch (logError) {
          console.warn('Failed to send error log:', logError);
        }
      }
    } else if (data.user) {
      // Ensure user records exist after successful signin
      await this.ensureUserRecords(data.user.id);
    }
    
    return { data, error };
  }

  /**
   * Sign out the current user
   */
  async signOut() {
    const { error } = await supabase.auth.signOut();
    return { error };
  }

  /**
   * Reset user password
   */
  async resetPassword(email: string) {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    });
    
    if (error) {
      // Enhanced error logging for password reset
      const errorDetails = {
        message: error?.message || 'Unknown password reset error',
        status: error?.status || 'unknown',
        name: error?.name || 'AuthError',
        timestamp: new Date().toISOString(),
        action: 'resetPassword',
        context: 'authentication',
        email: email ? email.substring(0, 3) + '***' : 'unknown'
      };
      
      console.error('❌ Password Reset Error:', errorDetails);
      
      // Log to external service if available
      if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
        try {
          window.navigator.sendBeacon('/api/log-error', JSON.stringify(errorDetails));
        } catch (logError) {
          console.warn('Failed to send error log:', logError);
        }
      }
    }
    
    return { data, error };
  }

  /**
   * Get current user
   */
  getCurrentUser() {
    return supabase.auth.getUser();
  }

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (event: AuthChangeEvent, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }

  /**
   * Helper function to ensure user records exist
   */
  private async ensureUserRecords(userId: string) {
    try {
      // Check if user profile exists
      const { error: profileError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        const { error: createProfileError } = await supabase
          .from('user_profiles')
          .insert({ id: userId });
        
        if (createProfileError) {
          console.warn('Failed to create user profile:', createProfileError);
        }
      }

      // Check if user preferences exist
      const { error: preferencesError } = await supabase
        .from('user_preferences')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (preferencesError && preferencesError.code === 'PGRST116') {
        // Preferences don't exist, create them
        const { error: createPreferencesError } = await supabase
          .from('user_preferences')
          .insert({ user_id: userId });
        
        if (createPreferencesError) {
          console.warn('Failed to create user preferences:', createPreferencesError);
        }
      }
    } catch (error) {
      console.warn('Error ensuring user records:', error);
    }
  }
}

/**
 * User Profile API
 * Handles user profile management
 */
export class UserProfileAPI {
  /**
   * Get user profile by ID
   */
  async getProfile(userId: string) {
    try {
      await this.ensureUserRecords(userId);
      
      return supabase.from('user_profiles').select('*').eq('id', userId).single();
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userId: string, updates: UserProfileUpdateData) {
    return supabase.from('user_profiles').update(updates).eq('id', userId).select().single();
  }

  /**
   * Create user profile
   */
  async createProfile(profileData: Omit<UserProfileRecord, 'created_at' | 'updated_at'>) {
    return supabase.from('user_profiles').insert(profileData).select().single();
  }

  /**
   * Helper function to ensure user records exist
   */
  private async ensureUserRecords(userId: string) {
    try {
      // Check if user profile exists
      const { error: profileError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        const { error: createProfileError } = await supabase
          .from('user_profiles')
          .insert({ id: userId });
        
        if (createProfileError) {
          console.warn('Failed to create user profile:', createProfileError);
        }
      }

      // Check if user preferences exist
      const { error: preferencesError } = await supabase
        .from('user_preferences')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (preferencesError && preferencesError.code === 'PGRST116') {
        // Preferences don't exist, create them
        const { error: createPreferencesError } = await supabase
          .from('user_preferences')
          .insert({ user_id: userId });
        
        if (createPreferencesError) {
          console.warn('Failed to create user preferences:', createPreferencesError);
        }
      }
    } catch (error) {
      console.warn('Error ensuring user records:', error);
    }
  }
}

/**
 * User Preferences API
 * Handles user preferences and settings
 */
export class UserPreferencesAPI {
  /**
   * Get user preferences by user ID
   */
  async getPreferences(userId: string) {
    try {
      await this.ensureUserRecords(userId);
      
      return supabase.from('user_preferences').select('*').eq('user_id', userId).single();
    } catch (error) {
      console.error('Error getting user preferences:', error);
      throw error;
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(userId: string, updates: UserPreferencesUpdateData) {
    return supabase.from('user_preferences').update(updates).eq('user_id', userId).select().single();
  }

  /**
   * Create user preferences
   */
  async createPreferences(preferencesData: Omit<UserPreferencesRecord, 'id' | 'created_at' | 'updated_at'>) {
    return supabase.from('user_preferences').insert(preferencesData).select().single();
  }

  /**
   * Helper function to ensure user records exist
   */
  private async ensureUserRecords(userId: string) {
    try {
      // Check if user profile exists
      const { error: profileError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        const { error: createProfileError } = await supabase
          .from('user_profiles')
          .insert({ id: userId });
        
        if (createProfileError) {
          console.warn('Failed to create user profile:', createProfileError);
        }
      }

      // Check if user preferences exist
      const { error: preferencesError } = await supabase
        .from('user_preferences')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (preferencesError && preferencesError.code === 'PGRST116') {
        // Preferences don't exist, create them
        const { error: createPreferencesError } = await supabase
          .from('user_preferences')
          .insert({ user_id: userId });
        
        if (createPreferencesError) {
          console.warn('Failed to create user preferences:', createPreferencesError);
        }
      }
    } catch (error) {
      console.warn('Error ensuring user records:', error);
    }
  }
}

/**
 * User Recommendations API
 * Handles AI recommendations for users
 */
export class UserRecommendationsAPI {
  /**
   * Get user-based recommendations
   */
  async getUserBasedRecommendations(userId: string, limit: number = 20) {
    try {
      await this.ensureUserRecords(userId);
      
      return supabase
        .from('user_recommendations')
        .select('*')
        .eq('user_id', userId)
        .eq('recommendation_type', 'user_based')
        .order('confidence_score', { ascending: false })
        .limit(limit);
    } catch (error) {
      console.error('Error getting user-based recommendations:', error);
      throw error;
    }
  }

  /**
   * Get similar game recommendations
   */
  async getSimilarGameRecommendations(userId: string, sourceGameId?: string, limit: number = 20) {
    try {
      await this.ensureUserRecords(userId);
      
      let query = supabase
        .from('user_recommendations')
        .select('*')
        .eq('user_id', userId)
        .eq('recommendation_type', 'similar_game')
        .order('confidence_score', { ascending: false })
        .limit(limit);

      if (sourceGameId) {
        query = query.eq('source_game_id', sourceGameId);
      }

      return query;
    } catch (error) {
      console.error('Error getting similar game recommendations:', error);
      throw error;
    }
  }

  /**
   * Save recommendations for a user
   */
  async saveRecommendations(recommendations: Omit<UserRecommendationRecord, 'id' | 'created_at' | 'updated_at'>[]) {
    return supabase
      .from('user_recommendations')
      .upsert(recommendations, {
        onConflict: 'user_id,igdb_id,recommendation_type,source_game_id'
      })
      .select();
  }

  /**
   * Clear user recommendations
   */
  async clearUserRecommendations(userId: string, recommendationType?: 'user_based' | 'similar_game') {
    let query = supabase
      .from('user_recommendations')
      .delete()
      .eq('user_id', userId);

    if (recommendationType) {
      query = query.eq('recommendation_type', recommendationType);
    }

    return query;
  }

  /**
   * Update a specific recommendation
   */
  async updateRecommendation(id: string, updates: UserRecommendationUpdateData) {
    return supabase.from('user_recommendations').update(updates).eq('id', id).select().single();
  }

  /**
   * Delete a specific recommendation
   */
  async deleteRecommendation(id: string) {
    return supabase.from('user_recommendations').delete().eq('id', id);
  }

  /**
   * Helper function to ensure user records exist
   */
  private async ensureUserRecords(userId: string) {
    try {
      // Check if user profile exists
      const { error: profileError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        const { error: createProfileError } = await supabase
          .from('user_profiles')
          .insert({ id: userId });
        
        if (createProfileError) {
          console.warn('Failed to create user profile:', createProfileError);
        }
      }

      // Check if user preferences exist
      const { error: preferencesError } = await supabase
        .from('user_preferences')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (preferencesError && preferencesError.code === 'PGRST116') {
        // Preferences don't exist, create them
        const { error: createPreferencesError } = await supabase
          .from('user_preferences')
          .insert({ user_id: userId });
        
        if (createPreferencesError) {
          console.warn('Failed to create user preferences:', createPreferencesError);
        }
      }
    } catch (error) {
      console.warn('Error ensuring user records:', error);
    }
  }
}

// Export API service instances
export const userAuthAPI = new UserAuthAPI();
export const userProfileAPI = new UserProfileAPI();
export const userPreferencesAPI = new UserPreferencesAPI();
export const userRecommendationsAPI = new UserRecommendationsAPI();

// Keep backward compatibility
export const auth = userAuthAPI;