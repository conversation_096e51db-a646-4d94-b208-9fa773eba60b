/*
  # Fix User Creation Issues Migration
  
  This migration fixes issues with user creation by:
  1. Ensuring user_profiles table has all required columns
  2. Fixing the user creation trigger
  3. Adding missing columns to user_preferences
  4. Ensuring proper RLS policies
*/

-- Add missing columns to user_profiles table
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS location text,
ADD COLUMN IF NOT <PERSON>XISTS website text,
ADD COLUMN IF NOT EXISTS social_links jsonb DEFAULT '{}';

-- Add missing columns to user_preferences table
ALTER TABLE user_preferences 
ADD COLUMN IF NOT EXISTS notifications jsonb DEFAULT '{"price_alerts": true, "new_deals": true, "game_updates": true, "newsletter": false}',
ADD COLUMN IF NOT EXISTS privacy jsonb DEFAULT '{"profile_public": true, "library_public": false, "activity_public": false}',
ADD COLUMN IF NOT EXISTS display jsonb DEFAULT '{"games_per_page": 20, "default_sort": "date_added", "show_metacritic": true, "show_screenshots": true}';

-- Update the user creation function to handle errors gracefully
CREATE OR REPLACE FUNCTION handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Insert user profile with error handling
  BEGIN
    INSERT INTO user_profiles (id, username, display_name)
    VALUES (
      new.id,
      new.raw_user_meta_data->>'username',
      COALESCE(
        new.raw_user_meta_data->>'display_name', 
        new.raw_user_meta_data->>'username', 
        split_part(new.email, '@', 1)
      )
    );
  EXCEPTION WHEN OTHERS THEN
    -- Log error but don't fail the user creation
    RAISE WARNING 'Failed to create user profile for user %: %', new.id, SQLERRM;
  END;
  
  -- Insert user preferences with error handling
  BEGIN
    INSERT INTO user_preferences (user_id)
    VALUES (new.id);
  EXCEPTION WHEN OTHERS THEN
    -- Log error but don't fail the user creation
    RAISE WARNING 'Failed to create user preferences for user %: %', new.id, SQLERRM;
  END;
  
  RETURN new;
END;
$$ language plpgsql security definer;

-- Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Ensure RLS policies are correct
DROP POLICY IF EXISTS "Users can view all profiles for public info" ON user_profiles;
CREATE POLICY "Users can view all profiles for public info"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (true);

DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
CREATE POLICY "Users can update their own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;
CREATE POLICY "Users can insert their own profile"
  ON user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Fix user_preferences policies
DROP POLICY IF EXISTS "Users can view their own preferences" ON user_preferences;
CREATE POLICY "Users can view their own preferences"
  ON user_preferences
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own preferences" ON user_preferences;
CREATE POLICY "Users can update their own preferences"
  ON user_preferences
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own preferences" ON user_preferences;
CREATE POLICY "Users can insert their own preferences"
  ON user_preferences
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Add a function to manually create missing user profiles for existing users
CREATE OR REPLACE FUNCTION create_missing_user_profiles()
RETURNS void AS $$
DECLARE
  user_record RECORD;
BEGIN
  FOR user_record IN 
    SELECT id, email, raw_user_meta_data 
    FROM auth.users 
    WHERE id NOT IN (SELECT id FROM user_profiles)
  LOOP
    BEGIN
      INSERT INTO user_profiles (id, username, display_name)
      VALUES (
        user_record.id,
        user_record.raw_user_meta_data->>'username',
        COALESCE(
          user_record.raw_user_meta_data->>'display_name', 
          user_record.raw_user_meta_data->>'username', 
          split_part(user_record.email, '@', 1)
        )
      );
    EXCEPTION WHEN OTHERS THEN
      RAISE WARNING 'Failed to create profile for existing user %: %', user_record.id, SQLERRM;
    END;
  END LOOP;
  
  FOR user_record IN 
    SELECT id 
    FROM auth.users 
    WHERE id NOT IN (SELECT user_id FROM user_preferences)
  LOOP
    BEGIN
      INSERT INTO user_preferences (user_id)
      VALUES (user_record.id);
    EXCEPTION WHEN OTHERS THEN
      RAISE WARNING 'Failed to create preferences for existing user %: %', user_record.id, SQLERRM;
    END;
  END LOOP;
END;
$$ language plpgsql security definer;

-- Run the function to create missing profiles for any existing users
SELECT create_missing_user_profiles();

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);
CREATE INDEX IF NOT EXISTS idx_user_profiles_display_name ON user_profiles(display_name);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON user_profiles TO authenticated;
GRANT ALL ON user_preferences TO authenticated;