import { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Input } from '@/components/ui/base/input';
import { Badge } from '@/components/ui/base/badge';
import { Skeleton } from '@/components/ui/base/skeleton';
import { 
  Search, 
  Mic, 
  MicOff, 
  History, 
  TrendingUp, 
  Sparkles, 
  Clock, 
  Brain,
  Zap,
  Volume2,
  VolumeX,
  Target,
  BarChart3
} from 'lucide-react';
import { useEnhancedSearch, useSearchHistory } from '@/hooks/useEnhancedSearch';
import { cn } from '@/lib/utils';

interface EnhancedSearchCardProps {
  className?: string;
  onSearchResults?: (results: unknown[]) => void;
  placeholder?: string;
  showVoiceSearch?: boolean;
  showSearchHistory?: boolean;
  showSuggestions?: boolean;
}

export const EnhancedSearchCard: React.FC<EnhancedSearchCardProps> = ({
  className = '',
  onSearchResults,
  placeholder = "Try 'relaxing puzzle games' or 'games like Skyrim'...",
  showVoiceSearch = true,
  showSearchHistory = true,
  showSuggestions = true
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    searchQuery,
    setSearchQuery,
    searchResults,
    isSearching,
    voiceSearch,
    popularSearches,
    suggestions,
    searchTime,
    gameCount,
    performSearch,
    startVoiceSearch,
    stopVoiceSearch
  } = useEnhancedSearch();

  const { getRecentSearches } = useSearchHistory();

  const recentSearches = getRecentSearches(5);

  useEffect(() => {
    if (searchResults && onSearchResults) {
      onSearchResults(searchResults.games);
    }
  }, [searchResults, onSearchResults]);

  const handleSearch = (query?: string) => {
    const searchTerm = query || searchQuery;
    if (searchTerm.trim()) {
      performSearch(searchTerm);
      setIsExpanded(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleVoiceSearch = () => {
    if (voiceSearch.isListening) {
      stopVoiceSearch();
    } else {
      startVoiceSearch();
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion);
    handleSearch(suggestion);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            Enhanced Search
            {searchResults?.intent && (
              <Badge variant="outline" className="text-xs">
                {searchResults.intent.type}
              </Badge>
            )}
          </div>
          {searchResults && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Target className="h-4 w-4" />
              {gameCount} games • {searchTime}ms
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  ref={inputRef}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  onFocus={() => setIsExpanded(true)}
                  placeholder={placeholder}
                  className="pl-10 pr-4"
                  disabled={isSearching}
                />
                {voiceSearch.transcript && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                      Voice: {voiceSearch.transcript}
                    </Badge>
                  </div>
                )}
              </div>
              
              {showVoiceSearch && voiceSearch.isSupported && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleVoiceSearch}
                  disabled={isSearching}
                  className={cn(
                    "transition-colors",
                    voiceSearch.isListening && "bg-red-100 border-red-300 text-red-600"
                  )}
                >
                  {voiceSearch.isListening ? (
                    <MicOff className="h-4 w-4 animate-pulse" />
                  ) : (
                    <Mic className="h-4 w-4" />
                  )}
                </Button>
              )}
              
              <Button
                onClick={() => handleSearch()}
                disabled={isSearching || !searchQuery.trim()}
                className="min-w-[100px]"
              >
                {isSearching ? (
                  <>
                    <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </>
                )}
              </Button>
            </div>

            {/* Voice Search Status */}
            {voiceSearch.isListening && (
              <div className="absolute top-full left-0 right-0 mt-2 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                <div className="flex items-center gap-2">
                  <Volume2 className="h-4 w-4 text-red-600 animate-pulse" />
                  <span className="text-sm text-red-700 dark:text-red-300">
                    Listening... Speak your search query
                  </span>
                </div>
              </div>
            )}

            {voiceSearch.error && (
              <div className="absolute top-full left-0 right-0 mt-2 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                <div className="flex items-center gap-2">
                  <VolumeX className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-red-700 dark:text-red-300">
                    {voiceSearch.error}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Search Intent Analysis */}
          {searchResults?.intent && (
            <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
                  Search Intent Analysis
                </span>
                <Badge variant="outline" className="text-xs">
                  {Math.round(searchResults.intent.confidence * 100)}% confidence
                </Badge>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-purple-700 dark:text-purple-300">
                  Type: <span className="font-medium">{searchResults.intent.type}</span>
                </p>
                {searchResults.intent.extractedTerms.moods && (
                  <p className="text-xs text-purple-700 dark:text-purple-300">
                    Mood: <span className="font-medium">{searchResults.intent.extractedTerms.moods.join(', ')}</span>
                  </p>
                )}
                {searchResults.intent.extractedTerms.genres && (
                  <p className="text-xs text-purple-700 dark:text-purple-300">
                    Genres: <span className="font-medium">{searchResults.intent.extractedTerms.genres.join(', ')}</span>
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Search Suggestions */}
          {(isExpanded || searchQuery) && showSuggestions && suggestions.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">AI Suggestions</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {suggestions.slice(0, 4).map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="text-xs h-8"
                  >
                    {suggestion}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Popular Searches */}
          {isExpanded && popularSearches && popularSearches.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Popular Searches</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {popularSearches.slice(0, 6).map((search, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSuggestionClick(search)}
                    className="text-xs h-8 hover:bg-green-50"
                  >
                    {search}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Recent Searches */}
          {isExpanded && showSearchHistory && recentSearches.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <History className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium">Recent Searches</span>
              </div>
              <div className="space-y-1">
                {recentSearches.slice(0, 3).map((search, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSuggestionClick(search)}
                    className="w-full justify-start text-xs h-8 font-normal hover:bg-orange-50"
                  >
                    <Clock className="h-3 w-3 mr-2 text-muted-foreground" />
                    {search}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Search Results Preview */}
          {searchResults && (
            <div className="p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Search Results</span>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{gameCount} games</Badge>
                  <Badge variant="outline" className="text-xs">{searchTime}ms</Badge>
                </div>
              </div>
              
              {/* Available Filters */}
              {searchResults.filters && (
                <div className="space-y-2">
                  {searchResults.filters.genres.length > 0 && (
                    <div>
                      <span className="text-xs text-muted-foreground">Genres: </span>
                      <span className="text-xs">{searchResults.filters.genres.slice(0, 3).join(', ')}</span>
                      {searchResults.filters.genres.length > 3 && (
                        <span className="text-xs text-muted-foreground"> +{searchResults.filters.genres.length - 3} more</span>
                      )}
                    </div>
                  )}
                  
                  {searchResults.filters.platforms.length > 0 && (
                    <div>
                      <span className="text-xs text-muted-foreground">Platforms: </span>
                      <span className="text-xs">{searchResults.filters.platforms.slice(0, 3).join(', ')}</span>
                      {searchResults.filters.platforms.length > 3 && (
                        <span className="text-xs text-muted-foreground"> +{searchResults.filters.platforms.length - 3} more</span>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Search Loading */}
          {isSearching && (
            <div className="space-y-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-blue-600 animate-spin" />
                <span className="text-sm text-blue-700 dark:text-blue-300">
                  AI is analyzing your search...
                </span>
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

interface SearchAnalyticsCardProps {
  className?: string;
}

export const SearchAnalyticsCard: React.FC<SearchAnalyticsCardProps> = ({ 
  className = '' 
}) => {
  const { getRecentSearches, getTopSearches } = useSearchHistory();
  
  const recentSearches = getRecentSearches(5);
  const topSearches = getTopSearches(3);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-blue-600" />
          Search Analytics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Top Searches */}
          {topSearches.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                Most Searched
              </h4>
              <div className="space-y-2">
                {topSearches.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm truncate">{item.query}</span>
                    <Badge variant="secondary" className="text-xs">
                      {item.count}x
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recent Activity */}
          {recentSearches.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Clock className="h-4 w-4 text-orange-600" />
                Recent Activity
              </h4>
              <div className="space-y-1">
                {recentSearches.map((search, index) => (
                  <div key={index} className="text-sm text-muted-foreground p-2 hover:bg-muted/50 rounded">
                    {search}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Empty State */}
          {recentSearches.length === 0 && topSearches.length === 0 && (
            <div className="text-center py-8">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
              <p className="text-sm text-muted-foreground">
                Start searching to see your analytics
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};