import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  private memoryUsage: number[] = [];
  private renderTimes: Map<string, number> = new Map();
  private _lastMemoryWarning: number = 0;

  private constructor() {
    this.startMemoryMonitoring();
    this.setupPerformanceObserver();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private startMemoryMonitoring() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as Performance & { memory?: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
        if (memory) {
          this.memoryUsage.push(memory.usedJSHeapSize);
          
          // Keep only last 100 measurements
          if (this.memoryUsage.length > 100) {
            this.memoryUsage.shift();
          }

          // Warn if memory usage is critically high (90% instead of 80%)
          // and only log every 30 seconds to reduce console spam
          const now = Date.now();
          if (memory.usedJSHeapSize > memory.totalJSHeapSize * 0.9 && 
              (!this._lastMemoryWarning || now - this._lastMemoryWarning > 30000)) {
            this._lastMemoryWarning = now;
            console.log('Memory usage info', {
              used: Math.round(memory.usedJSHeapSize / (1024 * 1024)) + ' MB',
              total: Math.round(memory.totalJSHeapSize / (1024 * 1024)) + ' MB',
              limit: Math.round(memory.jsHeapSizeLimit / (1024 * 1024)) + ' MB',
              percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100) + '%'
            });
          }
        }
      }, 5000); // Check every 5 seconds
    }
  }

  private setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric(entry.name, entry.duration);
          }
        });
        observer.observe({ entryTypes: ['measure', 'navigation'] });
      } catch {
        console.warn('PerformanceObserver not supported or failed to initialize');
      }
    }
  }

  public startTimer(name: string): void {
    performance.mark(`${name}-start`);
  }

  public endTimer(name: string): number {
    const endMark = `${name}-end`;
    const measureName = `${name}-measure`;
    
    performance.mark(endMark);
    performance.measure(measureName, `${name}-start`, endMark);
    
    const entries = performance.getEntriesByName(measureName);
    const duration = entries[entries.length - 1]?.duration || 0;
    
    this.recordMetric(name, duration);
    
    // Clean up marks and measures
    performance.clearMarks(`${name}-start`);
    performance.clearMarks(endMark);
    performance.clearMeasures(measureName);
    
    return duration;
  }

  public recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
    
    // Log slow operations
    if (value > 1000) { // More than 1 second
      console.warn(`Slow operation detected: ${name}`, { duration: value, operation: name });
    }
  }

  public getMetrics(name: string): { avg: number; min: number; max: number; count: number } {
    const values = this.metrics.get(name) || [];
    if (values.length === 0) {
      return { avg: 0, min: 0, max: 0, count: 0 };
    }
    
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);
    
    return { avg, min, max, count: values.length };
  }

  public getMemoryUsage(): number[] {
    return [...this.memoryUsage];
  }

  public recordRenderTime(componentName: string, time: number): void {
    this.renderTimes.set(componentName, time);
    this.recordMetric(`render-${componentName}`, time);
  }

  public getRenderTimes(): Map<string, number> {
    return new Map(this.renderTimes);
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

/**
 * Debounce hook for performance optimization
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Throttle hook for performance optimization
 */
export function useThrottle<T extends (...args: unknown[]) => unknown>(
  func: T,
  delay: number
): T {
  const lastRan = useRef<number>(0);
  const timeout = useRef<NodeJS.Timeout>();

  return useCallback(
    ((...args: Parameters<T>) => {
      const now = Date.now();
      
      if (now - lastRan.current >= delay) {
        func(...args);
        lastRan.current = now;
      } else {
        clearTimeout(timeout.current);
        timeout.current = setTimeout(() => {
          func(...args);
          lastRan.current = Date.now();
        }, delay - (now - lastRan.current));
      }
    }) as T,
    [func, delay]
  );
}

/**
 * Virtualization helper for large lists
 */
export function useVirtualization({
  itemCount,
  itemHeight,
  containerHeight,
  overscan = 5
}: {
  itemCount: number;
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}) {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleRange = useMemo(() => {
    const start = Math.floor(scrollTop / itemHeight);
    const end = Math.min(
      itemCount - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight)
    );
    
    return {
      start: Math.max(0, start - overscan),
      end: Math.min(itemCount - 1, end + overscan)
    };
  }, [scrollTop, itemHeight, containerHeight, itemCount, overscan]);

  const totalHeight = itemCount * itemHeight;
  const offsetY = visibleRange.start * itemHeight;

  return {
    visibleRange,
    totalHeight,
    offsetY,
    setScrollTop
  };
}

/**
 * Memoization with cache size limit
 */
export function useMemoWithLimit<T>(
  factory: () => T,
  deps: React.DependencyList | undefined,
  limit: number = 10
): T {
  const cache = useRef<Map<string, T>>(new Map());
  const keyOrder = useRef<string[]>([]);
  
  const key = useMemo(() => {
    return JSON.stringify(deps);
  }, [deps]);
  
  return useMemo(() => {
    if (cache.current.has(key)) {
      return cache.current.get(key)!;
    }
    
    const result = factory();
    
    // Add to cache
    cache.current.set(key, result);
    keyOrder.current.push(key);
    
    // Maintain cache size limit
    if (cache.current.size > limit) {
      const oldestKey = keyOrder.current.shift()!;
      cache.current.delete(oldestKey);
    }
    
    return result;
  }, [key, factory, limit]);
}

/**
 * Performance-optimized search hook
 */
export function useOptimizedSearch<T>(
  items: T[],
  searchFn: (item: T, query: string) => boolean,
  query: string,
  options: {
    debounceDelay?: number;
    pageSize?: number;
    enableCaching?: boolean;
  } = {}
) {
  const {
    debounceDelay = 300,
    pageSize = 20,
    enableCaching = true
  } = options;

  const debouncedQuery = useDebounce(query, debounceDelay);
  const [currentPage, setCurrentPage] = useState(0);
  const cache = useRef<Map<string, T[]>>(new Map());

  const filteredItems = useMemo(() => {
    if (!debouncedQuery.trim()) return items;
    
    const cacheKey = `${debouncedQuery}-${items.length}`;
    
    if (enableCaching && cache.current.has(cacheKey)) {
      return cache.current.get(cacheKey)!;
    }
    
    performanceMonitor.startTimer('search-filter');
    
    const results = items.filter(item => searchFn(item, debouncedQuery));
    
    const duration = performanceMonitor.endTimer('search-filter');
    
    if (enableCaching) {
      cache.current.set(cacheKey, results);
      
      // Limit cache size
      if (cache.current.size > 20) {
        const firstKey = cache.current.keys().next().value;
        if (firstKey) {
          cache.current.delete(firstKey);
        }
      }
    }
    
    console.info(`Search completed in ${duration.toFixed(2)}ms`, {
      query: debouncedQuery,
      resultCount: results.length,
      totalItems: items.length,
      duration
    });
    
    return results;
  }, [items, debouncedQuery, searchFn, enableCaching]);

  const paginatedItems = useMemo(() => {
    const start = currentPage * pageSize;
    return filteredItems.slice(start, start + pageSize);
  }, [filteredItems, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredItems.length / pageSize);

  const resetPage = useCallback(() => {
    setCurrentPage(0);
  }, []);

  // Reset page when query changes
  useEffect(() => {
    resetPage();
  }, [debouncedQuery, resetPage]);

  return {
    items: paginatedItems,
    totalItems: filteredItems.length,
    currentPage,
    totalPages,
    setCurrentPage,
    resetPage,
    isSearching: query !== debouncedQuery
  };
}

/**
 * Component performance profiler
 */
export function useComponentProfiler(componentName: string) {
  const renderStart = useRef<number>(0);
  
  useEffect(() => {
    renderStart.current = performance.now();
  });
  
  useEffect(() => {
    const renderTime = performance.now() - renderStart.current;
    performanceMonitor.recordRenderTime(componentName, renderTime);
  });
  
  return {
    startTimer: useCallback((name: string) => {
      performanceMonitor.startTimer(`${componentName}-${name}`);
    }, [componentName]),
    
    endTimer: useCallback((name: string) => {
      return performanceMonitor.endTimer(`${componentName}-${name}`);
    }, [componentName])
  };
}

/**
 * Intersection Observer hook for lazy loading
 */
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
      if (entry.isIntersecting && !hasIntersected) {
        setHasIntersected(true);
      }
    }, options);

    observer.observe(element);

    return () => observer.disconnect();
  }, [elementRef, options, hasIntersected]);

  return { isIntersecting, hasIntersected };
}

/**
 * Memory cleanup utilities
 */
export function useMemoryCleanup() {
  const cleanupTasks = useRef<(() => void)[]>([]);

  const addCleanupTask = useCallback((task: () => void) => {
    cleanupTasks.current.push(task);
  }, []);

  const cleanup = useCallback(() => {
    cleanupTasks.current.forEach(task => {
      try {
        task();
      } catch (error) {
        console.warn('Cleanup task failed:', error);
      }
    });
    cleanupTasks.current = [];
  }, []);

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return { addCleanupTask, cleanup };
}

/**
 * Simple performance timer
 */
export function createTimer(name: string) {
  const start = performance.now();
  
  return {
    end: () => {
      const duration = performance.now() - start;
      console.log(`${name}: ${duration.toFixed(2)}ms`);
      return duration;
    }
  };
}

/**
 * Measure function execution time
 */
export async function measureAsync<T>(
  name: string,
  fn: () => Promise<T>
): Promise<{ result: T; duration: number }> {
  const start = performance.now();
  const result = await fn();
  const duration = performance.now() - start;
  
  console.log(`${name}: ${duration.toFixed(2)}ms`);
  
  return { result, duration };
}

/**
 * Measure synchronous function execution time
 */
export function measureSync<T>(
  name: string,
  fn: () => T
): { result: T; duration: number } {
  const start = performance.now();
  const result = fn();
  const duration = performance.now() - start;
  
  console.log(`${name}: ${duration.toFixed(2)}ms`);
  
  return { result, duration };
}