/**
 * System Health Alerts Component
 * Displays active system health alerts with dismiss/resolve actions
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import {
  AlertTriangle,
  Info,
  AlertCircle,
  XCircle,
  X,
  CheckCircle,
  Clock,
  Settings
} from 'lucide-react';
import { systemHealthAlertingService, Alert } from '@/lib/services/systemHealthAlerting';
import { cn } from '@/lib/utils';

interface SystemHealthAlertsProps {
  className?: string;
  showResolved?: boolean;
  maxHeight?: string;
}

export function SystemHealthAlerts({ 
  className, 
  showResolved = false, 
  maxHeight = "400px" 
}: SystemHealthAlertsProps) {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Subscribe to alerts
    const unsubscribe = systemHealthAlertingService.subscribeToAlerts((newAlerts) => {
      setAlerts(showResolved ? systemHealthAlertingService.getAllAlerts() : newAlerts);
      setLoading(false);
    });

    return unsubscribe;
  }, [showResolved]);

  const getAlertIcon = (type: Alert['type']) => {
    switch (type) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getAlertBadgeVariant = (type: Alert['type']) => {
    switch (type) {
      case 'critical':
      case 'error':
        return 'destructive';
      case 'warning':
        return 'secondary';
      case 'info':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const handleDismissAlert = (alertId: string) => {
    systemHealthAlertingService.dismissAlert(alertId);
  };

  const handleResolveAlert = (alertId: string) => {
    systemHealthAlertingService.resolveAlert(alertId);
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Settings className="h-6 w-6 mx-auto mb-2 animate-spin" />
            <p>Loading alerts...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (alerts.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            System Health Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <p className="font-medium text-green-600">All systems operational</p>
            <p className="text-sm">No active alerts</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            System Health Alerts
            {alerts.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {alerts.length}
              </Badge>
            )}
          </CardTitle>
          
          {showResolved && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => systemHealthAlertingService.clearResolvedAlerts()}
            >
              Clear Resolved
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <ScrollArea style={{ height: maxHeight }}>
          <div className="space-y-3">
            {alerts.map((alert) => (
              <div
                key={alert.id}
                className={cn(
                  "p-3 rounded-lg border",
                  alert.resolved && "opacity-60",
                  alert.dismissed && "opacity-40"
                )}
              >
                <div className="flex items-start justify-between gap-3">
                  <div className="flex items-start gap-3 flex-1">
                    {getAlertIcon(alert.type)}
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-sm font-medium truncate">
                          {alert.title}
                        </h4>
                        <Badge variant={getAlertBadgeVariant(alert.type)} className="text-xs">
                          {alert.type}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {alert.service}
                        </Badge>
                      </div>
                      
                      <p className="text-xs text-muted-foreground mb-2">
                        {alert.message}
                      </p>
                      
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{formatTimestamp(alert.timestamp)}</span>
                        
                        {alert.resolved && (
                          <Badge variant="outline" className="text-xs">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Resolved
                          </Badge>
                        )}
                        
                        {alert.dismissed && (
                          <Badge variant="outline" className="text-xs">
                            <X className="h-3 w-3 mr-1" />
                            Dismissed
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {!alert.resolved && !alert.dismissed && (
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleResolveAlert(alert.id)}
                        className="h-6 w-6 p-0"
                        title="Mark as resolved"
                      >
                        <CheckCircle className="h-3 w-3" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDismissAlert(alert.id)}
                        className="h-6 w-6 p-0"
                        title="Dismiss alert"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
                
                {/* Show metadata if available */}
                {alert.metadata && Object.keys(alert.metadata).length > 0 && (
                  <div className="mt-2 pt-2 border-t">
                    <details className="text-xs">
                      <summary className="text-muted-foreground cursor-pointer hover:text-foreground">
                        Details
                      </summary>
                      <div className="mt-1 p-2 bg-muted/50 rounded text-xs font-mono">
                        <pre>{JSON.stringify(alert.metadata, null, 2)}</pre>
                      </div>
                    </details>
                  </div>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}