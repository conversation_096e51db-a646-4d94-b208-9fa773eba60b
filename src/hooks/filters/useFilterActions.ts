import { useState, useCallback, useRef, useEffect } from 'react';
import { GameFilters } from '@/types/filters';

interface UseFilterActionsOptions {
  enableDebounce?: boolean;
  debounceMs?: number;
  onFilterChange?: (filters: GameFilters) => void;
}

export function useFilterActions(
  filters: GameFilters,
  setFilters: (filters: GameFilters) => void,
  setSearchQuery: (query: string) => void,
  options: UseFilterActionsOptions = {}
) {
  const {
    enableDebounce = true,
    debounceMs = 300,
    onFilterChange
  } = options;

  const [isFiltering, setIsFiltering] = useState(false);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Notify parent of filter changes
  useEffect(() => {
    onFilterChange?.(filters);
  }, [filters, onFilterChange]);

  // Debounced search function
  const performSearch = useCallback(async (
    query: string, 
    additionalFilters: Partial<GameFilters> = {}
  ) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    setIsFiltering(true);
    
    try {
      // Clear previous debounce timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      const executeSearch = () => {
        setSearchQuery(query);
        
        // Apply any additional filters
        if (Object.keys(additionalFilters).length > 0) {
          setFilters({
            ...filters,
            ...additionalFilters
          });
        }
        
        setIsFiltering(false);
      };

      if (enableDebounce && debounceMs > 0) {
        debounceTimerRef.current = setTimeout(executeSearch, debounceMs);
      } else {
        executeSearch();
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Search error:', error);
        setIsFiltering(false);
      }
    }
  }, [enableDebounce, debounceMs, setSearchQuery, setFilters, filters]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    isFiltering,
    performSearch,
    abortController: abortControllerRef.current
  };
}