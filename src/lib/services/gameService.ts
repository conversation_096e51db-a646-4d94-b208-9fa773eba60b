import { igdbAPI, theGamesDBAPI } from '../api/games';
import { userGamesAPI } from '../api/collections';
import { Game, Platform, IGDBGame, TheGamesDBGame } from '../../types';
import { getCoverImageUrl, getScreenshotUrl } from '../utils/igdbImageUtils';

/**
 * Game Service - Business logic for game operations
 * Combines multiple API calls and implements complex game-related workflows
 */
export class GameService {
  /**
   * Search games across multiple providers with intelligent fallback
   */
  async searchGames(query: string, options: {
    platforms?: Platform[];
    limit?: number;
    includeUserContext?: boolean;
    userId?: string;
  } = {}): Promise<Game[]> {
    const { platforms = [], limit = 20, includeUserContext = false, userId } = options;

    try {
      // Primary search using IGDB
      let results = await igdbAPI.search(query, {
        platforms: platforms as string[],
        limit: Math.ceil(limit * 1.5), // Get more results for filtering
        sortBy: 'relevance'
      });

      // If IGDB results are insufficient, supplement with TheGamesDB
      if (results.length < limit / 2) {
        try {
          const tgdbResults = await theGamesDBAPI.searchByName(query, {
            platforms: platforms.map(p => p.toString())
          });
          
          // Convert and merge results
          const convertedTgdbResults = tgdbResults.map(game => 
            theGamesDBAPI.convertToGame(game)
          );
          
          // Merge and deduplicate
          const existingTitles = new Set(results.map(g => g.name.toLowerCase()));
          const newResults = convertedTgdbResults.filter(g => 
            !existingTitles.has(g.title.toLowerCase())
          );
          
          results = [...results, ...newResults.map(g => ({
            ...g,
            name: g.title,
            platforms: g.platforms?.map(p => ({ name: p })) || []
          } as IGDBGame))];
        } catch (error) {
          console.warn('TheGamesDB search failed:', error);
        }
      }

      // Convert to Game objects
      const games = results.slice(0, limit).map(result => this.convertIGDBToGame(result));

      // Add user context if requested
      if (includeUserContext && userId) {
        return await this.enrichWithUserContext(games, userId);
      }

      return games;
    } catch (error) {
      console.error('Error in searchGames:', error);
      throw new Error('Failed to search games');
    }
  }

  /**
   * Get comprehensive game details from multiple sources
   */
  async getGameDetails(gameId: string, userId?: string): Promise<Game | null> {
    try {
      // Try IGDB first
      let gameDetails: IGDBGame | TheGamesDBGame | null = null;
      
      if (gameId.startsWith('igdb_')) {
        const igdbId = parseInt(gameId.replace('igdb_', ''));
        gameDetails = await igdbAPI.getGameDetails(igdbId);
      } else if (gameId.startsWith('tgdb_')) {
        const tgdbId = parseInt(gameId.replace('tgdb_', ''));
        gameDetails = await theGamesDBAPI.getGameDetails(tgdbId);
      }

      if (!gameDetails) {
        return null;
      }

      // Convert to Game object
      const game = this.convertToGame(gameDetails);

      // Add user context if provided
      if (userId) {
        const enrichedGames = await this.enrichWithUserContext([game], userId);
        return enrichedGames[0] || null;
      }

      return game;
    } catch (error) {
      console.error('Error getting game details:', error);
      return null;
    }
  }

  /**
   * Get popular games with user context
   */
  async getPopularGames(options: {
    platform?: Platform;
    genre?: string;
    limit?: number;
    userId?: string;
  } = {}): Promise<Game[]> {
    const { platform, genre, limit = 20, userId } = options;

    try {
      // Get popular games from IGDB
      const results = await igdbAPI.search('', {
        platforms: platform ? [platform as string] : [],
        genres: genre ? [genre] : [],
        sortBy: 'popularity',
        limit
      });

      const games = results.map(result => this.convertIGDBToGame(result));

      // Add user context if provided
      if (userId) {
        return await this.enrichWithUserContext(games, userId);
      }

      return games;
    } catch (error) {
      console.error('Error getting popular games:', error);
      return [];
    }
  }

  /**
   * Get new releases with user context
   */
  async getNewReleases(options: {
    platform?: Platform;
    limit?: number;
    userId?: string;
  } = {}): Promise<Game[]> {
    const { platform, limit = 20, userId } = options;

    try {
      const results = await igdbAPI.search('', {
        platforms: platform ? [platform as string] : [],
        sortBy: 'release_date',
        sortDirection: 'desc',
        limit
      });

      const games = results.map(result => this.convertIGDBToGame(result));

      if (userId) {
        return await this.enrichWithUserContext(games, userId);
      }

      return games;
    } catch (error) {
      console.error('Error getting new releases:', error);
      return [];
    }
  }

  /**
   * Get similar games based on a reference game
   */
  async getSimilarGames(referenceGameId: string, options: {
    limit?: number;
    userId?: string;
  } = {}): Promise<Game[]> {
    const { limit = 10, userId } = options;

    try {
      // Get reference game details
      const referenceGame = await this.getGameDetails(referenceGameId);
      if (!referenceGame) {
        return [];
      }

      // Search for similar games based on genres and themes
      const searchTerms = [
        ...referenceGame.genres || [],
        referenceGame.developer,
        referenceGame.publisher
      ].filter(Boolean);

      const similarGames: Game[] = [];
      
      // Search by each term and collect results
      for (const term of searchTerms.slice(0, 3)) {
        try {
          const results = await this.searchGames(term!, {
            platforms: referenceGame.platforms as Platform[],
            limit: Math.ceil(limit / 2)
          });
          
          // Filter out the reference game itself
          const filtered = results.filter(g => g.id !== referenceGameId);
          similarGames.push(...filtered);
        } catch (error) {
          console.warn(`Error searching for similar games with term "${term}":`, error);
        }
      }

      // Remove duplicates and limit results
      const uniqueGames = similarGames.filter((game, index, self) => 
        index === self.findIndex(g => g.id === game.id)
      ).slice(0, limit);

      if (userId) {
        return await this.enrichWithUserContext(uniqueGames, userId);
      }

      return uniqueGames;
    } catch (error) {
      console.error('Error getting similar games:', error);
      return [];
    }
  }

  /**
   * Enrich games with user context (ownership status, ratings, etc.)
   */
  private async enrichWithUserContext(games: Game[], userId: string): Promise<Game[]> {
    try {
      const { data: userGames, error } = await userGamesAPI.getUserCollection(userId);
      if (error) {
        console.warn('Error getting user collection for context:', error);
        return games;
      }

      const userGameMap = new Map(
        (userGames || []).map(ug => [ug.game_id, ug])
      );

      return games.map(game => ({
        ...game,
        userContext: {
          owned: userGameMap.has(game.id),
          status: userGameMap.get(game.id)?.status,
          userRating: userGameMap.get(game.id)?.personal_rating,
          dateAdded: userGameMap.get(game.id)?.date_added,
          hoursPlayed: userGameMap.get(game.id)?.hours_played
        }
      }));
    } catch (error) {
      console.warn('Error enriching games with user context:', error);
      return games;
    }
  }

  /**
   * Validate game data before saving
   */
  validateGameData(game: Partial<Game>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!game.title || game.title.trim().length === 0) {
      errors.push('Game title is required');
    }

    if (!game.platforms || game.platforms.length === 0) {
      errors.push('At least one platform is required');
    }

    if (game.release_date && isNaN(Date.parse(game.release_date))) {
      errors.push('Invalid release date format');
    }

    if (game.metacritic_score && (game.metacritic_score < 0 || game.metacritic_score > 100)) {
      errors.push('Metacritic score must be between 0 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Convert IGDB game to Game interface
   */
  private convertIGDBToGame(igdbGame: IGDBGame): Game {
    return {
      id: `igdb_${igdbGame.id}`,
      title: igdbGame.name || 'Unknown Game',
      platforms: igdbGame.platforms?.map(p => p.name) || ['PC'],
      genres: igdbGame.genres?.map(g => g.name) || [],
      developer: igdbGame.involved_companies?.find(c => c.developer)?.company.name,
      publisher: igdbGame.involved_companies?.find(c => c.publisher)?.company.name,
      release_date: igdbGame.first_release_date ? new Date(igdbGame.first_release_date * 1000).toISOString().split('T')[0] : undefined,
      description: igdbGame.summary,
      cover_image: getCoverImageUrl(igdbGame.cover?.url, 'card'),
      screenshots: igdbGame.screenshots?.map(s => getScreenshotUrl(s.url, 'medium')) || [],
      youtube_links: igdbGame.videos?.map(v => `https://www.youtube.com/watch?v=${v.video_id}`) || [],
      metacritic_score: igdbGame.aggregated_rating ? Math.round(igdbGame.aggregated_rating) : undefined,
      igdb_id: igdbGame.id.toString()
    };
  }

  /**
   * Convert TheGamesDB game to Game interface
   */
  private convertTheGamesDBToGame(tgdbGame: TheGamesDBGame): Game {
    return {
      id: `tgdb_${tgdbGame.id}`,
      title: tgdbGame.game_title || 'Unknown Game',
      platforms: ['PC'], // Simplified for now
      genres: [],
      developer: undefined,
      publisher: undefined,
      release_date: tgdbGame.release_date,
      description: tgdbGame.overview,
      cover_image: undefined,
      screenshots: [],
      youtube_links: tgdbGame.youtube ? [`https://www.youtube.com/watch?v=${tgdbGame.youtube}`] : [],
      metacritic_score: undefined,
      igdb_id: undefined
    };
  }

  /**
   * Convert any game data to Game interface
   */
  private convertToGame(gameData: IGDBGame | TheGamesDBGame): Game {
    if ('name' in gameData) {
      return this.convertIGDBToGame(gameData as IGDBGame);
    } else {
      return this.convertTheGamesDBToGame(gameData as TheGamesDBGame);
    }
  }

  /**
   * Normalize game data from different sources
   */
  normalizeGameData(game: Partial<Game>): Game {
    return {
      id: game.id || '',
      title: game.title?.trim() || 'Unknown Game',
      platforms: game.platforms || ['PC'],
      genres: game.genres?.filter(Boolean) || [],
      developer: game.developer?.trim() || undefined,
      publisher: game.publisher?.trim() || undefined,
      release_date: game.release_date || undefined,
      description: game.description?.trim() || undefined,
      cover_image: game.cover_image || undefined,
      screenshots: game.screenshots?.filter(Boolean) || [],
      youtube_links: game.youtube_links?.filter(Boolean) || [],
      metacritic_score: game.metacritic_score || undefined,
      igdb_id: game.igdb_id || undefined
    };
  }
}

export const gameService = new GameService();