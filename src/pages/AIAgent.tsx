import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle, Button, Badge, <PERSON>bs, <PERSON>bs<PERSON>ontent, <PERSON>bs<PERSON>ist, <PERSON><PERSON><PERSON><PERSON>ger, Separator } from '@/components/ui/base';
import { 
  Bo<PERSON>, 
  MessageCircle, 
  History, 
  Settings, 
  Sparkles,
  Plus,
  Trash2,
  Gamepad2,
  RefreshCw,
  Zap,
  Brain
} from 'lucide-react';
import { ChatWindow } from '@/components/ui/chat/ChatWindow';
import { useAuth } from '@/contexts/AuthContext';
import { 
  enhancedAIService, 
  Conversation, 
  AIProvider 
} from '@/lib/enhancedAIService';
import { toast } from 'react-hot-toast';

const AIAgent: React.FC = () => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isChatMinimized, setIsChatMinimized] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [currentProvider, setCurrentProvider] = useState<{ provider: AIProvider; model: string } | null>(null);

  const loadConversations = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      const userConversations = await enhancedAIService.getUserConversations(user.id);
      setConversations(userConversations);
    } catch (error) {
      console.error('Error loading conversations:', error);
      toast.error('Failed to load conversations');
    }
  }, [user?.id]);

  useEffect(() => {
    if (user?.id) {
      loadConversations();
      setCurrentProvider(enhancedAIService.getCurrentProvider());
    }
  }, [user?.id, loadConversations]);

  const createNewConversation = async (title?: string) => {
    if (!user?.id) return;

    try {
      const conversationId = await enhancedAIService.createConversation(user.id, title);
      const newConversation = await enhancedAIService.getConversation(conversationId);
      
      setConversations(prev => [newConversation, ...prev]);
      setCurrentConversation(newConversation);
      setShowChat(true);
      
      toast.success('New conversation started');
    } catch (error) {
      console.error('Error creating conversation:', error);
      toast.error('Failed to create conversation');
    }
  };

  const selectConversation = async (conversationId: string) => {
    try {
      const conversation = await enhancedAIService.getConversation(conversationId);
      setCurrentConversation(conversation);
      setShowChat(true);
    } catch (error) {
      console.error('Error loading conversation:', error);
      toast.error('Failed to load conversation');
    }
  };

  const deleteConversation = async (conversationId: string) => {
    try {
      await enhancedAIService.deleteConversation(conversationId);
      setConversations(prev => prev.filter(conv => conv.id !== conversationId));
      
      if (currentConversation?.id === conversationId) {
        setCurrentConversation(null);
        setShowChat(false);
      }
      
      toast.success('Conversation deleted');
    } catch (error) {
      console.error('Error deleting conversation:', error);
      toast.error('Failed to delete conversation');
    }
  };

  const sendMessage = async (message: string) => {
    if (!currentConversation || !user?.id) return;

    setIsLoading(true);
    try {
      await enhancedAIService.sendChatMessage(
        currentConversation.id,
        message
      );

      // Reload the conversation to get updated messages
      const updatedConversation = await enhancedAIService.getConversation(currentConversation.id);
      setCurrentConversation(updatedConversation);
      
      // Update conversations list to reflect latest activity
      loadConversations();
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setIsLoading(false);
    }
  };

  const clearCurrentChat = async () => {
    if (!currentConversation || !user?.id) return;

    try {
      // Delete current conversation and create a new one
      await enhancedAIService.deleteConversation(currentConversation.id);
      await createNewConversation('New Chat');
    } catch (error) {
      console.error('Error clearing chat:', error);
      toast.error('Failed to clear chat');
    }
  };

  const handleProviderChange = (provider: AIProvider) => {
    try {
      enhancedAIService.setProvider(provider);
      setCurrentProvider(enhancedAIService.getCurrentProvider());
      toast.success(`Switched to ${provider.toUpperCase()}`);
    } catch (error) {
      console.error('Error switching provider:', error);
      toast.error('Failed to switch AI provider');
    }
  };

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (days === 1) {
      return 'Yesterday';
    } else if (days < 7) {
      return `${days} days ago`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  const QuickActions = () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card className="cursor-pointer hover:shadow-lg transition-shadow" 
            onClick={() => createNewConversation('Game Recommendations')}>
        <CardContent className="p-6 text-center">
          <Sparkles className="h-12 w-12 mx-auto mb-4 text-yellow-500" />
          <h3 className="font-semibold mb-2">Get Recommendations</h3>
          <p className="text-sm text-muted-foreground">
            Discover new games based on your preferences and gaming history
          </p>
        </CardContent>
      </Card>


      <Card className="cursor-pointer hover:shadow-lg transition-shadow" 
            onClick={() => createNewConversation('Game Discussion')}>
        <CardContent className="p-6 text-center">
          <Gamepad2 className="h-12 w-12 mx-auto mb-4 text-green-500" />
          <h3 className="font-semibold mb-2">Discuss Games</h3>
          <p className="text-sm text-muted-foreground">
            Chat about your favorite games, get tips, and explore gaming topics
          </p>
        </CardContent>
      </Card>

      <Card className="cursor-pointer hover:shadow-lg transition-shadow" 
            onClick={() => createNewConversation('Collection Analysis')}>
        <CardContent className="p-6 text-center">
          <Brain className="h-12 w-12 mx-auto mb-4 text-purple-500" />
          <h3 className="font-semibold mb-2">Analyze Collection</h3>
          <p className="text-sm text-muted-foreground">
            Get insights about your game library and discover patterns in your gaming preferences
          </p>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Bot className="h-8 w-8 text-blue-500" />
              AI Gaming Assistant
              <Sparkles className="h-6 w-6 text-yellow-500" />
            </h1>
            <p className="text-muted-foreground mt-2">
              Your intelligent companion for personalized game recommendations and discussions
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="flex items-center gap-2">
              <Brain className="h-3 w-3" />
              {currentProvider?.provider?.toUpperCase() || 'AI'} • {currentProvider?.model}
            </Badge>
            <Button onClick={() => createNewConversation()} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              New Chat
            </Button>
          </div>
        </div>
      </div>

      <Tabs defaultValue="quick-actions" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="quick-actions" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Quick Actions
          </TabsTrigger>
          <TabsTrigger value="conversations" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            Chat History
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="quick-actions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                Start a Conversation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <QuickActions />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>AI Capabilities</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <Sparkles className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
                  <h4 className="font-semibold text-sm">Smart Recommendations</h4>
                  <p className="text-xs text-muted-foreground mt-1">
                    Personalized game suggestions
                  </p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <MessageCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                  <h4 className="font-semibold text-sm">Natural Conversation</h4>
                  <p className="text-xs text-muted-foreground mt-1">
                    Discuss any gaming topic
                  </p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Brain className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                  <h4 className="font-semibold text-sm">Collection Insights</h4>
                  <p className="text-xs text-muted-foreground mt-1">
                    Analyze your gaming library
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="conversations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  Your Conversations
                </span>
                <Button variant="outline" onClick={loadConversations}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {conversations.length === 0 ? (
                <div className="text-center py-8">
                  <MessageCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="font-medium mb-2">No conversations yet</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Start your first conversation with the AI assistant
                  </p>
                  <Button onClick={() => createNewConversation()}>
                    <Plus className="h-4 w-4 mr-2" />
                    Start Chatting
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {conversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div 
                        className="flex-1 cursor-pointer"
                        onClick={() => selectConversation(conversation.id)}
                      >
                        <div className="flex items-center gap-3">
                          <MessageCircle className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <h4 className="font-medium">{conversation.title}</h4>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>{conversation.messages.length} messages</span>
                              <span>•</span>
                              <span>{formatTimestamp(conversation.updatedAt)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteConversation(conversation.id)}
                        className="text-red-500 hover:text-red-600"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                AI Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-medium mb-2">Current Provider</h4>
                <div className="flex items-center gap-4">
                  <Badge variant="outline" className="flex items-center gap-2">
                    <Brain className="h-3 w-3" />
                    {currentProvider?.provider?.toUpperCase() || 'Unknown'}
                  </Badge>
                  <Badge variant="secondary">
                    {currentProvider?.model || 'Unknown Model'}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  The AI provider is automatically selected based on available API keys.
                </p>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-2">Supported Providers</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h5 className="font-medium">OpenAI</h5>
                    <p className="text-sm text-muted-foreground">GPT-4, ChatGPT</p>
                    <Badge variant={currentProvider?.provider === 'openai' ? 'default' : 'outline'} className="mt-2">
                      {currentProvider?.provider === 'openai' ? 'Active' : 'Available'}
                    </Badge>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h5 className="font-medium">DeepSeek</h5>
                    <p className="text-sm text-muted-foreground">DeepSeek Chat</p>
                    <Badge variant={currentProvider?.provider === 'deepseek' ? 'default' : 'outline'} className="mt-2">
                      {currentProvider?.provider === 'deepseek' ? 'Active' : 'Available'}
                    </Badge>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h5 className="font-medium">Google Gemini</h5>
                    <p className="text-sm text-muted-foreground">Gemini 1.5 Flash</p>
                    <Badge variant={currentProvider?.provider === 'gemini' ? 'default' : 'outline'} className="mt-2">
                      {currentProvider?.provider === 'gemini' ? 'Active' : 'Available'}
                    </Badge>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-2">Features</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Game Recommendations</span>
                    <Badge variant="outline">Enabled</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Conversation History</span>
                    <Badge variant="outline">Enabled</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Context-Aware Responses</span>
                    <Badge variant="outline">Enabled</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Chat Window */}
      {showChat && currentConversation && (
        <ChatWindow
          messages={currentConversation.messages}
          isLoading={isLoading}
          isMinimized={isChatMinimized}
          onSendMessage={sendMessage}
          onMinimize={() => setIsChatMinimized(true)}
          onMaximize={() => setIsChatMinimized(false)}
          onClose={() => setShowChat(false)}
          onClearChat={clearCurrentChat}
          currentProvider={currentProvider}
          onProviderChange={handleProviderChange}
        />
      )}
    </div>
  );
};

export default AIAgent;