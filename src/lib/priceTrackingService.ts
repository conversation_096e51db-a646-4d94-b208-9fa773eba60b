import { supabase } from './supabase';

export interface PriceData {
  gameId: string;
  shopName: string;
  shopUrl: string;
  price: number;
  currency: string;
  originalPrice?: number;
  discountPercent?: number;
  isOnSale?: boolean;
}

export interface PriceAlert {
  id?: string;
  userId: string;
  gameId: string;
  targetPrice: number;
  currency: string;
  isActive: boolean;
  notificationMethods: ('email' | 'push' | 'toast')[];
  createdAt?: string;
}

export interface PriceHistory {
  date: string;
  price: number;
  shopName: string;
  currency: string;
}

export interface DealInfo {
  gameId: string;
  gameName: string;
  currentPrice: number;
  originalPrice: number;
  discountPercent: number;
  shopName: string;
  shopUrl: string;
  currency: string;
  expiresAt?: string;
}

class PriceTrackingService {
  private readonly STEAM_STORE_API = 'https://store.steampowered.com/api/appdetails';
  private readonly EPIC_STORE_API = 'https://store-site-backend-static.ak.epicgames.com/freeGamesPromotions';
  private readonly ISTHEREANYDEAL_API = 'https://api.isthereanydeal.com/v01';

  // Store mappings for different platforms
  private readonly STORE_MAPPINGS = {
    steam: {
      name: 'Steam',
      baseUrl: 'https://store.steampowered.com/app/',
      priceSelector: '.game_purchase_price'
    },
    epic: {
      name: 'Epic Games Store',
      baseUrl: 'https://store.epicgames.com/p/',
      priceSelector: '.price'
    },
    gog: {
      name: 'GOG',
      baseUrl: 'https://www.gog.com/game/',
      priceSelector: '.product-actions-price'
    },
    playstation: {
      name: 'PlayStation Store',
      baseUrl: 'https://store.playstation.com/product/',
      priceSelector: '.price'
    },
    xbox: {
      name: 'Xbox Store',
      baseUrl: 'https://www.xbox.com/games/store/',
      priceSelector: '.price'
    }
  };

  /**
   * Get current prices for a game from multiple platforms
   */
  async getCurrentPrices(gameId: string, gameName: string): Promise<PriceData[]> {
    const prices: PriceData[] = [];
    
    try {
      // Try to get prices from different platforms
      const steamPrice = await this.getSteamPrice(gameId, gameName);
      if (steamPrice) prices.push(steamPrice);

      const epicPrice = await this.getEpicPrice(gameId, gameName);
      if (epicPrice) prices.push(epicPrice);

      const gogPrice = await this.getGOGPrice(gameId, gameName);
      if (gogPrice) prices.push(gogPrice);

      // Use IsThereAnyDeal API for comprehensive price data
      const itadPrices = await this.getIsThereAnyDealPrices(gameName);
      prices.push(...itadPrices);

      return prices;
    } catch (error) {
      console.error('Error fetching current prices:', error);
      return [];
    }
  }

  /**
   * Get Steam price for a game
   */
  private async getSteamPrice(gameId: string, gameName: string): Promise<PriceData | null> {
    try {
      // First try to find Steam app ID from game name
      const steamAppId = await this.findSteamAppId(gameName);
      if (!steamAppId) return null;

      const response = await fetch(`${this.STEAM_STORE_API}?appids=${steamAppId}&cc=us&l=en`);
      const data = await response.json();
      
      if (data[steamAppId]?.success && data[steamAppId]?.data?.price_overview) {
        const priceData = data[steamAppId].data.price_overview;
        return {
          gameId,
          shopName: 'Steam',
          shopUrl: `https://store.steampowered.com/app/${steamAppId}`,
          price: priceData.final / 100, // Steam prices are in cents
          currency: priceData.currency,
          originalPrice: priceData.initial / 100,
          discountPercent: priceData.discount_percent,
          isOnSale: priceData.discount_percent > 0
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching Steam price:', error);
      return null;
    }
  }

  /**
   * Get Epic Games Store price
   */
  private async getEpicPrice(gameId: string, gameName: string): Promise<PriceData | null> {
    try {
      // Epic Games Store API is limited, so we'll use a placeholder approach
      // In a real implementation, you'd need to use their GraphQL API or scraping
      
      // For now, return mock data for demonstration
      if (Math.random() > 0.7) { // 30% chance of finding a price
        return {
          gameId,
          shopName: 'Epic Games Store',
          shopUrl: `https://store.epicgames.com/search?q=${encodeURIComponent(gameName)}`,
          price: Math.floor(Math.random() * 60) + 10,
          currency: 'USD',
          isOnSale: Math.random() > 0.5
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching Epic price:', error);
      return null;
    }
  }

  /**
   * Get GOG price
   */
  private async getGOGPrice(gameId: string, gameName: string): Promise<PriceData | null> {
    try {
      // GOG doesn't have a public API, so this would require scraping
      // For demonstration, we'll return mock data
      
      if (Math.random() > 0.6) { // 40% chance of finding a price
        return {
          gameId,
          shopName: 'GOG',
          shopUrl: `https://www.gog.com/search?query=${encodeURIComponent(gameName)}`,
          price: Math.floor(Math.random() * 50) + 5,
          currency: 'USD',
          isOnSale: Math.random() > 0.6
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching GOG price:', error);
      return null;
    }
  }

  /**
   * Get prices from IsThereAnyDeal API
   */
  private async getIsThereAnyDealPrices(gameName: string): Promise<PriceData[]> {
    try {
      // IsThereAnyDeal API requires authentication
      // For demonstration, we'll return mock data
      
      const mockStores = ['Steam', 'Epic Games Store', 'GOG', 'Humble Store', 'Fanatical'];
      const prices: PriceData[] = [];
      
      for (const store of mockStores) {
        if (Math.random() > 0.5) { // 50% chance for each store
          prices.push({
            gameId: 'mock',
            shopName: store,
            shopUrl: `https://isthereanydeal.com/search/?q=${encodeURIComponent(gameName)}`,
            price: Math.floor(Math.random() * 60) + 5,
            currency: 'USD',
            isOnSale: Math.random() > 0.6
          });
        }
      }
      
      return prices;
    } catch (error) {
      console.error('Error fetching IsThereAnyDeal prices:', error);
      return [];
    }
  }

  /**
   * Find Steam App ID for a game name
   */
  private async findSteamAppId(gameName: string): Promise<string | null> {
    try {
      // Steam doesn't have a direct search API, so we'll use a fallback approach
      // In a real implementation, you'd maintain a database of game name -> Steam ID mappings
      
      // For demonstration, return a few known Steam IDs
      const knownGames: { [key: string]: string } = {
        'cyberpunk 2077': '1091500',
        'the witcher 3': '292030',
        'grand theft auto v': '271590',
        'minecraft': '1085660',
        'among us': '945360'
      };
      
      const normalizedName = gameName.toLowerCase();
      for (const [name, appId] of Object.entries(knownGames)) {
        if (normalizedName.includes(name) || name.includes(normalizedName)) {
          return appId;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error finding Steam App ID:', error);
      return null;
    }
  }

  /**
   * Update price data for a game
   */
  async updateGamePrices(gameId: string, gameName: string): Promise<void> {
    try {
      const currentPrices = await this.getCurrentPrices(gameId, gameName);
      
      for (const priceData of currentPrices) {
        await supabase.from('price_tracking').insert({
          game_id: gameId,
          shop_name: priceData.shopName,
          shop_url: priceData.shopUrl,
          price: priceData.price,
          currency: priceData.currency,
          last_updated: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Error updating game prices:', error);
      throw error;
    }
  }

  /**
   * Get price history for a game
   */
  async getPriceHistory(gameId: string, days: number = 30): Promise<PriceHistory[]> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      
      const { data, error } = await supabase
        .from('price_tracking')
        .select('*')
        .eq('game_id', gameId)
        .gte('last_updated', cutoffDate.toISOString())
        .order('last_updated', { ascending: true });
      
      if (error) throw error;
      
      return data.map(item => ({
        date: item.last_updated,
        price: item.price,
        shopName: item.shop_name,
        currency: item.currency
      }));
    } catch (error) {
      console.error('Error fetching price history:', error);
      return [];
    }
  }

  /**
   * Create a price alert for a user
   */
  async createPriceAlert(alertData: PriceAlert): Promise<PriceAlert | null> {
    try {
      const { data, error } = await supabase
        .from('price_alerts')
        .insert({
          user_id: alertData.userId,
          game_id: alertData.gameId,
          target_price: alertData.targetPrice,
          currency: alertData.currency,
          is_active: alertData.isActive,
          notification_methods: alertData.notificationMethods,
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) throw error;
      
      return {
        id: data.id,
        userId: data.user_id,
        gameId: data.game_id,
        targetPrice: data.target_price,
        currency: data.currency,
        isActive: data.is_active,
        notificationMethods: data.notification_methods,
        createdAt: data.created_at
      };
    } catch (error) {
      console.error('Error creating price alert:', error);
      return null;
    }
  }

  /**
   * Check for price alerts and trigger notifications
   */
  async checkPriceAlerts(gameId: string): Promise<void> {
    try {
      // Get all active alerts for this game
      const { data: alerts, error } = await supabase
        .from('price_alerts')
        .select('*')
        .eq('game_id', gameId)
        .eq('is_active', true);
      
      if (error) throw error;
      if (!alerts || alerts.length === 0) return;
      
      // Get current prices
      const { data: currentPrices, error: priceError } = await supabase
        .from('price_tracking')
        .select('*')
        .eq('game_id', gameId)
        .order('last_updated', { ascending: false })
        .limit(10);
      
      if (priceError) throw priceError;
      if (!currentPrices || currentPrices.length === 0) return;
      
      // Check each alert against current prices
      for (const alert of alerts) {
        const triggeredPrices = currentPrices.filter(
          price => price.price <= alert.target_price && price.currency === alert.currency
        );
        
        if (triggeredPrices.length > 0) {
          await this.triggerPriceAlert(alert, triggeredPrices[0]);
        }
      }
    } catch (error) {
      console.error('Error checking price alerts:', error);
    }
  }

  /**
   * Trigger a price alert notification
   */
  private async triggerPriceAlert(
    alert: { 
      id: string; 
      game_id: string; 
      user_id: string; 
      target_price: number; 
      currency: string; 
      notification_methods: string[]; 
      trigger_count?: number;
    }, 
    priceData: { 
      price: number; 
      currency: string; 
      shop_name: string;
    }
  ): Promise<void> {
    try {
      // Get game information
      const { data: game, error: gameError } = await supabase
        .from('games')
        .select('*')
        .eq('id', alert.game_id)
        .single();
      
      if (gameError) throw gameError;
      
      const notification = {
        title: `Price Alert: ${game.name}`,
        message: `${game.name} is now ${priceData.currency} ${priceData.price} on ${priceData.shop_name}!`,
        gameId: alert.game_id,
        userId: alert.user_id,
        priceData: priceData
      };
      
      // Send notifications based on user preferences
      if (alert.notification_methods.includes('email')) {
        await this.sendEmailNotification(notification);
      }
      
      if (alert.notification_methods.includes('push')) {
        await this.sendPushNotification(notification);
      }
      
      if (alert.notification_methods.includes('toast')) {
        await this.sendToastNotification(notification);
      }
      
      // Mark alert as triggered (optional: deactivate or keep active)
      await supabase
        .from('price_alerts')
        .update({ 
          last_triggered: new Date().toISOString(),
          trigger_count: (alert.trigger_count || 0) + 1
        })
        .eq('id', alert.id);
        
    } catch (error) {
      console.error('Error triggering price alert:', error);
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(notification: { 
    title: string; 
    message: string; 
    gameId: string; 
    userId: string; 
    priceData: object;
  }): Promise<void> {
    try {
      // In a real implementation, you'd use Supabase Edge Functions
      // or a service like SendGrid, Mailgun, etc.
      console.log('Email notification:', notification);
      
      // For now, we'll use Supabase's built-in email functionality
      // This would require setting up email templates in Supabase
      
    } catch (error) {
      console.error('Error sending email notification:', error);
    }
  }

  /**
   * Send push notification
   */
  private async sendPushNotification(notification: { 
    title: string; 
    message: string; 
    gameId: string; 
    userId: string; 
    priceData: object;
  }): Promise<void> {
    try {
      // Push notifications would require service worker setup
      // and user permission handling
      console.log('Push notification:', notification);
      
      // For demonstration, we'll log the notification
      // In a real implementation, you'd use the Web Push API
      
    } catch (error) {
      console.error('Error sending push notification:', error);
    }
  }

  /**
   * Send toast notification
   */
  private async sendToastNotification(notification: { 
    title: string; 
    message: string; 
    gameId: string; 
    userId: string; 
    priceData: object;
  }): Promise<void> {
    try {
      // Toast notifications are handled by the UI components
      // This would be called from the frontend when checking alerts
      console.log('Toast notification:', notification);
      
      // The actual toast would be shown in the UI using react-hot-toast
      
    } catch (error) {
      console.error('Error sending toast notification:', error);
    }
  }

  /**
   * Get best deals for user's wishlist
   */
  async getBestDeals(userId: string, limit: number = 10): Promise<DealInfo[]> {
    try {
      const { data: wishlist, error } = await supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .eq('status', 'wishlist');
      
      if (error) throw error;
      if (!wishlist || wishlist.length === 0) return [];
      
      const deals: DealInfo[] = [];
      
      for (const item of wishlist) {
        // Get current prices for this game
        const { data: prices, error: priceError } = await supabase
          .from('price_tracking')
          .select('*')
          .eq('game_id', item.game_id)
          .order('last_updated', { ascending: false })
          .limit(5);
        
        if (priceError) continue;
        if (!prices || prices.length === 0) continue;
        
        // Find deals (games with discounts or low prices)
        for (const price of prices) {
          // Mock discount calculation - in real implementation, 
          // you'd compare with historical prices
          const isOnSale = Math.random() > 0.6;
          const originalPrice = price.price * (1 + Math.random() * 0.5);
          const discountPercent = Math.floor(((originalPrice - price.price) / originalPrice) * 100);
          
          if (isOnSale && discountPercent > 10) {
            deals.push({
              gameId: item.game_id,
              gameName: item.game.name,
              currentPrice: price.price,
              originalPrice: originalPrice,
              discountPercent: discountPercent,
              shopName: price.shop_name,
              shopUrl: price.shop_url,
              currency: price.currency
            });
          }
        }
      }
      
      // Sort by discount percentage and return top deals
      return deals
        .sort((a, b) => b.discountPercent - a.discountPercent)
        .slice(0, limit);
        
    } catch (error) {
      console.error('Error getting best deals:', error);
      return [];
    }
  }

  /**
   * Get price comparison for a game
   */
  async getPriceComparison(gameId: string): Promise<PriceData[]> {
    try {
      const { data: prices, error } = await supabase
        .from('price_tracking')
        .select('*')
        .eq('game_id', gameId)
        .order('price', { ascending: true });
      
      if (error) throw error;
      
      // Group by shop and get latest price for each
      const latestPrices = new Map<string, {
        game_id: string;
        shop_name: string;
        shop_url: string;
        price: number;
        currency: string;
        last_updated: string;
      }>();
      
      for (const price of prices || []) {
        const existing = latestPrices.get(price.shop_name);
        if (!existing || new Date(price.last_updated) > new Date(existing.last_updated)) {
          latestPrices.set(price.shop_name, price);
        }
      }
      
      return Array.from(latestPrices.values()).map(price => ({
        gameId: price.game_id,
        shopName: price.shop_name,
        shopUrl: price.shop_url,
        price: price.price,
        currency: price.currency,
        isOnSale: false // Would need historical data to determine
      }));
      
    } catch (error) {
      console.error('Error getting price comparison:', error);
      return [];
    }
  }
}

export const priceTrackingService = new PriceTrackingService();