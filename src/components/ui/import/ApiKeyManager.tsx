/**
 * API Key Manager Component
 * Secure interface for managing platform API keys
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Alert, AlertDescription } from '@/components/ui/base/alert';
import { LoadingSpinner } from '@/components/ui/utils';
import { 
  Key, 
  Shield, 
  Plus, 
  Trash2, 
  TestTube, 
  Eye, 
  EyeOff, 
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';
import { useApiKeys, usePlatformInfo, usePlatformConfiguration } from '@/hooks/useApiKeys';
import { type Platform } from '@/lib/apiKeyStore';
import { PlatformKeyForm } from './PlatformKeyForm';
import { SteamIdConfiguration } from './SteamIdConfiguration';
import { cn } from '@/lib/utils';

interface ApiKeyManagerProps {
  className?: string;
}

export function ApiKeyManager({ className }: ApiKeyManagerProps) {
  const { 
    storedKeys, 
    isLoading, 
    error, 
    deletePlatformKeys, 
    testKeys, 
    isDeleting, 
    isTesting 
  } = useApiKeys();
  
  const [showAddForm, setShowAddForm] = useState(false);

  if (isLoading) {
    return (
      <Card className={cn("h-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            API Key Management
          </CardTitle>
          <CardDescription>Loading API key configuration...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          API Key Management
        </CardTitle>
        <CardDescription>
          Securely store and manage your Steam API key. Keys are encrypted and stored locally.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Security Notice */}
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            All API keys are encrypted using AES-256-GCM encryption before storage. 
            Keys are unique to your account and cannot be accessed by others.
          </AlertDescription>
        </Alert>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Actions */}
        <div className="flex gap-2">
          <Button
            onClick={() => setShowAddForm(true)}
            disabled={showAddForm}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Steam API Key
          </Button>
          
          {storedKeys.length > 0 && (
            <Button
              variant="outline"
              onClick={testKeys}
              disabled={isTesting}
              className="flex items-center gap-2"
            >
              <TestTube className="h-4 w-4" />
              {isTesting ? 'Testing...' : 'Test Steam Key'}
            </Button>
          )}
        </div>

        {/* Add Key Form */}
        {showAddForm && (
          <PlatformKeyForm
            onSuccess={() => setShowAddForm(false)}
            onCancel={() => setShowAddForm(false)}
          />
        )}

        {/* Steam ID Configuration */}
        <SteamIdConfiguration />

        {/* Steam Configuration */}
        {storedKeys.length > 0 ? (
          <PlatformKeySection 
            platform="steam"
            onDelete={() => deletePlatformKeys("steam")}
            isDeleting={isDeleting}
          />
        ) : (
          <div className="text-center py-8">
            <Key className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Steam API Key Stored</h3>
            <p className="text-muted-foreground mb-4">
              Add your Steam API key to enable library imports and enhanced features.
            </p>
            <Button onClick={() => setShowAddForm(true)}>
              Add Steam API Key
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface PlatformKeySectionProps {
  platform: Platform;
  onDelete: () => void;
  isDeleting: boolean;
}

const PlatformKeySection: React.FC<PlatformKeySectionProps> = ({ platform, onDelete, isDeleting }) => {
  const { storedKeys } = useApiKeys();
  const { getPlatformLabel, getPlatformKeys } = usePlatformInfo();
  const { isConfigured, configuredKeys, missingKeys } = usePlatformConfiguration(platform);
  const [showValues, setShowValues] = useState(false);

  const platformKeys = storedKeys.filter(key => key.platform === platform);
  const platformRequirements = getPlatformKeys(platform);

  if (platformKeys.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-4">
            <Info className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
            <p className="text-muted-foreground">No keys configured for {getPlatformLabel(platform)}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {getPlatformLabel(platform)}
              {isConfigured ? (
                <Badge variant="default" className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  Configured
                </Badge>
              ) : (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  Incomplete
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {configuredKeys.length} of {platformRequirements.length} required keys configured
            </CardDescription>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowValues(!showValues)}
              className="flex items-center gap-1"
            >
              {showValues ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showValues ? 'Hide' : 'Show'}
            </Button>
            
            <Button
              variant="destructive"
              size="sm"
              onClick={onDelete}
              disabled={isDeleting}
              className="flex items-center gap-1"
            >
              <Trash2 className="h-4 w-4" />
              Delete All
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Configured Keys */}
        <div className="space-y-2">
          <h4 className="font-medium">Configured Keys</h4>
          {configuredKeys.map((key) => (
            <div key={key.id} className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <div className="font-medium">{key.keyName}</div>
                <div className="text-sm text-muted-foreground">
                  Added {new Date(key.createdAt).toLocaleDateString()}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {showValues && (
                  <code className="text-xs bg-muted p-1 rounded">
                    ••••••••
                  </code>
                )}
                <Badge variant="outline">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Active
                </Badge>
              </div>
            </div>
          ))}
        </div>

        {/* Missing Keys */}
        {missingKeys.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-orange-600">Missing Required Keys</h4>
            {missingKeys.map((missingKey) => (
              <div key={missingKey.type} className="flex items-center justify-between p-3 border border-orange-200 rounded-lg bg-orange-50">
                <div>
                  <div className="font-medium">{missingKey.label}</div>
                  <div className="text-sm text-muted-foreground">Required for platform access</div>
                </div>
                <Badge variant="outline" className="border-orange-300 text-orange-600">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Missing
                </Badge>
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            To get API keys for {getPlatformLabel(platform)}, visit their developer portal and create an application.
            Each platform has different requirements and setup procedures.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}