/**
 * Epic Games Store Import Service
 * Handles importing user's Epic Games library
 */

import { BasePlatformService, type PlatformGameData, type AuthenticationResult, type ImportProgress } from './basePlatformService';
import { type KeyType } from '@/lib/apiKeyStore';
import { type Game } from '@/types';

interface EpicAuthResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
}

interface EpicGameData {
  catalogItemId: string;
  namespace: string;
  title: string;
  description?: string;
  longDescription?: string;
  technicalDetails?: string;
  keyImages?: Array<{
    type: string;
    url: string;
    width?: number;
    height?: number;
  }>;
  categories?: Array<{
    path: string;
  }>;
  tags?: Array<{
    id: string;
    name: string;
  }>;
  developerDisplayName?: string;
  publisherDisplayName?: string;
  releaseDate?: string;
  effectiveDate?: string;
  pcReleaseDate?: string;
  platforms?: string[];
  customAttributes?: Record<string, unknown>;
}

interface EpicLibraryResponse {
  data: Array<{
    catalogItem: EpicGameData;
    entitlements?: Array<{
      id: string;
      grantDate: string;
      lastModifiedDate: string;
    }>;
  }>;
  paging?: {
    count: number;
    total: number;
  };
}

export class EpicImportService extends BasePlatformService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiry: Date | null = null;

  constructor() {
    super('epic');
  }

  protected getRequiredKeys(): KeyType[] {
    return ['client_id', 'client_secret'];
  }

  /**
   * Authenticate with Epic Games using OAuth2 Client Credentials flow
   */
  async authenticate(): Promise<AuthenticationResult> {
    try {
      const clientId = await this.getApiKey('client_id');
      const clientSecret = await this.getApiKey('client_secret');

      if (!clientId || !clientSecret) {
        return {
          success: false,
          error: 'Epic Games API credentials not found. Please configure your Client ID and Client Secret in Settings.',
        };
      }

      // Use Epic Games OAuth2 endpoint
      const tokenUrl = 'https://api.epicgames.dev/epic/oauth/v1/token';
      
      const response = await this.makeRequest(tokenUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${btoa(`${clientId}:${clientSecret}`)}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'grant_type=client_credentials&scope=public',
      });

      const authData: EpicAuthResponse = await response.json();

      this.accessToken = authData.access_token;
      this.refreshToken = authData.refresh_token;
      this.tokenExpiry = new Date(Date.now() + authData.expires_in * 1000);

      return {
        success: true,
        accessToken: this.accessToken,
        refreshToken: this.refreshToken,
        expiresAt: this.tokenExpiry,
      };
    } catch (error) {
      console.error('Epic Games authentication failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      };
    }
  }

  /**
   * Check if access token is valid and refresh if needed
   */
  private async ensureValidToken(): Promise<void> {
    if (!this.accessToken || !this.tokenExpiry || this.tokenExpiry <= new Date()) {
      const authResult = await this.authenticate();
      if (!authResult.success) {
        throw new Error(authResult.error || 'Failed to authenticate');
      }
    }
  }

  /**
   * Fetch user's Epic Games library
   */
  async fetchUserLibrary(progressCallback?: (progress: ImportProgress) => void): Promise<PlatformGameData[]> {
    await this.ensureValidToken();

    const games: PlatformGameData[] = [];
    let hasMore = true;
    const limit = 100;

    progressCallback?.({
      phase: 'fetching',
      current: 20,
      total: 100,
      message: 'Fetching Epic Games library...',
    });

    try {
      while (hasMore) {
        // Note: Epic Games doesn't have a direct "user library" endpoint in their public API
        // This is a conceptual implementation - actual implementation would need Epic's
        // specific library or entitlements endpoints, which may require special access
        
        const catalogUrl = `https://api.epicgames.dev/epic/catalog/v1/products`;
        
        const response = await this.makeRequest(catalogUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Accept': 'application/json',
          },
        });

        const libraryData: EpicLibraryResponse = await response.json();

        if (libraryData.data && libraryData.data.length > 0) {
          for (const item of libraryData.data) {
            const platformGame = this.convertEpicGameToPlatformData(item.catalogItem);
            games.push(platformGame);
          }

          // Update progress
          progressCallback?.({
            phase: 'fetching',
            current: 20 + (games.length / (libraryData.paging?.total || games.length)) * 40,
            total: 100,
            message: `Fetched ${games.length} games from Epic Games...`,
          });

          // offset += limit; // Not used in this conceptual implementation
          hasMore = libraryData.data.length === limit;
        } else {
          hasMore = false;
        }
      }

      return games;
    } catch (error) {
      console.error('Failed to fetch Epic Games library:', error);
      throw new Error(`Failed to fetch library: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert Epic Games data to our platform format
   */
  private convertEpicGameToPlatformData(epicGame: EpicGameData): PlatformGameData {
    // Find the best image (prefer Tall or DieselStoreFront images)
    const imageTypes = ['Tall', 'DieselStoreFront', 'DieselStoreFrontWide', 'OfferImageTall', 'OfferImageWide'];
    let imageUrl = '';
    
    if (epicGame.keyImages) {
      for (const imageType of imageTypes) {
        const image = epicGame.keyImages.find(img => img.type === imageType);
        if (image) {
          imageUrl = image.url;
          break;
        }
      }
    }

    // Extract genres from categories and tags
    const genres: string[] = [];
    if (epicGame.categories) {
      genres.push(...epicGame.categories.map(cat => cat.path.split('/').pop() || '').filter(Boolean));
    }
    if (epicGame.tags) {
      genres.push(...epicGame.tags.map(tag => tag.name));
    }

    // Determine platforms
    const platforms = epicGame.platforms || ['PC'];

    return {
      platformId: epicGame.catalogItemId,
      name: epicGame.title,
      description: epicGame.description || epicGame.longDescription,
      releaseDate: epicGame.releaseDate || epicGame.pcReleaseDate || epicGame.effectiveDate,
      developer: epicGame.developerDisplayName,
      publisher: epicGame.publisherDisplayName,
      genres: [...new Set(genres)], // Remove duplicates
      platforms,
      imageUrl,
      metadata: {
        epic_namespace: epicGame.namespace,
        epic_catalog_item_id: epicGame.catalogItemId,
        epic_technical_details: epicGame.technicalDetails,
        epic_custom_attributes: epicGame.customAttributes,
        epic_categories: epicGame.categories,
        epic_tags: epicGame.tags,
        epic_key_images: epicGame.keyImages,
      },
    };
  }

  /**
   * Convert platform data to our internal Game format
   */
  protected convertToGame(platformGame: PlatformGameData): Partial<Game> {
    return {
      name: platformGame.name,
      description: platformGame.description,
      release_date: platformGame.releaseDate,
      developer: platformGame.developer,
      publisher: platformGame.publisher,
      genres: platformGame.genres,
      platforms: platformGame.platforms,
      cover_image: platformGame.imageUrl,
      platform_ids: {
        epic: platformGame.platformId,
      },
      metadata: {
        import_source: 'epic',
        ...platformGame.metadata,
      },
    };
  }

  /**
   * Search Epic Games catalog (public endpoint)
   */
  async searchGames(): Promise<PlatformGameData[]> {
    await this.ensureValidToken();

    try {
      // Epic Games catalog search endpoint
      const searchUrl = `https://api.epicgames.dev/epic/catalog/v1/products/search`;
      
      const response = await this.makeRequest(searchUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Accept': 'application/json',
        },
      });

      const searchData: EpicLibraryResponse = await response.json();
      
      return searchData.data?.map(item => this.convertEpicGameToPlatformData(item.catalogItem)) || [];
    } catch (error) {
      console.error('Epic Games search failed:', error);
      throw new Error(`Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get detailed information about a specific game
   */
  async getGameDetails(catalogItemId: string): Promise<PlatformGameData | null> {
    await this.ensureValidToken();

    try {
      const detailsUrl = `https://api.epicgames.dev/epic/catalog/v1/products/${catalogItemId}`;
      
      const response = await this.makeRequest(detailsUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Accept': 'application/json',
        },
      });

      const gameData: EpicGameData = await response.json();
      return this.convertEpicGameToPlatformData(gameData);
    } catch (error) {
      console.error('Failed to get Epic game details:', error);
      return null;
    }
  }

  /**
   * Check if a game is free
   */
  static isGameFree(epicGame: EpicGameData): boolean {
    // Check if game has free-to-play or free category
    if (epicGame.categories) {
      return epicGame.categories.some(cat => 
        cat.path.toLowerCase().includes('free') || 
        cat.path.toLowerCase().includes('f2p')
      );
    }
    return false;
  }

  /**
   * Get Epic Games store URL for a game
   */
  static getStoreUrl(catalogItemId: string, namespace: string): string {
    return `https://store.epicgames.com/en-US/p/${namespace}/${catalogItemId}`;
  }
}

// Export singleton instance - DISABLED: Only Steam is supported as of July 2025
// export const epicImportService = new EpicImportService();