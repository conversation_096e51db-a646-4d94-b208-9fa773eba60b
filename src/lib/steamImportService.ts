import { db } from './supabase';
import { steamApiClient, SteamApiException, SteamApiError } from './steam-error-handler';
import { steamCache, SteamCache, CACHE_CONFIGS } from './steam-cache';

// Steam API response interfaces
interface SteamGameDetails {
  type?: string;
  name?: string;
  steam_appid?: number;
  short_description?: string;
  header_image?: string;
  developers?: string[];
  publishers?: string[];
  genres?: Array<{ description: string }>;
  platforms?: { windows?: boolean; mac?: boolean; linux?: boolean };
  metacritic?: { score?: number };
  screenshots?: Array<{ path_thumbnail: string }>;
  release_date?: { date?: string };
  price_overview?: {
    currency?: string;
    initial?: number;
    final?: number;
  };
}

interface SteamGame {
  appid: number;
  name: string;
  playtime_forever: number;
  img_icon_url?: string;
  img_logo_url?: string;
  has_community_visible_stats?: boolean;
  playtime_windows_forever?: number;
  playtime_mac_forever?: number;
  playtime_linux_forever?: number;
  rtime_last_played?: number;
}

interface SteamProfile {
  steamid: string;
  communityvisibilitystate: number;
  profilestate: number;
  personaname: string;
  profileurl: string;
  avatar: string;
  avatarmedium: string;
  avatarfull: string;
  personastate: number;
  realname?: string;
  timecreated?: number;
}

// Enhanced Steam API interfaces from specification
interface SteamPlayerBans {
  SteamId: string;
  CommunityBanned: boolean;
  VACBanned: boolean;
  NumberOfVACBans: number;
  DaysSinceLastBan: number;
  NumberOfGameBans: number;
  EconomyBan: string;
}

interface SteamAchievement {
  apiname: string;
  achieved: number;
  unlocktime: number;
  name?: string;
  description?: string;
}

interface SteamFriend {
  steamid: string;
  relationship: string; // "friend", "blocked", etc.
  friend_since: number;
}



interface LibraryComparison {
  common_games: number;
  friend_exclusive: number;
  user_exclusive: number;
  common_playtime: number;
}

// Enhanced game data structure
interface EnhancedSteamGame extends SteamGame {
  // Recently played data
  playtime_2weeks?: number;

  // Achievement data
  achievements?: SteamAchievement[];
  achievement_percentage?: number; // % of achievements unlocked

  // Statistics
  stats?: Array<{
    name: string;
    value: number;
  }>;

  // Last session info
  last_session_length?: number;
  last_session_date?: string;
}

// Enhanced Steam API error types
enum SteamApiError {
  RATE_LIMITED = 'RATE_LIMITED',
  PRIVATE_PROFILE = 'PRIVATE_PROFILE',
  INVALID_APP_ID = 'INVALID_APP_ID',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  INVALID_STEAM_ID = 'INVALID_STEAM_ID',
  NO_ACHIEVEMENTS = 'NO_ACHIEVEMENTS',
  FRIENDS_LIST_PRIVATE = 'FRIENDS_LIST_PRIVATE'
}

interface ImportProgress {
  phase: 'authenticating' | 'fetching_profile' | 'fetching_games' | 'processing' | 'saving' | 'complete';
  progress: number;
  message: string;
  gamesProcessed?: number;
  totalGames?: number;
}

class SteamImportService {
  private readonly STEAM_API_BASE = 'https://api.steampowered.com';
  private readonly STEAM_STORE_BASE = 'https://store.steampowered.com/api';
  
  /**
   * Get Steam profile information using Steam ID
   */
  async getSteamProfile(steamId: string): Promise<SteamProfile | null> {
    try {
      console.log(`Fetching Steam profile for: ${steamId}`);

      // Check cache first
      const cacheKey = SteamCache.generateKey(CACHE_CONFIGS.PROFILE.key, { steamId });
      const cached = steamCache.get<SteamProfile>(cacheKey);
      if (cached) {
        console.log(`✅ Found cached Steam profile: ${cached.personaname}`);
        return cached;
      }

      return await steamApiClient.makeRequest(async () => {
        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
        const response = await fetch(
          `${supabaseUrl}/functions/v1/steam-proxy`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
              endpoint: 'profile',
              steamId: steamId
            })
          }
        );

        if (!response.ok) {
          throw new Error(`Steam proxy error: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch Steam profile');
        }

        const profile = result.data;
        console.log(`✅ Found Steam profile: ${profile.personaname}`);

        const profileData = {
          steamid: profile.steamid,
          communityvisibilitystate: profile.communityvisibilitystate,
          profilestate: profile.profilestate,
          personaname: profile.personaname,
          profileurl: profile.profileurl,
          avatar: profile.avatar,
          avatarmedium: profile.avatarmedium,
          avatarfull: profile.avatarfull,
          personastate: profile.personastate,
          realname: profile.realname,
          timecreated: profile.timecreated
        };

        // Cache the profile data
        steamCache.set(cacheKey, profileData, CACHE_CONFIGS.PROFILE.ttl);

        return profileData;
      }, { steamId, endpoint: 'profile' });

    } catch (error) {
      if (error instanceof SteamApiException) {
        console.warn(`Steam API error for profile ${steamId}:`, error.details);

        // Handle specific error types gracefully
        switch (error.details.type) {
          case SteamApiError.PRIVATE_PROFILE:
          case SteamApiError.INVALID_STEAM_ID:
            console.info(`Profile ${steamId} is private or invalid - this is expected for some users`);
            return null;
          case SteamApiError.RATE_LIMITED:
            console.warn(`Rate limited, retry after ${error.details.retryAfter} seconds`);
            return null;
          default:
            console.error(`Unexpected Steam API error:`, error.details);
            return null;
        }
      }

      console.error('Error fetching Steam profile:', error);
      return null;
    }
  }

  /**
   * Get user's Steam game library
   */
  async getSteamLibrary(steamId: string): Promise<SteamGame[]> {
    try {
      console.log(`Fetching actual Steam library for: ${steamId}`);
      
      // Use Supabase Edge Function for secure Steam API proxy
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(
        `${supabaseUrl}/functions/v1/steam-proxy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            endpoint: 'library',
            steamId: steamId
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Steam proxy error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch Steam library');
      }

      const games: SteamGame[] = result.data.map((game: SteamGame) => ({
        appid: game.appid,
        name: game.name,
        playtime_forever: game.playtime_forever || 0,
        img_icon_url: game.img_icon_url || '',
        img_logo_url: game.img_logo_url || '',
        rtime_last_played: game.rtime_last_played || 0,
        playtime_windows_forever: game.playtime_windows_forever || 0,
        playtime_mac_forever: game.playtime_mac_forever || 0,
        playtime_linux_forever: game.playtime_linux_forever || 0,
        has_community_visible_stats: game.has_community_visible_stats || false
      }));

      console.log(`✅ Found ${games.length} games in Steam library`);
      return games;
    } catch (error) {
      console.error('Error fetching Steam library:', error);
      throw error;
    }
  }

  /**
   * Get detailed game information from Steam
   */
  async getSteamGameDetails(appId: number): Promise<SteamGameDetails> {
    try {
      console.log(`Fetching Steam game details for App ID: ${appId}`);
      
      // Use Supabase Edge Function for secure Steam API proxy
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(
        `${supabaseUrl}/functions/v1/steam-proxy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            endpoint: 'game-details',
            appId: appId
          })
        }
      );

      if (!response.ok) {
        console.warn(`Steam proxy error for app ${appId}: ${response.status}`);
        return {};
      }

      const result = await response.json();
      
      if (!result.success || !result.data) {
        console.warn(`No data found for Steam app ${appId}: ${result.error || 'Unknown error'}`);
        return {};
      }

      const gameData = result.data;
      return {
        type: gameData.type,
        name: gameData.name,
        steam_appid: gameData.steam_appid,
        short_description: gameData.short_description,
        header_image: gameData.header_image,
        developers: gameData.developers || [],
        publishers: gameData.publishers || [],
        genres: gameData.genres || [],
        platforms: gameData.platforms || {},
        metacritic: gameData.metacritic || {},
        screenshots: gameData.screenshots || [],
        release_date: gameData.release_date || {},
        price_overview: gameData.price_overview || {}
      };
    } catch (error) {
      console.error(`Error fetching Steam game details for app ${appId}:`, error);
      return {};
    }
  }

  /**
   * Get player achievements for a specific game
   */
  async getPlayerAchievements(steamId: string, appId: number): Promise<SteamAchievement[]> {
    try {
      console.log(`Fetching achievements for Steam ID: ${steamId}, App ID: ${appId}`);

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(
        `${supabaseUrl}/functions/v1/steam-proxy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            endpoint: 'achievements',
            steamId: steamId,
            appId: appId
          })
        }
      );

      if (!response.ok) {
        console.warn(`Steam proxy error for achievements ${steamId}/${appId}: ${response.status}`);
        return [];
      }

      const result = await response.json();

      if (!result.success) {
        console.warn(`Failed to fetch achievements for ${steamId}/${appId}: ${result.error}`);
        return [];
      }

      return result.data?.achievements || [];
    } catch (error) {
      console.error(`Error fetching achievements for ${steamId}/${appId}:`, error);
      return [];
    }
  }

  /**
   * Get recently played games for a Steam user
   */
  async getRecentlyPlayedGames(steamId: string): Promise<EnhancedSteamGame[]> {
    try {
      console.log(`Fetching recently played games for Steam ID: ${steamId}`);

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(
        `${supabaseUrl}/functions/v1/steam-proxy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            endpoint: 'recently-played',
            steamId: steamId
          })
        }
      );

      if (!response.ok) {
        console.warn(`Steam proxy error for recently played ${steamId}: ${response.status}`);
        return [];
      }

      const result = await response.json();

      if (!result.success) {
        console.warn(`Failed to fetch recently played games for ${steamId}: ${result.error}`);
        return [];
      }

      return result.data?.games || [];
    } catch (error) {
      console.error(`Error fetching recently played games for ${steamId}:`, error);
      return [];
    }
  }

  /**
   * Get player ban information
   */
  async getPlayerBans(steamId: string): Promise<SteamPlayerBans | null> {
    try {
      console.log(`Fetching player bans for Steam ID: ${steamId}`);

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(
        `${supabaseUrl}/functions/v1/steam-proxy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            endpoint: 'player-bans',
            steamId: steamId
          })
        }
      );

      if (!response.ok) {
        console.warn(`Steam proxy error for player bans ${steamId}: ${response.status}`);
        return null;
      }

      const result = await response.json();

      if (!result.success) {
        console.warn(`Failed to fetch player bans for ${steamId}: ${result.error}`);
        return null;
      }

      return result.data || null;
    } catch (error) {
      console.error(`Error fetching player bans for ${steamId}:`, error);
      return null;
    }
  }

  /**
   * Get Steam friends list
   */
  async getSteamFriends(steamId: string): Promise<SteamFriend[]> {
    try {
      console.log(`Fetching friends list for Steam ID: ${steamId}`);

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(
        `${supabaseUrl}/functions/v1/steam-proxy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            endpoint: 'friends',
            steamId: steamId
          })
        }
      );

      if (!response.ok) {
        console.warn(`Steam proxy error for friends ${steamId}: ${response.status}`);
        return [];
      }

      const result = await response.json();

      if (!result.success) {
        console.warn(`Failed to fetch friends for ${steamId}: ${result.error}`);
        return [];
      }

      return result.data?.friends || [];
    } catch (error) {
      console.error(`Error fetching friends for ${steamId}:`, error);
      return [];
    }
  }

  /**
   * Store detailed achievement data for a user's game
   */
  async storeGameAchievements(userGameId: string, steamId: string, appId: number): Promise<void> {
    try {
      const achievements = await this.getPlayerAchievements(steamId, appId);
      if (!achievements || achievements.length === 0) {
        return;
      }

      // Get global achievement percentages for rarity calculation
      const globalPercentages = await this.getGlobalAchievementPercentages(appId);
      const globalMap = new Map(globalPercentages.map(g => [g.name, g.percent]));

      for (const achievement of achievements) {
        if (achievement.achieved === 1) {
          const globalPercent = globalMap.get(achievement.apiname) || 100;
          const isRare = globalPercent < 10; // Consider achievements with <10% unlock rate as rare

          const achievementData = {
            user_game_id: userGameId,
            achievement_name: achievement.apiname,
            achievement_description: achievement.description || achievement.name || '',
            unlocked_at: achievement.unlocktime ? new Date(achievement.unlocktime * 1000).toISOString() : new Date().toISOString(),
            is_rare: isRare,
            global_percentage: globalPercent
          };

          // Insert achievement record (ignore if already exists)
          const { error } = await db.userGameAchievements.upsert(achievementData);

          if (error) {
            console.warn(`Failed to store achievement ${achievement.apiname}:`, error);
          }
        }
      }

      console.log(`✅ Stored achievement data for app ${appId}`);
    } catch (error) {
      console.error(`Error storing achievements for app ${appId}:`, error);
    }
  }

  /**
   * Get global achievement percentages for a game
   */
  async getGlobalAchievementPercentages(appId: number): Promise<Array<{ name: string; percent: number }>> {
    try {
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(
        `${supabaseUrl}/functions/v1/steam-proxy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            endpoint: 'global-achievement-percentages',
            appId: appId
          })
        }
      );

      if (!response.ok) {
        console.warn(`Steam proxy error for global achievements ${appId}: ${response.status}`);
        return [];
      }

      const result = await response.json();

      if (!result.success) {
        console.warn(`Failed to fetch global achievements for ${appId}: ${result.error}`);
        return [];
      }

      return result.data?.achievements || [];
    } catch (error) {
      console.error(`Error fetching global achievements for ${appId}:`, error);
      return [];
    }
  }

  /**
   * Import Steam friends for a user
   */
  async importSteamFriends(userId: string, steamId: string): Promise<{ success: boolean; imported: number; errors: string[] }> {
    const errors: string[] = [];
    let imported = 0;

    try {
      console.log(`🔄 Starting Steam friends import for user: ${userId}`);

      // Get friends list
      const friends = await this.getSteamFriends(steamId);
      if (!friends || friends.length === 0) {
        return {
          success: true,
          imported: 0,
          errors: ['No friends found or friends list is private']
        };
      }

      console.log(`📋 Found ${friends.length} friends to import`);

      // Get detailed profile information for friends (batch process)
      const friendProfiles = await this.getBatchSteamProfiles(friends.map(f => f.steamid));

      for (let i = 0; i < friends.length; i++) {
        const friend = friends[i];
        const profile = friendProfiles.find(p => p.steamid === friend.steamid);

        try {
          const friendData = {
            user_id: userId,
            friend_steam_id: friend.steamid,
            friend_name: profile?.personaname || 'Unknown',
            friend_avatar: profile?.avatar || '',
            relationship: friend.relationship || 'friend',
            friend_since: friend.friend_since ? new Date(friend.friend_since * 1000).toISOString() : null
          };

          const { error } = await db.steamFriends.upsert(friendData);
          if (error) {
            console.error(`Failed to import friend ${friend.steamid}:`, error);
            errors.push(`Failed to import friend: ${profile?.personaname || friend.steamid}`);
          } else {
            imported++;
            console.log(`✅ Imported friend: ${profile?.personaname || friend.steamid}`);
          }
        } catch (friendError) {
          console.error(`Error processing friend ${friend.steamid}:`, friendError);
          errors.push(`Error processing friend: ${friend.steamid}`);
        }
      }

      console.log(`✅ Steam friends import complete: ${imported} imported, ${errors.length} errors`);

      return {
        success: true,
        imported,
        errors
      };

    } catch (error) {
      console.error('Error importing Steam friends:', error);
      return {
        success: false,
        imported,
        errors: [...errors, error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Get batch Steam profiles for multiple Steam IDs
   */
  async getBatchSteamProfiles(steamIds: string[]): Promise<SteamProfile[]> {
    const profiles: SteamProfile[] = [];

    // Process in batches of 100 (Steam API limit)
    const batchSize = 100;
    for (let i = 0; i < steamIds.length; i += batchSize) {
      const batch = steamIds.slice(i, i + batchSize);

      try {
        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
        const response = await fetch(
          `${supabaseUrl}/functions/v1/steam-proxy`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
              endpoint: 'profile',
              steamId: batch.join(',') // Steam API supports comma-separated IDs
            })
          }
        );

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            // Handle both single profile and array of profiles
            const profileData = Array.isArray(result.data) ? result.data : [result.data];
            profiles.push(...profileData);
          }
        }
      } catch (error) {
        console.warn(`Failed to fetch batch profiles for batch starting at ${i}:`, error);
      }

      // Small delay between batches to respect rate limits
      if (i + batchSize < steamIds.length) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    return profiles;
  }

  /**
   * Compare libraries between two Steam users
   */
  async compareLibraries(userId: string, userSteamId: string, friendSteamId: string): Promise<LibraryComparison> {
    try {
      console.log(`🔍 Comparing libraries between ${userSteamId} and ${friendSteamId}`);

      // Get both libraries
      const [userLibrary, friendLibrary] = await Promise.all([
        this.getSteamLibrary(userSteamId),
        this.getSteamLibrary(friendSteamId)
      ]);

      // Create sets for efficient comparison
      const userGameIds = new Set(userLibrary.map(g => g.appid));
      const friendGameIds = new Set(friendLibrary.map(g => g.appid));

      // Find common games
      const commonGameIds = [...userGameIds].filter(id => friendGameIds.has(id));
      const userExclusiveIds = [...userGameIds].filter(id => !friendGameIds.has(id));
      const friendExclusiveIds = [...friendGameIds].filter(id => !userGameIds.has(id));

      // Calculate playtime for common games
      const commonPlaytime = commonGameIds.reduce((total, appId) => {
        const userGame = userLibrary.find(g => g.appid === appId);
        const friendGame = friendLibrary.find(g => g.appid === appId);
        return total + (userGame?.playtime_forever || 0) + (friendGame?.playtime_forever || 0);
      }, 0);

      const comparison: LibraryComparison = {
        common_games: commonGameIds.length,
        friend_exclusive: friendExclusiveIds.length,
        user_exclusive: userExclusiveIds.length,
        common_playtime: Math.round(commonPlaytime / 60) // Convert to hours
      };

      console.log(`📊 Library comparison complete:`, comparison);
      return comparison;

    } catch (error) {
      console.error('Error comparing libraries:', error);
      return {
        common_games: 0,
        friend_exclusive: 0,
        user_exclusive: 0,
        common_playtime: 0
      };
    }
  }

  /**
   * Get common games between user and friend
   */
  async getCommonGames(userId: string, userSteamId: string, friendSteamId: string): Promise<Array<{ game: SteamGame; userPlaytime: number; friendPlaytime: number }>> {
    try {
      // Get both libraries for detailed comparison
      const [userLibrary, friendLibrary] = await Promise.all([
        this.getSteamLibrary(userSteamId),
        this.getSteamLibrary(friendSteamId)
      ]);

      // Find common games with playtime details
      const commonGames = userLibrary
        .filter(userGame => friendLibrary.some(friendGame => friendGame.appid === userGame.appid))
        .map(userGame => {
          const friendGame = friendLibrary.find(fg => fg.appid === userGame.appid);
          return {
            game: userGame,
            userPlaytime: Math.round(userGame.playtime_forever / 60), // Convert to hours
            friendPlaytime: Math.round((friendGame?.playtime_forever || 0) / 60)
          };
        })
        .sort((a, b) => (b.userPlaytime + b.friendPlaytime) - (a.userPlaytime + a.friendPlaytime)); // Sort by total playtime

      return commonGames;
    } catch (error) {
      console.error('Error getting common games:', error);
      return [];
    }
  }

  /**
   * Get game recommendations based on friend's library
   */
  async getFriendGameRecommendations(userId: string, userSteamId: string, friendSteamId: string, limit: number = 10): Promise<SteamGame[]> {
    try {
      console.log(`🎯 Getting game recommendations based on friend's library`);

      // Get both libraries
      const [userLibrary, friendLibrary] = await Promise.all([
        this.getSteamLibrary(userSteamId),
        this.getSteamLibrary(friendSteamId)
      ]);

      const userGameIds = new Set(userLibrary.map(g => g.appid));

      // Find games friend has that user doesn't, sorted by friend's playtime
      const recommendations = friendLibrary
        .filter(friendGame => !userGameIds.has(friendGame.appid))
        .filter(friendGame => friendGame.playtime_forever > 60) // Friend has played for at least 1 hour
        .sort((a, b) => b.playtime_forever - a.playtime_forever)
        .slice(0, limit);

      console.log(`📋 Found ${recommendations.length} game recommendations from friend`);
      return recommendations;
    } catch (error) {
      console.error('Error getting friend game recommendations:', error);
      return [];
    }
  }

  /**
   * Store player ban information
   */
  async storePlayerBans(userId: string, steamId: string): Promise<void> {
    try {
      const bans = await this.getPlayerBans(steamId);
      if (!bans) return;

      const banData = {
        user_id: userId,
        steam_id: steamId,
        community_banned: bans.CommunityBanned,
        vac_banned: bans.VACBanned,
        number_of_vac_bans: bans.NumberOfVACBans,
        days_since_last_ban: bans.DaysSinceLastBan,
        number_of_game_bans: bans.NumberOfGameBans,
        economy_ban: bans.EconomyBan
      };

      const { error } = await db.steamPlayerBans.upsert(banData);
      if (error) {
        console.warn('Failed to store player ban data:', error);
      } else {
        console.log('✅ Stored player ban data');
      }
    } catch (error) {
      console.error('Error storing player bans:', error);
    }
  }

  /**
   * Import Steam library to user's collection
   */
  async importSteamLibrary(
    userId: string, 
    steamId: string,
    onProgress?: (progress: ImportProgress) => void
  ): Promise<{ success: boolean; imported: number; errors: string[]; message?: string }> {
    const errors: string[] = [];
    let imported = 0;

    try {
      // Phase 1: Authentication and profile fetch
      onProgress?.({
        phase: 'authenticating',
        progress: 10,
        message: 'Connecting to Steam...'
      });

      const profile = await this.getSteamProfile(steamId);
      if (!profile) {
        throw new Error('Failed to fetch Steam profile. Please check your Steam ID and ensure your profile is public.');
      }
      
      // Check if profile is public
      if (profile.communityvisibilitystate !== 3) {
        throw new Error('Your Steam profile must be public to import games. Please set your profile to public in Steam and try again.');
      }

      onProgress?.({
        phase: 'fetching_profile',
        progress: 20,
        message: `Found profile: ${profile.personaname}`
      });

      // Phase 2: Fetch game library
      onProgress?.({
        phase: 'fetching_games',
        progress: 30,
        message: 'Fetching your Steam library...'
      });

      const steamGames = await this.getSteamLibrary(steamId);
      if (steamGames.length === 0) {
        throw new Error('No games found in Steam library. Please ensure your game library is public in Steam Privacy Settings.');
      }

      onProgress?.({
        phase: 'processing',
        progress: 40,
        message: `Processing ${steamGames.length} games...`,
        totalGames: steamGames.length
      });

      // Phase 3: Process and save games
      for (let i = 0; i < steamGames.length; i++) {
        const steamGame = steamGames[i];
        
        try {
          onProgress?.({
            phase: 'processing',
            progress: 40 + (i / steamGames.length) * 50,
            message: `Processing: ${steamGame.name}`,
            gamesProcessed: i + 1,
            totalGames: steamGames.length
          });

          // Get detailed game information
          const gameDetails = await this.getSteamGameDetails(steamGame.appid);
          
          // Efficiently check if game already exists by Steam App ID
          let gameId: string;
          const { data: existingGameRecord } = await db.games.getBySteamAppId(steamGame.appid.toString());

          if (existingGameRecord) {
            gameId = existingGameRecord.id;
            console.log(`🔄 Found existing game: ${steamGame.name} (ID: ${gameId})`);
          } else {
            // Create new game entry with full Steam support
            const newGameData = {
              title: steamGame.name || 'Unknown Game',  // Ensure title is not empty
              platform: 'PC',  // Required field for Steam games
              description: gameDetails?.short_description || '',
              cover_image: gameDetails?.header_image || `https://cdn.akamai.steamstatic.com/steam/apps/${steamGame.appid}/header.jpg`,
              release_date: this.parseSteamReleaseDate(gameDetails?.release_date?.date),
              developer: gameDetails?.developers?.[0] || null,
              publisher: gameDetails?.publishers?.[0] || null,
              genres: gameDetails?.genres?.map((g: { description: string }) => g.description) || [],
              metacritic_score: this.validateMetacriticScore(gameDetails?.metacritic?.score),
              screenshots: gameDetails?.screenshots?.map((s: { path_thumbnail: string }) => s.path_thumbnail) || [],
              igdb_id: null,
              steam_app_id: steamGame.appid.toString(),
              steam_url: `https://store.steampowered.com/app/${steamGame.appid}`
            };

            // Validate required fields before attempting to create
            if (!newGameData.title || !newGameData.platform) {
              errors.push(`Invalid game data for: ${steamGame.name} (missing required fields)`);
              continue;
            }

            // Debug logging - see exactly what we're sending to the database
            console.log(`🔍 Creating game "${steamGame.name}" with data:`, JSON.stringify(newGameData, null, 2));

            const { data: createdGame, error: createError } = await db.games.upsert(newGameData);
            if (createError) {
              console.error(`❌ Failed to create game "${steamGame.name}":`, {
                error: createError,
                errorMessage: createError.message,
                errorDetails: createError.details,
                errorHint: createError.hint,
                errorCode: createError.code,
                gameData: newGameData,
                steamAppId: steamGame.appid
              });
              
              // More detailed error message
              const errorMsg = createError.message || createError.details || 'Unknown database error';
              errors.push(`Failed to create game: ${steamGame.name} - ${errorMsg}`);
              continue;
            }
            gameId = createdGame.id;
            console.log(`✅ Created game: ${steamGame.name} (ID: ${gameId})`);
          }

          // Check if user already has this game in their collection
          const { data: existingUserGame } = await db.userGames.getByUserAndGame(userId, gameId);
          
          // Fetch achievement data if the game has achievements
          const achievementData = { achievements_unlocked: 0, achievements_total: 0 };
          if (steamGame.has_community_visible_stats) {
            try {
              const achievements = await this.getPlayerAchievements(steamId, steamGame.appid);
              if (achievements && achievements.length > 0) {
                achievementData.achievements_total = achievements.length;
                achievementData.achievements_unlocked = achievements.filter(a => a.achieved === 1).length;
                console.log(`📊 Found ${achievementData.achievements_unlocked}/${achievementData.achievements_total} achievements for ${steamGame.name}`);
              }
            } catch (achievementError) {
              console.warn(`Failed to fetch achievements for ${steamGame.name}:`, achievementError);
            }
          }

          const userGameData = {
            user_id: userId,
            game_id: gameId,
            status: this.determineGameStatus(steamGame),
            hours_played: Math.max(0, Math.round(steamGame.playtime_forever / 60)),
            personal_notes: `Imported from Steam. App ID: ${steamGame.appid}${steamGame.has_community_visible_stats ? '. Has Steam achievements.' : ''}`,
            date_added: existingUserGame?.date_added || new Date().toISOString(),
            import_source: 'steam',
            last_played: steamGame.rtime_last_played ? new Date(steamGame.rtime_last_played * 1000).toISOString() : null,
            achievements_unlocked: achievementData.achievements_unlocked,
            achievements_total: achievementData.achievements_total
          };

          const { error: addError } = await db.userGames.upsertToCollection(userGameData);
          if (addError) {
            // Log detailed error information for debugging
            console.error(`❌ Failed to add/update "${steamGame.name}" in collection:`, {
              error: addError,
              errorCode: addError.code,
              errorMessage: addError.message,
              errorDetails: addError.details,
              userGameData: userGameData,
              userId: userId,
              gameId: gameId
            });
            errors.push(`Failed to add to collection: ${steamGame.name} (${addError.message || addError.code || 'Unknown error'})`);
            continue;
          }

          imported++;
          const action = existingUserGame ? 'Updated' : 'Added';
          console.log(`✅ ${action} in collection: ${steamGame.name} (${Math.round(steamGame.playtime_forever / 60)}h played)`);

          // Store detailed achievement data if the game has achievements
          if (steamGame.has_community_visible_stats && achievementData.achievements_total > 0) {
            try {
              // Get the user_game record ID for storing achievements
              const { data: userGameRecord } = await db.userGames.getByUserAndGame(userId, gameId);
              if (userGameRecord) {
                await this.storeGameAchievements(userGameRecord.id, steamId, steamGame.appid);
              }
            } catch (achievementStoreError) {
              console.warn(`Failed to store detailed achievements for ${steamGame.name}:`, achievementStoreError);
            }
          }

          // Small delay to prevent overwhelming the API
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (gameError) {
          console.error(`Error processing game ${steamGame.name}:`, gameError);
          errors.push(`Error processing: ${steamGame.name}`);
        }
      }

      onProgress?.({
        phase: 'complete',
        progress: 100,
        message: `Import complete! Added ${imported} games to your collection.`
      });

      // Provide helpful summary message
      const successMessage = imported > 0 
        ? `Successfully imported ${imported} games from Steam!` 
        : 'No new games were imported (all games may already be in your library).';
      
      return {
        success: true,
        imported,
        errors,
        message: successMessage
      };

    } catch (error) {
      console.error('Steam import error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      errors.push(errorMessage);
      
      // Provide helpful error messages based on common issues
      let userFriendlyMessage = errorMessage;
      if (errorMessage.includes('profile') && errorMessage.includes('public')) {
        userFriendlyMessage = 'Please set your Steam profile to public and try again.';
      } else if (errorMessage.includes('library') && errorMessage.includes('private')) {
        userFriendlyMessage = 'Please set your Steam game library to public in Privacy Settings and try again.';
      } else if (errorMessage.includes('Steam ID')) {
        userFriendlyMessage = 'Please check your Steam ID and try again.';
      }
      
      return {
        success: false,
        imported,
        errors,
        message: userFriendlyMessage
      };
    }
  }

  /**
   * Determine game status based on playtime
   */
  private determineGameStatus(steamGame: SteamGame): 'playing' | 'completed' | 'backlog' | 'wishlist' {
    const playtimeHours = steamGame.playtime_forever / 60;
    const lastPlayedDays = steamGame.rtime_last_played 
      ? (Date.now() / 1000 - steamGame.rtime_last_played) / 86400 
      : 999;

    if (playtimeHours === 0) {
      return 'backlog';
    } else if (lastPlayedDays <= 7 && playtimeHours > 2) {
      return 'playing';
    } else if (playtimeHours > 50) { // Assume completion for games with high playtime
      return 'completed';
    } else {
      return 'backlog'; // Changed from 'library' to 'backlog' as valid status
    }
  }

  /**
   * Extract platform information (currently unused but may be needed for future enhancements)
   */
  // private extractPlatforms(platforms: { windows?: boolean; mac?: boolean; linux?: boolean }): string[] {
  //   if (!platforms) return ['PC'];
  //   
  //   const platformList: string[] = [];
  //   if (platforms.windows) platformList.push('PC');
  //   if (platforms.mac) platformList.push('Mac');
  //   if (platforms.linux) platformList.push('Linux');
  //   
  //   return platformList.length > 0 ? platformList : ['PC'];
  // }

  /**
   * Parse and validate Steam release date
   */
  private parseSteamReleaseDate(dateString?: string): string | null {
    if (!dateString) return null;
    
    try {
      // Steam dates can be in various formats like "Dec 10, 2020" or "Coming Soon"
      if (dateString.toLowerCase().includes('coming soon') || dateString.toLowerCase().includes('tba')) {
        return null;
      }
      
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return null;
      }
      
      return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
    } catch (error) {
      console.warn(`Failed to parse release date: ${dateString}`, error);
      return null;
    }
  }

  /**
   * Validate Metacritic score
   */
  private validateMetacriticScore(score?: number): number | null {
    if (typeof score !== 'number' || isNaN(score)) return null;
    if (score < 0 || score > 100) return null;
    return Math.round(score);
  }

  /**
   * Validate Steam ID format
   */
  validateSteamId(steamId: string): boolean {
    // Steam ID can be:
    // - 64-bit Steam ID (17 digits)
    // - Custom URL (letters, numbers, underscore)
    // - Profile URL
    
    if (!steamId.trim()) return false;
    
    // Extract from URL if provided
    const urlMatch = steamId.match(/steamcommunity\.com\/(?:id|profiles)\/([^/]+)/);
    if (urlMatch) {
      steamId = urlMatch[1];
    }
    
    // Check for 64-bit Steam ID (17 digits starting with 7656119)
    if (/^7656119\d{10}$/.test(steamId)) {
      return true;
    }
    
    // Check for custom URL (3-32 characters, alphanumeric + underscore)
    if (/^[a-zA-Z0-9_]{3,32}$/.test(steamId)) {
      return true;
    }
    
    return false;
  }

  /**
   * Extract Steam ID from various input formats
   */
  extractSteamId(input: string): string {
    const trimmed = input.trim();
    
    // Extract from Steam profile URL
    const urlMatch = trimmed.match(/steamcommunity\.com\/(?:id|profiles)\/([^/]+)/);
    if (urlMatch) {
      return urlMatch[1];
    }
    
    return trimmed;
  }

  /**
   * Get import statistics for user
   */
  async getImportStats(userId: string): Promise<{
    totalImported: number;
    steamGames: number;
    epicGames: number;
    consoleGames: number;
    lastImport?: string;
  }> {
    try {
      const { data: userGames, error } = await db.userGames.getUserCollection(userId);
      if (error) throw error;

      const totalImported = userGames?.length || 0;
      
      // Use import_source field for accurate Steam game counting
      const steamGames = userGames?.filter(g => 
        g.import_source === 'steam' || // Primary check: import source
        g.personal_notes?.includes('Imported from Steam') || // Fallback for legacy imports
        g.personal_notes?.includes('App ID:')
      ).length || 0;
      
      // Count other import sources (currently 0 until we have proper tracking)
      const epicGames = 0;
      const consoleGames = 0;

      // Get most recent Steam import using import_source
      const steamImports = userGames?.filter(g => 
        g.import_source === 'steam' || // Primary check: import source
        g.personal_notes?.includes('Imported from Steam') || // Fallback for legacy imports
        g.personal_notes?.includes('App ID:')
      );
      const lastImport = steamImports && steamImports.length > 0 
        ? steamImports.sort((a, b) => new Date(b.date_added).getTime() - new Date(a.date_added).getTime())[0].date_added
        : undefined;

      return {
        totalImported,
        steamGames,
        epicGames,
        consoleGames,
        lastImport
      };
    } catch (error) {
      console.error('Error getting import stats:', error);
      return {
        totalImported: 0,
        steamGames: 0,
        epicGames: 0,
        consoleGames: 0
      };
    }
  }
}

export const steamImportService = new SteamImportService();
export type { SteamGame, SteamProfile, ImportProgress };