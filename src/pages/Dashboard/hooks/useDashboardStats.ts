import { useMemo } from 'react';
import { DashboardStats } from '../types';

export interface DashboardStatsCalculations {
  completionRate: number;
  playingToTotalRatio: number;
  wishlistToLibraryRatio: number;
  totalLibraryGames: number;
  isActiveGamer: boolean;
}

export function useDashboardStats(stats?: DashboardStats): DashboardStatsCalculations {
  return useMemo(() => {
    if (!stats) {
      return {
        completionRate: 0,
        playingToTotalRatio: 0,
        wishlistToLibraryRatio: 0,
        totalLibraryGames: 0,
        isActiveGamer: false
      };
    }

    const totalLibraryGames = stats.totalGames - stats.wishlistCount;
    const completionRate = totalLibraryGames > 0 ? (stats.completed / totalLibraryGames) * 100 : 0;
    const playingToTotalRatio = stats.totalGames > 0 ? (stats.currentlyPlaying / stats.totalGames) * 100 : 0;
    const wishlistToLibraryRatio = totalLibraryGames > 0 ? (stats.wishlistCount / totalLibraryGames) * 100 : 0;
    const isActiveGamer = stats.currentlyPlaying > 0 && stats.totalGames > 5;

    return {
      completionRate: Math.round(completionRate),
      playingToTotalRatio: Math.round(playingToTotalRatio),
      wishlistToLibraryRatio: Math.round(wishlistToLibraryRatio),
      totalLibraryGames,
      isActiveGamer
    };
  }, [stats]);
}