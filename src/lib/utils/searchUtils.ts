/**
 * Search and pattern matching utilities extracted from enhancedSearchService
 */

// Predefined patterns for quick parsing
export const MOOD_KEYWORDS = {
  relaxing: ['relaxing', 'chill', 'calm', 'peaceful', 'zen', 'meditative'],
  challenging: ['challenging', 'difficult', 'hard', 'hardcore', 'demanding', 'punishing'],
  'story-driven': ['story', 'narrative', 'plot', 'cinematic', 'emotional', 'character-driven'],
  multiplayer: ['multiplayer', 'co-op', 'online', 'competitive', 'social', 'team'],
  creative: ['creative', 'building', 'crafting', 'sandbox', 'designing', 'artistic']
} as const;

export const GENRE_KEYWORDS = {
  action: ['action', 'combat', 'fighting', 'shooter', 'fps'],
  rpg: ['rpg', 'role-playing', 'character development', 'leveling'],
  strategy: ['strategy', 'tactical', 'rts', 'turn-based', 'planning'],
  adventure: ['adventure', 'exploration', 'puzzle', 'mystery'],
  simulation: ['simulation', 'sim', 'management', 'tycoon'],
  sports: ['sports', 'racing', 'football', 'soccer', 'basketball'],
  indie: ['indie', 'independent', 'artistic', 'experimental']
} as const;

export const PLATFORM_KEYWORDS = {
  pc: ['pc', 'computer', 'steam', 'windows'],
  playstation: ['playstation', 'ps4', 'ps5', 'sony'],
  xbox: ['xbox', 'microsoft', 'gamepass'],
  nintendo: ['nintendo', 'switch', 'mario'],
  mobile: ['mobile', 'phone', 'android', 'ios']
} as const;

/**
 * Clean query for basic search
 */
export function cleanQuery(query: string): string {
  return query
    .replace(/\b(like|similar to|games|game)\b/gi, '')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Check if query is likely a game name
 */
export function isLikelyGameName(query: string): boolean {
  const trimmed = query.trim();
  // Simple heuristics for game names
  return trimmed.length > 2 && 
         trimmed.length < 50 && 
         !trimmed.includes('games') &&
         !trimmed.includes('like') &&
         !/\b(from|in|before|after|since)\b/i.test(trimmed);
}

/**
 * Check if query contains mood keywords
 */
export function containsMoodKeywords(query: string): boolean {
  return Object.values(MOOD_KEYWORDS).flat().some(keyword => 
    query.includes(keyword)
  );
}

/**
 * Extract mood from query
 */
export function extractMood(query: string): string | null {
  for (const [mood, keywords] of Object.entries(MOOD_KEYWORDS)) {
    if (keywords.some(keyword => query.includes(keyword))) {
      return mood;
    }
  }
  return null;
}

/**
 * Check if query contains genre keywords
 */
export function containsGenreKeywords(query: string): boolean {
  return Object.values(GENRE_KEYWORDS).flat().some(keyword => 
    query.includes(keyword)
  );
}

/**
 * Extract genre from query
 */
export function extractGenre(query: string): string | null {
  for (const [genre, keywords] of Object.entries(GENRE_KEYWORDS)) {
    if (keywords.some(keyword => query.includes(keyword))) {
      return genre;
    }
  }
  return null;
}

/**
 * Check if query contains platform keywords
 */
export function containsPlatformKeywords(query: string): boolean {
  return Object.values(PLATFORM_KEYWORDS).flat().some(keyword => 
    query.includes(keyword)
  );
}

/**
 * Extract platform from query
 */
export function extractPlatform(query: string): string | null {
  for (const [platform, keywords] of Object.entries(PLATFORM_KEYWORDS)) {
    if (keywords.some(keyword => query.includes(keyword))) {
      return platform;
    }
  }
  return null;
}

/**
 * Extract year range from query
 */
export function extractYearRange(query: string): { from?: number; to?: number } | null {
  const yearMatches = query.match(/\b(19|20)\d{2}\b/g);
  if (yearMatches) {
    const years = yearMatches.map(y => parseInt(y)).sort();
    if (years.length === 1) {
      return { from: years[0], to: years[0] };
    } else if (years.length >= 2) {
      return { from: years[0], to: years[years.length - 1] };
    }
  }
  return null;
}

/**
 * Extract rating range from query
 */
export function extractRatingRange(query: string): { min?: number; max?: number } | null {
  // Look for patterns like "rated above 8", "score over 90", etc.
  const ratingPatterns = [
    /(?:rated?|score[ds]?)\s+(?:above|over|more than)\s+(\d+(?:\.\d+)?)/i,
    /(?:rated?|score[ds]?)\s+(?:below|under|less than)\s+(\d+(?:\.\d+)?)/i,
    /(?:rated?|score[ds]?)\s+(\d+(?:\.\d+)?)\s*\+/i,
    /(?:rated?|score[ds]?)\s+(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)/i
  ];

  for (const pattern of ratingPatterns) {
    const match = query.match(pattern);
    if (match) {
      const rating = parseFloat(match[1]);
      if (pattern.source.includes('above|over|more than')) {
        return { min: rating };
      } else if (pattern.source.includes('below|under|less than')) {
        return { max: rating };
      } else if (match[2]) {
        return { min: rating, max: parseFloat(match[2]) };
      } else {
        return { min: rating };
      }
    }
  }
  return null;
}

/**
 * Extract similar game query
 */
export function extractSimilarGameQuery(query: string): string | null {
  const patterns = [
    /(?:like|similar\s+to)\s+(.+?)(?:\s+game[s]?|$)/i,
    /games?\s+(?:like|similar\s+to)\s+(.+)/i
  ];

  for (const pattern of patterns) {
    const match = query.match(pattern);
    if (match) {
      return match[1].trim();
    }
  }
  return null;
}

/**
 * Check if query is a recommendation query
 */
export function isRecommendationQuery(query: string): boolean {
  const recommendationKeywords = [
    'recommend', 'suggest', 'should i play', 'what game', 'good games',
    'best games', 'top games', 'must play'
  ];
  return recommendationKeywords.some(keyword => query.includes(keyword));
}

/**
 * Simple fuzzy matching algorithm
 */
export function fuzzyMatch(text: string, pattern: string): boolean {
  const patternChars = pattern.split('');
  let textIndex = 0;
  
  for (const char of patternChars) {
    const foundIndex = text.indexOf(char, textIndex);
    if (foundIndex === -1) {
      return false;
    }
    textIndex = foundIndex + 1;
  }
  
  return true;
}

/**
 * Advanced fuzzy matching with scoring
 */
export function fuzzyMatchWithScore(text: string, pattern: string): { match: boolean; score: number } {
  const textLower = text.toLowerCase();
  const patternLower = pattern.toLowerCase();
  
  // Exact match gets highest score
  if (textLower.includes(patternLower)) {
    return { match: true, score: 1.0 };
  }
  
  // Character-by-character fuzzy match
  const patternChars = patternLower.split('');
  let textIndex = 0;
  let matchedChars = 0;
  
  for (const char of patternChars) {
    const foundIndex = textLower.indexOf(char, textIndex);
    if (foundIndex !== -1) {
      matchedChars++;
      textIndex = foundIndex + 1;
    }
  }
  
  const score = matchedChars / patternChars.length;
  return { match: score > 0.6, score }; // Require 60% character match
}

/**
 * Highlight matching text in search results
 */
export function highlightMatches(text: string, query: string): string {
  if (!query.trim()) return text;
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

/**
 * Generate search suggestions based on query
 */
export function generateSearchSuggestions(query: string, limit: number = 5): string[] {
  const suggestions: string[] = [];
  const queryLower = query.toLowerCase();
  
  // Add mood-based suggestions
  if (containsMoodKeywords(queryLower)) {
    suggestions.push(
      'relaxing puzzle games',
      'challenging action games',
      'story-driven RPGs'
    );
  }
  
  // Add genre-based suggestions
  if (containsGenreKeywords(queryLower)) {
    suggestions.push(
      'indie action games',
      'turn-based strategy',
      'open-world RPGs'
    );
  }
  
  // Add platform-based suggestions
  if (containsPlatformKeywords(queryLower)) {
    suggestions.push(
      'Steam exclusive games',
      'PlayStation exclusives',
      'Nintendo Switch games'
    );
  }
  
  // Add general suggestions if no specific patterns found
  if (suggestions.length === 0) {
    suggestions.push(
      'popular games',
      'best rated games',
      'new releases',
      'indie games',
      'action games'
    );
  }
  
  return suggestions.slice(0, limit);
}

/**
 * Clean and normalize search query
 */
export function normalizeSearchQuery(query: string): string {
  return query
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters except hyphens
    .replace(/\s+/g, ' '); // Normalize whitespace
}

/**
 * Extract keywords from search query
 */
export function extractKeywords(query: string): string[] {
  const normalized = normalizeSearchQuery(query);
  const words = normalized.split(' ').filter(word => word.length > 2);
  
  // Remove common stop words
  const stopWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'];
  
  return words.filter(word => !stopWords.includes(word));
}