import { useMemo } from 'react';
import { useUserLibrary } from '@/hooks/useUserLibrary';
import { LibraryStats } from '../types';

export function useLibraryData() {
  const { data: library, isLoading, error } = useUserLibrary();
  
  const libraryGames = useMemo(() => library || [], [library]);
  
  const stats = useMemo((): LibraryStats => {
    const total = libraryGames.length;
    const playing = libraryGames.filter(g => g.status === 'playing').length;
    const completed = libraryGames.filter(g => g.status === 'completed').length;
    const backlog = libraryGames.filter(g => g.status === 'backlog').length;
    const wishlist = libraryGames.filter(g => g.status === 'wishlist').length;
    
    const completionRate = total > 0 ? (completed / total) * 100 : 0;
    
    const ratedGames = libraryGames.filter(g => g.personal_rating);
    const averageRating = ratedGames.length > 0 
      ? ratedGames.reduce((sum, g) => sum + (g.personal_rating || 0), 0) / ratedGames.length
      : 0;
    
    const totalHours = libraryGames.reduce((sum, g) => sum + (g.hours_played || 0), 0);
    const averageHoursPerGame = total > 0 ? totalHours / total : 0;
    
    const mostPlayedGame = libraryGames
      .sort((a, b) => (b.hours_played || 0) - (a.hours_played || 0))[0]?.game?.title;
    
    return {
      total,
      playing,
      completed,
      backlog,
      wishlist,
      completionRate,
      averageRating,
      totalHours,
      averageHoursPerGame,
      mostPlayedGame
    };
  }, [libraryGames]);
  
  return {
    libraryGames,
    stats,
    isLoading,
    error
  };
}