import { useMemo, useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { priceTrackingService } from '../lib/priceTrackingService';
import { db } from '@/lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';
import { PriceEntry, PriceAlert } from '@/types/database';

/**
 * Hook for managing price tracking for a specific game
 */
export const usePriceTracking = (gameId: string, gameName: string) => {
  const queryClient = useQueryClient();

  // Get current prices for a game
  const {
    data: currentPricesResponse,
    isLoading: isLoadingPrices,
    error: pricesError,
    refetch: refetchPrices
  } = useQuery({
    queryKey: ['game-prices', gameId],
    queryFn: async () => {
      const { data, error } = await db.priceTracking.getCurrentPrices(gameId);
      if (error) throw error;
      return data;
    },
    enabled: !!gameId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  const currentPrices = currentPricesResponse || [];

  // Get price history for a game
  const {
    data: priceHistory,
    isLoading: isLoadingHistory,
    error: historyError
  } = useQuery({
    queryKey: ['price-history', gameId],
    queryFn: () => priceTrackingService.getPriceHistory(gameId, 30),
    enabled: !!gameId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2
  });

  // Get price comparison across stores
  const {
    data: priceComparison,
    isLoading: isLoadingComparison,
    error: comparisonError
  } = useQuery({
    queryKey: ['price-comparison', gameId],
    queryFn: () => priceTrackingService.getPriceComparison(gameId),
    enabled: !!gameId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  // Update game prices mutation
  const updatePricesMutation = useMutation({
    mutationFn: () => priceTrackingService.updateGamePrices(gameId, gameName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['game-prices', gameId] });
      queryClient.invalidateQueries({ queryKey: ['price-history', gameId] });
      queryClient.invalidateQueries({ queryKey: ['price-comparison', gameId] });
      toast.success('Prices updated successfully!');
    },
    onError: (error) => {
      console.error('Error updating prices:', error);
      toast.error('Failed to update prices. Please try again.');
    }
  });

  // Get lowest price for the game
  const lowestPrice = currentPrices?.reduce((lowest: PriceEntry | null, current: PriceEntry) => {
    return !lowest || current.price < lowest.price ? current : lowest;
  }, null as PriceEntry | null);

  // Get highest price for the game
  const highestPrice = currentPrices?.reduce((highest: PriceEntry | null, current: PriceEntry) => {
    return !highest || current.price > highest.price ? current : highest;
  }, null as PriceEntry | null);

  // Calculate average price
  const averagePrice = currentPrices && currentPrices.length > 0 
    ? currentPrices.reduce((sum: number, price: PriceEntry) => sum + price.price, 0) / currentPrices.length
    : 0;

  // Check if any prices are on sale
  const hasDeals = currentPrices?.some((price: PriceEntry) => {
    return price.isOnSale;
  });

  return {
    // Data
    currentPrices: currentPrices || [],
    priceHistory: priceHistory || [],
    priceComparison: priceComparison || [],
    lowestPrice,
    highestPrice,
    averagePrice,
    hasDeals,
    
    // Loading states
    isLoadingPrices,
    isLoadingHistory,
    isLoadingComparison,
    isUpdatingPrices: updatePricesMutation.isPending,
    
    // Errors
    pricesError,
    historyError,
    comparisonError,
    
    // Actions
    updatePrices: updatePricesMutation.mutate,
    refetchPrices
  };
};

/**
 * Hook for managing price alerts
 */
export const usePriceAlerts = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Get user's price alerts
  const {
    data: alertsResponse,
    isLoading: isLoadingAlerts,
    error: alertsError
  } = useQuery({
    queryKey: ['price-alerts', user?.id],
    queryFn: async () => {
      const { data, error } = await db.priceTracking.getPriceAlerts(user?.id || '');
      if (error) throw error;
      return data;
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  const alerts = alertsResponse || [];

  // Create price alert mutation
  const createAlertMutation = useMutation({
    mutationFn: (alertData: Omit<PriceAlert, 'id'>) => 
      priceTrackingService.createPriceAlert(alertData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['price-alerts', user?.id] });
      toast.success('Price alert created successfully!');
    },
    onError: (error) => {
      console.error('Error creating price alert:', error);
      toast.error('Failed to create price alert. Please try again.');
    }
  });

  // Update price alert mutation
  const updateAlertMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<PriceAlert> }) => {
      const { data, error } = await db.priceTracking.updatePriceAlert(id, updates);
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['price-alerts', user?.id] });
      toast.success('Price alert updated successfully!');
    },
    onError: (error) => {
      console.error('Error updating price alert:', error);
      toast.error('Failed to update price alert. Please try again.');
    }
  });

  // Delete price alert mutation
  const deleteAlertMutation = useMutation({
    mutationFn: async (alertId: string) => {
      const { data, error } = await db.priceTracking.deletePriceAlert(alertId);
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['price-alerts', user?.id] });
      toast.success('Price alert deleted successfully!');
    },
    onError: (error) => {
      console.error('Error deleting price alert:', error);
      toast.error('Failed to delete price alert. Please try again.');
    }
  });

  return {
    // Data
    alerts: alerts || [],
    
    // Loading states
    isLoadingAlerts,
    isCreatingAlert: createAlertMutation.isPending,
    isUpdatingAlert: updateAlertMutation.isPending,
    isDeletingAlert: deleteAlertMutation.isPending,
    
    // Errors
    alertsError,
    
    // Actions
    createAlert: createAlertMutation.mutate,
    updateAlert: updateAlertMutation.mutate,
    deleteAlert: deleteAlertMutation.mutate
  };
};

/**
 * Hook for getting deals and best offers
 */
export const useDeals = () => {
  const { user } = useAuth();

  // Get best deals for user's wishlist
  const {
    data: bestDeals,
    isLoading: isLoadingDeals,
    error: dealsError,
    refetch: refetchDeals
  } = useQuery({
    queryKey: ['best-deals', user?.id],
    queryFn: () => priceTrackingService.getBestDeals(user?.id || '', 20),
    enabled: !!user?.id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2
  });

  // Get wishlist with current prices
  const {
    data: wishlistResponse,
    isLoading: isLoadingWishlist,
    error: wishlistError
  } = useQuery({
    queryKey: ['wishlist-prices', user?.id],
    queryFn: async () => {
      const { data, error } = await db.priceTracking.getWishlistWithPrices(user?.id || '');
      if (error) throw error;
      return data;
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  const wishlistWithPrices = wishlistResponse || [];

  // Calculate deal statistics
  const dealStats = {
    totalDeals: (bestDeals || []).length,
    averageDiscount: bestDeals && bestDeals.length > 0 
      ? bestDeals.reduce((sum, deal) => sum + deal.discountPercent, 0) / bestDeals.length
      : 0,
    biggestDiscount: bestDeals && bestDeals.length > 0 
      ? Math.max(...bestDeals.map(deal => deal.discountPercent))
      : 0,
    totalSavings: (bestDeals || []).reduce((sum, deal) => 
      sum + (deal.originalPrice - deal.currentPrice), 0)
  };

  return {
    // Data
    bestDeals: bestDeals || [],
    wishlistWithPrices: wishlistWithPrices || [],
    dealStats,
    
    // Loading states
    isLoadingDeals,
    isLoadingWishlist,
    
    // Errors
    dealsError,
    wishlistError,
    
    // Actions
    refetchDeals
  };
};

/**
 * Hook for price history and analytics
 */
export const usePriceAnalytics = (gameId: string) => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  
  const days: Record<'7d' | '30d' | '90d' | '1y', number> = {
    '7d': 7,
    '30d': 30,
    '90d': 90,
    '1y': 365
  };

  // Get price history for analytics
  const {
    data: priceHistory,
    isLoading: isLoadingHistory,
    error: historyError
  } = useQuery({
    queryKey: ['price-analytics', gameId, timeRange],
    queryFn: () => priceTrackingService.getPriceHistory(gameId, days[timeRange]),
    enabled: !!gameId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2
  });

  // Calculate analytics from price history
  const analytics = useMemo(() => {
    if (!priceHistory || priceHistory.length === 0) {
      return {
        priceChange: 0,
        priceChangePercent: 0,
        lowestPrice: 0,
        highestPrice: 0,
        averagePrice: 0,
        volatility: 0,
        trendDirection: 'stable' as 'up' | 'down' | 'stable'
      };
    }

    const prices = priceHistory.map(h => h.price);
    const firstPrice = prices[0];
    const lastPrice = prices[prices.length - 1];
    
    const priceChange = lastPrice - firstPrice;
    const priceChangePercent = firstPrice > 0 ? (priceChange / firstPrice) * 100 : 0;
    
    const lowestPrice = Math.min(...prices);
    const highestPrice = Math.max(...prices);
    const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    
    // Calculate volatility (standard deviation)
    const variance = prices.reduce((sum, price) => sum + Math.pow(price - averagePrice, 2), 0) / prices.length;
    const volatility = Math.sqrt(variance);
    
    // Determine trend direction
    const trendDirection = priceChangePercent > 5 ? 'up' : 
                          priceChangePercent < -5 ? 'down' : 'stable';

    return {
      priceChange,
      priceChangePercent,
      lowestPrice,
      highestPrice,
      averagePrice,
      volatility,
      trendDirection
    };
  }, [priceHistory]);

  return {
    // Data
    priceHistory: priceHistory || [],
    analytics,
    timeRange,
    
    // Loading states
    isLoadingHistory,
    
    // Errors
    historyError,
    
    // Actions
    setTimeRange
  };
};

// Helper function to format price
export const formatPrice = (price: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(price);
};

// Helper function to format discount percentage
export const formatDiscount = (discountPercent: number) => {
  return `${discountPercent}% OFF`;
};

// Helper function to get deal badge color
export const getDealBadgeColor = (discountPercent: number) => {
  if (discountPercent >= 75) return 'bg-red-500';
  if (discountPercent >= 50) return 'bg-orange-500';
  if (discountPercent >= 25) return 'bg-yellow-500';
  return 'bg-green-500';
};