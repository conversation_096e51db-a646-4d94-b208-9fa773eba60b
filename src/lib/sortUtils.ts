// Re-export from organized utils
export * from './utils/filterUtils';

import { Game } from '@/types';
import { SortOptions } from '@/components/ui/filters';

export function sortGames(games: Game[], sortOptions: SortOptions): Game[] {
  return [...games].sort((a, b) => {
    let comparison = 0;
    
    switch (sortOptions.field) {
      case 'title': {
        comparison = a.title.localeCompare(b.title);
        break;
      }
      case 'release_date': {
        const dateA = a.release_date ? new Date(a.release_date).getTime() : 0;
        const dateB = b.release_date ? new Date(b.release_date).getTime() : 0;
        comparison = dateA - dateB;
        break;
      }
      case 'metacritic_score': {
        const scoreA = a.metacritic_score || 0;
        const scoreB = b.metacritic_score || 0;
        comparison = scoreA - scoreB;
        break;
      }
      case 'added_at': {
        // This will be used for library/wishlist pages where we have user game data
        // For now, default to title sorting
        comparison = a.title.localeCompare(b.title);
        break;
      }
      default:
        comparison = 0;
    }
    
    return sortOptions.direction === 'asc' ? comparison : -comparison;
  });
}

export function sortUserGames(userGames: unknown[], sortOptions: SortOptions): unknown[] {
  return [...userGames].sort((a, b) => {
    let comparison = 0;
    
    switch (sortOptions.field) {
      case 'title': {
        const titleA = (a as { game?: { title?: string } })?.game?.title || '';
        const titleB = (b as { game?: { title?: string } })?.game?.title || '';
        comparison = titleA.localeCompare(titleB);
        break;
      }
      case 'release_date': {
        const dateA = (a as { game?: { release_date?: string } })?.game?.release_date ? new Date((a as { game: { release_date: string } }).game.release_date).getTime() : 0;
        const dateB = (b as { game?: { release_date?: string } })?.game?.release_date ? new Date((b as { game: { release_date: string } }).game.release_date).getTime() : 0;
        comparison = dateA - dateB;
        break;
      }
      case 'metacritic_score': {
        const scoreA = (a as { game?: { metacritic_score?: number } })?.game?.metacritic_score || 0;
        const scoreB = (b as { game?: { metacritic_score?: number } })?.game?.metacritic_score || 0;
        comparison = scoreA - scoreB;
        break;
      }
      case 'added_at': {
        const addedA = (a as { added_at?: string })?.added_at ? new Date((a as { added_at: string }).added_at).getTime() : 0;
        const addedB = (b as { added_at?: string })?.added_at ? new Date((b as { added_at: string }).added_at).getTime() : 0;
        comparison = addedA - addedB;
        break;
      }
      default:
        comparison = 0;
    }
    
    return sortOptions.direction === 'asc' ? comparison : -comparison;
  });
}