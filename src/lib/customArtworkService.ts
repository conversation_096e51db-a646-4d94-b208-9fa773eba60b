import { supabase } from './supabase';

export type ArtworkType = 'front' | 'back' | 'spine' | '3d' | 'manual' | 'disc';

export interface CustomArtwork {
  id: string;
  user_game_id: string;
  artwork_type: ArtworkType;
  file_url: string;
  file_size?: number;
  file_type?: string;
  is_primary: boolean;
  uploaded_at: string;
  created_at: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadResult {
  success: boolean;
  artwork?: CustomArtwork;
  error?: string;
}

class CustomArtworkService {
  private readonly BUCKET_NAME = 'custom-artwork';
  private readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private readonly ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];

  /**
   * Upload custom artwork for a user game
   */
  async uploadArtwork(
    userGameId: string,
    artworkType: ArtworkType,
    file: File,
    isPrimary: boolean = false,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      // Validate file
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Generate file path
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
      const fileName = `${user.id}/${userGameId}/${artworkType}_${Date.now()}.${fileExtension}`;

      // Upload file to storage
      const { error: uploadError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        return { success: false, error: `Upload failed: ${uploadError.message}` };
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(fileName);

      // Save artwork record to database
      const { data: artworkData, error: dbError } = await supabase
        .from('user_game_artwork')
        .insert({
          user_game_id: userGameId,
          artwork_type: artworkType,
          file_url: publicUrl,
          file_size: file.size,
          file_type: file.type,
          is_primary: isPrimary
        })
        .select()
        .single();

      if (dbError) {
        // Clean up uploaded file if database insert fails
        await supabase.storage.from(this.BUCKET_NAME).remove([fileName]);
        return { success: false, error: `Database error: ${dbError.message}` };
      }

      return { success: true, artwork: artworkData };
    } catch (error) {
      console.error('Upload error:', error);
      return { success: false, error: 'Unexpected error during upload' };
    }
  }

  /**
   * Get all artwork for a user game
   */
  async getArtworkForUserGame(userGameId: string): Promise<CustomArtwork[]> {
    try {
      const { data, error } = await supabase
        .from('user_game_artwork')
        .select('*')
        .eq('user_game_id', userGameId)
        .order('artwork_type', { ascending: true })
        .order('is_primary', { ascending: false });

      if (error) {
        console.error('Error fetching artwork:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching artwork:', error);
      return [];
    }
  }

  /**
   * Get primary artwork for a specific type
   */
  async getPrimaryArtwork(userGameId: string, artworkType: ArtworkType): Promise<CustomArtwork | null> {
    try {
      const { data, error } = await supabase
        .from('user_game_artwork')
        .select('*')
        .eq('user_game_id', userGameId)
        .eq('artwork_type', artworkType)
        .eq('is_primary', true)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        console.error('Error fetching primary artwork:', error);
        return null;
      }

      return data || null;
    } catch (error) {
      console.error('Error fetching primary artwork:', error);
      return null;
    }
  }

  /**
   * Set artwork as primary for its type
   */
  async setPrimaryArtwork(artworkId: string): Promise<boolean> {
    try {
      // First get the artwork details to know which user_game_id and artwork_type we're working with
      const { data: artwork, error: fetchError } = await supabase
        .from('user_game_artwork')
        .select('user_game_id, artwork_type')
        .eq('id', artworkId)
        .single();

      if (fetchError || !artwork) {
        console.error('Error fetching artwork details:', fetchError);
        return false;
      }

      // Use a transaction to ensure atomicity
      const { error } = await supabase.rpc('set_primary_artwork', {
        artwork_id: artworkId,
        user_game_id: artwork.user_game_id,
        artwork_type: artwork.artwork_type
      });

      if (error) {
        console.error('Error setting primary artwork:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error setting primary artwork:', error);
      return false;
    }
  }

  /**
   * Unset artwork as primary (remove primary status)
   */
  async unsetPrimaryArtwork(artworkId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_game_artwork')
        .update({ is_primary: false })
        .eq('id', artworkId)
        .eq('is_primary', true); // Only update if it's currently primary

      if (error) {
        console.error('Error unsetting primary artwork:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error unsetting primary artwork:', error);
      return false;
    }
  }

  /**
   * Toggle primary status of artwork
   */
  async togglePrimaryArtwork(artworkId: string): Promise<boolean> {
    try {
      // First check if the artwork is currently primary
      const { data: artwork, error: fetchError } = await supabase
        .from('user_game_artwork')
        .select('is_primary, user_game_id, artwork_type')
        .eq('id', artworkId)
        .single();

      if (fetchError || !artwork) {
        console.error('Error fetching artwork details:', fetchError);
        return false;
      }

      if (artwork.is_primary) {
        // If currently primary, unset it
        return await this.unsetPrimaryArtwork(artworkId);
      } else {
        // If not primary, set it as primary
        return await this.setPrimaryArtwork(artworkId);
      }
    } catch (error) {
      console.error('Error toggling primary artwork:', error);
      return false;
    }
  }

  /**
   * Delete artwork
   */
  async deleteArtwork(artworkId: string): Promise<boolean> {
    try {
      // Get artwork details first
      const { data: artwork, error: fetchError } = await supabase
        .from('user_game_artwork')
        .select('*')
        .eq('id', artworkId)
        .single();

      if (fetchError || !artwork) {
        console.error('Error fetching artwork for deletion:', fetchError);
        return false;
      }

      // Extract file path from URL
      const url = new URL(artwork.file_url);
      const filePath = url.pathname.split('/storage/v1/object/public/custom-artwork/')[1];

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);

      if (storageError) {
        console.error('Error deleting file from storage:', storageError);
        // Continue with database deletion even if storage deletion fails
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('user_game_artwork')
        .delete()
        .eq('id', artworkId);

      if (dbError) {
        console.error('Error deleting artwork from database:', dbError);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting artwork:', error);
      return false;
    }
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File): { valid: boolean; error?: string } {
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Please upload JPEG, PNG, or WebP images only.'
      };
    }

    if (file.size > this.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: 'File size too large. Maximum size is 5MB.'
      };
    }

    return { valid: true };
  }

  /**
   * Get artwork types with display names
   */
  getArtworkTypes(): Array<{ value: ArtworkType; label: string; description: string }> {
    return [
      { value: 'front', label: 'Front Cover', description: 'Game box front cover' },
      { value: 'back', label: 'Back Cover', description: 'Game box back cover with screenshots' },
      { value: 'spine', label: 'Spine', description: 'Game box spine/side view' },
      { value: '3d', label: '3D View', description: 'Complete box view for 3D display' },
      { value: 'manual', label: 'Manual', description: 'Game manual or instruction booklet' },
      { value: 'disc', label: 'Disc/Cartridge', description: 'Game disc or cartridge artwork' }
    ];
  }

  /**
   * Upload artwork from URL (for Google Images search integration)
   */
  async uploadFromUrl(
    userGameId: string,
    artworkType: ArtworkType,
    imageUrl: string,
    title: string,
    isPrimary: boolean = false,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Notify progress start
      onProgress?.({ loaded: 0, total: 100, percentage: 0 });

      // Fetch image from URL
      onProgress?.({ loaded: 20, total: 100, percentage: 20 });
      const response = await fetch(imageUrl);
      if (!response.ok) {
        return { success: false, error: 'Failed to fetch image from URL' };
      }

      onProgress?.({ loaded: 40, total: 100, percentage: 40 });
      const blob = await response.blob();

      // Validate file type and size
      if (!this.ALLOWED_TYPES.includes(blob.type)) {
        return { success: false, error: 'Invalid image format. Only JPEG, PNG, and WebP are supported.' };
      }

      if (blob.size > this.MAX_FILE_SIZE) {
        return { success: false, error: 'Image is too large. Maximum size is 5MB.' };
      }

      onProgress?.({ loaded: 60, total: 100, percentage: 60 });

      // Create file from blob
      const sanitizedTitle = title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
      const fileExtension = blob.type.split('/')[1];
      const fileName = `${user.id}/${userGameId}/${artworkType}_${sanitizedTitle}_${Date.now()}.${fileExtension}`;

      // Upload file to storage
      onProgress?.({ loaded: 80, total: 100, percentage: 80 });
      const { error: uploadError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, blob, {
          cacheControl: '3600',
          upsert: false,
          contentType: blob.type
        });

      if (uploadError) {
        return { success: false, error: `Upload failed: ${uploadError.message}` };
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(fileName);

      onProgress?.({ loaded: 90, total: 100, percentage: 90 });

      // Save artwork record to database
      const { data: artworkData, error: dbError } = await supabase
        .from('user_game_artwork')
        .insert({
          user_game_id: userGameId,
          artwork_type: artworkType,
          file_url: publicUrl,
          file_size: blob.size,
          file_type: blob.type,
          is_primary: isPrimary
        })
        .select()
        .single();

      if (dbError) {
        // Clean up uploaded file if database insert fails
        await supabase.storage.from(this.BUCKET_NAME).remove([fileName]);
        return { success: false, error: `Database error: ${dbError.message}` };
      }

      onProgress?.({ loaded: 100, total: 100, percentage: 100 });
      return { success: true, artwork: artworkData };
    } catch (error) {
      console.error('Upload from URL error:', error);
      return { success: false, error: 'Unexpected error during upload from URL' };
    }
  }

  /**
   * Optimize image for upload (resize, compress)
   */
  async optimizeImage(file: File, maxWidth: number = 1200, quality: number = 0.85): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
        const newWidth = img.width * ratio;
        const newHeight = img.height * ratio;

        canvas.width = newWidth;
        canvas.height = newHeight;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, newWidth, newHeight);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const optimizedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              });
              resolve(optimizedFile);
            } else {
              resolve(file); // Fallback to original file
            }
          },
          'image/jpeg',
          quality
        );
      };

      img.onerror = () => resolve(file); // Fallback to original file
      img.src = URL.createObjectURL(file);
    });
  }
}

export const customArtworkService = new CustomArtworkService();