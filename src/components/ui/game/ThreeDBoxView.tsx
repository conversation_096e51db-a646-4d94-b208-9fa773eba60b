import React, { useState, useRef, useEffect } from 'react';
import { Card } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { RotateCcw, Move3D, ZoomIn, ZoomOut } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ThreeDBoxViewProps {
  frontImage?: string;
  backImage?: string;
  spineImage?: string;
  title: string;
  className?: string;
  width?: number;
  height?: number;
  autoRotate?: boolean;
}

export const ThreeDBoxView: React.FC<ThreeDBoxViewProps> = ({
  frontImage,
  backImage,
  spineImage,
  title,
  className = '',
  width = 300,
  height = 400,
  autoRotate = false
}) => {
  const [rotationX, setRotationX] = useState(0);
  const [rotationY, setRotationY] = useState(0);
  const [scale, setScale] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-rotation effect
  useEffect(() => {
    if (!autoRotate) return;

    const interval = setInterval(() => {
      setRotationY(prev => (prev + 1) % 360);
    }, 50);

    return () => clearInterval(interval);
  }, [autoRotate]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setLastMousePos({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;

    const deltaX = e.clientX - lastMousePos.x;
    const deltaY = e.clientY - lastMousePos.y;

    setRotationY(prev => prev + deltaX * 0.5);
    setRotationX(prev => Math.max(-90, Math.min(90, prev - deltaY * 0.5)));
    setLastMousePos({ x: e.clientX, y: e.clientY });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const resetView = () => {
    setRotationX(0);
    setRotationY(0);
    setScale(1);
  };

  const zoomIn = () => {
    setScale(prev => Math.min(2, prev + 0.1));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(0.5, prev - 0.1));
  };

  // Calculate which face is most visible based on rotation
  const getVisibleFace = () => {
    const normalizedY = ((rotationY % 360) + 360) % 360;
    if (normalizedY < 45 || normalizedY >= 315) return 'front';
    if (normalizedY >= 45 && normalizedY < 135) return 'spine';
    if (normalizedY >= 135 && normalizedY < 225) return 'back';
    return 'spine';
  };

  const visibleFace = getVisibleFace();

  // Box dimensions for 3D effect
  const boxWidth = width * 0.8;
  const boxHeight = height * 0.9;
  const boxDepth = width * 0.15;

  return (
    <Card className={cn('relative overflow-hidden bg-gradient-to-br from-muted/20 to-muted/40', className)}>
      <div className="p-4">
        {/* Controls */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              <Move3D className="h-3 w-3 mr-1" />
              3D View
            </Badge>
            <Badge variant="secondary" className="text-xs capitalize">
              {visibleFace}
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={zoomOut}
              className="h-7 w-7 p-0"
              title="Zoom Out"
            >
              <ZoomOut className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={zoomIn}
              className="h-7 w-7 p-0"
              title="Zoom In"
            >
              <ZoomIn className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={resetView}
              className="h-7 w-7 p-0"
              title="Reset View"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* 3D Box Container */}
        <div 
          className="relative mx-auto cursor-grab active:cursor-grabbing"
          style={{ 
            width: `${width}px`, 
            height: `${height}px`,
            perspective: '1000px'
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          ref={containerRef}
        >
          {/* 3D Box */}
          <div
            className="absolute inset-0 transition-transform duration-200 ease-out preserve-3d"
            style={{
              transform: `
                scale(${scale})
                rotateX(${rotationX}deg) 
                rotateY(${rotationY}deg)
                translateZ(${boxDepth / 2}px)
              `,
              transformStyle: 'preserve-3d'
            }}
          >
            {/* Front Face */}
            <div
              className="absolute inset-0 bg-muted border border-border/50 rounded-lg overflow-hidden shadow-lg"
              style={{
                width: `${boxWidth}px`,
                height: `${boxHeight}px`,
                left: '50%',
                top: '50%',
                marginLeft: `${-boxWidth / 2}px`,
                marginTop: `${-boxHeight / 2}px`,
                transform: `translateZ(${boxDepth / 2}px)`,
                backfaceVisibility: 'hidden'
              }}
            >
              {frontImage ? (
                <img
                  src={frontImage}
                  alt={`${title} front cover`}
                  className="w-full h-full object-cover"
                  draggable={false}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-muted to-muted/80">
                  <span className="text-muted-foreground text-sm">Front</span>
                </div>
              )}
            </div>

            {/* Back Face */}
            <div
              className="absolute inset-0 bg-muted border border-border/50 rounded-lg overflow-hidden shadow-lg"
              style={{
                width: `${boxWidth}px`,
                height: `${boxHeight}px`,
                left: '50%',
                top: '50%',
                marginLeft: `${-boxWidth / 2}px`,
                marginTop: `${-boxHeight / 2}px`,
                transform: `translateZ(${-boxDepth / 2}px) rotateY(180deg)`,
                backfaceVisibility: 'hidden'
              }}
            >
              {backImage ? (
                <img
                  src={backImage}
                  alt={`${title} back cover`}
                  className="w-full h-full object-cover"
                  draggable={false}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-muted to-muted/80">
                  <span className="text-muted-foreground text-sm">Back</span>
                </div>
              )}
            </div>

            {/* Right Spine */}
            <div
              className="absolute bg-muted border border-border/50 overflow-hidden shadow-lg"
              style={{
                width: `${boxDepth}px`,
                height: `${boxHeight}px`,
                left: '50%',
                top: '50%',
                marginLeft: `${boxWidth / 2}px`,
                marginTop: `${-boxHeight / 2}px`,
                transform: `rotateY(90deg) translateZ(${boxDepth / 2}px)`,
                backfaceVisibility: 'hidden'
              }}
            >
              {spineImage ? (
                <img
                  src={spineImage}
                  alt={`${title} spine`}
                  className="w-full h-full object-cover"
                  draggable={false}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-muted/80 to-muted">
                  <span 
                    className="text-muted-foreground text-xs transform -rotate-90 whitespace-nowrap truncate"
                    style={{ maxWidth: `${boxHeight - 20}px` }}
                  >
                    {title}
                  </span>
                </div>
              )}
            </div>

            {/* Left Spine */}
            <div
              className="absolute bg-muted border border-border/50 overflow-hidden shadow-lg"
              style={{
                width: `${boxDepth}px`,
                height: `${boxHeight}px`,
                left: '50%',
                top: '50%',
                marginLeft: `${-boxWidth / 2 - boxDepth}px`,
                marginTop: `${-boxHeight / 2}px`,
                transform: `rotateY(-90deg) translateZ(${boxDepth / 2}px)`,
                backfaceVisibility: 'hidden'
              }}
            >
              <div className="w-full h-full bg-gradient-to-br from-muted/60 to-muted/40" />
            </div>

            {/* Top Face */}
            <div
              className="absolute bg-muted border border-border/50 overflow-hidden shadow-lg"
              style={{
                width: `${boxWidth}px`,
                height: `${boxDepth}px`,
                left: '50%',
                top: '50%',
                marginLeft: `${-boxWidth / 2}px`,
                marginTop: `${-boxHeight / 2 - boxDepth}px`,
                transform: `rotateX(90deg) translateZ(${boxDepth / 2}px)`,
                backfaceVisibility: 'hidden'
              }}
            >
              <div className="w-full h-full bg-gradient-to-br from-muted/80 to-muted/60" />
            </div>

            {/* Bottom Face */}
            <div
              className="absolute bg-muted border border-border/50 overflow-hidden shadow-lg"
              style={{
                width: `${boxWidth}px`,
                height: `${boxDepth}px`,
                left: '50%',
                top: '50%',
                marginLeft: `${-boxWidth / 2}px`,
                marginTop: `${boxHeight / 2}px`,
                transform: `rotateX(-90deg) translateZ(${boxDepth / 2}px)`,
                backfaceVisibility: 'hidden'
              }}
            >
              <div className="w-full h-full bg-gradient-to-br from-muted/40 to-muted/20" />
            </div>
          </div>

          {/* Shadow */}
          <div
            className="absolute inset-x-0 bottom-0 bg-black/20 rounded-full"
            style={{
              height: '20px',
              transform: `scale(${scale * 0.8}) translateY(${height * 0.4}px)`,
              opacity: Math.max(0.2, 1 - scale * 0.3)
            }}
          />
        </div>

        {/* Instructions */}
        <div className="mt-4 text-center">
          <p className="text-xs text-muted-foreground">
            Click and drag to rotate • Use controls to zoom and reset
          </p>
        </div>
      </div>

      {/* CSS for 3D transforms */}
      <style jsx>{`
        .preserve-3d {
          transform-style: preserve-3d;
        }
      `}</style>
    </Card>
  );
};

export default ThreeDBoxView;