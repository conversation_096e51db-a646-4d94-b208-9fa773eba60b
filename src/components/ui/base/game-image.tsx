import React from 'react';
import { cn } from '@/lib/utils';
import { useImageWithFallback } from '@/hooks/useImageWithFallback';

interface GameImageProps {
  src?: string | null;
  alt: string;
  className?: string;
  gameName?: string;
  type?: 'cover' | 'thumbnail' | 'screenshot';
  aspectRatio?: 'cover' | 'square' | 'wide';
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

export const GameImage: React.FC<GameImageProps> = ({
  src,
  alt,
  className,
  gameName,
  type = 'cover',
  aspectRatio = 'cover',
  loading = 'lazy',
  onLoad,
  onError
}) => {
  const {
    imageSrc,
    isLoading,
    hasError,
    handleLoad,
    handleError
  } = useImageWithFallback({
    src,
    fallbackText: alt,
    type,
    gameName
  });

  const handleImageLoad = () => {
    handleLoad();
    onLoad?.();
  };

  const handleImageError = () => {
    handleError();
    onError?.();
  };

  const aspectClasses = {
    cover: 'aspect-[2/3]',
    square: 'aspect-square',
    wide: 'aspect-video'
  };

  return (
    <div className={cn('relative overflow-hidden', aspectClasses[aspectRatio])}>
      {/* Loading skeleton */}
      {isLoading && !hasError && src && (
        <div className="absolute inset-0 bg-gradient-to-br from-muted/50 to-muted/80 animate-pulse" />
      )}
      
      {/* Image */}
      <img
        src={imageSrc}
        alt={alt}
        className={cn(
          'w-full h-full object-cover transition-opacity duration-200',
          type === 'cover' && 'game-cover', // Add class for global handler identification
          isLoading && !hasError && src ? 'opacity-0' : 'opacity-100',
          className
        )}
        loading={loading}
        decoding="async"
        onLoad={handleImageLoad}
        onError={handleImageError}
      />
    </div>
  );
};

export default GameImage;