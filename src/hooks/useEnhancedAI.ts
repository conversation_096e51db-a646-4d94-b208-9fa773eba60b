import { useState, useCallback, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  enhancedAIService, 
  BoxArtResult,
  AIProvider 
} from '@/lib/enhancedAIService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';

/**
 * Hook for managing AI conversations
 */
export const useAIConversations = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Get user conversations
  const {
    data: conversations = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['ai-conversations', user?.id],
    queryFn: () => enhancedAIService.getUserConversations(user?.id || ''),
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create new conversation
  const createConversationMutation = useMutation({
    mutationFn: (title?: string) => 
      enhancedAIService.createConversation(user?.id || '', title),
    onSuccess: (conversationId) => {
      // Invalidate conversations to refresh the list
      queryClient.invalidateQueries({ queryKey: ['ai-conversations', user?.id] });
      toast.success('New conversation created');
      return conversationId;
    },
    onError: (error) => {
      console.error('Error creating conversation:', error);
      toast.error('Failed to create conversation');
    }
  });

  // Delete conversation
  const deleteConversationMutation = useMutation({
    mutationFn: (conversationId: string) => 
      enhancedAIService.deleteConversation(conversationId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ai-conversations', user?.id] });
      toast.success('Conversation deleted');
    },
    onError: (error) => {
      console.error('Error deleting conversation:', error);
      toast.error('Failed to delete conversation');
    }
  });

  const createConversation = useCallback(async (title?: string) => {
    if (!user?.id) {
      toast.error('Please log in to create conversations');
      return null;
    }
    return createConversationMutation.mutateAsync(title);
  }, [user?.id, createConversationMutation]);

  const deleteConversation = useCallback((conversationId: string) => {
    return deleteConversationMutation.mutate(conversationId);
  }, [deleteConversationMutation]);

  return {
    conversations,
    isLoading,
    error,
    refetch,
    createConversation,
    deleteConversation,
    isCreating: createConversationMutation.isPending,
    isDeleting: deleteConversationMutation.isPending
  };
};

/**
 * Hook for managing a specific conversation
 */
export const useAIConversation = (conversationId: string | null) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isStreaming, setIsStreaming] = useState(false);

  // Get conversation details
  const {
    data: conversation,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['ai-conversation', conversationId],
    queryFn: () => enhancedAIService.getConversation(conversationId!),
    enabled: !!conversationId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: ({ 
      message, 
      context 
    }: { 
      message: string; 
      context?: { 
        gameContext?: string; 
        searchQuery?: string; 
        boxArtSearch?: boolean; 
      } 
    }) => enhancedAIService.sendChatMessage(conversationId!, message, context),
    onMutate: () => {
      setIsStreaming(true);
    },
    onSuccess: () => {
      // Refresh the conversation to get updated messages
      queryClient.invalidateQueries({ queryKey: ['ai-conversation', conversationId] });
      // Also refresh the conversations list to update timestamps
      queryClient.invalidateQueries({ queryKey: ['ai-conversations', user?.id] });
    },
    onError: (error) => {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    },
    onSettled: () => {
      setIsStreaming(false);
    }
  });

  const sendMessage = useCallback((
    message: string, 
    context?: { 
      gameContext?: string; 
      searchQuery?: string; 
      boxArtSearch?: boolean; 
    }
  ) => {
    if (!conversationId) {
      toast.error('No conversation selected');
      return;
    }
    if (!user?.id) {
      toast.error('Please log in to send messages');
      return;
    }
    return sendMessageMutation.mutate({ message, context });
  }, [conversationId, user?.id, sendMessageMutation]);

  return {
    conversation,
    messages: conversation?.messages || [],
    isLoading,
    isSending: sendMessageMutation.isPending || isStreaming,
    error,
    refetch,
    sendMessage
  };
};

/**
 * Hook for box art search functionality
 */
export const useBoxArtSearch = () => {
  const [isSearching, setIsSearching] = useState(false);
  const [lastSearchResults, setLastSearchResults] = useState<BoxArtResult[]>([]);

  const searchBoxArt = useCallback(async (gameName: string): Promise<BoxArtResult[]> => {
    if (!gameName.trim()) {
      toast.error('Please provide a game name');
      return [];
    }

    setIsSearching(true);
    try {
      const results = await enhancedAIService.searchGameBoxArt(gameName);
      setLastSearchResults(results);
      
      if (results.length === 0) {
        toast.error(`No box art found for "${gameName}"`);
      } else {
        toast.success(`Found ${results.length} box art results for "${gameName}"`);
      }
      
      return results;
    } catch (error) {
      console.error('Error searching for box art:', error);
      toast.error('Failed to search for box art');
      return [];
    } finally {
      setIsSearching(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setLastSearchResults([]);
  }, []);

  return {
    searchBoxArt,
    isSearching,
    lastSearchResults,
    clearResults
  };
};

/**
 * Hook for AI provider management
 */
export const useAIProvider = () => {
  const [currentProvider, setCurrentProvider] = useState<{ 
    provider: AIProvider; 
    model: string 
  } | null>(null);

  useEffect(() => {
    try {
      const provider = enhancedAIService.getCurrentProvider();
      setCurrentProvider(provider);
    } catch (error) {
      console.error('Error getting AI provider:', error);
      setCurrentProvider(null);
    }
  }, []);

  const switchProvider = useCallback((provider: AIProvider) => {
    try {
      enhancedAIService.setProvider(provider);
      const newProvider = enhancedAIService.getCurrentProvider();
      setCurrentProvider(newProvider);
      toast.success(`Switched to ${provider.toUpperCase()}`);
    } catch (error) {
      console.error('Error switching provider:', error);
      toast.error(`Failed to switch to ${provider.toUpperCase()}`);
    }
  }, []);

  return {
    currentProvider,
    switchProvider
  };
};

/**
 * Comprehensive hook that combines all AI functionality
 */
export const useEnhancedAI = () => {
  const conversations = useAIConversations();
  const boxArtSearch = useBoxArtSearch();
  const provider = useAIProvider();
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  
  const currentConversation = useAIConversation(currentConversationId);

  const startNewConversation = useCallback(async (title?: string) => {
    const conversationId = await conversations.createConversation(title);
    if (conversationId) {
      setCurrentConversationId(conversationId);
    }
    return conversationId;
  }, [conversations]);

  const selectConversation = useCallback((conversationId: string) => {
    setCurrentConversationId(conversationId);
  }, []);

  const deleteCurrentConversation = useCallback(() => {
    if (currentConversationId) {
      conversations.deleteConversation(currentConversationId);
      setCurrentConversationId(null);
    }
  }, [currentConversationId, conversations]);

  const sendMessageWithBoxArt = useCallback(async (message: string) => {
    // Check if this is a box art request
    const isBoxArtRequest = /(?:find|search|get|show).*(?:box art|cover|artwork|image)/i.test(message);
    
    if (isBoxArtRequest) {
      return currentConversation.sendMessage(message, { boxArtSearch: true });
    } else {
      return currentConversation.sendMessage(message);
    }
  }, [currentConversation]);

  return {
    // Conversations
    conversations: conversations.conversations,
    currentConversation: currentConversation.conversation,
    currentMessages: currentConversation.messages,
    isLoadingConversations: conversations.isLoading,
    isSendingMessage: currentConversation.isSending,
    
    // Actions
    startNewConversation,
    selectConversation,
    deleteCurrentConversation,
    sendMessage: sendMessageWithBoxArt,
    
    // Box art search
    searchBoxArt: boxArtSearch.searchBoxArt,
    isSearchingBoxArt: boxArtSearch.isSearching,
    boxArtResults: boxArtSearch.lastSearchResults,
    
    // Provider
    currentProvider: provider.currentProvider,
    switchProvider: provider.switchProvider,
    
    // Status
    isReady: !!provider.currentProvider
  };
};