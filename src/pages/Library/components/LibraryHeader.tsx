import { Badge } from '@/components/ui/base';

export function LibraryHeader({ gameCount }: { gameCount: number }) {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold">My Library</h1>
        <p className="text-muted-foreground">Track your gaming collection</p>
      </div>
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
        <Badge variant="outline" className="bg-primary/10 w-fit">
          {gameCount} games
        </Badge>
      </div>
    </div>
  );
}