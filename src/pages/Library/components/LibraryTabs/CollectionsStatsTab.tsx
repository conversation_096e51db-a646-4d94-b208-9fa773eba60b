import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/base';
import { UserGameWithDetails } from '@/types/database';
import { LibraryStats as LibraryStatsType } from '../../types';
import { CollectionsTab } from './CollectionsTab';
import { StatsTab } from './StatsTab';
import { 
  FolderPlus, 
  BarChart3 
} from '@/lib/icons';

interface CollectionsStatsTabProps {
  libraryGames: UserGameWithDetails[];
  isLoading: boolean;
  error: Error | null;
  stats: LibraryStatsType;
  onCollectionCreate: (collection: Record<string, unknown>) => void;
  onCollectionUpdate: (id: string, updates: Record<string, unknown>) => void;
  onCollectionDelete: (id: string) => void;
  onCollectionDuplicate: (id: string) => void;
}

export function CollectionsStatsTab({ 
  libraryGames, 
  isLoading, 
  error, 
  stats,
  onCollectionCreate,
  onCollectionUpdate,
  onCollectionDelete,
  onCollectionDuplicate
}: CollectionsStatsTabProps) {
  const [activeSubTab, setActiveSubTab] = useState('collections');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderPlus className="h-5 w-5" />
            Collections & Statistics
          </CardTitle>
          <CardDescription>
            Organize your games into collections and view detailed library statistics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeSubTab} onValueChange={setActiveSubTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="collections" className="flex items-center gap-2">
                <FolderPlus className="h-4 w-4" />
                Collections
              </TabsTrigger>
              <TabsTrigger value="stats" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Statistics
              </TabsTrigger>
            </TabsList>

            <TabsContent value="collections" className="mt-6">
              <CollectionsTab
                libraryGames={libraryGames}
                isLoading={isLoading}
                error={error}
                onCollectionCreate={onCollectionCreate}
                onCollectionUpdate={onCollectionUpdate}
                onCollectionDelete={onCollectionDelete}
                onCollectionDuplicate={onCollectionDuplicate}
              />
            </TabsContent>

            <TabsContent value="stats" className="mt-6">
              <StatsTab
                libraryGames={libraryGames}
                isLoading={isLoading}
                error={error}
                stats={stats}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}