// Deno global type declarations
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
  serve(handler: (req: Request) => Promise<Response> | Response): void;
};

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, client-id, content-type, apikey, x-client-info",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

// Cache for access token and rate limiting
let accessToken: string | null = null;
let tokenExpiry: number = 0;

// Response cache for performance optimization
const responseCache = new Map<string, { data: unknown; expiry: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

// Rate limiting tracking
const rateLimitMap = new Map<string, { count: number; windowStart: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100; // Max requests per minute per IP

// Security monitoring
const securityLog = new Map<string, { attempts: number; lastAttempt: number }>();
const MAX_FAILED_ATTEMPTS = 5;
const SECURITY_WINDOW = 15 * 60 * 1000; // 15 minutes

function getRateLimitKey(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  return forwarded || realIp || 'unknown';
}

function isRateLimited(ip: string): boolean {
  const now = Date.now();
  const current = rateLimitMap.get(ip);
  
  if (!current || now - current.windowStart > RATE_LIMIT_WINDOW) {
    rateLimitMap.set(ip, { count: 1, windowStart: now });
    return false;
  }
  
  if (current.count >= RATE_LIMIT_MAX_REQUESTS) {
    return true;
  }
  
  current.count++;
  return false;
}

// Cache utility functions
function getCacheKey(endpoint: string, query: string): string {
  // Create a simple hash for the cache key
  const input = `${endpoint}:${query}`;
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

function getCachedResponse(cacheKey: string): unknown | null {
  const cached = responseCache.get(cacheKey);
  if (!cached) return null;
  
  const now = Date.now();
  if (now > cached.expiry) {
    responseCache.delete(cacheKey);
    return null;
  }
  
  return cached.data;
}

function setCachedResponse(cacheKey: string, data: unknown): void {
  const expiry = Date.now() + CACHE_TTL;
  responseCache.set(cacheKey, { data, expiry });
  
  // Clean up expired entries if cache gets too large
  if (responseCache.size > 1000) {
    const now = Date.now();
    for (const [key, value] of responseCache.entries()) {
      if (now > value.expiry) {
        responseCache.delete(key);
      }
    }
  }
}

function logSecurityEvent(ip: string, event: string, details: Record<string, unknown>) {
  const securityDetails = {
    timestamp: new Date().toISOString(),
    ip,
    event,
    details,
    userAgent: details.userAgent || 'unknown'
  };
  
  console.warn('🔒 Security Event:', securityDetails);
  
  // Track failed attempts
  if (event === 'failed_auth' || event === 'invalid_request') {
    const current = securityLog.get(ip) || { attempts: 0, lastAttempt: 0 };
    current.attempts++;
    current.lastAttempt = Date.now();
    securityLog.set(ip, current);
  }
}

function isSecurityBlocked(ip: string): boolean {
  const current = securityLog.get(ip);
  if (!current) return false;
  
  const now = Date.now();
  if (now - current.lastAttempt > SECURITY_WINDOW) {
    securityLog.delete(ip);
    return false;
  }
  
  return current.attempts >= MAX_FAILED_ATTEMPTS;
}

async function getAccessToken(): Promise<string> {
  const clientId = Deno.env.get("IGDB_CLIENT_ID");
  const clientSecret = Deno.env.get("IGDB_CLIENT_SECRET");

  if (!clientId || !clientSecret) {
    throw new Error("IGDB credentials not configured in environment variables");
  }

  // Return cached token if still valid (with 5 minute buffer)
  if (accessToken && Date.now() < tokenExpiry - 300000) {
    return accessToken;
  }

  console.log("Getting new IGDB access token...");
  
  try {
    const response = await fetch("https://id.twitch.tv/oauth2/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: "client_credentials",
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Token request failed:", response.status, errorText);
      throw new Error(`Failed to get access token: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    accessToken = data.access_token;
    tokenExpiry = Date.now() + (data.expires_in * 1000);

    console.log("✅ IGDB access token obtained successfully");
    return accessToken!;
  } catch (error) {
    console.error("❌ Error getting IGDB access token:", error);
    throw new Error(`Authentication failed: ${error.message}`);
  }
}

Deno.serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  const ip = getRateLimitKey(req);
  const userAgent = req.headers.get('user-agent') || 'unknown';
  
  try {
    // Security checks
    if (isSecurityBlocked(ip)) {
      logSecurityEvent(ip, 'blocked_request', { userAgent, reason: 'too_many_failed_attempts' });
      return new Response(JSON.stringify({ error: "Access temporarily blocked due to security concerns" }), {
        status: 429,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Rate limiting
    if (isRateLimited(ip)) {
      logSecurityEvent(ip, 'rate_limited', { userAgent });
      return new Response(JSON.stringify({ error: "Rate limit exceeded. Please try again later." }), {
        status: 429,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Validate request body
    let requestBody;
    try {
      requestBody = await req.json();
    } catch {
      logSecurityEvent(ip, 'invalid_request', { userAgent, error: 'invalid_json' });
      return new Response(JSON.stringify({ error: "Invalid JSON in request body" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const { endpoint, query } = requestBody;
    console.log("✅ Received valid request for endpoint:", endpoint);
    console.log("Query sample:", query?.substring(0, 100) + "...");

    // Input validation with security checks
    if (!endpoint || typeof endpoint !== 'string') {
      logSecurityEvent(ip, 'invalid_request', { userAgent, error: 'missing_endpoint' });
      return new Response(JSON.stringify({ error: "Valid endpoint required in request body" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    if (!query || typeof query !== 'string') {
      logSecurityEvent(ip, 'invalid_request', { userAgent, error: 'missing_query' });
      return new Response(JSON.stringify({ error: "Valid query required in request body" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Validate endpoint against whitelist
    const allowedEndpoints = ['games', 'platforms', 'genres', 'companies', 'characters'];
    if (!allowedEndpoints.includes(endpoint)) {
      logSecurityEvent(ip, 'invalid_request', { userAgent, error: 'invalid_endpoint', endpoint });
      return new Response(JSON.stringify({ error: "Invalid endpoint requested" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Query length validation
    if (query.length > 1000) {
      logSecurityEvent(ip, 'invalid_request', { userAgent, error: 'query_too_long' });
      return new Response(JSON.stringify({ error: "Query too long" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Check cache first for performance
    const cacheKey = getCacheKey(endpoint, query);
    const cachedData = getCachedResponse(cacheKey);
    
    if (cachedData) {
      console.log("✅ Cache hit for query, returning cached response");
      return new Response(JSON.stringify(cachedData), {
        status: 200,
        headers: { 
          ...corsHeaders, 
          "Content-Type": "application/json",
          "X-Cache": "HIT"
        },
      });
    }

    // Get access token from environment (server-side)
    const token = await getAccessToken();
    const clientId = Deno.env.get("IGDB_CLIENT_ID");

    console.log("Making request to IGDB API...");
    console.log("Client ID:", clientId?.substring(0, 8) + "...");
    console.log("Token present:", !!token);

    const igdbResponse = await fetch(`https://api.igdb.com/v4/${endpoint}`, {
      method: "POST",
      headers: {
        "Client-ID": clientId!,
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json",
        "Content-Type": "text/plain",
      },
      body: query,
    });

    console.log("IGDB Response status:", igdbResponse.status);
    
    if (!igdbResponse.ok) {
      const errorText = await igdbResponse.text();
      console.error("IGDB API error:", igdbResponse.status, errorText);
      
      // If token expired, clear cache and retry once
      if (igdbResponse.status === 401 && accessToken) {
        console.log("Token expired, retrying with new token...");
        accessToken = null;
        tokenExpiry = 0;
        
        try {
          const newToken = await getAccessToken();
          const retryResponse = await fetch(`https://api.igdb.com/v4/${endpoint}`, {
            method: "POST",
            headers: {
              "Client-ID": clientId!,
              "Authorization": `Bearer ${newToken}`,
              "Accept": "application/json",
              "Content-Type": "text/plain",
            },
            body: query,
          });
          
          if (retryResponse.ok) {
            const retryText = await retryResponse.text();
            const retryData = JSON.parse(retryText);
            // Cache the retry response as well
            setCachedResponse(cacheKey, retryData);
            return new Response(JSON.stringify(retryData), {
              status: retryResponse.status,
              headers: { ...corsHeaders, "Content-Type": "application/json", "X-Cache": "MISS" },
            });
          }
        } catch (retryError) {
          console.error("Retry failed:", retryError);
        }
      }
      
      throw new Error(`IGDB API error: ${igdbResponse.status} - ${errorText}`);
    }

    const responseText = await igdbResponse.text();
    console.log("IGDB Response data length:", responseText.length);
    console.log("IGDB Response data sample:", responseText.substring(0, 200));

    // Parse the response as JSON to validate it
    let jsonData;
    try {
      jsonData = JSON.parse(responseText);
      console.log("Successfully parsed JSON response");
    } catch (parseError) {
      console.error("Failed to parse IGDB response as JSON:", parseError);
      console.error("Raw response:", responseText);
      throw new Error(`Invalid JSON response from IGDB: ${responseText.substring(0, 500)}`);
    }

    // Cache the successful response for future requests
    setCachedResponse(cacheKey, jsonData);
    console.log("✅ Response cached for future requests");

    return new Response(JSON.stringify(jsonData), {
      status: igdbResponse.status,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json",
        "X-Cache": "MISS"
      },
    });
  } catch (error) {
    // Enhanced error logging with security context
    const errorDetails = {
      message: error?.message || 'Unknown error',
      name: error?.name || 'Error',
      stack: error?.stack || 'No stack trace',
      timestamp: new Date().toISOString(),
      ip,
      userAgent,
      context: 'igdb-proxy'
    };
    
    console.error("❌ IGDB Proxy Error:", errorDetails);
    logSecurityEvent(ip, 'server_error', { userAgent, error: errorDetails });
    
    // Don't expose internal error details to client
    return new Response(JSON.stringify({
      error: "Internal server error",
      details: "Failed to process request"
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});