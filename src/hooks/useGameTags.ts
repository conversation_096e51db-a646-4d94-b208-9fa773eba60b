import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { userGameTags } from '@/lib/supabase';

export function useGameTags(userGameId: string) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const {
    data: gameTags = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['game-tags', userGameId],
    queryFn: async () => {
      if (!userGameId) return [];
      const { data, error } = await userGameTags.getByUserGame(userGameId);
      if (error) throw error;
      return data;
    },
    enabled: !!userGameId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000 // 5 minutes
  });

  const addTagMutation = useMutation({
    mutationFn: async (tagId: string) => {
      if (!user?.id || !userGameId) throw new Error('Missing required data');
      const { data, error } = await userGameTags.addTagToGame(user.id, userGameId, tagId);
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['game-tags', userGameId] });
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
    }
  });

  const removeTagMutation = useMutation({
    mutationFn: async (tagId: string) => {
      if (!userGameId) throw new Error('User game ID required');
      const { error } = await userGameTags.removeTagFromGame(userGameId, tagId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['game-tags', userGameId] });
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
    }
  });

  const bulkAddTagsMutation = useMutation({
    mutationFn: async (tagIds: string[]) => {
      if (!user?.id || !userGameId) throw new Error('Missing required data');
      const { data, error } = await userGameTags.bulkAddTags(user.id, userGameId, tagIds);
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['game-tags', userGameId] });
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
    }
  });

  const removeAllTagsMutation = useMutation({
    mutationFn: async () => {
      if (!userGameId) throw new Error('User game ID required');
      const { error } = await userGameTags.removeAllTagsFromGame(userGameId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['game-tags', userGameId] });
      queryClient.invalidateQueries({ queryKey: ['user-tags', user?.id] });
    }
  });

  return {
    gameTags,
    isLoading,
    error,
    refetch,
    addTag: addTagMutation.mutate,
    removeTag: removeTagMutation.mutate,
    bulkAddTags: bulkAddTagsMutation.mutate,
    removeAllTags: removeAllTagsMutation.mutate,
    isAdding: addTagMutation.isPending,
    isRemoving: removeTagMutation.isPending,
    isBulkAdding: bulkAddTagsMutation.isPending,
    isRemovingAll: removeAllTagsMutation.isPending
  };
}

export function useGamesByTag(tagId: string) {
  const { user } = useAuth();

  const {
    data: games = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['games-by-tag', tagId, user?.id],
    queryFn: async () => {
      if (!user?.id || !tagId) return [];
      const { data, error } = await userGameTags.getGamesByTag(user.id, tagId);
      if (error) throw error;
      return data;
    },
    enabled: !!user?.id && !!tagId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });

  return {
    games,
    isLoading,
    error,
    refetch
  };
}