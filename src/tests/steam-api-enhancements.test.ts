/**
 * Comprehensive tests for Steam API enhancements
 * Tests all new functionality including achievements, friends, error handling, and caching
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SteamImportService } from '../lib/steamImportService';
import { steamCache, steamBatchProcessor, SteamCache, CACHE_CONFIGS } from '../lib/steam-cache';
import { steamApiClient, SteamApiException, SteamApiError } from '../lib/steam-error-handler';

// Mock data
const mockSteamProfile = {
  steamid: '76561198037867621',
  personaname: 'TestUser',
  avatar: 'https://example.com/avatar.jpg',
  communityvisibilitystate: 3,
  profilestate: 1,
  profileurl: 'https://steamcommunity.com/id/testuser/',
  avatarmedium: 'https://example.com/avatar_medium.jpg',
  avatarfull: 'https://example.com/avatar_full.jpg',
  personastate: 1,
  realname: 'Test User',
  timecreated: 1234567890
};

const mockSteamGame = {
  appid: 440,
  name: 'Team Fortress 2',
  playtime_forever: 1200,
  has_community_visible_stats: true,
  rtime_last_played: Math.floor(Date.now() / 1000) - 86400
};

const mockAchievements = [
  {
    apiname: 'TF_PLAY_GAME_EVERYCLASS',
    achieved: 1,
    unlocktime: Math.floor(Date.now() / 1000) - 86400,
    name: 'Head of the Class',
    description: 'Play a complete game with every class.'
  },
  {
    apiname: 'TF_GET_HEALPOINTS',
    achieved: 0,
    unlocktime: 0,
    name: 'Team Doctor',
    description: 'Accumulate 25000 heal points as a Medic.'
  }
];

const mockFriends = [
  {
    steamid: '76561198037867622',
    relationship: 'friend',
    friend_since: Math.floor(Date.now() / 1000) - 86400 * 30
  }
];

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock environment variables
vi.mock('import.meta', () => ({
  env: {
    VITE_SUPABASE_URL: 'http://localhost:54321',
    VITE_SUPABASE_ANON_KEY: 'test-key'
  }
}));

describe('Steam API Enhancements', () => {
  let steamService: SteamImportService;

  beforeEach(() => {
    steamService = new SteamImportService();
    steamCache.clear();
    mockFetch.mockClear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Enhanced Steam Profile Fetching', () => {
    it('should fetch and cache Steam profile successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: mockSteamProfile
        })
      });

      const profile = await steamService.getSteamProfile('76561198037867621');

      expect(profile).toEqual(mockSteamProfile);
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // Second call should use cache
      const cachedProfile = await steamService.getSteamProfile('76561198037867621');
      expect(cachedProfile).toEqual(mockSteamProfile);
      expect(mockFetch).toHaveBeenCalledTimes(1); // No additional fetch
    });

    it('should handle private profile gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      });

      const profile = await steamService.getSteamProfile('76561198037867621');
      expect(profile).toBeNull();
    });

    it('should handle rate limiting with retry', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 429,
          statusText: 'Too Many Requests',
          headers: { 'retry-after': '60' }
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: mockSteamProfile
          })
        });

      const profile = await steamService.getSteamProfile('76561198037867621');
      expect(profile).toBeNull(); // Should fail gracefully on rate limit
    });
  });

  describe('Achievement System', () => {
    it('should fetch player achievements successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { achievements: mockAchievements }
        })
      });

      const achievements = await steamService.getPlayerAchievements('76561198037867621', 440);

      expect(achievements).toEqual(mockAchievements);
      expect(achievements.filter(a => a.achieved === 1)).toHaveLength(1);
    });

    it('should handle games with no achievements', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { achievements: [] }
        })
      });

      const achievements = await steamService.getPlayerAchievements('76561198037867621', 999);
      expect(achievements).toEqual([]);
    });

    it('should fetch global achievement percentages', async () => {
      const mockGlobalAchievements = [
        { name: 'TF_PLAY_GAME_EVERYCLASS', percent: 45.2 },
        { name: 'TF_GET_HEALPOINTS', percent: 8.7 }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { achievements: mockGlobalAchievements }
        })
      });

      const globalAchievements = await steamService.getGlobalAchievementPercentages(440);
      expect(globalAchievements).toEqual(mockGlobalAchievements);
    });
  });

  describe('Steam Friends Integration', () => {
    it('should fetch Steam friends list', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { friends: mockFriends }
        })
      });

      const friends = await steamService.getSteamFriends('76561198037867621');
      expect(friends).toEqual(mockFriends);
    });

    it('should handle private friends list', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      });

      const friends = await steamService.getSteamFriends('76561198037867621');
      expect(friends).toEqual([]);
    });

    it('should compare libraries between users', async () => {
      const userLibrary = [mockSteamGame, { ...mockSteamGame, appid: 730, name: 'CS:GO' }];
      const friendLibrary = [mockSteamGame, { ...mockSteamGame, appid: 570, name: 'Dota 2' }];

      // Mock library calls
      vi.spyOn(steamService, 'getSteamLibrary')
        .mockResolvedValueOnce(userLibrary)
        .mockResolvedValueOnce(friendLibrary);

      const comparison = await steamService.compareLibraries(
        'user-id',
        '76561198037867621',
        '76561198037867622'
      );

      expect(comparison.common_games).toBe(1); // TF2 is common
      expect(comparison.user_exclusive).toBe(1); // CS:GO is user exclusive
      expect(comparison.friend_exclusive).toBe(1); // Dota 2 is friend exclusive
    });
  });

  describe('Recently Played Games', () => {
    it('should fetch recently played games', async () => {
      const mockRecentGames = [
        {
          appid: 440,
          name: 'Team Fortress 2',
          playtime_2weeks: 120,
          playtime_forever: 1200
        }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { games: mockRecentGames }
        })
      });

      const recentGames = await steamService.getRecentlyPlayedGames('76561198037867621');
      expect(recentGames).toEqual(mockRecentGames);
    });
  });

  describe('Player Bans Information', () => {
    it('should fetch player ban information', async () => {
      const mockBans = {
        SteamId: '76561198037867621',
        CommunityBanned: false,
        VACBanned: false,
        NumberOfVACBans: 0,
        DaysSinceLastBan: 0,
        NumberOfGameBans: 0,
        EconomyBan: 'none'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: mockBans
        })
      });

      const bans = await steamService.getPlayerBans('76561198037867621');
      expect(bans).toEqual(mockBans);
    });
  });

  describe('Caching System', () => {
    it('should cache and retrieve data correctly', () => {
      const testData = { test: 'data' };
      const key = 'test-key';

      steamCache.set(key, testData, 1000);
      const retrieved = steamCache.get(key);

      expect(retrieved).toEqual(testData);
    });

    it('should expire cached data after TTL', async () => {
      const testData = { test: 'data' };
      const key = 'test-key';

      steamCache.set(key, testData, 100); // 100ms TTL
      
      // Should be available immediately
      expect(steamCache.get(key)).toEqual(testData);
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Should be expired
      expect(steamCache.get(key)).toBeNull();
    });

    it('should generate consistent cache keys', () => {
      const key1 = SteamCache.generateKey('profile', { steamId: '123', extra: 'data' });
      const key2 = SteamCache.generateKey('profile', { extra: 'data', steamId: '123' });
      
      expect(key1).toBe(key2); // Should be same regardless of param order
    });

    it('should provide cache statistics', () => {
      steamCache.clear();
      
      // Add some data
      steamCache.set('key1', 'data1');
      steamCache.set('key2', 'data2');
      
      // Access data
      steamCache.get('key1');
      steamCache.get('key1'); // Hit
      steamCache.get('nonexistent'); // Miss
      
      const stats = steamCache.getStats();
      expect(stats.entries).toBe(2);
      expect(stats.hits).toBe(2);
      expect(stats.misses).toBe(1);
      expect(stats.hitRate).toBe(66.67);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      mockFetch.mockRejectedValueOnce(new TypeError('Failed to fetch'));

      const profile = await steamService.getSteamProfile('76561198037867621');
      expect(profile).toBeNull();
    });

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'AbortError';
      mockFetch.mockRejectedValueOnce(timeoutError);

      const profile = await steamService.getSteamProfile('76561198037867621');
      expect(profile).toBeNull();
    });

    it('should handle service unavailable errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable'
      });

      const profile = await steamService.getSteamProfile('76561198037867621');
      expect(profile).toBeNull();
    });
  });

  describe('Batch Processing', () => {
    it('should process operations in batches', async () => {
      const operations = Array.from({ length: 10 }, (_, i) => 
        () => Promise.resolve(`result-${i}`)
      );

      const promises = operations.map(op => steamBatchProcessor.add(op));
      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      expect(results[0]).toBe('result-0');
      expect(results[9]).toBe('result-9');
    });

    it('should handle batch processing errors', async () => {
      const successOp = () => Promise.resolve('success');
      const errorOp = () => Promise.reject(new Error('batch error'));

      const [successResult, errorResult] = await Promise.allSettled([
        steamBatchProcessor.add(successOp),
        steamBatchProcessor.add(errorOp)
      ]);

      expect(successResult.status).toBe('fulfilled');
      expect(errorResult.status).toBe('rejected');
    });
  });
});
