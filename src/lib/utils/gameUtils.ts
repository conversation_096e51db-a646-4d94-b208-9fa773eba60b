import React from 'react';
import { 
  Play, 
  CheckCircle, 
  Pause, 
  Gamepad2, 
  Clock,
  Star
} from 'lucide-react';
import { Game } from '@/types';

export type GameStatus = 'playing' | 'completed' | 'backlog' | 'wishlist' | 'not_started';

/**
 * Get the appropriate icon component for a game status
 */
export const getStatusIcon = (status: string) => {
  switch (status) {
    case 'playing':
      return Play;
    case 'completed':
      return CheckCircle;
    case 'backlog':
      return Pause;
    case 'wishlist':
      return Star;
    case 'not_started':
      return Clock;
    default:
      return Gamepad2;
  }
};

/**
 * Get a React element for a status icon
 */
export const getStatusIconElement = (status: string, className: string = "h-4 w-4"): React.ReactElement => {
  const IconComponent = getStatusIcon(status);
  const colorClass = getStatusIconColor(status);
  return React.createElement(IconComponent, { className: `${className} ${colorClass}` });
};

/**
 * Get the color class for a status icon
 */
export const getStatusIconColor = (status: string) => {
  switch (status) {
    case 'playing':
      return 'text-green-500';
    case 'completed':
      return 'text-blue-500';
    case 'backlog':
      return 'text-orange-500';
    case 'wishlist':
      return 'text-purple-500';
    case 'not_started':
      return 'text-gray-500';
    default:
      return 'text-gray-500';
  }
};

/**
 * Get the background color class for a status badge
 */
export const getStatusColor = (status: string) => {
  switch (status) {
    case 'playing':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'completed':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'backlog':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'wishlist':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'not_started':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

/**
 * Get the display name for a status
 */
export const getStatusDisplayName = (status: string) => {
  switch (status) {
    case 'playing':
      return 'Currently Playing';
    case 'completed':
      return 'Completed';
    case 'backlog':
      return 'Backlog';
    case 'wishlist':
      return 'Wishlist';
    case 'not_started':
      return 'Not Started';
    default:
      return 'Unknown';
  }
};

/**
 * Get all available game statuses
 */
export const getAllStatuses = (): { value: GameStatus; label: string; icon: typeof Play }[] => [
  { value: 'playing', label: 'Currently Playing', icon: Play },
  { value: 'completed', label: 'Completed', icon: CheckCircle },
  { value: 'backlog', label: 'Backlog', icon: Pause },
  { value: 'wishlist', label: 'Wishlist', icon: Star },
  { value: 'not_started', label: 'Not Started', icon: Clock },
];

/**
 * Calculate completion percentage for a game collection
 */
export function calculateCompletionRate(games: { status: string }[]): number {
  if (games.length === 0) return 0;
  
  const completedGames = games.filter(game => game.status === 'completed').length;
  return Math.round((completedGames / games.length) * 100);
}

/**
 * Get games by status
 */
export function getGamesByStatus<T extends { status: string }>(games: T[], status: string): T[] {
  return games.filter(game => game.status === status);
}

/**
 * Calculate average rating for a collection of games
 */
export function calculateAverageRating(games: Game[]): number {
  const gamesWithRatings = games.filter(game => game.metacritic_score && game.metacritic_score > 0);
  
  if (gamesWithRatings.length === 0) return 0;
  
  const totalRating = gamesWithRatings.reduce((sum, game) => sum + (game.metacritic_score || 0), 0);
  return Math.round(totalRating / gamesWithRatings.length);
}

/**
 * Get the most played platform from a collection of games
 */
export function getMostPlayedPlatform(games: Game[]): string | null {
  const platformCounts = new Map<string, number>();
  
  games.forEach(game => {
    if (game.platforms) {
      game.platforms.forEach(platform => {
        platformCounts.set(platform, (platformCounts.get(platform) || 0) + 1);
      });
    }
  });
  
  if (platformCounts.size === 0) return null;
  
  let mostPlayedPlatform = '';
  let maxCount = 0;
  
  platformCounts.forEach((count, platform) => {
    if (count > maxCount) {
      maxCount = count;
      mostPlayedPlatform = platform;
    }
  });
  
  return mostPlayedPlatform;
}

/**
 * Get the most popular genre from a collection of games
 */
export function getMostPopularGenre(games: Game[]): string | null {
  const genreCounts = new Map<string, number>();
  
  games.forEach(game => {
    if (game.genres) {
      game.genres.forEach(genre => {
        genreCounts.set(genre, (genreCounts.get(genre) || 0) + 1);
      });
    }
  });
  
  if (genreCounts.size === 0) return null;
  
  let mostPopularGenre = '';
  let maxCount = 0;
  
  genreCounts.forEach((count, genre) => {
    if (count > maxCount) {
      maxCount = count;
      mostPopularGenre = genre;
    }
  });
  
  return mostPopularGenre;
}

/**
 * Format game release date
 */
export function formatReleaseDate(dateString: string | null | undefined): string {
  if (!dateString) return 'Unknown';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  } catch {
    return 'Unknown';
  }
}

/**
 * Get release year from date string
 */
export function getReleaseYear(dateString: string | null | undefined): number | null {
  if (!dateString) return null;
  
  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    return year > 1900 && year <= new Date().getFullYear() + 2 ? year : null;
  } catch {
    return null;
  }
}

/**
 * Check if a game is recently released (within last 6 months)
 */
export function isRecentlyReleased(dateString: string | null | undefined): boolean {
  if (!dateString) return false;
  
  try {
    const releaseDate = new Date(dateString);
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    return releaseDate >= sixMonthsAgo && releaseDate <= new Date();
  } catch {
    return false;
  }
}

/**
 * Check if a game is upcoming (release date in the future)
 */
export function isUpcoming(dateString: string | null | undefined): boolean {
  if (!dateString) return false;
  
  try {
    const releaseDate = new Date(dateString);
    return releaseDate > new Date();
  } catch {
    return false;
  }
}

/**
 * Format metacritic score with color indication
 */
export function formatMetacriticScore(score: number | null | undefined): {
  score: string;
  colorClass: string;
} {
  if (!score || score <= 0) {
    return { score: 'N/A', colorClass: 'text-gray-500' };
  }
  
  let colorClass = 'text-gray-500';
  if (score >= 75) {
    colorClass = 'text-green-600';
  } else if (score >= 50) {
    colorClass = 'text-yellow-600';
  } else {
    colorClass = 'text-red-600';
  }
  
  return { score: score.toString(), colorClass };
}

/**
 * Truncate game description
 */
export function truncateDescription(description: string | null | undefined, maxLength: number = 150): string {
  if (!description) return '';
  
  if (description.length <= maxLength) return description;
  
  return description.substring(0, maxLength).trim() + '...';
}

/**
 * Generate game slug from title
 */
export function generateGameSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}