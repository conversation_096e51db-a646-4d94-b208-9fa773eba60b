import { useCallback } from 'react';
import { useSearchFilters as useBaseSearchFilters } from '@/hooks/useSearchFilters';
import { Game } from '@/types';
import { GameFilters } from '@/types/filters';

export interface SearchFiltersConfig {
  persistToURL?: boolean;
  enableDebounce?: boolean;
  debounceMs?: number;
}

export interface SearchFiltersState {
  filters: GameFilters;
  filteredGames: Game[];
  hasActiveFilters: boolean;
  activeFilterCount: number;
}

export interface SearchFiltersActions {
  updateFilter: <T extends keyof GameFilters>(filterType: T, update: Partial<GameFilters[T]>) => void;
  resetFilters: (keepSearchQuery?: boolean) => void;
  handleFilterReset: () => void;
}

export interface UseSearchFiltersReturn extends SearchFiltersState, SearchFiltersActions {}

export function useSearchFilters(
  allGames: Game[], 
  config: SearchFiltersConfig = {}
): UseSearchFiltersReturn {
  const {
    persistToURL = true,
    enableDebounce = true,
    debounceMs = 300,
  } = config;

  // Use the base search filters hook with configuration
  const {
    filters,
    updateFilter,
    resetFilters,
    hasActiveFilters,
    activeFilterCount,
    filteredGames
  } = useBaseSearchFilters(allGames, {
    persistToURL,
    enableDebounce,
    debounceMs
  });

  // Handle filter reset with search query preservation
  const handleFilterReset = useCallback(() => {
    resetFilters(true); // Keep search query
  }, [resetFilters]);

  return {
    // State
    filters,
    filteredGames,
    hasActiveFilters,
    activeFilterCount,
    // Actions
    updateFilter,
    resetFilters,
    handleFilterReset,
  };
}