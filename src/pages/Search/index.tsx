import { useState, useMemo } from 'react';
import { SortOptions } from '@/components/ui/filters';
import { sortGames } from '@/lib/sortUtils';
import { SearchHeader, SearchInterface, SearchFilters, SearchResults } from './components';
import { useSearchData, useSearchFilters } from './hooks';

export default function Search() {
  const [sortOptions, setSortOptions] = useState<SortOptions>({
    field: 'metacritic_score',
    direction: 'desc'
  });
  const [availablePlatforms, setAvailablePlatforms] = useState<string[]>([]);

  // Use custom hooks for data and filter management
  const {
    allGames,
    loading,
    error,
    handleSearchResults,
    setLoading,
    setError
  } = useSearchData();

  const {
    filters,
    filteredGames,
    hasActiveFilters,
    activeFilterCount,
    updateFilter,
    handleFilterReset
  } = useSearchFilters(allGames, {
    persistToURL: true,
    enableDebounce: true,
    debounceMs: 300
  });

  // Sort filtered games
  const sortedGames = useMemo(() => {
    return sortGames(filteredGames, sortOptions);
  }, [filteredGames, sortOptions]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-primary/5">
      <div className="container mx-auto p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Page Header */}
          <SearchHeader />

          {/* Main Search Interface */}
          <SearchInterface
            onSearchResults={handleSearchResults}
            onLoading={setLoading}
            onError={setError}
            platformFilters={filters.platforms.enabled ? filters.platforms.platforms : []}
          />

          {/* Search Filters */}
          <SearchFilters
            filters={filters}
            onUpdateFilter={updateFilter}
            onReset={handleFilterReset}
            activeFilterCount={activeFilterCount}
            isLoading={loading}
            availablePlatforms={availablePlatforms}
          />

          {/* Search Results */}
          <SearchResults
            games={sortedGames}
            allGames={allGames}
            loading={loading}
            error={error}
            sortOptions={sortOptions}
            onSortChange={setSortOptions}
            hasActiveFilters={hasActiveFilters}
            onAvailablePlatformsChange={setAvailablePlatforms}
          />
        </div>
      </div>
    </div>
  );
}