import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Search, Clock, TrendingUp, Star, X } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { enhancedSearchService } from '@/lib/enhancedSearchService';

interface SearchSuggestionsProps {
  searchTerm: string;
  onSuggestionSelect: (suggestion: string) => void;
  isVisible: boolean;
  onClose: () => void;
}

interface GameSuggestion {
  title: string;
  type: 'game' | 'recent' | 'trending' | 'popular';
  metadata?: {
    year?: number;
    rating?: number;
    platform?: string;
  };
}

// Convert string suggestions to GameSuggestion format
const formatSuggestion = (title: string, type: 'popular' | 'trending'): GameSuggestion => ({
  title,
  type,
  metadata: {
    // Could be enhanced with actual metadata from the API in the future
  }
});

export function SearchSuggestions({ searchTerm, onSuggestionSelect, isVisible, onClose }: SearchSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<GameSuggestion[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // Fetch popular searches from the enhanced search service
  const { data: popularSearches = [] } = useQuery({
    queryKey: ['popular-searches'],
    queryFn: () => enhancedSearchService.getPopularSearches(),
    staleTime: 30 * 60 * 1000, // 30 minutes
    retry: 1
  });

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('recent-searches');
    if (saved) {
      setRecentSearches(JSON.parse(saved).slice(0, 5));
    }
  }, []);

  useEffect(() => {
    if (!searchTerm.trim()) {
      // Show recent searches and popular games when no search term
      const recentSuggestions = recentSearches.map(search => ({
        title: search,
        type: 'recent' as const
      }));
      
      // Use popular searches from API, fallback to empty array
      const popularSuggestions = popularSearches
        .slice(0, 5)
        .map(game => formatSuggestion(game, 'popular'));
      
      setSuggestions([...recentSuggestions, ...popularSuggestions]);
      return;
    }

    // Filter suggestions based on search term from popular searches
    const filtered = popularSearches
      .filter(game => 
        game.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .slice(0, 8)
      .map(game => formatSuggestion(game, 'popular'));

    setSuggestions(filtered);
  }, [searchTerm, recentSearches, popularSearches]);

  const handleSuggestionClick = (suggestion: string) => {
    // Add to recent searches
    const updated = [suggestion, ...recentSearches.filter(s => s !== suggestion)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recent-searches', JSON.stringify(updated));
    
    onSuggestionSelect(suggestion);
    onClose();
  };

  const clearSearchHistory = () => {
    setRecentSearches([]);
    localStorage.removeItem('recent-searches');
  };

  const removeRecentSearch = (searchToRemove: string) => {
    const updated = recentSearches.filter(s => s !== searchToRemove);
    setRecentSearches(updated);
    if (updated.length > 0) {
      localStorage.setItem('recent-searches', JSON.stringify(updated));
    } else {
      localStorage.removeItem('recent-searches');
    }
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'recent': return Clock;
      case 'trending': return TrendingUp;
      case 'popular': return Star;
      default: return Search;
    }
  };

  const getSuggestionColor = (type: string) => {
    switch (type) {
      case 'recent': return 'text-blue-600';
      case 'trending': return 'text-orange-600';
      case 'popular': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  if (!isVisible || suggestions.length === 0) {
    return null;
  }

  return (
    <div className="absolute top-full left-0 right-0 z-[99999] mt-2">
      <Card className="border-primary/20 shadow-xl bg-background animate-in fade-in-0 slide-in-from-top-2 duration-200">
        <CardContent className="p-2">
          <div className="space-y-1 max-h-96 overflow-y-auto">
            {!searchTerm && recentSearches.length > 0 && (
              <div className="px-3 py-2 text-xs font-medium text-muted-foreground border-b flex items-center justify-between">
                <span>Recent Searches</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearchHistory}
                  className="h-6 px-2 text-xs text-muted-foreground hover:text-destructive"
                >
                  <X className="h-3 w-3 mr-1" />
                  Clear
                </Button>
              </div>
            )}
            
            {suggestions.map((suggestion, index) => {
              const IconComponent = getSuggestionIcon(suggestion.type);
              const colorClass = getSuggestionColor(suggestion.type);
              
              return (
                <div key={`${suggestion.title}-${index}`} className="flex items-center group">
                  <Button
                    variant="ghost"
                    className="flex-1 justify-start h-auto p-3 text-left hover:bg-accent"
                    onClick={() => handleSuggestionClick(suggestion.title)}
                  >
                    <div className="flex items-center gap-3 w-full">
                      <IconComponent className={`h-4 w-4 ${colorClass} flex-shrink-0`} />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">
                          {suggestion.title}
                        </div>
                        {suggestion.metadata && (
                          <div className="flex items-center gap-2 mt-1">
                            {suggestion.metadata.year && (
                              <Badge variant="outline" className="text-xs">
                                {suggestion.metadata.year}
                              </Badge>
                            )}
                            {suggestion.metadata.rating && (
                              <Badge variant="outline" className="text-xs">
                                <Star className="h-3 w-3 mr-1" />
                                {suggestion.metadata.rating}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        {suggestion.type === 'trending' && (
                          <Badge variant="secondary" className="bg-orange-100 text-orange-700 text-xs">
                            Trending
                          </Badge>
                        )}
                        {suggestion.type === 'popular' && (
                          <Badge variant="secondary" className="bg-purple-100 text-purple-700 text-xs">
                            Popular
                          </Badge>
                        )}
                      </div>
                    </div>
                  </Button>
                  {suggestion.type === 'recent' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeRecentSearch(suggestion.title);
                      }}
                      className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground hover:text-destructive"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              );
            })}
          </div>
          
          {searchTerm && suggestions.length > 0 && (
            <div className="px-3 py-2 text-xs text-muted-foreground border-t mt-2">
              Press Enter to search for "{searchTerm}"
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}