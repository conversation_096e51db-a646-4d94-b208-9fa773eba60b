import { useCallback } from 'react';
import { useSearchHistory } from './useSearchHistory';

/**
 * Hook for search analytics
 */
export function useSearchAnalytics() {
  const { history } = useSearchHistory();

  const getSearchStats = useCallback(() => {
    const totalSearches = history.length;
    const uniqueQueries = new Set(history.map(h => h.query)).size;
    const avgResultCount = history.length > 0 
      ? history.reduce((sum, h) => sum + h.resultCount, 0) / history.length 
      : 0;

    const intentDistribution = history.reduce((acc, h) => {
      acc[h.intent.type] = (acc[h.intent.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const searchFrequency = history.reduce((acc, h) => {
      const date = new Date(h.timestamp).toDateString();
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalSearches,
      uniqueQueries,
      avgResultCount,
      intentDistribution,
      searchFrequency
    };
  }, [history]);

  return {
    history,
    getSearchStats
  };
}