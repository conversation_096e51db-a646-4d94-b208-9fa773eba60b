import { Badge } from '@/components/ui/base';
import { TagManager } from '@/components/ui/tags/TagManager';
import { Database, Zap } from 'lucide-react';

export function SearchHeader() {
  return (
    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <div>
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 bg-primary/10 rounded-xl">
            <Database className="h-6 w-6 text-primary" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
            Game Discovery
          </h1>
        </div>
        <p className="text-muted-foreground text-lg">
          Discover and explore games with advanced filtering
        </p>
      </div>

      {/* IGDB Branding and Tag Management */}
      <div className="flex gap-2">
        <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
          <Database className="h-3 w-3 mr-1" />
          Powered by IGDB
        </Badge>
        <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
          <Zap className="h-3 w-3 mr-1" />
          Enhanced Search
        </Badge>
        <TagManager />
      </div>
    </div>
  );
}