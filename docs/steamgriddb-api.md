# SteamGridDB API Integration

## Overview

The SteamGridDB API integration provides access to high-quality game artwork including covers, banners, logos, and icons. This service enhances the visual presentation of games in your library with community-curated artwork.

## Features

- 🎨 **High-Quality Artwork** - Access to thousands of custom game covers and banners
- 🎮 **Steam Integration** - Direct lookup by Steam game IDs
- 🖼️ **Multiple Formats** - Support for PNG, JPG, and ICO formats
- 📏 **Various Dimensions** - Different sizes for different use cases
- 🎭 **Style Options** - Alternate, blurred, white logo, material, and more
- ⚡ **Fast & Reliable** - Cached CDN delivery for optimal performance

## Setup

### 1. API Key Configuration

Add your SteamGridDB API key to your environment variables:

```env
# .env
VITE_STEAMGRIDDB_API_KEY=your_steamgriddb_api_key
```

### 2. Import the Service

```typescript
import { steamGridDBAPI } from '@/lib/api';
// or
import { api } from '@/lib/api';
const steamGridDB = api.games.steamGridDB;
```

## API Reference

### Core Methods

#### `getGameBySteamId(steamId: string)`

Get game information by Steam ID (most reliable method).

```typescript
const game = await steamGridDBAPI.getGameBySteamId('1091500'); // Cyberpunk 2077
// Returns: { id: 5209422, name: "Cyberpunk 2077", types: ["steam"], verified: true }
```

#### `getGrids(gameId: number, options?: SteamGridDBGridOptions)`

Get cover art/grid images for a game.

```typescript
const grids = await steamGridDBAPI.getGrids(gameId, {
  styles: ['alternate', 'blurred', 'white_logo'],
  dimensions: ['460x215', '920x430'],
  formats: ['png', 'jpg'],
  nsfw: 'false',
  humor: 'any'
});
```

#### `getHeroes(gameId: number, options?: SteamGridDBGridOptions)`

Get hero banners (wide format images) for a game.

```typescript
const heroes = await steamGridDBAPI.getHeroes(gameId, {
  dimensions: ['1920x620', '3840x1240'],
  styles: ['alternate', 'blurred', 'material']
});
```

#### `getLogos(gameId: number, options?: SteamGridDBGridOptions)`

Get game logos (transparent PNG format).

```typescript
const logos = await steamGridDBAPI.getLogos(gameId, {
  styles: ['official', 'white', 'black'],
  formats: ['png']
});
```

#### `getIcons(gameId: number, options?: SteamGridDBGridOptions)`

Get game icons in various sizes.

```typescript
const icons = await steamGridDBAPI.getIcons(gameId, {
  dimensions: ['32', '64', '128', '256'],
  formats: ['png', 'ico']
});
```

#### `searchWithArtwork(term: string)`

Enhanced search that returns games with their best artwork (fallback method).

```typescript
const gamesWithArt = await steamGridDBAPI.searchWithArtwork('cyberpunk');
// Returns games with attached artwork property
```

### Options Interface

```typescript
interface SteamGridDBGridOptions {
  styles?: string[];        // ['alternate', 'blurred', 'white_logo', 'material', 'no_logo']
  dimensions?: string[];    // ['460x215', '920x430', '1920x620', etc.]
  formats?: string[];       // ['png', 'jpg', 'ico']
  nsfw?: 'any' | 'false' | 'true';
  humor?: 'any' | 'false' | 'true';
  epilepsy?: 'any' | 'false' | 'true';
  untagged?: 'any' | 'false' | 'true';
  page?: number;
}
```

## Usage Examples

### Basic Game Artwork Retrieval

```typescript
async function getGameArtwork(steamId: string) {
  try {
    // 1. Get game info
    const game = await steamGridDBAPI.getGameBySteamId(steamId);
    if (!game) {
      console.log('Game not found');
      return null;
    }

    // 2. Get cover art
    const grids = await steamGridDBAPI.getGrids(game.id, {
      dimensions: ['460x215'],
      styles: ['alternate', 'white_logo'],
      formats: ['png', 'jpg']
    });

    // 3. Get best artwork (highest score)
    const bestArtwork = grids.sort((a, b) => b.score - a.score)[0];
    
    return {
      game,
      artwork: bestArtwork?.url,
      thumbnail: bestArtwork?.thumb
    };
  } catch (error) {
    console.error('Failed to get artwork:', error);
    return null;
  }
}

// Usage
const result = await getGameArtwork('1091500'); // Cyberpunk 2077
```

### Complete Game Media Package

```typescript
async function getCompleteGameMedia(steamId: string) {
  const game = await steamGridDBAPI.getGameBySteamId(steamId);
  if (!game) return null;

  const [grids, heroes, logos, icons] = await Promise.all([
    steamGridDBAPI.getGrids(game.id, { dimensions: ['460x215'] }),
    steamGridDBAPI.getHeroes(game.id, { dimensions: ['1920x620'] }),
    steamGridDBAPI.getLogos(game.id, { formats: ['png'] }),
    steamGridDBAPI.getIcons(game.id, { dimensions: ['128'] })
  ]);

  return {
    game,
    cover: grids[0]?.url,
    banner: heroes[0]?.url,
    logo: logos[0]?.url,
    icon: icons[0]?.url
  };
}
```

### React Component Integration

```tsx
import { useQuery } from '@tanstack/react-query';
import { steamGridDBAPI } from '@/lib/api';

function GameCover({ steamId }: { steamId: string }) {
  const { data: artwork, isLoading } = useQuery({
    queryKey: ['steamgriddb', 'artwork', steamId],
    queryFn: async () => {
      const game = await steamGridDBAPI.getGameBySteamId(steamId);
      if (!game) return null;
      
      const grids = await steamGridDBAPI.getGrids(game.id, {
        dimensions: ['460x215'],
        styles: ['alternate', 'white_logo']
      });
      
      return grids.sort((a, b) => b.score - a.score)[0];
    },
    staleTime: 1000 * 60 * 60, // 1 hour
  });

  if (isLoading) return <div className="animate-pulse bg-gray-200 w-[460px] h-[215px]" />;
  if (!artwork) return <div className="bg-gray-100 w-[460px] h-[215px] flex items-center justify-center">No artwork</div>;

  return (
    <img 
      src={artwork.url} 
      alt="Game cover"
      className="w-[460px] h-[215px] object-cover rounded-lg"
    />
  );
}
```

## Available Artwork Styles

- **`alternate`** - Alternative/custom artwork
- **`blurred`** - Blurred background versions
- **`white_logo`** - White logo on artwork
- **`material`** - Material design style
- **`no_logo`** - Clean artwork without logos
- **`official`** - Official game artwork

## Common Dimensions

### Grids (Covers)
- `460x215` - Steam grid format
- `920x430` - High-res Steam grid
- `600x900` - Portrait format
- `342x482` - Classic portrait

### Heroes (Banners)
- `1920x620` - Standard hero banner
- `3840x1240` - 4K hero banner

### Icons
- `32` - Small icon
- `64` - Medium icon
- `128` - Large icon
- `256` - Extra large icon

## Error Handling

The service includes comprehensive error handling:

```typescript
try {
  const game = await steamGridDBAPI.getGameBySteamId('invalid-id');
} catch (error) {
  if (error.message.includes('404')) {
    console.log('Game not found in SteamGridDB');
  } else if (error.message.includes('API key')) {
    console.log('Invalid API key');
  } else {
    console.log('Network or other error:', error.message);
  }
}
```

## Performance Tips

1. **Cache Results** - Use React Query or similar caching
2. **Lazy Loading** - Load artwork only when needed
3. **Thumbnail First** - Use `thumb` property for previews
4. **Batch Requests** - Group multiple artwork requests
5. **Fallback Images** - Always have fallback artwork

## Rate Limits

SteamGridDB has rate limits. The service automatically handles:
- Request throttling
- Error retry logic
- Graceful degradation

## Troubleshooting

### Common Issues

1. **404 Errors on Search**
   - Search endpoint may not be available
   - Use `getGameBySteamId()` instead

2. **No Artwork Found**
   - Try different style options
   - Check if game exists in SteamGridDB

3. **API Key Issues**
   - Verify key in environment variables
   - Check key validity on SteamGridDB website

### Debug Mode

Enable debug logging:

```typescript
// The service automatically logs requests and responses
// Check browser console for detailed information
```

## Integration with Existing APIs

The SteamGridDB service works seamlessly with other game APIs:

```typescript
// Combine with IGDB for complete game data
const igdbGame = await igdbAPI.search('Cyberpunk 2077');
const steamId = extractSteamId(igdbGame); // Your logic
const artwork = await steamGridDBAPI.getGameBySteamId(steamId);
```

## Contributing

To extend the SteamGridDB integration:

1. Add new methods to `SteamGridDBAPIService`
2. Update TypeScript interfaces
3. Add comprehensive error handling
4. Include usage examples
5. Update this documentation

## Links

- [SteamGridDB Website](https://www.steamgriddb.com/)
- [SteamGridDB API Documentation](https://www.steamgriddb.com/api/v2)
- [Get API Key](https://www.steamgriddb.com/profile/preferences/api)