#!/bin/bash

echo "🔍 Searching for running development servers..."

# Common dev keywords to look for
KEYWORDS=("npm run dev" "pnpm dev" "vite" "next dev" "nuxt dev" "react-scripts start" "node" "nodemon" "ts-node")

# Initialize empty list of PIDs
PIDS=""

for keyword in "${KEYWORDS[@]}"; do
  FOUND=$(ps aux | grep "$keyword" | grep -v grep | awk '{print $2}')
  if [ ! -z "$FOUND" ]; then
    PIDS="$PIDS $FOUND"
  fi
done

if [ -z "$PIDS" ]; then
  echo "✅ No development servers running."
else
  echo "⚠️ Found running dev processes:"
  echo "$PIDS"
  echo "🔪 Killing them all..."
  kill -9 $PIDS
  echo "✅ All dev servers have been terminated."
fi
