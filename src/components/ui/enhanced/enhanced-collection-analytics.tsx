import React, { memo, useMemo, useCallback, useState } from 'react';
import { UserGameWithDetails } from '@/types/database';
import { CollectionAnalytics, AnalyticsTimeframe } from '@/types/library';
import { cn } from '@/lib/utils';
import { 
  Bar<PERSON>hart3,
  TrendingUp,
  Calendar,
  Clock,
  Trophy,
  Target,
  Star,
  Gamepad2,
  Zap,
  Users,
  <PERSON>Chart,
  LineChart,
  Download,
  Share2,
  Sparkles,
  Award,
  Timer,
  ArrowUp,
  ArrowDown,
  Minus,
  Heart
} from '@/lib/icons';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/base/tabs';
import { Progress } from '@/components/ui/base/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/base/select';

interface EnhancedCollectionAnalyticsProps {
  games: UserGameWithDetails[];
  timeframe: AnalyticsTimeframe;
  onTimeframeChange: (timeframe: AnalyticsTimeframe) => void;
  className?: string;
}

interface AnalyticsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    direction: 'up' | 'down' | 'neutral';
    label: string;
  };
  icon: React.ReactNode;
  subtitle?: string;
  className?: string;
}


// Analytics calculation utility
const calculateCollectionAnalytics = (
  games: UserGameWithDetails[], 
  timeframe: AnalyticsTimeframe
): CollectionAnalytics => {
  const now = new Date();
  let startDate: Date;
  
  switch (timeframe) {
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case 'quarter':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case 'year':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(0);
  }

  const periodGames = games.filter(game => 
    new Date(game.date_added) >= startDate
  );

  // Basic metrics
  const totalGames = games.length;
  const totalHours = games.reduce((sum, game) => sum + (game.hours_played || 0), 0);
  const completedGames = games.filter(game => game.status === 'completed').length;
  const playingGames = games.filter(game => game.status === 'playing').length;
  const backlogGames = games.filter(game => game.status === 'backlog').length;
  const wishlistGames = games.filter(game => game.status === 'wishlist').length;

  // Period-specific metrics
  const newGamesThisPeriod = periodGames.length;
  const hoursThisPeriod = periodGames.reduce((sum, game) => sum + (game.hours_played || 0), 0);
  const completedThisPeriod = periodGames.filter(game => game.status === 'completed').length;

  // Platform distribution
  const platformStats = games.reduce((acc, game) => {
    const platforms = game.game?.platforms || ['Unknown'];
    platforms.forEach(platform => {
      acc[platform] = (acc[platform] || 0) + 1;
    });
    return acc;
  }, {} as Record<string, number>);

  // Genre distribution
  const genreStats = games.reduce((acc, game) => {
    const genres = game.game?.genres || ['Unknown'];
    genres.forEach(genre => {
      acc[genre] = (acc[genre] || 0) + 1;
    });
    return acc;
  }, {} as Record<string, number>);

  // Release year distribution
  const yearStats = games.reduce((acc, game) => {
    const year = game.game?.release_date 
      ? new Date(game.game.release_date).getFullYear()
      : new Date().getFullYear();
    const decade = Math.floor(year / 10) * 10;
    const decadeLabel = `${decade}s`;
    acc[decadeLabel] = (acc[decadeLabel] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Rating distribution
  const ratingStats = games.reduce((acc, game) => {
    if (game.personal_rating) {
      const rating = Math.floor(game.personal_rating);
      acc[rating] = (acc[rating] || 0) + 1;
    }
    return acc;
  }, {} as Record<number, number>);

  // Developer analysis
  const developerStats = games.reduce((acc, game) => {
    const developer = game.game?.developer || 'Unknown';
    acc[developer] = (acc[developer] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Playtime analysis
  const playtimeCategories = {
    'Not Played': games.filter(g => !g.hours_played || g.hours_played === 0).length,
    'Short Sessions (< 5h)': games.filter(g => g.hours_played && g.hours_played < 5).length,
    'Medium Sessions (5-20h)': games.filter(g => g.hours_played && g.hours_played >= 5 && g.hours_played < 20).length,
    'Long Sessions (20-50h)': games.filter(g => g.hours_played && g.hours_played >= 20 && g.hours_played < 50).length,
    'Extended Sessions (50h+)': games.filter(g => g.hours_played && g.hours_played >= 50).length,
  };

  // Completion analysis
  const completionRate = totalGames > 0 ? (completedGames / totalGames) * 100 : 0;
  const avgHoursPerGame = totalGames > 0 ? totalHours / totalGames : 0;
  const avgRating = games.filter(g => g.personal_rating).length > 0 
    ? games.filter(g => g.personal_rating).reduce((sum, g) => sum + (g.personal_rating || 0), 0) / games.filter(g => g.personal_rating).length
    : 0;

  // Quality metrics
  const highlyRatedGames = games.filter(g => g.personal_rating && g.personal_rating >= 8).length;
  const metacriticHighGames = games.filter(g => g.game?.metacritic_score && g.game.metacritic_score >= 80).length;

  return {
    overview: {
      totalGames,
      totalHours,
      completedGames,
      playingGames,
      backlogGames,
      wishlistGames,
      completionRate,
      avgHoursPerGame,
      avgRating,
      newGamesThisPeriod,
      hoursThisPeriod,
      completedThisPeriod,
      highlyRatedGames,
      metacriticHighGames
    },
    distributions: {
      platforms: platformStats,
      genres: genreStats,
      releaseYears: yearStats,
      ratings: ratingStats,
      developers: developerStats,
      playtimeCategories
    },
    timeframe,
    generatedAt: new Date().toISOString()
  };
};

// Generate insights from analytics data
const generateInsights = (analytics: CollectionAnalytics) => {
  const insights = [];

  // Collection size insights
  if (analytics.overview.totalGames >= 1000) {
    insights.push({
      type: 'achievement',
      title: 'Massive Collection',
      description: `You have ${analytics.overview.totalGames} games! You're in the top 1% of collectors.`,
      icon: Trophy,
      color: 'gold'
    });
  } else if (analytics.overview.totalGames >= 500) {
    insights.push({
      type: 'achievement',
      title: 'Serious Collector',
      description: `${analytics.overview.totalGames} games shows serious dedication to gaming.`,
      icon: Award,
      color: 'purple'
    });
  }

  // Completion rate insights
  if (analytics.overview.completionRate >= 80) {
    insights.push({
      type: 'achievement',
      title: 'Completion Master',
      description: `${analytics.overview.completionRate.toFixed(1)}% completion rate! You finish what you start.`,
      icon: Target,
      color: 'green'
    });
  } else if (analytics.overview.completionRate < 20) {
    insights.push({
      type: 'suggestion',
      title: 'Focus Opportunity',
      description: `Consider focusing on completing some of your ${analytics.overview.backlogGames} backlog games.`,
      icon: Zap,
      color: 'orange'
    });
  }

  // Playtime insights
  if (analytics.overview.totalHours >= 10000) {
    insights.push({
      type: 'achievement',
      title: 'Gaming Veteran',
      description: `${Math.round(analytics.overview.totalHours)} hours of gaming! That's dedication.`,
      icon: Timer,
      color: 'blue'
    });
  }

  // Recent activity
  if (analytics.overview.newGamesThisPeriod >= 10) {
    insights.push({
      type: 'observation',
      title: 'Active Period',
      description: `You've added ${analytics.overview.newGamesThisPeriod} games this ${analytics.timeframe}.`,
      icon: TrendingUp,
      color: 'blue'
    });
  }

  // Genre analysis
  const topGenre = Object.entries(analytics.distributions.genres)
    .sort(([,a], [,b]) => b - a)[0];
  if (topGenre && topGenre[1] >= 10) {
    insights.push({
      type: 'observation',
      title: 'Genre Preference',
      description: `You love ${topGenre[0]} games - ${topGenre[1]} in your collection!`,
      icon: Heart,
      color: 'red'
    });
  }

  return insights;
};

// Analytics card component
const AnalyticsCard = memo<AnalyticsCardProps>(({
  title,
  value,
  change,
  icon,
  subtitle,
  className
}) => {
  return (
    <Card className={cn('transition-all hover:shadow-md', className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <div className="flex items-baseline space-x-2">
              <span className="text-2xl font-bold">{value}</span>
              {subtitle && (
                <span className="text-sm text-muted-foreground">{subtitle}</span>
              )}
            </div>
            {change && (
              <div className={cn(
                'flex items-center space-x-1 text-xs',
                change.direction === 'up' && 'text-green-600',
                change.direction === 'down' && 'text-red-600',
                change.direction === 'neutral' && 'text-muted-foreground'
              )}>
                {change.direction === 'up' && <ArrowUp size={12} />}
                {change.direction === 'down' && <ArrowDown size={12} />}
                {change.direction === 'neutral' && <Minus size={12} />}
                <span>{Math.abs(change.value)} {change.label}</span>
              </div>
            )}
          </div>
          <div className="text-muted-foreground">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

AnalyticsCard.displayName = 'AnalyticsCard';

// Distribution chart component
interface DistributionChartProps {
  title: string;
  data: Record<string, number>;
  limit?: number;
  showPercentages?: boolean;
  icon?: React.ReactNode;
}

const DistributionChart = memo<DistributionChartProps>(({
  title,
  data,
  limit = 8,
  showPercentages = true,
  icon
}) => {
  const sortedData = Object.entries(data)
    .sort(([,a], [,b]) => b - a)
    .slice(0, limit);

  const total = Object.values(data).reduce((sum, value) => sum + value, 0);
  const maxValue = Math.max(...sortedData.map(([,value]) => value));

  if (sortedData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {icon}
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <PieChart size={32} className="mx-auto mb-2 opacity-50" />
            <p>No data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {sortedData.map(([name, value]) => {
            const percentage = total > 0 ? (value / total) * 100 : 0;
            const barWidth = maxValue > 0 ? (value / maxValue) * 100 : 0;
            
            return (
              <div key={name} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="truncate flex-1 mr-2">{name}</span>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{value}</span>
                    {showPercentages && (
                      <span className="text-muted-foreground">
                        ({percentage.toFixed(1)}%)
                      </span>
                    )}
                  </div>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div 
                    className="bg-primary rounded-full h-2 transition-all duration-300"
                    style={{ width: `${barWidth}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
});

DistributionChart.displayName = 'DistributionChart';

// Insights component
interface InsightsProps {
  insights: Array<{
    type: string;
    title: string;
    description: string;
    icon: React.ComponentType<{ size?: number; className?: string }>;
    color: string;
  }>;
}

const InsightsPanel = memo<InsightsProps>(({ insights }) => {
  if (insights.length === 0) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles size={18} />
          Collection Insights
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {insights.map((insight, index) => {
            const Icon = insight.icon;
            return (
              <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-muted/30">
                <div className={cn(
                  'p-2 rounded-lg shrink-0',
                  insight.color === 'gold' && 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400',
                  insight.color === 'purple' && 'bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400',
                  insight.color === 'green' && 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400',
                  insight.color === 'orange' && 'bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400',
                  insight.color === 'blue' && 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400',
                  insight.color === 'red' && 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400'
                )}>
                  <Icon size={16} />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm">{insight.title}</h4>
                  <p className="text-sm text-muted-foreground mt-1">{insight.description}</p>
                  <Badge 
                    variant="secondary" 
                    className="mt-2 text-xs"
                  >
                    {insight.type}
                  </Badge>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
});

InsightsPanel.displayName = 'InsightsPanel';

export const EnhancedCollectionAnalytics = memo<EnhancedCollectionAnalyticsProps>(({
  games,
  timeframe,
  onTimeframeChange,
  className
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  const analytics = useMemo(() => 
    calculateCollectionAnalytics(games, timeframe), 
    [games, timeframe]
  );

  const insights = useMemo(() => 
    generateInsights(analytics), 
    [analytics]
  );

  const handleExportData = useCallback(() => {
    const exportData = {
      analytics,
      exportedAt: new Date().toISOString(),
      gameCount: games.length
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `collection-analytics-${timeframe}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [analytics, games.length, timeframe]);

  if (games.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-64', className)}>
        <div className="text-center">
          <BarChart3 size={48} className="mx-auto text-muted-foreground mb-4" />
          <p className="text-lg text-muted-foreground">No data to analyze</p>
          <p className="text-sm text-muted-foreground mt-2">
            Add some games to your library to see analytics
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('enhanced-collection-analytics space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 size={24} />
            Collection Analytics
          </h2>
          <p className="text-muted-foreground">
            Deep insights into your gaming collection and habits
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={timeframe} onValueChange={onTimeframeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Last Week</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
              <SelectItem value="quarter">Last Quarter</SelectItem>
              <SelectItem value="year">Last Year</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm" onClick={handleExportData}>
            <Download size={16} className="mr-2" />
            Export
          </Button>
          
          <Button variant="outline" size="sm">
            <Share2 size={16} className="mr-2" />
            Share
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <AnalyticsCard
          title="Total Games"
          value={analytics.overview.totalGames}
          change={analytics.overview.newGamesThisPeriod > 0 ? {
            value: analytics.overview.newGamesThisPeriod,
            direction: 'up',
            label: `new this ${timeframe}`
          } : undefined}
          icon={<Gamepad2 size={24} />}
        />
        
        <AnalyticsCard
          title="Completion Rate"
          value={`${analytics.overview.completionRate.toFixed(1)}%`}
          subtitle={`${analytics.overview.completedGames} completed`}
          icon={<Trophy size={24} />}
        />
        
        <AnalyticsCard
          title="Total Hours"
          value={Math.round(analytics.overview.totalHours)}
          subtitle="played"
          change={analytics.overview.hoursThisPeriod > 0 ? {
            value: Math.round(analytics.overview.hoursThisPeriod),
            direction: 'up',
            label: `hours this ${timeframe}`
          } : undefined}
          icon={<Clock size={24} />}
        />
        
        <AnalyticsCard
          title="Average Rating"
          value={analytics.overview.avgRating.toFixed(1)}
          subtitle="/ 10"
          icon={<Star size={24} />}
        />
      </div>

      {/* Main Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-5 w-full max-w-2xl">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="platforms">Platforms</TabsTrigger>
          <TabsTrigger value="genres">Genres</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target size={18} />
                  Status Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Playing</span>
                      <span>{analytics.overview.playingGames}</span>
                    </div>
                    <Progress value={(analytics.overview.playingGames / analytics.overview.totalGames) * 100} className="h-2" />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Completed</span>
                      <span>{analytics.overview.completedGames}</span>
                    </div>
                    <Progress value={(analytics.overview.completedGames / analytics.overview.totalGames) * 100} className="h-2 [&>div]:bg-green-500" />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Backlog</span>
                      <span>{analytics.overview.backlogGames}</span>
                    </div>
                    <Progress value={(analytics.overview.backlogGames / analytics.overview.totalGames) * 100} className="h-2 [&>div]:bg-yellow-500" />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Wishlist</span>
                      <span>{analytics.overview.wishlistGames}</span>
                    </div>
                    <Progress value={(analytics.overview.wishlistGames / analytics.overview.totalGames) * 100} className="h-2 [&>div]:bg-purple-500" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Playtime Categories */}
            <DistributionChart
              title="Playtime Distribution"
              data={analytics.distributions.playtimeCategories}
              icon={<Clock size={18} />}
              showPercentages={true}
            />
          </div>

          {/* Quality Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6 text-center">
                <Star size={24} className="mx-auto text-yellow-500 mb-2" />
                <div className="text-2xl font-bold">{analytics.overview.highlyRatedGames}</div>
                <div className="text-sm text-muted-foreground">Highly Rated (8+)</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6 text-center">
                <Award size={24} className="mx-auto text-green-500 mb-2" />
                <div className="text-2xl font-bold">{analytics.overview.metacriticHighGames}</div>
                <div className="text-sm text-muted-foreground">Metacritic 80+</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6 text-center">
                <Timer size={24} className="mx-auto text-blue-500 mb-2" />
                <div className="text-2xl font-bold">{analytics.overview.avgHoursPerGame.toFixed(1)}h</div>
                <div className="text-sm text-muted-foreground">Avg per Game</div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="platforms" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <DistributionChart
              title="Platform Distribution"
              data={analytics.distributions.platforms}
              icon={<Gamepad2 size={18} />}
              limit={10}
            />
            
            <DistributionChart
              title="Top Developers"
              data={analytics.distributions.developers}
              icon={<Users size={18} />}
              limit={8}
            />
          </div>
        </TabsContent>

        <TabsContent value="genres" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <DistributionChart
              title="Genre Distribution"
              data={analytics.distributions.genres}
              icon={<Star size={18} />}
              limit={10}
            />
            
            <DistributionChart
              title="Release Era"
              data={analytics.distributions.releaseYears}
              icon={<Calendar size={18} />}
              limit={8}
            />
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <div className="text-center py-12 text-muted-foreground">
            <LineChart size={48} className="mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Trend Analysis</h3>
            <p>Historical trend analysis coming soon</p>
            <p className="text-sm">Track your gaming habits over time</p>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <InsightsPanel insights={insights} />
          
          {insights.length === 0 && (
            <div className="text-center py-12 text-muted-foreground">
              <Sparkles size={48} className="mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No Insights Yet</h3>
              <p>Build up your collection to unlock personalized insights</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
});

EnhancedCollectionAnalytics.displayName = 'EnhancedCollectionAnalytics';