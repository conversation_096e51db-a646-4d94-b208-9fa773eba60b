-- RLS Policy Optimization Migration
-- Optimizes Row Level Security policies for better query performance

-- Drop existing RLS policies that might be inefficient
DROP POLICY IF EXISTS "Users can view their own game entries" ON user_games;
DROP POLICY IF EXISTS "Users can insert their own game entries" ON user_games;
DROP POLICY IF EXISTS "Users can update their own game entries" ON user_games;
DROP POLICY IF EXISTS "Users can delete their own game entries" ON user_games;

-- Create optimized RLS policies for user_games
-- Optimized SELECT policy using index-friendly conditions
CREATE POLICY "Users can view their own game entries" ON user_games
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

-- Optimized INSERT policy
CREATE POLICY "Users can insert their own game entries" ON user_games
  FOR INSERT TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Optimized UPDATE policy
CREATE POLICY "Users can update their own game entries" ON user_games
  FOR UPDATE TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Optimized DELETE policy
CREATE POLICY "Users can delete their own game entries" ON user_games
  FOR DELETE TO authenticated
  USING (user_id = auth.uid());

-- Optimize price_tracking RLS policies
DROP POLICY IF EXISTS "Users can view price tracking" ON price_tracking;
DROP POLICY IF EXISTS "Users can insert price tracking" ON price_tracking;

-- Price tracking policies (more permissive for performance)
CREATE POLICY "Users can view price tracking" ON price_tracking
  FOR SELECT TO authenticated
  USING (true); -- Price data is not user-specific

DROP POLICY IF EXISTS "Service can manage price tracking" ON price_tracking;
CREATE POLICY "Service can manage price tracking" ON price_tracking
  FOR ALL TO service_role
  USING (true);

-- Optimize user_recommendations RLS policies (only if table exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_recommendations') THEN
        EXECUTE 'DROP POLICY IF EXISTS "Users can view their own recommendations" ON user_recommendations';
        EXECUTE 'DROP POLICY IF EXISTS "Users can manage their own recommendations" ON user_recommendations';
        
        EXECUTE 'CREATE POLICY "Users can view their own recommendations" ON user_recommendations
          FOR SELECT TO authenticated
          USING (user_id = auth.uid())';
          
        EXECUTE 'CREATE POLICY "Users can manage their own recommendations" ON user_recommendations
          FOR ALL TO authenticated
          USING (user_id = auth.uid())
          WITH CHECK (user_id = auth.uid())';
    END IF;
END $$;

-- Optimize ai_conversations RLS policies
DROP POLICY IF EXISTS "Users can view their own conversations" ON ai_conversations;
DROP POLICY IF EXISTS "Users can manage their own conversations" ON ai_conversations;

CREATE POLICY "Users can view their own conversations" ON ai_conversations
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own conversations" ON ai_conversations
  FOR ALL TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Optimize user_collection_stats RLS policies (only if table exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_collection_stats') THEN
        EXECUTE 'DROP POLICY IF EXISTS "Users can view their own stats" ON user_collection_stats';
        EXECUTE 'DROP POLICY IF EXISTS "Users can update their own stats" ON user_collection_stats';
        
        EXECUTE 'CREATE POLICY "Users can view their own stats" ON user_collection_stats
          FOR SELECT TO authenticated
          USING (user_id = auth.uid())';
          
        EXECUTE 'DROP POLICY IF EXISTS "Users can manage their own stats" ON user_collection_stats';
        EXECUTE 'CREATE POLICY "Users can manage their own stats" ON user_collection_stats
          FOR ALL TO authenticated
          USING (user_id = auth.uid())
          WITH CHECK (user_id = auth.uid())';
    END IF;
END $$;

-- Create security definer functions for better performance
-- These functions run with elevated privileges, bypassing RLS for performance
CREATE OR REPLACE FUNCTION secure_get_user_games(p_user_id UUID)
RETURNS SETOF user_games AS $$
BEGIN
  -- Verify the user is requesting their own data
  IF p_user_id != auth.uid() THEN
    RAISE EXCEPTION 'Access denied: can only access own data';
  END IF;
  
  RETURN QUERY
  SELECT * FROM user_games 
  WHERE user_id = p_user_id
  ORDER BY date_added DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create user recommendations function only if table exists
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_recommendations') THEN
        EXECUTE '
        CREATE OR REPLACE FUNCTION secure_get_user_recommendations(p_user_id UUID)
        RETURNS SETOF user_recommendations AS $func$
        BEGIN
          -- Verify the user is requesting their own data
          IF p_user_id != auth.uid() THEN
            RAISE EXCEPTION ''Access denied: can only access own data'';
          END IF;
          
          RETURN QUERY
          SELECT * FROM user_recommendations 
          WHERE user_id = p_user_id
          ORDER BY confidence DESC, created_at DESC;
        END;
        $func$ LANGUAGE plpgsql SECURITY DEFINER';
    END IF;
END $$;

-- Function to batch insert user games for better performance
CREATE OR REPLACE FUNCTION secure_batch_insert_user_games(
  p_user_id UUID,
  p_games JSONB
)
RETURNS INTEGER AS $$
DECLARE
  inserted_count INTEGER := 0;
  game_record JSONB;
BEGIN
  -- Verify the user is inserting their own data
  IF p_user_id != auth.uid() THEN
    RAISE EXCEPTION 'Access denied: can only insert own data';
  END IF;
  
  -- Insert games in batch
  FOR game_record IN SELECT * FROM jsonb_array_elements(p_games)
  LOOP
    INSERT INTO user_games (
      user_id,
      game_id,
      status,
      personal_rating,
      hours_played,
      is_wishlist
    ) VALUES (
      p_user_id,
      (game_record->>'game_id')::UUID,
      COALESCE(game_record->>'status', 'backlog'),
      (game_record->>'personal_rating')::INTEGER,
      (game_record->>'hours_played')::DECIMAL,
      COALESCE((game_record->>'is_wishlist')::BOOLEAN, false)
    ) ON CONFLICT (user_id, game_id) DO UPDATE SET
      status = EXCLUDED.status,
      personal_rating = EXCLUDED.personal_rating,
      hours_played = EXCLUDED.hours_played,
      is_wishlist = EXCLUDED.is_wishlist,
      date_added = CURRENT_TIMESTAMP;
    
    inserted_count := inserted_count + 1;
  END LOOP;
  
  RETURN inserted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant appropriate permissions
GRANT EXECUTE ON FUNCTION secure_get_user_games(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION secure_batch_insert_user_games(UUID, JSONB) TO authenticated;

-- Grant permission for user recommendations function only if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'secure_get_user_recommendations') THEN
        EXECUTE 'GRANT EXECUTE ON FUNCTION secure_get_user_recommendations(UUID) TO authenticated';
    END IF;
END $$;

-- Add performance monitoring for RLS policies
CREATE OR REPLACE FUNCTION log_slow_queries()
RETURNS EVENT_TRIGGER AS $$
BEGIN
  -- This will help identify slow queries for future optimization
  RAISE NOTICE 'Query execution time monitoring enabled';
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON FUNCTION secure_get_user_games(UUID) IS 'Security definer function for fast user games retrieval';
COMMENT ON FUNCTION secure_batch_insert_user_games(UUID, JSONB) IS 'Batch insert function for importing multiple games efficiently';

-- Comment on user recommendations function only if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'secure_get_user_recommendations') THEN
        EXECUTE 'COMMENT ON FUNCTION secure_get_user_recommendations(UUID) IS ''Security definer function for fast user recommendations retrieval''';
    END IF;
END $$;