-- Fix RLS policies for user_api_keys table to allow proper upsert operations

-- Drop the existing policy first
DROP POLICY IF EXISTS "Users can only access their own API keys" ON user_api_keys;

-- Create separate policies for different operations
CREATE POLICY "Users can select their own API keys" ON user_api_keys
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own API keys" ON user_api_keys
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own API keys" ON user_api_keys
  FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own API keys" ON user_api_keys
  FOR DELETE USING (auth.uid() = user_id);

-- Ensure the table has RLS enabled
ALTER TABLE user_api_keys ENABLE ROW LEVEL SECURITY;