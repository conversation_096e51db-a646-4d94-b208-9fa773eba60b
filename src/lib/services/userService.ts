import { userAuthAPI, userProfileAPI, userPreferencesAPI } from '../api/users';
import { userGamesAPI } from '../api/collections';
import { UserProfileRecord, UserPreferencesRecord } from '../../types/database';

/**
 * User Service - Business logic for user operations
 * Handles complex user workflows and data transformations
 */
export class UserService {
  /**
   * Complete user registration workflow
   */
  async registerUser(userData: {
    email: string;
    password: string;
    username?: string;
    preferences?: Partial<UserPreferencesRecord>;
  }) {
    const { email, password, username, preferences } = userData;

    try {
      // Register user
      const { data: authData, error: authError } = await userAuthAPI.signUp(email, password, username);
      
      if (authError) {
        throw new Error(`Registration failed: ${authError.message}`);
      }

      if (!authData.user) {
        throw new Error('User registration failed - no user data returned');
      }

      const userId = authData.user.id;

      // Create user profile
      try {
        await userProfileAPI.createProfile({
          id: userId,
          username: username || email.split('@')[0],
          email,
          avatar_url: null,
          bio: null,
          location: null,
          website: null,
          is_public: true,
          gaming_since: null,
          favorite_genres: [],
          favorite_platforms: [],
          steam_id: null,
          xbox_gamertag: null,
          psn_id: null,
          nintendo_friend_code: null
        });
      } catch (profileError) {
        console.warn('Failed to create user profile:', profileError);
        // Continue - profile creation is not critical for registration
      }

      // Create user preferences
      try {
        await userPreferencesAPI.createPreferences({
          user_id: userId,
          theme: preferences?.theme || 'dark',
          language: preferences?.language || 'en',
          timezone: preferences?.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
          email_notifications: preferences?.email_notifications ?? true,
          push_notifications: preferences?.push_notifications ?? true,
          privacy_level: preferences?.privacy_level || 'public',
          default_platform: preferences?.default_platform || 'PC',
          preferred_currency: preferences?.preferred_currency || 'USD',
          date_format: preferences?.date_format || 'MM/DD/YYYY',
          time_format: preferences?.time_format || '12h',
          show_adult_content: preferences?.show_adult_content ?? false,
          auto_add_achievements: preferences?.auto_add_achievements ?? true,
          price_alert_threshold: preferences?.price_alert_threshold || 20
        });
      } catch (preferencesError) {
        console.warn('Failed to create user preferences:', preferencesError);
        // Continue - preferences creation is not critical for registration
      }

      return {
        user: authData.user,
        session: authData.session
      };
    } catch (error) {
      console.error('Error in user registration workflow:', error);
      throw error;
    }
  }

  /**
   * Complete user profile setup
   */
  async completeUserProfile(userId: string, profileData: {
    username?: string;
    bio?: string;
    location?: string;
    website?: string;
    gaming_since?: string;
    favorite_genres?: string[];
    favorite_platforms?: string[];
    steam_id?: string;
    xbox_gamertag?: string;
    psn_id?: string;
    nintendo_friend_code?: string;
  }) {
    try {
      // Update user profile
      const { data: profile, error: profileError } = await userProfileAPI.updateProfile(userId, {
        ...profileData,
        updated_at: new Date().toISOString()
      });

      if (profileError) {
        throw new Error(`Failed to update profile: ${profileError.message}`);
      }

      // Update preferences based on profile data
      if (profileData.favorite_platforms && profileData.favorite_platforms.length > 0) {
        try {
          await userPreferencesAPI.updatePreferences(userId, {
            default_platform: profileData.favorite_platforms[0],
            updated_at: new Date().toISOString()
          });
        } catch (preferencesError) {
          console.warn('Failed to update preferences from profile:', preferencesError);
        }
      }

      return profile;
    } catch (error) {
      console.error('Error completing user profile:', error);
      throw error;
    }
  }

  /**
   * Get complete user data (profile + preferences + stats)
   */
  async getCompleteUserData(userId: string) {
    try {
      const [profileResult, preferencesResult, statsResult] = await Promise.allSettled([
        userProfileAPI.getProfile(userId),
        userPreferencesAPI.getPreferences(userId),
        userGamesAPI.getCollectionStats(userId)
      ]);

      const profile = profileResult.status === 'fulfilled' ? profileResult.value.data : null;
      const preferences = preferencesResult.status === 'fulfilled' ? preferencesResult.value.data : null;
      const stats = statsResult.status === 'fulfilled' ? statsResult.value.data : null;

      // Log any errors
      if (profileResult.status === 'rejected') {
        console.warn('Failed to get user profile:', profileResult.reason);
      }
      if (preferencesResult.status === 'rejected') {
        console.warn('Failed to get user preferences:', preferencesResult.reason);
      }
      if (statsResult.status === 'rejected') {
        console.warn('Failed to get user stats:', statsResult.reason);
      }

      return {
        profile,
        preferences,
        stats,
        isComplete: !!(profile && preferences)
      };
    } catch (error) {
      console.error('Error getting complete user data:', error);
      throw error;
    }
  }

  /**
   * Update user settings (profile + preferences)
   */
  async updateUserSettings(userId: string, settings: {
    profile?: Partial<UserProfileRecord>;
    preferences?: Partial<UserPreferencesRecord>;
  }) {
    const { profile, preferences } = settings;
    const results: { profile?: any; preferences?: any; errors: string[] } = { errors: [] };

    try {
      // Update profile if provided
      if (profile) {
        try {
          const { data: profileData, error: profileError } = await userProfileAPI.updateProfile(userId, {
            ...profile,
            updated_at: new Date().toISOString()
          });

          if (profileError) {
            results.errors.push(`Profile update failed: ${profileError.message}`);
          } else {
            results.profile = profileData;
          }
        } catch (error) {
          results.errors.push(`Profile update error: ${error}`);
        }
      }

      // Update preferences if provided
      if (preferences) {
        try {
          const { data: preferencesData, error: preferencesError } = await userPreferencesAPI.updatePreferences(userId, {
            ...preferences,
            updated_at: new Date().toISOString()
          });

          if (preferencesError) {
            results.errors.push(`Preferences update failed: ${preferencesError.message}`);
          } else {
            results.preferences = preferencesData;
          }
        } catch (error) {
          results.errors.push(`Preferences update error: ${error}`);
        }
      }

      return results;
    } catch (error) {
      console.error('Error updating user settings:', error);
      throw error;
    }
  }

  /**
   * Delete user account and all associated data
   */
  async deleteUserAccount(userId: string) {
    try {
      // This would typically involve:
      // 1. Delete user games
      // 2. Delete user preferences
      // 3. Delete user profile
      // 4. Delete auth user
      // For now, we'll implement a basic version

      const errors: string[] = [];

      // Note: In a real implementation, you'd want to handle this in a transaction
      // and possibly implement a soft delete with data retention policies

      try {
        // Delete user games (this would cascade to related data)
        const { data: userGames } = await userGamesAPI.getUserCollection(userId);
        if (userGames && userGames.length > 0) {
          for (const game of userGames) {
            await userGamesAPI.removeFromCollection(game.id);
          }
        }
      } catch (error) {
        errors.push(`Failed to delete user games: ${error}`);
      }

      try {
        // Delete preferences
        // Note: This would need to be implemented in the API
        console.log('Would delete user preferences for:', userId);
      } catch (error) {
        errors.push(`Failed to delete user preferences: ${error}`);
      }

      try {
        // Delete profile
        // Note: This would need to be implemented in the API
        console.log('Would delete user profile for:', userId);
      } catch (error) {
        errors.push(`Failed to delete user profile: ${error}`);
      }

      // Sign out user
      await userAuthAPI.signOut();

      return {
        success: errors.length === 0,
        errors
      };
    } catch (error) {
      console.error('Error deleting user account:', error);
      throw error;
    }
  }

  /**
   * Validate user profile data
   */
  validateProfileData(profileData: Partial<UserProfileRecord>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (profileData.username) {
      if (profileData.username.length < 3) {
        errors.push('Username must be at least 3 characters long');
      }
      if (profileData.username.length > 30) {
        errors.push('Username must be less than 30 characters');
      }
      if (!/^[a-zA-Z0-9_-]+$/.test(profileData.username)) {
        errors.push('Username can only contain letters, numbers, underscores, and hyphens');
      }
    }

    if (profileData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profileData.email)) {
      errors.push('Invalid email format');
    }

    if (profileData.website && profileData.website.length > 0) {
      try {
        new URL(profileData.website);
      } catch {
        errors.push('Invalid website URL');
      }
    }

    if (profileData.bio && profileData.bio.length > 500) {
      errors.push('Bio must be less than 500 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate user statistics summary
   */
  async generateUserStatsSummary(userId: string) {
    try {
      const { data: userGames, error } = await userGamesAPI.getUserCollection(userId);
      
      if (error) {
        throw new Error(`Failed to get user games: ${error.message}`);
      }

      const games = userGames || [];
      
      // Calculate basic stats
      const totalGames = games.length;
      const completedGames = games.filter(g => g.status === 'completed').length;
      const playingGames = games.filter(g => g.status === 'playing').length;
      const wishlistGames = games.filter(g => g.status === 'wishlist').length;
      const backlogGames = games.filter(g => g.status === 'backlog').length;

      // Calculate completion rate
      const ownedGames = games.filter(g => g.status !== 'wishlist').length;
      const completionRate = ownedGames > 0 ? (completedGames / ownedGames) * 100 : 0;

      // Calculate platform distribution
      const platformCounts = new Map<string, number>();
      games.forEach(game => {
        if (game.platform) {
          platformCounts.set(game.platform, (platformCounts.get(game.platform) || 0) + 1);
        }
      });

      const topPlatforms = Array.from(platformCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([platform, count]) => ({ platform, count }));

      // Calculate total hours played (if available)
      const totalHoursPlayed = games.reduce((total, game) => {
        return total + (game.hours_played || 0);
      }, 0);

      // Calculate average rating
      const ratedGames = games.filter(g => g.personal_rating && g.personal_rating > 0);
      const averageRating = ratedGames.length > 0
        ? ratedGames.reduce((sum, g) => sum + (g.personal_rating || 0), 0) / ratedGames.length
        : 0;

      return {
        totalGames,
        completedGames,
        playingGames,
        wishlistGames,
        backlogGames,
        completionRate: Math.round(completionRate * 100) / 100,
        topPlatforms,
        totalHoursPlayed,
        averageRating: Math.round(averageRating * 100) / 100,
        gamesRated: ratedGames.length
      };
    } catch (error) {
      console.error('Error generating user stats summary:', error);
      throw error;
    }
  }
}

export const userService = new UserService();