import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { TooltipProvider } from '../ui/base';

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <TooltipProvider>
      <div className="grid min-h-screen w-full md:grid-cols-[280px_1fr] bg-gradient-to-br from-background via-background/95 to-background/90">
        <Sidebar />
        <div className="flex flex-col min-h-screen">
          <Header />
          <main className="flex flex-1 flex-col gap-4 p-4 lg:gap-6 lg:p-6 md:ml-0 ml-0">
            <div className="w-full max-w-none">
              {children}
            </div>
          </main>
        </div>
      </div>
    </TooltipProvider>
  );
}