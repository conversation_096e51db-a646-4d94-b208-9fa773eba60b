import { supabase } from './supabase-client';
import { logError } from './supabase-utils';

export interface StorageResult {
  data: string | null;
  error: any;
}

export const storage = {
  uploadGameCover: async (file: File, gameId: string): Promise<StorageResult> => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${gameId}.${fileExt}`;
    
    const { error } = await supabase.storage
      .from('game-covers')
      .upload(fileName, file, { upsert: true });
    
    if (error) {
      logError('uploadGameCover', error, {
        gameId: gameId || 'unknown',
        fileName: fileName || 'unknown'
      });
      
      return { data: null, error };
    }
    
    const { data: publicUrl } = supabase.storage
      .from('game-covers')
      .getPublicUrl(fileName);
    
    return { data: publicUrl.publicUrl, error: null };
  },

  uploadScreenshot: async (file: File, gameId: string, index: number): Promise<StorageResult> => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${gameId}_screenshot_${index}.${fileExt}`;
    
    const { error } = await supabase.storage
      .from('game-screenshots')
      .upload(fileName, file, { upsert: true });
    
    if (error) {
      logError('uploadScreenshot', error, {
        gameId: gameId || 'unknown',
        fileName: fileName || 'unknown',
        index
      });
      
      return { data: null, error };
    }
    
    const { data: publicUrl } = supabase.storage
      .from('game-screenshots')
      .getPublicUrl(fileName);
    
    return { data: publicUrl.publicUrl, error: null };
  },

  uploadUserAvatar: async (file: File, userId: string): Promise<StorageResult> => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}/avatar.${fileExt}`;
    
    const { error } = await supabase.storage
      .from('user-avatars')
      .upload(fileName, file, { upsert: true });
    
    if (error) {
      logError('uploadUserAvatar', error, {
        userId: userId || 'unknown',
        fileName: fileName || 'unknown'
      });
      
      return { data: null, error };
    }
    
    const { data: publicUrl } = supabase.storage
      .from('user-avatars')
      .getPublicUrl(fileName);
    
    return { data: publicUrl.publicUrl, error: null };
  },

  deleteUserAvatar: async (userId: string): Promise<{ data: any; error: any }> => {
    const { data, error } = await supabase.storage
      .from('user-avatars')
      .remove([`${userId}/avatar.jpg`, `${userId}/avatar.png`, `${userId}/avatar.webp`]);
    
    if (error) {
      logError('deleteUserAvatar', error, { userId: userId || 'unknown' });
    }
    
    return { data, error };
  },

  uploadCustomArtwork: async (file: File, userGameId: string, artworkType: string): Promise<StorageResult> => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${userGameId}_${artworkType}.${fileExt}`;
    
    const { error } = await supabase.storage
      .from('custom-artwork')
      .upload(fileName, file, { upsert: true });
    
    if (error) {
      logError('uploadCustomArtwork', error, {
        userGameId: userGameId || 'unknown',
        artworkType: artworkType || 'unknown',
        fileName: fileName || 'unknown'
      });
      
      return { data: null, error };
    }
    
    const { data: publicUrl } = supabase.storage
      .from('custom-artwork')
      .getPublicUrl(fileName);
    
    return { data: publicUrl.publicUrl, error: null };
  },

  deleteCustomArtwork: async (userGameId: string, artworkType: string): Promise<{ data: any; error: any }> => {
    const extensions = ['jpg', 'jpeg', 'png', 'webp'];
    const filesToDelete = extensions.map(ext => `${userGameId}_${artworkType}.${ext}`);
    
    const { data, error } = await supabase.storage
      .from('custom-artwork')
      .remove(filesToDelete);
    
    if (error) {
      logError('deleteCustomArtwork', error, {
        userGameId: userGameId || 'unknown',
        artworkType: artworkType || 'unknown'
      });
    }
    
    return { data, error };
  }
};