import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/base/sheet';
import { Filter } from 'lucide-react';
import { GameFilters } from '@/types/filters';
import { FilterPanel } from './FilterPanel';
import { cn } from '@/lib/utils';

interface FloatingFilterButtonProps {
  filters: GameFilters;
  onUpdateFilter: <T extends keyof GameFilters>(
    filterType: T, 
    update: Partial<GameFilters[T]>
  ) => void;
  onReset: () => void;
  activeFilterCount: number;
  isLoading?: boolean;
  className?: string;
  availablePlatforms?: string[];
}

export function FloatingFilterButton({
  filters,
  onUpdateFilter,
  onReset,
  activeFilterCount,
  isLoading = false,
  className,
  availablePlatforms
}: FloatingFilterButtonProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Show button when scrolled down more than 200px
      setIsVisible(window.scrollY > 200);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleFilterReset = () => {
    onReset();
    setIsOpen(false);
  };

  return (
    <div
      className={cn(
        "fixed bottom-4 right-4 z-50 transition-all duration-300 md:hidden",
        isVisible ? "translate-y-0 opacity-100" : "translate-y-16 opacity-0 pointer-events-none",
        className
      )}
    >
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            size="lg"
            className={cn(
              "rounded-full w-14 h-14 p-0 shadow-lg hover:shadow-xl transition-all duration-300",
              "bg-primary hover:bg-primary/90 border-2 border-background",
              "hover:scale-110 active:scale-95",
              activeFilterCount > 0 && "animate-pulse"
            )}
            aria-label="Open filters"
          >
            <div className="relative">
              <Filter className="h-6 w-6" />
              {activeFilterCount > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -top-3 -right-3 h-6 w-6 flex items-center justify-center p-0 text-xs font-bold animate-bounce"
                >
                  {activeFilterCount > 9 ? '9+' : activeFilterCount}
                </Badge>
              )}
            </div>
          </Button>
        </SheetTrigger>
        
        <SheetContent 
          side="bottom" 
          className="h-[80vh] overflow-y-auto rounded-t-3xl border-t-4 border-primary/20"
        >
          <SheetHeader className="pb-4">
            <SheetTitle className="flex items-center gap-2 text-xl">
              <Filter className="h-6 w-6 text-primary" />
              Filter Games
              {activeFilterCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeFilterCount} active
                </Badge>
              )}
            </SheetTitle>
          </SheetHeader>
          
          <div className="pb-6">
            <FilterPanel
              filters={filters}
              onUpdateFilter={onUpdateFilter}
              onReset={handleFilterReset}
              activeFilterCount={activeFilterCount}
              isLoading={isLoading}
              availablePlatforms={availablePlatforms}
              className="border-none shadow-none bg-transparent"
            />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}