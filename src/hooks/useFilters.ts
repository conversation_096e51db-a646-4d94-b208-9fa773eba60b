import { useReducer, useMemo, useCallback } from 'react';
import { 
  GameFilters, 
  DEFAULT_FILTERS, 
  FilterAction, 
  FilterValidation,
  UseFiltersReturn
} from '@/types/filters';

// Filter reducer
function filterReducer(state: GameFilters, action: FilterAction): GameFilters {
  switch (action.type) {
    case 'UPDATE_FILTER':
      return {
        ...state,
        [action.filterType]: {
          ...state[action.filterType],
          ...action.payload
        }
      };

    case 'RESET_FILTERS':
      return action.keepSearch 
        ? { ...DEFAULT_FILTERS, search: state.search }
        : DEFAULT_FILTERS;

    case 'LOAD_PRESET':
      return action.preset.filters;

    default:
      return state;
  }
}

// Validation function
function validateFilters(filters: GameFilters): FilterValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Year validation
  if (filters.year.enabled) {
    if (filters.year.minYear && filters.year.maxYear) {
      if (filters.year.minYear > filters.year.maxYear) {
        errors.push('Minimum year cannot be greater than maximum year');
      }
    }
    if (filters.year.minYear && filters.year.minYear < 1970) {
      warnings.push('Games from before 1970 are very rare');
    }
    if (filters.year.maxYear && filters.year.maxYear > new Date().getFullYear() + 5) {
      warnings.push('Release dates too far in the future may not be accurate');
    }
  }

  // Rating validation
  if (filters.rating.enabled) {
    if (filters.rating.minRating && filters.rating.maxRating) {
      if (filters.rating.minRating > filters.rating.maxRating) {
        errors.push('Minimum rating cannot be greater than maximum rating');
      }
    }
    if (filters.rating.minRating && filters.rating.minRating < 0) {
      errors.push('Minimum rating cannot be less than 0');
    }
    if (filters.rating.maxRating && filters.rating.maxRating > 100) {
      errors.push('Maximum rating cannot be greater than 100');
    }
  }

  // Search validation
  if (filters.search.enabled && filters.search.query.length < 2) {
    warnings.push('Search queries with less than 2 characters may return too many results');
  }

  // Platform validation
  if (filters.platforms.enabled && filters.platforms.platforms.length === 0) {
    warnings.push('No platforms selected - this will show no results');
  }

  // Genre validation
  if (filters.genres.enabled && filters.genres.genres.length === 0) {
    warnings.push('No genres selected - this may affect search results');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

export function useFilters(initialFilters: GameFilters = DEFAULT_FILTERS): UseFiltersReturn {
  const [filters, dispatch] = useReducer(filterReducer, initialFilters);

  // Update specific filter
  const updateFilter = useCallback(<T extends keyof GameFilters>(
    filterType: T, 
    update: Partial<GameFilters[T]>
  ) => {
    dispatch({
      type: 'UPDATE_FILTER',
      filterType,
      payload: update
    });
  }, []);

  // Reset all filters
  const resetFilters = useCallback((keepSearch = false) => {
    dispatch({
      type: 'RESET_FILTERS',
      keepSearch
    });
  }, []);

  // Load filter preset
  const loadPreset = useCallback((preset: { filters: GameFilters }) => {
    dispatch({
      type: 'LOAD_PRESET',
      preset
    });
  }, []);

  // Validate current filters
  const validateCurrentFilters = useCallback(() => {
    return validateFilters(filters);
  }, [filters]);

  // Calculate if filters are active
  const hasActiveFilters = useMemo(() => {
    return (
      filters.search.enabled ||
      filters.platforms.enabled ||
      filters.genres.enabled ||
      filters.year.enabled ||
      filters.rating.enabled ||
      filters.developer.enabled ||
      filters.publisher.enabled
    );
  }, [filters]);

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0;
    
    if (filters.search.enabled && filters.search.query.trim()) count++;
    if (filters.platforms.enabled && filters.platforms.platforms.length > 0) count++;
    if (filters.genres.enabled && filters.genres.genres.length > 0) count++;
    if (filters.year.enabled && (filters.year.minYear || filters.year.maxYear)) count++;
    if (filters.rating.enabled && (filters.rating.minRating || filters.rating.maxRating)) count++;
    if (filters.developer.enabled && filters.developer.developers.length > 0) count++;
    if (filters.publisher.enabled && filters.publisher.publishers.length > 0) count++;
    
    return count;
  }, [filters]);

  return {
    filters,
    updateFilter,
    resetFilters,
    loadPreset,
    hasActiveFilters,
    activeFilterCount,
    validateFilters: validateCurrentFilters
  };
}