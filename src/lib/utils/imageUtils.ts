/**
 * Image utility functions for handling placeholders, fallbacks, and image operations
 */

export interface PlaceholderOptions {
  width?: number;
  height?: number;
  backgroundColor?: string;
  textColor?: string;
  text?: string;
  fontSize?: number;
}

/**
 * Generate a data URL for an SVG placeholder image
 */
export function generatePlaceholderImage(options: PlaceholderOptions = {}): string {
  const {
    width = 600,
    height = 800,
    backgroundColor = '#4a90e2',
    textColor = '#ffffff',
    text = 'No Image',
    fontSize = 24
  } = options;

  // Sanitize text to prevent XSS and encoding issues
  const sanitizedText = String(text || 'No Image')
    .replace(/[<>&"']/g, (char) => {
      const entities: Record<string, string> = {
        '<': '&lt;',
        '>': '&gt;',
        '&': '&amp;',
        '"': '&quot;',
        "'": '&#39;'
      };
      return entities[char] || char;
    })
    .substring(0, 50); // Limit text length

  // Calculate text positioning
  const textX = width / 2;
  const textY = height / 2;
  
  // Create SVG content with proper escaping
  const svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
<rect width="100%" height="100%" fill="${backgroundColor}"/>
<text x="${textX}" y="${textY}" font-family="system-ui, -apple-system, sans-serif" font-size="${fontSize}" font-weight="500" fill="${textColor}" text-anchor="middle" dominant-baseline="middle">${sanitizedText}</text>
</svg>`;

  // Convert to data URL with proper encoding
  try {
    return `data:image/svg+xml;base64,${btoa(svg)}`;
  } catch (error) {
    // Fallback for encoding issues
    return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
  }
}

/**
 * Generate a game cover placeholder with game name
 */
export function generateGameCoverPlaceholder(gameName: string, type: 'box-art' | 'cover' = 'box-art'): string {
  const colors = {
    'box-art': { bg: '#4a90e2', text: '#ffffff' },
    'cover': { bg: '#50c878', text: '#ffffff' }
  };

  const color = colors[type];
  const displayText = gameName.length > 20 ? `${gameName.substring(0, 17)}...` : gameName;

  return generatePlaceholderImage({
    width: 600,
    height: 800,
    backgroundColor: color.bg,
    textColor: color.text,
    text: displayText,
    fontSize: gameName.length > 15 ? 20 : 24
  });
}

/**
 * Generate a square placeholder for thumbnails
 */
export function generateThumbnailPlaceholder(text: string = 'Image'): string {
  return generatePlaceholderImage({
    width: 300,
    height: 300,
    backgroundColor: '#6b7280',
    textColor: '#ffffff',
    text,
    fontSize: 16
  });
}

/**
 * Check if an image URL is valid and accessible
 */
export async function isImageAccessible(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok && (response.headers.get('content-type')?.startsWith('image/') ?? false);
  } catch {
    return false;
  }
}

/**
 * Get a fallback image URL with error handling
 */
export function getImageWithFallback(
  primaryUrl: string | null | undefined,
  fallbackUrl?: string,
  placeholderText?: string
): string {
  if (primaryUrl && primaryUrl.trim()) {
    return primaryUrl;
  }
  
  if (fallbackUrl && fallbackUrl.trim()) {
    return fallbackUrl;
  }
  
  return generateThumbnailPlaceholder(placeholderText);
}

/**
 * Get a game cover image with proper fallback handling
 */
export function getGameCoverWithFallback(
  coverUrl: string | null | undefined,
  gameName: string,
  type: 'box-art' | 'cover' = 'box-art'
): string {
  if (coverUrl && coverUrl.trim()) {
    return coverUrl;
  }
  
  return generateGameCoverPlaceholder(gameName, type);
}

/**
 * Create an image element with automatic fallback handling
 */
export function createImageWithFallback(
  src: string,
  alt: string,
  fallbackText?: string
): HTMLImageElement {
  const img = new Image();
  img.alt = alt;
  img.src = src;
  
  img.onerror = () => {
    img.src = generateThumbnailPlaceholder(fallbackText || alt);
  };
  
  return img;
}

/**
 * Preload an image and return a promise
 */
export function preloadImage(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = src;
  });
}

/**
 * Extract video ID from YouTube URL
 */
export function extractYouTubeVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /youtube\.com\/v\/([^&\n?#]+)/
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) return match[1];
  }
  
  return null;
}

/**
 * Get YouTube thumbnail URL with fallback handling
 */
export function getYouTubeThumbnail(
  videoId: string, 
  quality: 'maxres' | 'hq' | 'mq' | 'sd' = 'maxres'
): string {
  const qualityMap = {
    maxres: 'maxresdefault.jpg',
    hq: 'hqdefault.jpg',
    mq: 'mqdefault.jpg',
    sd: 'sddefault.jpg'
  };
  
  return `https://img.youtube.com/vi/${videoId}/${qualityMap[quality]}`;
}