import { useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';
import { VoiceSearchState } from './useSearchState';

// Speech Recognition type definitions
interface SpeechRecognitionEvent {
  results: SpeechRecognitionResultList;
}

interface SpeechRecognitionResultList {
  [index: number]: SpeechRecognitionResult;
  length: number;
}

interface SpeechRecognitionResult {
  [index: number]: SpeechRecognitionAlternative;
  length: number;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognitionErrorEvent {
  error: string;
  message: string;
}

export function useVoiceSearch(
  voiceSearch: VoiceSearchState,
  setVoiceSearch: (updates: Partial<VoiceSearchState>) => void,
  onVoiceResult: (transcript: string) => void
) {
  const { user } = useAuth();

  // Start voice search
  const startVoiceSearch = useCallback(() => {
    if (!voiceSearch.isSupported) {
      toast.error('Voice search is not supported in your browser');
      return;
    }

    const recognition = new (window as Window & { webkitSpeechRecognition: new() => {
      continuous: boolean;
      interimResults: boolean;
      lang: string;
      start: () => void;
      stop: () => void;
      onstart: () => void;
      onresult: (event: SpeechRecognitionEvent) => void;
      onerror: (event: SpeechRecognitionErrorEvent) => void;
      onend: () => void;
    }}).webkitSpeechRecognition();
    
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';

    setVoiceSearch({ isListening: true, error: null });

    recognition.onresult = (event: SpeechRecognitionEvent) => {
      const transcript = event.results[0][0].transcript;
      setVoiceSearch({ 
        transcript, 
        isListening: false 
      });
      
      // Process voice search
      onVoiceResult(transcript);
    };

    recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      // Enhanced error logging for speech recognition
      const errorDetails = {
        error: event.error,
        message: event.message || 'Speech recognition error',
        timestamp: new Date().toISOString(),
        userId: user?.id || 'anonymous',
        action: 'speechRecognition',
        context: 'useVoiceSearch'
      };
      
      console.error('❌ Speech Recognition Error:', errorDetails);
      
      // Log to external service if available
      if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
        try {
          window.navigator.sendBeacon('/api/log-error', JSON.stringify(errorDetails));
        } catch (logError) {
          console.warn('Failed to send error log:', logError);
        }
      }
      
      const errorMessage = `Voice search error: ${event.error}`;
      setVoiceSearch({
        isListening: false,
        error: errorMessage
      });
      toast.error(errorMessage);
    };

    recognition.onend = () => {
      setVoiceSearch({ isListening: false });
    };

    recognition.start();
  }, [voiceSearch.isSupported, setVoiceSearch, onVoiceResult, user?.id]);

  // Stop voice search
  const stopVoiceSearch = useCallback(() => {
    setVoiceSearch({ isListening: false });
  }, [setVoiceSearch]);

  return {
    startVoiceSearch,
    stopVoiceSearch
  };
}