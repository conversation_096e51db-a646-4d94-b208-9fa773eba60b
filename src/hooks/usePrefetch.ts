import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/supabase';

/**
 * Hook to prefetch commonly accessed data for better performance
 */
export function usePrefetch() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  useEffect(() => {
    if (!user?.id) return;

    // Clear any potentially corrupted cache first
    queryClient.removeQueries({ queryKey: ['user-collection', user.id] });

    // Prefetch user collection data on login
    const prefetchUserData = () => {
      // Prefetch user collection (most important)
      queryClient.prefetchQuery({
        queryKey: ['user-collection', user.id],
        queryFn: async () => {
          try {
            const { data, error } = await db.userGames.getUserCollection(user.id);
            
            if (error) {
              console.error('Failed to prefetch user collection:', error);
              throw error; // Don't cache failed results
            }
            
            return data || [];
          } catch (error) {
            console.error('Error prefetching user collection:', error);
            throw error; // Don't cache failed results
          }
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
      });

      // Prefetch user profile
      queryClient.prefetchQuery({
        queryKey: ['user-profile', user.id],
        queryFn: async () => {
          try {
            const { data, error } = await db.userProfiles.getProfile(user.id);
            if (error && error.code !== 'PGRST116') {
              console.warn('Failed to fetch user profile:', error);
            }
            return data || null;
          } catch (error) {
            console.warn('Error prefetching user profile:', error);
            return null;
          }
        },
        staleTime: 10 * 60 * 1000, // 10 minutes
      });

      // Prefetch user preferences
      queryClient.prefetchQuery({
        queryKey: ['user-preferences', user.id],
        queryFn: async () => {
          try {
            const { data, error } = await db.userPreferences.getPreferences(user.id);
            if (error && error.code !== 'PGRST116') {
              console.warn('Failed to fetch user preferences:', error);
            }
            return data || null;
          } catch (error) {
            console.warn('Error prefetching user preferences:', error);
            return null;
          }
        },
        staleTime: 15 * 60 * 1000, // 15 minutes
      });
    };

    // Run prefetch with a small delay to not block initial render
    const timeoutId = setTimeout(prefetchUserData, 100);

    return () => clearTimeout(timeoutId);
  }, [user?.id, queryClient]);

  // Prefetch data when hovering over navigation links
  const prefetchPageData = (page: string) => {
    if (!user?.id) return;

    switch (page) {
      case 'search':
        // No prefetch needed for search as it's user input dependent
        break;
      case 'deals':
        // Prefetch deals data if we have a deals API
        break;
      default:
        // User collection is already prefetched
        break;
    }
  };

  return { prefetchPageData };
}

/**
 * Hook to intelligently preload images for better perceived performance
 */
export function useImagePreload() {
  const preloadImage = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = reject;
      img.src = src;
    });
  };

  const preloadImages = async (urls: string[]) => {
    const promises = urls.slice(0, 10).map(url => preloadImage(url)); // Limit to first 10 images
    try {
      await Promise.allSettled(promises);
    } catch (error) {
      console.warn('Some images failed to preload:', error);
    }
  };

  return { preloadImage, preloadImages };
}