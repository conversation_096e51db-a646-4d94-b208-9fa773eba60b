import { useState } from 'react';
import { Checkbox } from '@/components/ui/base/checkbox';
import { Input } from '@/components/ui/base/input';
import { But<PERSON> } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { Switch } from '@/components/ui/base/switch';
import { Label } from '@/components/ui/base/label';
import { Search, X } from 'lucide-react';
import { Platform } from '@/types';
import { PlatformFilter } from '@/types/filters';
import { groupPlatformsByFamily } from '@/lib/utils/platformMapper';

interface PlatformFilterComponentProps {
  filter: PlatformFilter;
  onChange: (update: Partial<PlatformFilter>) => void;
  disabled?: boolean;
  availablePlatforms?: string[];
}

// All available platforms - this could be fetched from API in the future
const ALL_PLATFORMS: Platform[] = [
  'PC', 'PlayStation 5', 'PlayStation 4', 'PlayStation 3', 'PlayStation 2', 'PlayStation',
  'Xbox Series X/S', 'Xbox One', 'Xbox 360', 'Xbox',
  'Nintendo Switch', 'Nintendo 3DS', 'Nintendo DS', 'Nintendo Wii U', 'Nintendo Wii', 'Nintendo GameCube',
  'iOS', 'Android', 'macOS', 'Linux',
  'Steam Deck', 'Epic Games Store', 'GOG',
  'Arcade', 'Browser'
];


export function PlatformFilterComponent({ 
  filter, 
  onChange, 
  disabled = false,
  availablePlatforms
}: PlatformFilterComponentProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Use dynamic platforms from search results, fallback to static list
  const activePlatforms = availablePlatforms && availablePlatforms.length > 0 
    ? availablePlatforms 
    : ALL_PLATFORMS;

  const filteredPlatforms = activePlatforms.filter(platform => 
    platform.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group available platforms by family instead of using static categories
  const dynamicPlatformsByFamily = groupPlatformsByFamily(activePlatforms);
  const platformsByCategory = selectedCategory && dynamicPlatformsByFamily[selectedCategory]
    ? { [selectedCategory]: dynamicPlatformsByFamily[selectedCategory] }
    : dynamicPlatformsByFamily;

  const handleToggleEnabled = (enabled: boolean) => {
    onChange({ enabled });
  };

  const handlePlatformToggle = (platform: Platform) => {
    const isSelected = filter.platforms.includes(platform);
    const newPlatforms = isSelected
      ? filter.platforms.filter(p => p !== platform)
      : [...filter.platforms, platform];
    
    onChange({ platforms: newPlatforms });
  };

  const handleSelectAll = (platforms: Platform[]) => {
    const allSelected = platforms.every(platform => filter.platforms.includes(platform));
    
    if (allSelected) {
      // Deselect all platforms in this category
      const newPlatforms = filter.platforms.filter(p => !platforms.includes(p));
      onChange({ platforms: newPlatforms });
    } else {
      // Select all platforms in this category
      const newPlatforms = [...new Set([...filter.platforms, ...platforms])];
      onChange({ platforms: newPlatforms });
    }
  };

  const clearAll = () => {
    onChange({ platforms: [] });
  };

  const getCategorySelectionState = (platforms: Platform[]) => {
    const selectedCount = platforms.filter(p => filter.platforms.includes(p)).length;
    if (selectedCount === 0) return 'none';
    if (selectedCount === platforms.length) return 'all';
    return 'partial';
  };

  return (
    <div className="space-y-4">
      {/* Enable/Disable Toggle */}
      <div className="flex items-center justify-between">
        <Label htmlFor="platform-enabled" className="text-sm font-medium">
          Filter by Platform
        </Label>
        <Switch
          id="platform-enabled"
          checked={filter.enabled}
          onCheckedChange={handleToggleEnabled}
          disabled={disabled}
        />
      </div>

      {filter.enabled && (
        <>
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search platforms..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              disabled={disabled}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery('')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Selected Platforms Summary */}
          {filter.platforms.length > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex flex-wrap gap-1">
                {filter.platforms.slice(0, 3).map(platform => (
                  <Badge key={platform} variant="secondary" className="text-xs">
                    {platform}
                  </Badge>
                ))}
                {filter.platforms.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{filter.platforms.length - 3} more
                  </Badge>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAll}
                className="text-xs h-6"
                disabled={disabled}
              >
                Clear All
              </Button>
            </div>
          )}

          {/* Category Filter Buttons */}
          {!searchQuery && Object.keys(dynamicPlatformsByFamily).length > 1 && (
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === null ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(null)}
                className="text-xs h-7"
              >
                All Categories
              </Button>
              {Object.keys(dynamicPlatformsByFamily).map(category => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(selectedCategory === category ? null : category)}
                  className="text-xs h-7"
                >
                  {category} ({dynamicPlatformsByFamily[category].length})
                </Button>
              ))}
            </div>
          )}

          {/* Platform Selection */}
          <ScrollArea className="h-48">
            <div className="space-y-4">
              {searchQuery ? (
                // Show filtered results when searching
                <div className="space-y-2">
                  {filteredPlatforms.map(platform => (
                    <div key={platform} className="flex items-center space-x-2">
                      <Checkbox
                        id={`platform-${platform}`}
                        checked={filter.platforms.includes(platform)}
                        onCheckedChange={() => handlePlatformToggle(platform)}
                        disabled={disabled}
                      />
                      <Label
                        htmlFor={`platform-${platform}`}
                        className="text-sm cursor-pointer flex-1"
                      >
                        {platform}
                      </Label>
                    </div>
                  ))}
                  {filteredPlatforms.length === 0 && (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      No platforms found matching "{searchQuery}"
                    </p>
                  )}
                </div>
              ) : (
                // Show by category when not searching
                Object.entries(platformsByCategory).map(([category, platforms]) => (
                  <div key={category} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium text-muted-foreground">
                        {category}
                      </Label>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSelectAll(platforms)}
                        className="text-xs h-6"
                        disabled={disabled}
                      >
                        {getCategorySelectionState(platforms) === 'all' ? 'Deselect All' : 'Select All'}
                      </Button>
                    </div>
                    <div className="space-y-1 pl-4">
                      {platforms.map(platform => (
                        <div key={platform} className="flex items-center space-x-2">
                          <Checkbox
                            id={`platform-${platform}`}
                            checked={filter.platforms.includes(platform)}
                            onCheckedChange={() => handlePlatformToggle(platform)}
                            disabled={disabled}
                          />
                          <Label
                            htmlFor={`platform-${platform}`}
                            className="text-sm cursor-pointer flex-1"
                          >
                            {platform}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </>
      )}
    </div>
  );
}