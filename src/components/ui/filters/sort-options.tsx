import { Button } from '@/components/ui/base/button';
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/base/dropdown-menu';
import { Badge } from '@/components/ui/base/badge';
import { 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown, 
  SortAsc,
  Calendar,
  Star,
  Clock,
  ChevronDown
} from 'lucide-react';

import { SortOptions, SortField, SortDirection } from '@/types/filters';
export type { SortOptions, SortField, SortDirection };

interface SortOptionsProps {
  sortOptions: SortOptions;
  onSortChange: (options: SortOptions) => void;
  showAddedAt?: boolean;
  className?: string;
}

const sortConfigs = {
  title: {
    label: 'Alphabetical',
    icon: SortAsc,
    ascLabel: 'A → Z',
    descLabel: 'Z → A'
  },
  release_date: {
    label: 'Release Date',
    icon: Calendar,
    ascLabel: 'Oldest First',
    descLabel: 'Newest First'
  },
  metacritic_score: {
    label: 'Rating',
    icon: Star,
    ascLabel: 'Lowest First',
    descLabel: 'Highest First'
  },
  added_at: {
    label: 'Date Added',
    icon: Clock,
    ascLabel: 'Oldest First',
    descLabel: 'Recently Added'
  }
};

export function SortOptionsComponent({ 
  sortOptions, 
  onSortChange, 
  showAddedAt = false,
  className 
}: SortOptionsProps) {
  const currentConfig = sortConfigs[sortOptions.field];
  const DirectionIcon = sortOptions.direction === 'asc' ? ArrowUp : ArrowDown;

  const handleSortChange = (field: SortField) => {
    if (field === sortOptions.field) {
      // Toggle direction if same field
      onSortChange({
        field,
        direction: sortOptions.direction === 'asc' ? 'desc' : 'asc'
      });
    } else {
      // Set new field with default direction
      const defaultDirection = field === 'title' ? 'asc' : 'desc';
      onSortChange({
        field,
        direction: defaultDirection
      });
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm text-muted-foreground">Sort by:</span>
      
      <DropdownMenu
        trigger={
          <Button variant="outline" size="sm" className="gap-2">
            <currentConfig.icon className="h-4 w-4" />
            {currentConfig.label}
            <DirectionIcon className="h-3 w-3" />
            <ChevronDown className="h-4 w-4" />
          </Button>
        }
      >
        <DropdownMenuItem onClick={() => handleSortChange('title')}>
          <SortAsc className="h-4 w-4 mr-2" />
          Alphabetical
          {sortOptions.field === 'title' && (
            <Badge variant="secondary" className="ml-auto text-xs">
              {sortOptions.direction === 'asc' ? 'A→Z' : 'Z→A'}
            </Badge>
          )}
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => handleSortChange('release_date')}>
          <Calendar className="h-4 w-4 mr-2" />
          Release Date
          {sortOptions.field === 'release_date' && (
            <Badge variant="secondary" className="ml-auto text-xs">
              {sortOptions.direction === 'desc' ? 'New' : 'Old'}
            </Badge>
          )}
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => handleSortChange('metacritic_score')}>
          <Star className="h-4 w-4 mr-2" />
          Rating
          {sortOptions.field === 'metacritic_score' && (
            <Badge variant="secondary" className="ml-auto text-xs">
              {sortOptions.direction === 'desc' ? 'High' : 'Low'}
            </Badge>
          )}
        </DropdownMenuItem>
        
        {showAddedAt && (
          <DropdownMenuItem onClick={() => handleSortChange('added_at')}>
            <Clock className="h-4 w-4 mr-2" />
            Date Added
            {sortOptions.field === 'added_at' && (
              <Badge variant="secondary" className="ml-auto text-xs">
                {sortOptions.direction === 'desc' ? 'Recent' : 'Old'}
              </Badge>
            )}
          </DropdownMenuItem>
        )}
      </DropdownMenu>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onSortChange({
          ...sortOptions,
          direction: sortOptions.direction === 'asc' ? 'desc' : 'asc'
        })}
        className="px-2"
      >
        <ArrowUpDown className="h-4 w-4" />
      </Button>
    </div>
  );
}