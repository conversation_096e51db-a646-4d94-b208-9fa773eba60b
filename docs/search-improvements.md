# Search Improvements for Codexa

This document outlines recommended improvements to enhance the search functionality in Codexa, addressing the discrepancy between app search results and web-based game databases.

## Current Issues

Our app's search results are sometimes inferior to web versions of game databases (IGDB, TheGamesDB) due to several limitations in our implementation:

- Limited field selection from APIs
- Overly aggressive deduplication
- Simplified search query construction
- Restricted result limits
- Biased scoring algorithm
- Suboptimal caching strategy
- Incomplete API integration
- Query complexity limitations

## Recommended Improvements

### 1. Expand IGDB API Field Selection

**Current Implementation:**

```typescript
const fields = [
  'name',
  'platforms.name',
  'genres.name',
  'first_release_date',
  'summary',
  'cover.image_id',
  'aggregated_rating',
  'version_parent',
  'version_title'
];
```

**Recommended Changes:**

- Add more comprehensive fields to IGDB requests:

  ```typescript
  const fields = [
    'name',
    'platforms.name',
    'genres.name',
    'first_release_date',
    'summary',
    'cover.image_id',
    'screenshots.image_id',
    'aggregated_rating',
    'aggregated_rating_count',
    'rating',
    'rating_count',
    'total_rating',
    'total_rating_count',
    'involved_companies.company.name',
    'involved_companies.developer',
    'involved_companies.publisher',
    'videos.video_id',
    'videos.name',
    'similar_games.name',
    'similar_games.cover.image_id',
    'themes.name',
    'game_modes.name',
    'keywords.name',
    'alternative_names.name',
    'collection.name',
    'franchise.name',
    'version_parent',
    'version_title',
    'category'
  ];
  ```

- If edge function limits are an issue, implement multiple smaller requests:
  - Basic info request (name, platforms, etc.)
  - Media request (screenshots, videos)
  - Related content request (similar games, collections)

### 2. Refine Deduplication Logic

**Current Implementation:**

```typescript
const DEFAULT_OPTIONS: DeduplicationOptions = {
  titleSimilarityThreshold: 0.8,
  platformSpecific: true,
  mergeStrategy: 'merge_best',
  allowPlatformVariants: false
};
```

**Recommended Changes:**

- Make deduplication less aggressive to preserve important game variants:

  ```typescript
  const DEFAULT_OPTIONS: DeduplicationOptions = {
    titleSimilarityThreshold: 0.7,
    platformSpecific: false,
    mergeStrategy: 'prefer_igdb',
    allowPlatformVariants: true
  };
  ```

- Improve the game matching algorithm to better distinguish between:
  - Different editions of the same game
  - Platform-specific versions
  - Remasters vs original games
- Add user preference for deduplication strictness

### 3. Implement Multi-Strategy Search

**Current Implementation:**

```typescript
// Smart search: try exact first, then fuzzy if needed
searchClause = `search "${searchTerm}";`;
```

**Recommended Changes:**

- Implement a multi-strategy search approach:

  ```typescript
  async function enhancedSearch(term: string, options: SearchOptions = {}): Promise<IGDBGame[]> {
    // Try exact search first
    const exactResults = await search(term, { ...options, searchType: 'exact' });
    
    // If results are insufficient, add fuzzy search results
    if (exactResults.length < 10) {
      const fuzzyResults = await search(term, { ...options, searchType: 'fuzzy' });
      return mergeAndDeduplicate(exactResults, fuzzyResults);
    }
    
    return exactResults;
  }
  ```

- Add alternative name searches:
  - Common abbreviations (e.g., "GTA" for "Grand Theft Auto")
  - Regional name variations
  - Sequels without numbers (e.g., "Halo Infinite" for "Halo 6")
- Implement keyword-based search for genres and themes

### 4. Increase Result Limits and Improve Pagination

**Current Implementation:**

```typescript
limit: platformFilters.length > 0 ? 75 : 50 // Higher limit for platform-specific searches
```

**Recommended Changes:**

- Increase default limits for better coverage:

  ```typescript
  limit: platformFilters.length > 0 ? 150 : 100 // Higher limits for better coverage
  ```

- Implement proper pagination:

  ```typescript
  async function paginatedSearch(term: string, page: number = 1, pageSize: number = 50): Promise<{
    games: Game[],
    hasMore: boolean,
    totalCount: number
  }> {
    const offset = (page - 1) * pageSize;
    const results = await search(term, { limit: pageSize, offset });
    return {
      games: results,
      hasMore: results.length === pageSize,
      totalCount: estimateTotalCount(results, page, pageSize)
    };
  }
  ```

- Add "Load more" functionality for users
- Implement infinite scrolling for search results

### 5. Balance Scoring Algorithm

**Current Implementation:**

```typescript
const DEFAULT_WEIGHTS: ScoringWeights = {
  titleSimilarity: 0.5,    // 50% - Most important
  platformRelevance: 0.2,  // 20% - Platform preference
  recencyScore: 0.15,      // 15% - Newer games slight boost
  ratingScore: 0.15        // 15% - Quality preference
};
```

**Recommended Changes:**

- Adjust scoring weights for more balanced results:

  ```typescript
  const DEFAULT_WEIGHTS: ScoringWeights = {
    titleSimilarity: 0.4,    // 40% - Still important but less dominant
    platformRelevance: 0.2,  // 20% - Same weight for platform preference
    recencyScore: 0.1,       // 10% - Reduced recency bias
    ratingScore: 0.2,        // 20% - Increased quality preference
    popularityScore: 0.1     // 10% - New factor for game popularity
  };
  ```

- Add new scoring factors:
  - Popularity (based on player count, follows, etc.)
  - Relevance to user's library
  - Cultural significance (awards, influence)
- Allow users to customize scoring weights based on preferences

### 6. Optimize Caching Strategy

**Current Implementation:**

```typescript
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache
```

**Recommended Changes:**

- Implement smarter, context-aware caching:

  ```typescript
  // Dynamic TTL based on query type
  function getDynamicCacheTTL(query: string): number {
    // Popular searches cache longer
    if (isPopularSearch(query)) return 10 * 60 * 1000; // 10 minutes
    
    // New releases cache shorter
    if (query.includes('new') || query.includes('upcoming')) {
      return 2 * 60 * 1000; // 2 minutes
    }
    
    // Default
    return 5 * 60 * 1000; // 5 minutes
  }
  ```

- Add cache-busting mechanisms:
  - Force refresh option for users
  - Automatic refresh for critical searches
  - Time-based cache invalidation for trending games
- Implement client-side caching for frequently accessed data

### 7. Enhance API Integration

**Current Implementation:**

- IGDB: Primary search
- TheGamesDB: Supplementary data
- SteamGridDB: Primarily artwork

**Recommended Changes:**

- Better leverage all three APIs:

  ```typescript
  async function comprehensiveSearch(term: string): Promise<Game[]> {
    // Run all searches in parallel
    const [igdbResults, tgdbResults, steamResults, steamApiResults] = 
      await Promise.all([
        igdbAPI.search(term),
        theGamesDBAPI.searchByName(term),
        steamGridDBAPI.searchWithArtwork(term),
        steamAPI.searchGames(term) // New API integration
      ]);
    
    // Intelligent merging with source prioritization
    return intelligentMerge([
      { source: 'igdb', results: igdbResults, priority: 1 },
      { source: 'tgdb', results: tgdbResults, priority: 2 },
      { source: 'steam', results: steamResults, priority: 3 },
      { source: 'steamApi', results: steamApiResults, priority: 2 }
    ]);
  }
  ```

- Consider adding additional data sources:
  - Steam API for PC games
  - PlayStation Store API
  - Xbox Game Pass API
  - Nintendo eShop data
- Implement source-specific optimizations for each API

### 8. Improve Query Complexity

**Current Implementation:**

```typescript
// Only add basic filtering to avoid query complexity issues
if (excludeDLC) {
  whereConditions.push('category != 1');
}
```

**Recommended Changes:**

- Optimize edge functions to handle more sophisticated queries:

  ```typescript
  // Enhanced query builder with complexity management
  function buildOptimizedQuery(params: QueryParams): string {
    const { searchTerm, filters, sort, limit } = params;
    
    // Split complex query into manageable parts
    const baseQuery = buildBaseQuery(searchTerm);
    const filterQuery = buildFilterQuery(filters);
    const sortQuery = buildSortQuery(sort);
    
    // Combine with complexity checks
    return optimizeQueryComplexity(`
      ${baseQuery}
      ${filterQuery}
      ${sortQuery}
      limit ${limit};
    `);
  }
  ```

- Implement query splitting for complex searches:
  - Break down complex queries into multiple simpler ones
  - Merge results client-side when necessary
- Add client-side filtering for certain conditions
- Optimize edge function configuration:
  - Increase timeout limits
  - Allocate more memory
  - Implement retry logic with backoff

## Implementation Priority

1. **Expand IGDB Field Selection** - Highest impact for minimal effort
2. **Balance Scoring Algorithm** - Quick win to improve result relevance
3. **Refine Deduplication Logic** - Critical for preserving important game variants
4. **Implement Multi-Strategy Search** - Significant improvement in search coverage
5. **Increase Result Limits** - Easy improvement for result quantity
6. **Optimize Caching Strategy** - Important for performance and freshness
7. **Enhance API Integration** - Longer-term improvement for data quality
8. **Improve Query Complexity** - Technical foundation for advanced features

## Success Metrics

To measure the effectiveness of these improvements:

1. **Result Relevance**: Compare top 10 results with web versions for popular searches
2. **Result Coverage**: Track the number of unique games returned for various queries
3. **User Satisfaction**: Implement a simple "Was this helpful?" feedback mechanism
4. **Search Abandonment Rate**: Track how often users abandon searches without selecting a result
5. **Search-to-Detail Ratio**: Measure how often searches lead to game detail views

## Conclusion

Implementing these improvements will significantly enhance the search experience in Codexa, bringing results closer to or exceeding the quality of web-based game databases. The changes range from simple configuration adjustments to more complex architectural improvements, allowing for incremental implementation based on development resources and priorities.
