// Re-export from organized utils
export * from './utils/validationUtils';

import { supabase } from './supabase';

/**
 * Utility function to ensure user records exist for the current user
 * This can be called manually to fix existing users who might be missing records
 */
export async function ensureCurrentUserRecords() {
  try {
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('Error getting current user:', userError);
      return { success: false, error: userError.message };
    }
    
    if (!user) {
      console.warn('No authenticated user found');
      return { success: false, error: 'No authenticated user' };
    }
    
    const userId = user.id;
    const results = {
      profile: { exists: false, created: false },
      preferences: { exists: false, created: false }
    };
    
    // Check and create user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('id', userId)
      .single();
    
    if (profileError && profileError.code === 'PGRST116') {
      // Profile doesn't exist, create it
      console.log('Creating user profile for user:', userId);
      const { error: createProfileError } = await supabase
        .from('user_profiles')
        .insert({
          id: userId,
          username: user.user_metadata?.username || null,
          display_name: user.user_metadata?.display_name || 
                       user.user_metadata?.username || 
                       user.email?.split('@')[0] || 'User'
        });
      
      if (createProfileError) {
        console.error('Failed to create user profile:', createProfileError);
        results.profile = { exists: false, created: false };
      } else {
        console.log('✅ User profile created successfully');
        results.profile = { exists: false, created: true };
      }
    } else if (profileError) {
      console.error('Error checking user profile:', profileError);
      results.profile = { exists: false, created: false };
    } else {
      console.log('✅ User profile already exists');
      results.profile = { exists: true, created: false };
    }
    
    // Check and create user preferences
    const { error: preferencesError } = await supabase
      .from('user_preferences')
      .select('id')
      .eq('user_id', userId)
      .single();
    
    if (preferencesError && preferencesError.code === 'PGRST116') {
      // Preferences don't exist, create them
      console.log('Creating user preferences for user:', userId);
      const { error: createPreferencesError } = await supabase
        .from('user_preferences')
        .insert({
          user_id: userId,
          theme: 'system',
          show_completed_games: true,
          enable_notifications: false,
          auto_backup: false,
          onboarding_completed: false
        });
      
      if (createPreferencesError) {
        console.error('Failed to create user preferences:', createPreferencesError);
        results.preferences = { exists: false, created: false };
      } else {
        console.log('✅ User preferences created successfully');
        results.preferences = { exists: false, created: true };
      }
    } else if (preferencesError) {
      console.error('Error checking user preferences:', preferencesError);
      results.preferences = { exists: false, created: false };
    } else {
      console.log('✅ User preferences already exist');
      results.preferences = { exists: true, created: false };
    }
    
    return {
      success: true,
      userId,
      results,
      message: `Profile: ${results.profile.exists ? 'existed' : results.profile.created ? 'created' : 'failed'}, Preferences: ${results.preferences.exists ? 'existed' : results.preferences.created ? 'created' : 'failed'}`
    };
    
  } catch (error) {
    console.error('Error ensuring user records:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Function to check if user records exist without creating them
 */
export async function checkUserRecords() {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'No authenticated user' };
    }
    
    const userId = user.id;
    
    // Check user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('id', userId)
      .single();
    
    // Check user preferences
    const { error: preferencesError } = await supabase
      .from('user_preferences')
      .select('id')
      .eq('user_id', userId)
      .single();
    
    return {
      success: true,
      userId,
      profile: {
        exists: !profileError || profileError.code !== 'PGRST116',
        error: profileError?.message
      },
      preferences: {
        exists: !preferencesError || preferencesError.code !== 'PGRST116',
        error: preferencesError?.message
      }
    };
    
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Make functions available globally for debugging
if (typeof window !== 'undefined') {
  (window as typeof window & {
    ensureCurrentUserRecords?: typeof ensureCurrentUserRecords;
    checkUserRecords?: typeof checkUserRecords;
  }).ensureCurrentUserRecords = ensureCurrentUserRecords;
  (window as typeof window & {
    ensureCurrentUserRecords?: typeof ensureCurrentUserRecords;
    checkUserRecords?: typeof checkUserRecords;
  }).checkUserRecords = checkUserRecords;
}