import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Zap } from 'lucide-react';
import { But<PERSON> } from '../ui/base/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/base/card';
import { Badge } from '../ui/base/badge';
import { Separator } from '../ui/base/separator';
import { ThemeToggle, CompactThemeToggle, LabeledThemeToggle } from '../ui/theme-toggle';
import { useTheme } from '../../hooks/useTheme';

interface TestResult {
  name: string;
  passed: boolean;
  description: string;
}

export function DarkModeTest() {
  const { theme, resolvedTheme, systemTheme, isLoading, error } = useTheme();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const runTests = async () => {
    setIsRunningTests(true);
    const results: TestResult[] = [];

    // Test 1: Theme Context Available
    results.push({
      name: 'Theme Context',
      passed: !isLoading && !error,
      description: 'Theme context is available and working'
    });

    // Test 2: CSS Variables Present
    const root = document.documentElement;
    const bgColor = getComputedStyle(root).getPropertyValue('--background');
    results.push({
      name: 'CSS Variables',
      passed: bgColor.trim() !== '',
      description: 'CSS custom properties are defined'
    });

    // Test 3: Theme Classes Applied
    const hasThemeClass = root.classList.contains('light') || root.classList.contains('dark');
    results.push({
      name: 'Theme Classes',
      passed: hasThemeClass,
      description: 'Theme classes are applied to document'
    });

    // Test 4: System Theme Detection
    const supportsColorScheme = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').media !== 'not all';
    results.push({
      name: 'System Detection',
      passed: supportsColorScheme,
      description: 'Browser supports prefers-color-scheme'
    });

    // Test 5: Local Storage
    const storedTheme = localStorage.getItem('codexa-theme');
    results.push({
      name: 'Persistence',
      passed: storedTheme !== null,
      description: 'Theme preference is stored in localStorage'
    });

    // Test 6: Color Contrast (basic check)
    const computedBg = getComputedStyle(document.body).backgroundColor;
    const computedColor = getComputedStyle(document.body).color;
    results.push({
      name: 'Color Contrast',
      passed: computedBg !== computedColor,
      description: 'Background and text colors are different'
    });

    // Test 7: Smooth Transitions
    const hasTransition = getComputedStyle(document.body).transition.includes('background-color');
    results.push({
      name: 'Smooth Transitions',
      passed: hasTransition,
      description: 'CSS transitions are applied'
    });

    // Test 8: Reduced Motion Support
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    results.push({
      name: 'Reduced Motion',
      passed: true, // Always pass as this is a preference check
      description: `User prefers ${prefersReducedMotion ? 'reduced' : 'normal'} motion`
    });

    setTestResults(results);
    setIsRunningTests(false);
  };

  useEffect(() => {
    // Run tests automatically when component mounts
    setTimeout(runTests, 1000);
  }, []);

  const passedTests = testResults.filter(test => test.passed).length;
  const totalTests = testResults.length;
  const allTestsPassed = passedTests === totalTests && totalTests > 0;

  return (
    <div className="space-y-6 p-6 max-w-4xl mx-auto">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">🧪 Dark Mode Test Suite</h1>
        <p className="text-muted-foreground">
          Comprehensive testing of dark mode implementation
        </p>
      </div>

      {/* Test Results Summary */}
      <Card className={`border-2 ${allTestsPassed ? 'border-green-500' : 'border-yellow-500'}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {allTestsPassed ? (
              <Check className="h-5 w-5 text-green-500" />
            ) : (
              <Zap className="h-5 w-5 text-yellow-500" />
            )}
            Test Results
          </CardTitle>
          <CardDescription>
            {totalTests > 0 ? (
              <>
                {passedTests} of {totalTests} tests passed
                {allTestsPassed && ' - All systems operational! 🎉'}
              </>
            ) : (
              'Running tests...'
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {testResults.map((test, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                <div className="flex items-center gap-3">
                  {test.passed ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-red-500" />
                  )}
                  <div>
                    <div className="font-medium">{test.name}</div>
                    <div className="text-sm text-muted-foreground">{test.description}</div>
                  </div>
                </div>
                <Badge variant={test.passed ? 'default' : 'destructive'}>
                  {test.passed ? 'PASS' : 'FAIL'}
                </Badge>
              </div>
            ))}
          </div>
          
          <div className="mt-4 pt-4 border-t">
            <Button 
              onClick={runTests} 
              disabled={isRunningTests}
              className="w-full"
            >
              {isRunningTests ? 'Running Tests...' : 'Run Tests Again'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Theme Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            Current Theme Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="text-sm font-medium">Selected Theme</div>
              <Badge variant="outline" className="gap-2">
                {theme === 'light' && <Sun className="h-3 w-3" />}
                {theme === 'dark' && <Moon className="h-3 w-3" />}
                {theme === 'system' && <Monitor className="h-3 w-3" />}
                {theme.charAt(0).toUpperCase() + theme.slice(1)}
              </Badge>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Resolved Theme</div>
              <Badge variant={resolvedTheme === 'dark' ? 'default' : 'secondary'} className="gap-2">
                {resolvedTheme === 'dark' ? <Moon className="h-3 w-3" /> : <Sun className="h-3 w-3" />}
                {resolvedTheme.charAt(0).toUpperCase() + resolvedTheme.slice(1)}
              </Badge>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">System Preference</div>
              <Badge variant="outline" className="gap-2">
                {systemTheme === 'dark' ? <Moon className="h-3 w-3" /> : <Sun className="h-3 w-3" />}
                {systemTheme.charAt(0).toUpperCase() + systemTheme.slice(1)}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Theme Toggle Variants */}
      <Card>
        <CardHeader>
          <CardTitle>Theme Toggle Components</CardTitle>
          <CardDescription>
            Test all theme toggle variants
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium">Standard Toggle</h4>
              <p className="text-sm text-muted-foreground">
                Cycles through light → dark → system
              </p>
              <ThemeToggle size="md" />
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium">Compact Toggle</h4>
              <p className="text-sm text-muted-foreground">
                Space-efficient version
              </p>
              <CompactThemeToggle />
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium">Labeled Toggle</h4>
              <p className="text-sm text-muted-foreground">
                With visible label
              </p>
              <LabeledThemeToggle />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interactive Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Visual Test
          </CardTitle>
          <CardDescription>
            Verify visual consistency across components
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button variant="default">Primary</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="destructive">Destructive</Button>
          </div>
          
          <Separator />
          
          <div className="flex flex-wrap gap-2">
            <Badge>Default</Badge>
            <Badge variant="secondary">Secondary</Badge>
            <Badge variant="outline">Outline</Badge>
            <Badge variant="destructive">Destructive</Badge>
          </div>

          <Separator />

          <div className="p-4 rounded-lg border bg-muted/50">
            <p className="text-sm">
              This is a muted background area to test contrast and readability.
              The text should be clearly visible in both light and dark modes.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Manual Testing Instructions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>Click any theme toggle to cycle through themes</li>
            <li>Verify smooth transitions between light and dark modes</li>
            <li>Check that all UI elements maintain proper contrast</li>
            <li>Test keyboard navigation (Tab to toggle, Enter to activate)</li>
            <li>Reload the page to verify theme persistence</li>
            <li>Change your system theme preference to test auto-detection</li>
            <li>Verify the theme toggle appears in both header and mobile menu</li>
            <li>Test on different screen sizes and devices</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  );
}
