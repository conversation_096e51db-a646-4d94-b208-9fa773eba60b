/**
 * Smart debouncing utility for search queries
 * Provides adaptive debouncing based on query similarity and user behavior
 */

interface DebouncedFunction<T extends (...args: unknown[]) => unknown> {
  (...args: Parameters<T>): void;
  cancel: () => void;
  flush: () => void;
}

interface SmartDebounceOptions {
  delay: number;
  minDelay?: number;
  maxDelay?: number;
  similarityThreshold?: number;
  adaptiveScaling?: boolean;
}

interface QueryHistory {
  query: string;
  timestamp: number;
  delay: number;
}

class SmartDebounceManager {
  private queryHistory: QueryHistory[] = [];
  private readonly maxHistorySize = 10;
  
  /**
   * Calculate similarity between two strings using simple character overlap
   */
  private calculateSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1.0;
    if (!str1 || !str2) return 0;
    
    const s1 = str1.toLowerCase();
    const s2 = str2.toLowerCase();
    
    // Character overlap similarity
    const chars1 = new Set(s1);
    const chars2 = new Set(s2);
    const chars1Array = Array.from(chars1);
    const intersection = new Set(chars1Array.filter(x => chars2.has(x)));
    const union = new Set([...chars1Array, ...Array.from(chars2)]);
    
    return intersection.size / union.size;
  }
  
  /**
   * Get adaptive delay based on query history and similarity
   */
  getAdaptiveDelay(query: string, baseDelay: number, options: SmartDebounceOptions): number {
    if (!options.adaptiveScaling) {
      return baseDelay;
    }
    
    const { minDelay = 100, maxDelay = 1000, similarityThreshold = 0.7 } = options;
    
    if (this.queryHistory.length === 0) {
      return baseDelay;
    }
    
    const lastQuery = this.queryHistory[this.queryHistory.length - 1];
    const similarity = this.calculateSimilarity(query, lastQuery.query);
    
    // If queries are very similar, use shorter delay
    if (similarity > similarityThreshold) {
      return Math.max(minDelay, baseDelay * 0.5);
    }
    
    // If query is completely different, use normal delay
    if (similarity < 0.3) {
      return Math.min(maxDelay, baseDelay);
    }
    
    // Scale delay based on similarity
    const scaleFactor = 1 - (similarity * 0.4);
    return Math.max(minDelay, Math.min(maxDelay, baseDelay * scaleFactor));
  }
  
  /**
   * Add query to history
   */
  addToHistory(query: string, delay: number): void {
    this.queryHistory.push({
      query,
      timestamp: Date.now(),
      delay
    });
    
    // Trim history to max size
    if (this.queryHistory.length > this.maxHistorySize) {
      this.queryHistory.shift();
    }
  }
  
  /**
   * Check if this query was recently searched
   */
  isRecentDuplicate(query: string, timeWindow: number = 5000): boolean {
    const now = Date.now();
    return this.queryHistory.some(entry => 
      entry.query === query && 
      (now - entry.timestamp) < timeWindow
    );
  }
  
  /**
   * Clear history (useful for cleanup)
   */
  clearHistory(): void {
    this.queryHistory = [];
  }
}

// Global instance for managing debounce across components
const globalDebounceManager = new SmartDebounceManager();

/**
 * Smart debounce function with adaptive delays
 */
export function smartDebounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  options: SmartDebounceOptions
): DebouncedFunction<T> {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastArgs: Parameters<T>;
  let lastThis: ThisParameterType<T>;
  
  const debouncedFunction = function(this: ThisParameterType<T>, ...args: Parameters<T>) {
    lastArgs = args;
    lastThis = this;
    
    // Extract query from first argument (assuming it's the search term)
    const query = args[0] as string;
    
    // Check if this is a recent duplicate
    if (globalDebounceManager.isRecentDuplicate(query)) {
      console.log(`⚡ Skipping duplicate query: "${query}"`);
      return;
    }
    
    // Calculate adaptive delay
    const adaptiveDelay = globalDebounceManager.getAdaptiveDelay(
      query,
      options.delay,
      options
    );
    
    console.log(`⏱️ Smart debounce: "${query}" (delay: ${adaptiveDelay}ms)`);
    
    // Clear existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    // Set new timeout with adaptive delay
    timeoutId = setTimeout(() => {
      // Add to history
      globalDebounceManager.addToHistory(query, adaptiveDelay);
      
      // Execute function
      func.apply(lastThis, lastArgs);
      timeoutId = null;
    }, adaptiveDelay);
  } as DebouncedFunction<T>;
  
  // Cancel function
  debouncedFunction.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };
  
  // Flush function (execute immediately)
  debouncedFunction.flush = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      func.apply(lastThis, lastArgs);
      timeoutId = null;
    }
  };
  
  return debouncedFunction;
}

/**
 * Simple debounce with just delay (backward compatibility)
 */
export function simpleDebounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  delay: number
): DebouncedFunction<T> {
  return smartDebounce(func, { delay, adaptiveScaling: false });
}

/**
 * Debounce specifically optimized for search queries
 */
export function searchDebounce<T extends (query: string, ...args: unknown[]) => unknown>(
  searchFunc: T,
  options: Partial<SmartDebounceOptions> = {}
): DebouncedFunction<T> {
  const defaultOptions: SmartDebounceOptions = {
    delay: 300,
    minDelay: 150,
    maxDelay: 600,
    similarityThreshold: 0.7,
    adaptiveScaling: true,
    ...options
  };
  
  return smartDebounce(searchFunc as (...args: unknown[]) => unknown, defaultOptions) as DebouncedFunction<T>;
}

/**
 * Clear global debounce history (useful for component cleanup)
 */
export function clearDebounceHistory(): void {
  globalDebounceManager.clearHistory();
}

/**
 * Get debounce statistics (for debugging)
 */
export function getDebounceStats() {
  return {
    historySize: globalDebounceManager['queryHistory'].length,
    recentQueries: globalDebounceManager['queryHistory'].slice(-5).map(entry => ({
      query: entry.query,
      delay: entry.delay,
      age: Date.now() - entry.timestamp
    }))
  };
}