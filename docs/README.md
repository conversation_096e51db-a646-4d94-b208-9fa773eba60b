# Documentation

This directory contains the core documentation for the Codexa gaming library application.

## Documentation Structure

### Core Documents

- **[prd.md](prd.md)** - Product Requirements Document
  - Comprehensive product specification and technical requirements
  - Updated to reflect current implementation status
  - Includes implemented features and future roadmap

- **[feature.md](feature.md)** - Future Features & Improvements
  - Detailed roadmap for upcoming enhancements
  - Organized by implementation phases
  - Includes already implemented features as of July 2025

- **[DASHBOARD_COMPONENTS.md](DASHBOARD_COMPONENTS.md)** - Dashboard Components Documentation
  - Technical documentation for dashboard components
  - Component structure and implementation details
  - Development guidelines and best practices

## Main Documentation

The primary documentation for developers is located in the root directory:

- **[CLAUDE.md](../CLAUDE.md)** - Comprehensive development guide for Claude Code
  - Development rules and best practices
  - Architecture overview and technical stack
  - Database schema and migration management
  - Code quality standards and workflows

- **[README.md](../README.md)** - Project overview and setup instructions
  - Quick start guide and installation
  - Feature overview and tech stack
  - API key management and configuration

## Documentation Maintenance

- **Last Cleanup**: July 2025
- **Status**: Active and maintained
- **Update Policy**: Documents should be updated when significant features are added or architectural changes are made

## Removed Documentation

The following documentation has been removed as it was outdated or covered by other sources:

- `TODO.md` - Replaced by comprehensive CLAUDE.md
- `ISSUES.md` - Issues resolved and integrated into codebase
- `SECURITY_ISSUE_API_KEYS.md` - Security issue resolved
- `SETUP_INSTRUCTIONS.md` - Covered in main README
- `TOAST_DEMO.md` - Feature implemented and documented elsewhere
- `AI_RECOMMENDATIONS_REFACTOR.md` - Refactor completed
- `PRICE_TRACKING_IMPLEMENTATION.md` - Implementation details integrated

## Contributing to Documentation

When updating documentation:

1. Keep information current with the actual implementation
2. Update the "Last Updated" dates when making significant changes
3. Follow the established documentation structure and formatting
4. Ensure technical accuracy and completeness
5. Remove outdated information promptly
