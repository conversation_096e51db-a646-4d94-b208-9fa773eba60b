import { useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { enhancedSearchService, SearchHistory, SearchIntent } from '../../lib/enhancedSearchService';

export function useSearchUtils() {
  const { user } = useAuth();

  // Get popular searches
  const { data: popularSearches } = useQuery({
    queryKey: ['popular-searches'],
    queryFn: () => enhancedSearchService.getPopularSearches(),
    staleTime: 30 * 60 * 1000, // 30 minutes
    retry: 1
  });

  // Get search suggestions based on current query
  const getSearchSuggestions = useCallback((query: string, searchHistory: SearchHistory[]): string[] => {
    if (!query.trim()) return popularSearches || [];
    
    // Combine history and popular searches
    const historySuggestions = searchHistory
      .filter(h => h.query.toLowerCase().includes(query.toLowerCase()))
      .map(h => h.query)
      .slice(0, 3);
    
    const popularSuggestions = (popularSearches || [])
      .filter(p => p.toLowerCase().includes(query.toLowerCase()))
      .slice(0, 3);
    
    return Array.from(new Set([...historySuggestions, ...popularSuggestions]));
  }, [popularSearches]);

  // Analyze search intent
  const analyzeSearchIntent = useCallback(async (query: string): Promise<SearchIntent | null> => {
    if (!query.trim()) return null;
    
    try {
      const result = await enhancedSearchService.enhancedSearch(query, user?.id);
      return result.intent;
    } catch (error) {
      // Enhanced error logging for intent analysis
      const errorDetails = {
        message: (error as Error)?.message || 'Unknown intent analysis error',
        name: (error as Error)?.name || 'IntentAnalysisError',
        stack: (error as Error)?.stack || 'No stack trace',
        timestamp: new Date().toISOString(),
        userId: user?.id || 'anonymous',
        action: 'analyzeSearchIntent',
        context: 'useSearchUtils',
        query: query || 'unknown'
      };
      
      console.error('❌ Intent Analysis Error:', errorDetails);
      
      // Log to external service if available
      if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
        try {
          window.navigator.sendBeacon('/api/log-error', JSON.stringify(errorDetails));
        } catch (logError) {
          console.warn('Failed to send error log:', logError);
        }
      }
      
      return null;
    }
  }, [user?.id]);

  return {
    popularSearches,
    getSearchSuggestions,
    analyzeSearchIntent
  };
}