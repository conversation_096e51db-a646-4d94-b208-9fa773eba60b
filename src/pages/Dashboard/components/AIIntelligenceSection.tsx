import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/base';
import { CollectionInsightsCard, BacklogOptimizationCard, AIRecommendationsCard } from '@/components/ui/analytics';
import { useDashboardInsights } from '@/hooks/useCollectionInsights';
import { Brain } from 'lucide-react';
import ErrorBoundary from '@/components/layout/ErrorBoundary';

function AIIntelligenceSectionContent() {
  const { 
    collectionStats, 
    overallInsightsScore, 
    averageScore,
    isLoading: insightsLoading 
  } = useDashboardInsights();

  if (insightsLoading || !collectionStats.stats) {
    return null;
  }

  return (
    <>
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Collection Insights */}
        <CollectionInsightsCard className="lg:col-span-2" />
        
        {/* Overall AI Score */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI Intelligence Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-4">
              <div className="text-4xl font-bold text-primary">
                {isNaN(averageScore) ? '0' : averageScore}/100
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Collection Health</span>
                  <span>{Math.round(overallInsightsScore.collectionHealth || 0)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Backlog Efficiency</span>
                  <span>{Math.round(overallInsightsScore.backlogEfficiency || 0)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Discovery Progress</span>
                  <span>{Math.round(overallInsightsScore.discoveryProgress || 0)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Personality Alignment</span>
                  <span>{Math.round(overallInsightsScore.personalityAlignment || 0)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Recommendations */}
      <AIRecommendationsCard />

      {/* Backlog Optimization */}
      <BacklogOptimizationCard />
    </>
  );
}

export function AIIntelligenceSection() {
  return (
    <ErrorBoundary>
      <AIIntelligenceSectionContent />
    </ErrorBoundary>
  );
}