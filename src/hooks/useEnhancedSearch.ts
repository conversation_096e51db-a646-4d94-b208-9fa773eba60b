// This file now re-exports the composed hook for backward compatibility
// The original large hook has been broken down into focused hooks in the /search folder

export { 
  useEnhancedSearchComposed as useEnhancedSearch,
  useSearchHistory,
  useSearchAnalytics,
  type SearchFilters,
  type VoiceSearchState
} from './search';

// For components that want to use the individual hooks directly
export {
  useSearchState,
  useSearchActions,
  useVoiceSearch,
  useSearchUtils
} from './search';