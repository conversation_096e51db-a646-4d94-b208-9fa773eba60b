import { useUserStats, useRecentActivity } from '@/hooks/useUserStats';
import { useDashboardInsights } from '@/hooks/useCollectionInsights';
import { useDeals } from '@/hooks/usePriceTracking';

export interface DashboardData {
  stats: {
    data: any;
    isLoading: boolean;
    error: any;
  };
  recentActivity: {
    data: any;
    isLoading: boolean;
  };
  insights: {
    collectionStats: any;
    overallInsightsScore: any;
    averageScore: number;
    isLoading: boolean;
  };
  deals: {
    bestDeals: any[];
    dealStats: any;
    isLoading: boolean;
  };
}

export function useDashboardData(): DashboardData {
  const { data: stats, isLoading: statsLoading, error: statsError } = useUserStats();
  const { data: recentActivity, isLoading: activityLoading } = useRecentActivity();
  const { 
    collectionStats, 
    overallInsightsScore, 
    averageScore,
    isLoading: insightsLoading 
  } = useDashboardInsights();
  const { bestDeals, dealStats, isLoadingDeals } = useDeals();

  return {
    stats: {
      data: stats,
      isLoading: statsLoading,
      error: statsError
    },
    recentActivity: {
      data: recentActivity,
      isLoading: activityLoading
    },
    insights: {
      collectionStats,
      overallInsightsScore,
      averageScore,
      isLoading: insightsLoading
    },
    deals: {
      bestDeals,
      dealStats,
      isLoading: isLoadingDeals
    }
  };
}