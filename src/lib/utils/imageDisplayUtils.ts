/**
 * Utilities for improved game image display with dynamic aspect ratios and smart object fitting
 * Based on the game-card-image-improvements.md specifications
 */

export type AspectRatioClass = 'aspect-square' | 'aspect-[2/3]' | 'aspect-[3/2]' | 'aspect-[3/4]' | 'aspect-[4/3]' | 'aspect-[16/9]' | 'aspect-[460/215]';
export type ObjectFitStrategy = 'contain' | 'cover' | 'fill' | 'scale-down' | 'none';
export type ImageDisplayMode = 'adaptive' | 'steam' | 'traditional' | 'flexible';

export interface ImageDimensions {
  width: number;
  height: number;
}

export interface ImageDisplayConfig {
  aspectRatio: AspectRatioClass;
  objectFit: ObjectFitStrategy;
  containerPadding: string;
  backgroundStyle: string;
}

/**
 * Calculate optimal aspect ratio based on image dimensions
 * Implements the logic from Phase 1.2 of the markdown file
 */
export function getOptimalAspectRatio(width: number, height: number): AspectRatioClass {
  if (!width || !height || width <= 0 || height <= 0) {
    return 'aspect-[2/3]'; // Default portrait for game covers
  }

  const ratio = width / height;

  // Steam capsule format (wide landscape)
  if (ratio >= 2.0) {
    return 'aspect-[460/215]'; // Steam's standard capsule ratio
  }
  
  // Wide landscape
  if (ratio > 1.5) {
    return 'aspect-[16/9]'; // Common widescreen ratio
  }
  
  // Standard landscape
  if (ratio > 1.2) {
    return 'aspect-[4/3]'; // Traditional landscape
  }
  
  // Square-ish (close to 1:1)
  if (ratio >= 0.8 && ratio <= 1.2) {
    return 'aspect-square';
  }
  
  // Portrait (taller than wide)
  if (ratio >= 0.6) {
    return 'aspect-[3/4]'; // Slightly less tall than 2:3
  }
  
  // Tall portrait (typical game box art)
  return 'aspect-[2/3]'; // Standard game cover ratio
}

/**
 * Determine smart object fit strategy based on image and container ratios
 * Implements the logic from Phase 1.1 and 2.2 of the markdown file
 */
export function getSmartObjectFit(
  imageRatio: number, 
  containerRatio: number,
  mode: ImageDisplayMode = 'adaptive'
): ObjectFitStrategy {
  if (!imageRatio || !containerRatio || imageRatio <= 0 || containerRatio <= 0) {
    return 'cover'; // Safe fallback
  }

  const ratioDifference = Math.abs(imageRatio - containerRatio);

  switch (mode) {
    case 'steam':
      // Steam prioritizes showing full artwork
      return ratioDifference > 0.3 ? 'contain' : 'cover';
      
    case 'adaptive':
      // Intelligent switching based on ratio difference
      if (ratioDifference > 0.5) {
        return 'contain'; // Show full image when ratios are very different
      }
      if (ratioDifference > 0.2) {
        return 'scale-down'; // Scale down if needed, but don't crop
      }
      return 'cover'; // Ratios are similar, cover is acceptable
      
    case 'flexible':
      // Always prioritize showing the full image
      return 'contain';
      
    case 'traditional':
      // Traditional approach - always fill the container
      return 'cover';
      
    default:
      return 'cover';
  }
}

/**
 * Get container padding based on aspect ratio and display mode
 * Implements Phase 1.3 container padding logic
 */
export function getContainerPadding(
  aspectRatio: AspectRatioClass,
  mode: ImageDisplayMode = 'adaptive'
): string {
  switch (mode) {
    case 'steam':
      return 'p-1'; // Minimal padding for Steam-style display
      
    case 'adaptive':
    case 'flexible':
      // More padding for non-standard aspect ratios
      if (aspectRatio === 'aspect-[460/215]' || aspectRatio === 'aspect-[16/9]') {
        return 'p-1'; // Less padding for wide images
      }
      return 'p-2'; // Standard padding
      
    case 'traditional':
      return 'p-0'; // No padding for traditional display
      
    default:
      return 'p-1';
  }
}

/**
 * Get background style for image containers
 * Implements improved background gradients from the markdown
 */
export function getImageBackground(mode: ImageDisplayMode = 'adaptive'): string {
  switch (mode) {
    case 'steam':
      return 'bg-gradient-to-br from-slate-800 to-slate-900';
      
    case 'adaptive':
    case 'flexible':
      return 'bg-gradient-to-br from-muted via-muted to-muted/80';
      
    case 'traditional':
      return 'bg-muted';
      
    default:
      return 'bg-gradient-to-br from-muted via-muted to-muted/80';
  }
}

/**
 * Generate complete image display configuration
 * Combines all the utilities into a single configuration object
 */
export function getImageDisplayConfig(
  imageDimensions: ImageDimensions | null,
  containerDimensions: ImageDimensions | null,
  mode: ImageDisplayMode = 'adaptive'
): ImageDisplayConfig {
  // Default configuration
  let aspectRatio: AspectRatioClass = 'aspect-[2/3]';
  let objectFit: ObjectFitStrategy = 'cover';

  if (imageDimensions) {
    aspectRatio = getOptimalAspectRatio(imageDimensions.width, imageDimensions.height);
    
    if (containerDimensions) {
      const imageRatio = imageDimensions.width / imageDimensions.height;
      const containerRatio = containerDimensions.width / containerDimensions.height;
      objectFit = getSmartObjectFit(imageRatio, containerRatio, mode);
    } else {
      // If no container dimensions, use smart defaults based on mode
      objectFit = mode === 'steam' || mode === 'flexible' ? 'contain' : 'cover';
    }
  }

  return {
    aspectRatio,
    objectFit,
    containerPadding: getContainerPadding(aspectRatio, mode),
    backgroundStyle: getImageBackground(mode)
  };
}

/**
 * Hook-friendly function to get image display classes
 * Returns Tailwind classes ready to be used in components
 */
export function getImageDisplayClasses(
  config: ImageDisplayConfig,
  enableHover: boolean = true
): {
  containerClasses: string;
  imageClasses: string;
} {
  const containerClasses = [
    'relative overflow-hidden rounded-lg',
    config.backgroundStyle,
    config.aspectRatio,
    config.containerPadding,
    enableHover && 'group-hover:shadow-2xl group-hover:shadow-primary/20 transition-premium box-art-glow'
  ].filter(Boolean).join(' ');

  const imageClasses = [
    'w-full h-full transition-premium',
    `object-${config.objectFit}`,
    'object-center',
    enableHover && 'group-hover:scale-105 group-hover:brightness-110 group-hover:saturate-110'
  ].filter(Boolean).join(' ');

  return {
    containerClasses,
    imageClasses
  };
}

/**
 * Utility to extract image dimensions from an image element or URL
 * Returns a promise that resolves with the image dimensions
 */
export function getImageDimensions(src: string): Promise<ImageDimensions> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    
    img.src = src;
  });
}

/**
 * Utility to get Steam-specific image display configuration
 * Optimized for Steam's capsule image format
 */
export function getSteamImageConfig(
  imageDimensions: ImageDimensions | null
): ImageDisplayConfig {
  return getImageDisplayConfig(imageDimensions, null, 'steam');
}

/**
 * Utility to get flexible image display configuration
 * Prioritizes showing the full image content
 */
export function getFlexibleImageConfig(
  imageDimensions: ImageDimensions | null
): ImageDisplayConfig {
  return getImageDisplayConfig(imageDimensions, null, 'flexible');
}
