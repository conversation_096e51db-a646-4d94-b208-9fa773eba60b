import { useCallback } from 'react';
import { useSearchState } from './useSearchState';
import { useSearchActions } from './useSearchActions';
import { useVoiceSearch } from './useVoiceSearch';
import { useSearchUtils } from './useSearchUtils';

/**
 * Main composed hook for enhanced search functionality
 * This replaces the large useEnhancedSearch hook with a composed approach
 */
export function useEnhancedSearchComposed() {
  const {
    searchQuery,
    searchFilters,
    searchHistory,
    voiceSearch,
    setSearchQuery,
    setSearchFilters,
    setVoiceSearch,
    updateSearchHistory
  } = useSearchState();

  const {
    voiceSearchMutation,
    performSearch,
    applyFilters,
    searchWithSuggestion,
    clearSearchHistory,
    isSearching,
    searchError,
    searchResults,
    isVoiceSearching,
    voiceSearchError
  } = useSearchActions();

  const {
    popularSearches,
    getSearchSuggestions,
    analyzeSearchIntent
  } = useSearchUtils();

  // Handle voice search result
  const handleVoiceResult = useCallback((transcript: string) => {
    voiceSearchMutation.mutate(transcript);
  }, [voiceSearchMutation]);

  const {
    startVoiceSearch,
    stopVoiceSearch
  } = useVoiceSearch(voiceSearch, setVoiceSearch, handleVoiceResult);

  // Enhanced apply filters that updates state
  const applyFiltersWithState = useCallback((filters: typeof searchFilters) => {
    setSearchFilters(filters);
    applyFilters(filters, searchQuery);
  }, [setSearchFilters, applyFilters, searchQuery]);

  // Enhanced perform search that updates history
  const performSearchWithHistory = useCallback((query: string) => {
    setSearchQuery(query);
    performSearch(query);
    // Update history after successful search
    setTimeout(updateSearchHistory, 1000);
  }, [setSearchQuery, performSearch, updateSearchHistory]);

  // Enhanced clear history that updates state
  const clearSearchHistoryWithState = useCallback(() => {
    clearSearchHistory();
    updateSearchHistory();
  }, [clearSearchHistory, updateSearchHistory]);

  return {
    // State
    searchQuery,
    searchFilters,
    searchHistory,
    voiceSearch,
    popularSearches,
    
    // Search results
    searchResults,
    isSearching,
    searchError,
    
    // Voice search state
    isVoiceSearching,
    voiceSearchError,
    
    // Actions
    performSearch: performSearchWithHistory,
    setSearchQuery,
    startVoiceSearch,
    stopVoiceSearch,
    clearSearchHistory: clearSearchHistoryWithState,
    searchWithSuggestion,
    applyFilters: applyFiltersWithState,
    
    // Utilities
    getSearchSuggestions: (query: string) => getSearchSuggestions(query, searchHistory),
    analyzeSearchIntent,
    
    // Data
    suggestions: searchResults?.suggestions || [],
    searchTime: searchResults?.searchTime,
    gameCount: searchResults?.games.length || 0
  };
}