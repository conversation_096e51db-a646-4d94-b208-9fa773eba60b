import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/base';
import { LibraryStats as LibraryStatsType } from '../types';

interface LibraryStatsProps {
  stats: LibraryStatsType;
  isLoading?: boolean;
}

export function LibraryStats({ stats, isLoading }: LibraryStatsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted rounded animate-pulse" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-3">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Playing</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-accent">{stats.playing}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Completed</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-success">{stats.completed}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Backlog</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-primary">{stats.backlog}</div>
        </CardContent>
      </Card>
    </div>
  );
}