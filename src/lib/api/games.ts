import { supabase } from '../supabase';
import { 
  Game, 
  Platform,
  TheGamesDBGame,
  TheGamesDBImage,
  TheGamesDBImageBaseUrl,
  TheGamesDBPlatform,
  TheGamesDBGenre,
  TheGamesDBDeveloper,
  TheGamesDBPublisher,
  TheGamesDBSearchOptions
} from '../../types';
import { igdbPlatformIds, igdbPlatformMap, tgdbPlatformMap } from './platformMappings';
import { 
  SteamGridDBGame, 
  SteamGridDBGrid, 
  SteamGridDBSearchOptions,
  SteamGridDBGridOptions
} from './steamgriddb';

// IGDB Game interface matching API response
export interface IGDBGame {
  id: number;
  name: string;
  platforms?: Array<{ id: number; name: string }>;
  genres?: Array<{ id: number; name: string }>;
  involved_companies?: Array<{
    company: { id: number; name: string };
    developer: boolean;
    publisher: boolean;
  }>;
  first_release_date?: number;
  summary?: string;
  cover?: { url: string; image_id: string };
  screenshots?: Array<{ url: string; image_id: string }>;
  aggregated_rating?: number;
  videos?: Array<{ video_id: string; name: string }>;
  similar_games?: Array<{ id: number; name: string }>;
  category?: number; // 0 = main game, 1 = DLC, 2 = expansion
  themes?: Array<{ id: number; name: string }>;
  keywords?: Array<{ id: number; name: string }>;
  version_parent?: number | null; // Parent game ID if this is a version
  version_title?: string; // Title of the specific version
}

// Search options interface
export interface IGDBSearchOptions {
  limit?: number;
  platforms?: string[];
  genres?: string[];
  minRating?: number;
  maxRating?: number;
  minYear?: number;
  maxYear?: number;
  sortBy?: 'relevance' | 'rating' | 'release_date' | 'name' | 'popularity';
  sortDirection?: 'asc' | 'desc';
  excludeDLC?: boolean;
  excludeExpansions?: boolean;
  searchType?: 'exact' | 'fuzzy' | 'smart';
}

/**
 * TheGamesDB API Service for game data
 */
export class TheGamesDBAPIService {
  private readonly baseUrl = 'https://api.thegamesdb.net';
  private readonly apiKey: string | null;
  private platformMap: Record<number, Platform> = tgdbPlatformMap;
  private genreMap: Record<number, string> = {};
  private developerMap: Record<number, string> = {};
  private publisherMap: Record<number, string> = {};

  constructor() {
    this.apiKey = import.meta.env.VITE_THEGAMESDB_API_KEY;
    console.log('🎮 TheGamesDB API Service initialized');
    this.initializeMappings();
  }

  isConfigured(): boolean {
    return !!(this.apiKey && this.apiKey !== 'your_thegamesdb_api_key');
  }

  private async initializeMappings() {
    if (!this.isConfigured()) {
      console.warn('⚠️ TheGamesDB API not configured');
      return;
    }

    try {
      await Promise.all([
        this.loadPlatformMapping(),
        this.loadGenreMapping(),
        this.loadDeveloperMapping(),
        this.loadPublisherMapping()
      ]);
      console.log('✅ TheGamesDB mappings initialized');
    } catch (error) {
      console.warn('⚠️ Failed to initialize TheGamesDB mappings:', error);
    }
  }

  private async makeRequest<T>(endpoint: string, params: Record<string, string | number | string[]> = {}): Promise<T> {
    if (!this.isConfigured()) {
      throw new Error('TheGamesDB API key not configured');
    }

    try {
      console.log(`🔍 TheGamesDB request via proxy: ${endpoint}`);
      console.log(`🔍 TheGamesDB params:`, params);

      const { data, error } = await supabase.functions.invoke('thegamesdb-proxy', {
        headers: {
          'X-API-Key': this.apiKey!,
        },
        body: { endpoint, params },
      });

      if (error) {
        console.error('❌ TheGamesDB proxy error:', error);
        throw new Error(`TheGamesDB proxy error: ${error.message}`);
      }

      if (data?.error) {
        console.error('❌ TheGamesDB response error:', data.error);
        throw new Error(`TheGamesDB error: ${data.error}`);
      }

      if (!data) {
        throw new Error('No data received from TheGamesDB API');
      }

      console.log(`✅ TheGamesDB request successful - Monthly allowance remaining: ${data.remaining_monthly_allowance}`);
      return data.data;
    } catch (error) {
      console.error('❌ TheGamesDB API error:', error);
      throw error;
    }
  }

  private async loadPlatformMapping() {
    try {
      // We're already using the predefined mappings from platformMappings.ts
      // This method is kept for backward compatibility and potential future dynamic loading
      const response = await this.makeRequest<{ count: number; platforms: Record<string, TheGamesDBPlatform> }>('/v1/Platforms');
      
      // If we get new platforms from the API, we could add them here
      // But for now, we'll just use our predefined mappings
      console.log(`✅ TheGamesDB platforms loaded: ${response?.count || 0} platforms available`);
    } catch (error) {
      console.warn('⚠️ Failed to load platform mapping:', error);
    }
  }

  private async loadGenreMapping() {
    try {
      const data = await this.makeRequest<{ count: number; genres: Record<string, TheGamesDBGenre> }>('/v1/Genres');
      
      this.genreMap = Object.values(data.genres).reduce((acc, genre) => {
        acc[genre.id] = genre.name;
        return acc;
      }, {} as Record<number, string>);
    } catch (error) {
      console.warn('⚠️ Failed to load genre mapping:', error);
    }
  }

  private async loadDeveloperMapping() {
    try {
      const data = await this.makeRequest<{ count: number; developers: Record<string, TheGamesDBDeveloper> }>('/v1/Developers');
      
      this.developerMap = Object.values(data.developers).reduce((acc, dev) => {
        acc[dev.id] = dev.name;
        return acc;
      }, {} as Record<number, string>);
    } catch (error) {
      console.warn('⚠️ Failed to load developer mapping:', error);
    }
  }

  private async loadPublisherMapping() {
    try {
      const data = await this.makeRequest<{ count: number; publishers: Record<string, TheGamesDBPublisher> }>('/v1/Publishers');
      
      this.publisherMap = Object.values(data.publishers).reduce((acc, pub) => {
        acc[pub.id] = pub.name;
        return acc;
      }, {} as Record<number, string>);
    } catch (error) {
      console.warn('⚠️ Failed to load publisher mapping:', error);
    }
  }

  async searchByName(name: string, options: TheGamesDBSearchOptions = {}): Promise<TheGamesDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ TheGamesDB API not configured');
      return [];
    }

    const {
      platforms = [],
      fields = ['players', 'publishers', 'genres', 'overview', 'rating', 'platform', 'coop', 'youtube'],
      include = ['boxart', 'platform'],
      page = 1
    } = options;

    try {
      const params: Record<string, string | number | string[]> = {
        name,
        fields: fields.join(','),
        include: include.join(','),
        page
      };

      // Implement proper platform filtering using TheGamesDB format
      if (platforms.length > 0) {
        console.log(`🎯 TheGamesDB API: Applying platform filters to search query`);
        // Convert platform names to TheGamesDB platform IDs
        const platformIds = platforms.map((platformName: string | number) => {
          if (typeof platformName === 'number') {
            return platformName.toString();
          }
          
          // Find the platform ID by matching the platform name to our mapping
          const foundId = Object.entries(this.platformMap).find(([, mappedPlatform]) => {
            const platformStr = platformName as string;
            return mappedPlatform === platformStr || 
                   mappedPlatform.toLowerCase().includes(platformStr.toLowerCase()) ||
                   platformStr.toLowerCase().includes(mappedPlatform.toLowerCase());
          });
          
          console.log(`🎯 TheGamesDB API: Platform "${platformName}" -> ID ${foundId ? foundId[0] : 'NOT FOUND'}`);
          return foundId ? foundId[0] : null;
        }).filter(id => id !== null);
        
        // Use TheGamesDB's platform filter syntax - use the 'platform' parameter with comma-separated IDs
        if (platformIds.length > 0) {
          params.platform = platformIds.join(',');
          console.log(`✅ TheGamesDB API: Platform filter applied - IDs: ${platformIds.join(', ')} (${platforms.join(', ')})`);
        } else {
          console.warn(`⚠️ TheGamesDB API: No valid platform IDs found for: ${platforms.join(', ')}`);
        }
      } else {
        console.log(`🔍 TheGamesDB API: No platform filters applied - searching all platforms`);
      }

      const data = await this.makeRequest<{
        count: number;
        games: TheGamesDBGame[];
      }>('/v1.1/Games/ByGameName', params);

      console.log(`🔍 TheGamesDB search for "${name}": ${data.games?.length || 0} results`);
      return data.games || [];
    } catch (error) {
      console.error('❌ TheGamesDB search error:', error);
      return [];
    }
  }

  // Search games by specific platform
  async searchByPlatform(platform: Platform, searchTerm: string = '', options: TheGamesDBSearchOptions = {}): Promise<TheGamesDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ TheGamesDB API not configured');
      return [];
    }

    // Find the platform ID for this platform
    const platformId = Object.entries(this.platformMap).find(([, mappedPlatform]) => 
      mappedPlatform === platform
    )?.[0];

    if (!platformId) {
      console.warn(`⚠️ Platform ID not found for: ${platform}`);
      return [];
    }

    console.log(`🎮 TheGamesDB searching platform "${platform}" (ID: ${platformId}) with term: "${searchTerm}"`);

    return this.searchByName(searchTerm || platform, {
      ...options,
      platforms: [parseInt(platformId)]
    });
  }

  async getGameDetails(gameId: number): Promise<TheGamesDBGame | null> {
    if (!this.isConfigured()) {
      return null;
    }

    try {
      const data = await this.makeRequest<{
        count: number;
        games: TheGamesDBGame[];
      }>('/v1/Games/ByGameID', {
        id: gameId,
        fields: 'players,publishers,genres,overview,rating,platform,coop,youtube,os,processor,ram,hdd,video,sound,alternates',
        include: 'boxart,platform'
      });

      return data.games?.[0] || null;
    } catch (error) {
      console.error(`❌ TheGamesDB game details error for ID ${gameId}:`, error);
      return null;
    }
  }

  async getGameImages(gameIds: number[], imageTypes?: string[]): Promise<Record<number, TheGamesDBImage[]>> {
    if (!this.isConfigured()) {
      return {};
    }

    try {
      const params: Record<string, string | number | string[]> = {
        games_id: gameIds.map(String)
      };

      if (imageTypes && imageTypes.length > 0) {
        params['filter[type]'] = imageTypes;
      }

      const data = await this.makeRequest<{
        count: number;
        base_url: TheGamesDBImageBaseUrl;
        images: Record<string, TheGamesDBImage[]>;
      }>('/v1/Games/Images', params);

      const result: Record<number, TheGamesDBImage[]> = {};
      Object.entries(data.images || {}).forEach(([gameId, images]) => {
        result[parseInt(gameId)] = images.map(img => ({
          ...img,
          filename: this.buildImageUrl(data.base_url, 'original', img.filename)
        }));
      });

      return result;
    } catch (error) {
      console.error('❌ TheGamesDB images error:', error);
      return {};
    }
  }

  private buildImageUrl(baseUrl: TheGamesDBImageBaseUrl, size: keyof TheGamesDBImageBaseUrl, filename: string): string {
    return `${baseUrl[size]}${filename}`;
  }

  convertToGame(tgdbGame: TheGamesDBGame, images?: TheGamesDBImage[]): Game {
    // Type-safe platform handling with fallback
    const platform = tgdbGame.platform ? this.platformMap[tgdbGame.platform] : undefined;
    if (!platform && tgdbGame.platform) {
      console.warn(`🔍 Unknown TheGamesDB platform ID: ${tgdbGame.platform} for game "${tgdbGame.game_title || 'Unknown'}"`);
    }
    const finalPlatform = platform || 'PC';
    console.log(`🎯 TheGamesDB "${tgdbGame.game_title || 'Unknown'}" platform ID ${tgdbGame.platform || 'unknown'} -> "${finalPlatform}"`);
    
    // Type-safe genre mapping with validation
    const genres = (tgdbGame.genres?.map(id => this.genreMap[id]).filter((genre): genre is string => Boolean(genre)) || []);
    
    // Type-safe developer and publisher mapping
    const developers = tgdbGame.developers?.map(id => this.developerMap[id]).filter((dev): dev is string => Boolean(dev)) || [];
    const publishers = tgdbGame.publishers?.map(id => this.publisherMap[id]).filter((pub): pub is string => Boolean(pub)) || [];

    // Type-safe image handling
    const boxartImages = images?.filter((img): img is TheGamesDBImage =>
      img?.type === 'boxart' && img?.side === 'front' && Boolean(img.filename)
    ) || [];
    const screenshotImages = images?.filter((img): img is TheGamesDBImage =>
      img?.type === 'screenshot' && Boolean(img.filename)
    ) || [];
    
    const coverImage = boxartImages[0]?.filename;
    const screenshots = screenshotImages.map(img => img.filename).filter(Boolean);

    // Type-safe YouTube link handling
    const youtubeLinks = tgdbGame.youtube && typeof tgdbGame.youtube === 'string'
      ? [`https://www.youtube.com/watch?v=${tgdbGame.youtube}`]
      : [];

    return {
      id: `tgdb_${tgdbGame.id}`,
      title: tgdbGame.game_title || 'Unknown Game',
      platforms: [finalPlatform],
      genres,
      developer: developers[0],
      publisher: publishers[0],
      release_date: tgdbGame.release_date,
      description: tgdbGame.overview,
      cover_image: coverImage,
      screenshots,
      youtube_links: youtubeLinks,
      metacritic_score: undefined, // TheGamesDB doesn't provide Metacritic scores
      igdb_id: undefined
    };
  }
}

/**
 * SteamGridDB API Service for game artwork and enhanced search
 * Provides high-quality game artwork and metadata from the SteamGridDB community
 */
export class SteamGridDBAPIService {
  private readonly apiKey: string | null;

  constructor() {
    this.apiKey = import.meta.env.VITE_STEAMGRIDDB_API_KEY || '4817ee34de0022325fcba49f1108bcd0';
    console.log('🎨 SteamGridDB API Service initialized');
  }

  isConfigured(): boolean {
    return !!(this.apiKey && this.apiKey !== 'your_steamgriddb_api_key');
  }

  private async makeRequest<T>(endpoint: string, params: Record<string, unknown> = {}): Promise<T> {
    if (!this.isConfigured()) {
      throw new Error('SteamGridDB API key not configured');
    }

    try {
      console.log(`🔍 SteamGridDB request via proxy: ${endpoint}`);
      console.log(`🔍 SteamGridDB params:`, params);

      const { data, error } = await supabase.functions.invoke('steamgriddb-proxy', {
        headers: {
          'X-API-Key': this.apiKey!,
        },
        body: { endpoint, params },
      });

      if (error) {
        console.error('❌ SteamGridDB proxy error:', error);
        throw new Error(`SteamGridDB proxy error: ${error.message}`);
      }

      if (data?.error) {
        console.error('❌ SteamGridDB response error:', data.error);
        throw new Error(`SteamGridDB error: ${data.error}`);
      }

      if (!data) {
        throw new Error('No data received from SteamGridDB API');
      }

      console.log(`✅ SteamGridDB request successful - ${Array.isArray(data) ? data.length : 1} results`);
      return data;
    } catch (error) {
      console.error('❌ SteamGridDB API error:', error);
      throw error;
    }
  }

  async searchByName(name: string, options: SteamGridDBSearchOptions = {}): Promise<SteamGridDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ SteamGridDB API not configured');
      return [];
    }

    // Note: SteamGridDB doesn't have a public search endpoint
    // This service is primarily for artwork retrieval, not game search
    // Return empty results and let IGDB/TheGamesDB handle the search
    console.log('📝 SteamGridDB search not available - use for artwork only');
    return [];
  }

  async searchByPlatform(platform: Platform, searchTerm: string = '', options: SteamGridDBSearchOptions = {}): Promise<SteamGridDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ SteamGridDB API not configured');
      return [];
    }

    // SteamGridDB doesn't have a public search endpoint
    // This service is primarily for artwork retrieval, not game search
    console.log('📝 SteamGridDB platform search not available - use for artwork only');
    return [];
  }

  async getGameBySteamId(steamId: string): Promise<SteamGridDBGame | null> {
    if (!this.isConfigured()) {
      return null;
    }

    try {
      const game = await this.makeRequest<SteamGridDBGame>(`/games/steam/${steamId}`, {});
      console.log(`🔍 SteamGridDB game details for Steam ID ${steamId}:`, game?.name || 'Not found');
      return game;
    } catch (error) {
      console.warn(`⚠️ Failed to get SteamGridDB game by Steam ID ${steamId}:`, error);
      return null;
    }
  }

  async getGameDetails(gameId: number): Promise<SteamGridDBGame | null> {
    if (!this.isConfigured()) {
      return null;
    }

    try {
      const game = await this.makeRequest<SteamGridDBGame>(`/games/id/${gameId}`, {});
      console.log(`🔍 SteamGridDB game details for ID ${gameId}:`, game?.name || 'Not found');
      return game;
    } catch (error) {
      console.error(`❌ SteamGridDB game details error for ID ${gameId}:`, error);
      return null;
    }
  }

  async getGameArtwork(gameId: number, options: SteamGridDBGridOptions = {}): Promise<SteamGridDBGrid[]> {
    if (!this.isConfigured()) {
      return [];
    }

    try {
      const params = {
        styles: options.styles || ['alternate', 'blurred', 'white_logo'],
        dimensions: options.dimensions || ['460x215', '920x430'],
        formats: options.formats || ['png', 'jpg'],
        nsfw: options.nsfw || 'false',
        humor: options.humor || 'any',
        epilepsy: options.epilepsy || 'false',
        untagged: options.untagged || 'any',
        page: options.page || 0,
        ...options
      };

      const grids = await this.makeRequest<SteamGridDBGrid[]>(`/grids/game/${gameId}`, params);
      console.log(`🎨 SteamGridDB artwork for game ${gameId}: ${grids?.length || 0} items`);
      return grids || [];
    } catch (error) {
      console.error(`❌ SteamGridDB artwork error for game ${gameId}:`, error);
      return [];
    }
  }

  convertToGame(sgdbGame: SteamGridDBGame, artwork?: SteamGridDBGrid): Game {
    // SteamGridDB games have limited metadata, so we create a minimal Game object
    // This is primarily used for artwork enhancement rather than comprehensive game data
    
    return {
      id: `sgdb_${sgdbGame.id}`,
      title: sgdbGame.name || 'Unknown Game',
      platforms: ['PC'], // SteamGridDB doesn't provide platform info, default to PC
      genres: [], // SteamGridDB doesn't provide genre info
      developer: undefined,
      publisher: undefined,
      release_date: undefined,
      description: undefined,
      cover_image: artwork?.url,
      screenshots: artwork ? [artwork.url] : [],
      youtube_links: [],
      metacritic_score: undefined,
      igdb_id: undefined
    };
  }

  // Enhanced search that combines game search with artwork
  async searchWithArtwork(term: string, options: SteamGridDBSearchOptions = {}): Promise<Array<SteamGridDBGame & { artwork?: SteamGridDBGrid }>> {
    if (!this.isConfigured()) {
      console.warn('⚠️ SteamGridDB API not configured');
      return [];
    }

    try {
      const games = await this.searchByName(term, { ...options, limit: options.limit || 10 });
      
      if (games.length === 0) {
        return [];
      }

      // Get artwork for each game
      const gamesWithArtwork = await Promise.all(
        games.map(async (game) => {
          try {
            const artwork = await this.getGameArtwork(game.id, {
              styles: ['alternate', 'blurred', 'white_logo'],
              dimensions: ['460x215'],
              formats: ['png', 'jpg']
            });
            
            // Get the highest scored artwork
            const bestArtwork = artwork.sort((a, b) => b.score - a.score)[0];
            
            return {
              ...game,
              artwork: bestArtwork
            };
          } catch (error) {
            console.warn(`Failed to get artwork for SteamGridDB game ${game.name}:`, error);
            return game;
          }
        })
      );

      console.log(`🎨 SteamGridDB enhanced search completed: ${gamesWithArtwork.length} games with artwork`);
      return gamesWithArtwork;
    } catch (error) {
      console.error('❌ SteamGridDB search with artwork failed:', error);
      return [];
    }
  }
}

/**
 * 
IGDB API Service for comprehensive game data
 */
export class IGDBAPIService {
  // Using centralized platform mappings from platformMappings.ts
  private readonly igdbPlatformIds = igdbPlatformIds;
  private readonly platformMap = igdbPlatformMap;

  constructor() {
    console.log('🎮 IGDB API Service initialized - Enhanced gaming database access');
    this.testConnection();
  }

  // Test basic IGDB connection
  private async testConnection() {
    try {
      console.log('🔍 Testing IGDB connection via proxy...');
      
      // Test with a simple query
      const testQuery = 'fields name; where id = 1942; limit 1;';
      await this.makeRequest<IGDBGame[]>('games', testQuery);
      console.log('✅ IGDB proxy connection successful');
    } catch (error) {
      console.error('❌ IGDB connection test failed:', error);
    }
  }

  // Check if IGDB is properly configured (always true since handled server-side)
  isConfigured(): boolean {
    return true; // Server-side handles authentication
  }

  // Get platform ID for IGDB filtering
  getPlatformId(platform: Platform): number | undefined {
    return this.igdbPlatformIds[platform];
  }

  // Enhanced IGDB request method with retry logic
  private async makeRequest<T>(endpoint: string, query: string, retryCount: number = 0): Promise<T> {
    const maxRetries = 3;

    try {
      console.log(`🔍 IGDB ${endpoint} request:`, query.replace(/\s+/g, ' ').trim());

      const { data, error } = await supabase.functions.invoke('igdb-proxy', {
        body: { endpoint, query },
      });

      if (error) {
        console.error('❌ IGDB proxy error:', error);
        console.error('❌ Error details:', JSON.stringify(error, null, 2));
        
        // Retry on network errors or auth issues
        if (retryCount < maxRetries && (
          error.message.includes('network') ||
          error.message.includes('token') ||
          error.message.includes('401') ||
          error.message.includes('500')
        )) {
          console.log(`🔄 Retrying IGDB request (attempt ${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
          return this.makeRequest<T>(endpoint, query, retryCount + 1);
        }
        
        throw new Error(`IGDB API error: ${error.message}`);
      }

      if (data?.error) {
        console.error('❌ IGDB response error:', data.error);
        throw new Error(`IGDB error: ${data.error}`);
      }

      if (!data) {
        throw new Error('No data received from IGDB API');
      }

      console.log(`✅ IGDB request successful - ${Array.isArray(data) ? data.length : 1} results`);
      return data as T;
    } catch (error) {
      if (retryCount < maxRetries && error instanceof Error && (
        error.message.includes('Failed to authenticate') ||
        error.message.includes('500')
      )) {
        console.log(`🔄 Retrying request (attempt ${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
        return this.makeRequest<T>(endpoint, query, retryCount + 1);
      }
      throw error;
    }
  }

  // Enhanced search method with multiple strategies
  async search(searchTerm: string, options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured, search unavailable');
      console.log('Debug: Client ID:', import.meta.env.VITE_IGDB_CLIENT_ID ? 'Present' : 'Missing');
      console.log('Debug: Client Secret:', import.meta.env.VITE_IGDB_CLIENT_SECRET ? 'Present' : 'Missing');
      return [];
    }

    const {
      limit = (options.platforms || []).length > 0 ? 75 : 50, // Higher limit for platform-specific searches
      platforms = [], // Extract platforms for filtering
      sortBy = 'relevance',
      sortDirection = 'desc',
      excludeDLC = false, // Changed to false to get more results
      excludeExpansions = false,
      searchType = 'smart'
    } = options;

    try {
      // Build minimal field list to avoid edge function issues
      const fields = [
        'name',
        'platforms.name',
        'genres.name',
        'first_release_date',
        'summary',
        'cover.image_id',
        'aggregated_rating',
        'version_parent',
        'version_title'
      ];

      // Build search clause based on search type
      let searchClause = '';
      switch (searchType) {
        case 'exact':
          searchClause = `search "${searchTerm}";`;
          break;
        case 'fuzzy':
          searchClause = `where name ~ *"${searchTerm}"*;`;
          break;
        case 'smart':
        default:
          // Smart search: try exact first, then fuzzy if needed
          searchClause = `search "${searchTerm}";`;
          break;
      }

      // Build where conditions (simplified to avoid complex queries)
      const whereConditions: string[] = [];

      // Add platform filtering if specified
      if (platforms.length > 0) {
        console.log(`🎯 IGDB API: Applying platform filters to search query`);
        // Convert platform names to IGDB platform IDs for proper filtering
        const platformIds = platforms.map(platformName => {
          const platformId = this.getPlatformId(platformName as Platform);
          console.log(`🎯 IGDB API: Platform "${platformName}" -> ID ${platformId}`);
          return platformId;
        }).filter(id => id !== undefined);

        if (platformIds.length > 0) {
          whereConditions.push(`platforms = (${platformIds.join(',')})`);
          console.log(`✅ IGDB API: Platform filter applied - IDs: ${platformIds.join(', ')} (${platforms.join(', ')})`);
        } else {
          // Fallback to name-based filtering if no platform IDs found
          const platformNames = platforms.map(p => `"${p}"`).join(',');
          whereConditions.push(`platforms.name = (${platformNames})`);
          console.log(`⚠️ IGDB API: Using name-based platform filtering: ${platforms.join(', ')}`);
        }
      } else {
        console.log(`🔍 IGDB API: No platform filters applied - searching all platforms`);
      }

      // Only add basic filtering to avoid query complexity issues
      if (excludeDLC) {
        whereConditions.push('category != 1');
      }
      if (excludeExpansions) {
        whereConditions.push('category != 2');
      }

      // Combine clauses
      let whereClause = '';
      if (searchType === 'fuzzy') {
        // For fuzzy search, combine search with where conditions
        const fuzzyConditions = [`name ~ *"${searchTerm}"*`];
        
        // Add platform filtering for fuzzy search
        if (platforms.length > 0) {
          // Convert platform names to IGDB platform IDs for proper filtering
          const platformIds = platforms.map(platformName => {
            const platformId = this.getPlatformId(platformName as Platform);
            return platformId;
          }).filter(id => id !== undefined);

          if (platformIds.length > 0) {
            fuzzyConditions.push(`platforms = (${platformIds.join(',')})`);
          } else {
            // Fallback to name-based filtering if no platform IDs found
            const platformNames = platforms.map(p => `"${p}"`).join(',');
            fuzzyConditions.push(`platforms.name = (${platformNames})`);
          }
        }
        
        // Add other conditions
        if (excludeDLC) {
          fuzzyConditions.push('category != 1');
        }
        if (excludeExpansions) {
          fuzzyConditions.push('category != 2');
        }
        
        if (fuzzyConditions.length > 0) {
          whereClause = `where ${fuzzyConditions.join(' & ')};`;
        }
        searchClause = '';
      } else if (whereConditions.length > 0) {
        whereClause = `where ${whereConditions.join(' & ')};`;
      }

      // Build sort clause
      let sortClause = '';
      switch (sortBy) {
        case 'rating':
          sortClause = `sort aggregated_rating ${sortDirection};`;
          break;
        case 'release_date':
          sortClause = `sort first_release_date ${sortDirection};`;
          break;
        case 'name':
          sortClause = `sort name ${sortDirection};`;
          break;
        case 'popularity':
          sortClause = 'sort follows desc;';
          break;
        case 'relevance':
        default:
          // Let IGDB handle relevance sorting for search queries
          sortClause = '';
          break;
      }

      // Build complete query
      const queryParts = [
        searchClause,
        `fields ${fields.join(', ')};`,
        whereClause,
        sortClause,
        `limit ${limit};`
      ].filter(Boolean);

      const query = queryParts.join('\n');
      console.log('🔍 Final IGDB query:', query);
      
      const games = await this.makeRequest<IGDBGame[]>('games', query);

      if (!Array.isArray(games)) {
        console.error('❌ IGDB response is not an array:', games);
        return [];
      }

      // Expand search results to include game versions (different platform releases)
      const expandedGames = await this.expandGameVersions(games);
      console.log(`🔍 Expanded ${games.length} games to ${expandedGames.length} including versions`);

      // If smart search and few results, try different approaches to get more results
      if (searchType === 'smart' && expandedGames.length < 5 && searchTerm.length > 2) {
        console.log('🔍 Smart search: trying fuzzy search for more results...');
        try {
          const fuzzyResults = await this.search(searchTerm, { 
            ...options, 
            searchType: 'fuzzy',
            limit: Math.max(limit - expandedGames.length, 10) // Ensure we get at least 10 more
          });
          
          // Combine results, removing duplicates
          const existingIds = new Set(expandedGames.map(g => g.id));
          const newResults = fuzzyResults.filter(g => !existingIds.has(g.id));
          expandedGames.push(...newResults);
          
          console.log(`🔍 Combined search results: ${expandedGames.length} total games`);
        } catch (fuzzyError) {
          console.warn('⚠️ Fuzzy search fallback failed:', fuzzyError);
        }
      }

      return expandedGames;
    } catch (error) {
      console.error('❌ IGDB search error:', error);
      
      // Try a simplified fallback search
      try {
        console.log('🔄 Attempting simplified fallback search...');
        const fallbackQuery = `search "${searchTerm}"; fields name, platforms.name, genres.name; limit ${Math.min(limit, 50)};`;
        console.log('🔍 Fallback query:', fallbackQuery);
        
        const fallbackGames = await this.makeRequest<IGDBGame[]>('games', fallbackQuery);
        console.log(`✅ Fallback search succeeded: ${fallbackGames.length} games found`);
        return fallbackGames;
      } catch (fallbackError) {
        console.error('❌ Fallback search also failed:', fallbackError);
        throw error; // Throw the original error
      }
    }
  }

  // Search by genre
  async searchByGenre(genre: string, options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    console.log(`🎯 Searching IGDB by genre: ${genre}`);
    return this.search('', { ...options, genres: [genre] });
  }

  // Search by platform with proper IGDB platform filtering
  async searchByPlatform(platform: string, options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    console.log(`🎮 Searching IGDB by platform: ${platform}`);
    
    // Get the platform ID for IGDB filtering
    const platformKey = platform as Platform;
    const platformId = this.igdbPlatformIds[platformKey];
    
    if (!platformId) {
      console.warn(`⚠️ Unknown platform for IGDB filtering: ${platform}`);
      return this.search('', options);
    }

    return this.searchWithPlatformFilter('', platformId, options);
  }

  // Enhanced search with platform filtering
  async searchWithPlatformFilter(searchTerm: string, platformId: number, options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured');
      return [];
    }

    const {
      limit = 50,
      sortBy = 'relevance',
      sortDirection = 'desc',
      excludeDLC = true,
      excludeExpansions = true
    } = options;

    try {
      const fields = [
        'name',
        'platforms.name',
        'genres.name',
        'first_release_date',
        'summary',
        'cover.image_id',
        'aggregated_rating',
        'version_parent',
        'version_title'
      ];

      // Build where conditions with platform filtering
      const whereConditions: string[] = [`platforms = (${platformId})`];
      
      if (excludeDLC) {
        whereConditions.push('category != 1');
      }
      if (excludeExpansions) {
        whereConditions.push('category != 2');
      }

      // Build complete query with platform filter
      const queryParts = [];
      
      if (searchTerm.trim()) {
        queryParts.push(`search "${searchTerm}";`);
      }
      
      queryParts.push(`fields ${fields.join(', ')};`);
      queryParts.push(`where ${whereConditions.join(' & ')};`);
      
      // Add sorting
      if (sortBy !== 'relevance') {
        let sortField = 'aggregated_rating';
        switch (sortBy) {
          case 'release_date':
            sortField = 'first_release_date';
            break;
          case 'name':
            sortField = 'name';
            break;
          case 'popularity':
            sortField = 'follows';
            break;
        }
        queryParts.push(`sort ${sortField} ${sortDirection};`);
      }
      
      queryParts.push(`limit ${limit};`);

      const query = queryParts.join('\n');
      console.log(`🔍 IGDB platform-filtered query (Platform ID: ${platformId}):`, query);
      
      const games = await this.makeRequest<IGDBGame[]>('games', query);
      console.log(`✅ IGDB platform search completed: ${games.length} games found for platform ID ${platformId}`);
      
      return games;
    } catch (error) {
      console.error('❌ IGDB platform search error:', error);
      return [];
    }
  }

  // Search by developer
  async searchByDeveloper(developer: string, options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured');
      return [];
    }

    try {
      const query = `
        fields name, platforms.name, genres.name, first_release_date, summary, cover.image_id, aggregated_rating;
        where involved_companies.company.name ~ *"${developer}"* & involved_companies.developer = true;
        limit ${options.limit || 50};
      `;

      console.log(`🔍 IGDB developer search for "${developer}":`, query);
      const games = await this.makeRequest<IGDBGame[]>('games', query);
      console.log(`✅ IGDB developer search completed: ${games.length} games found`);
      
      return games;
    } catch (error) {
      console.error('❌ IGDB developer search error:', error);
      return [];
    }
  }

  // Get popular games
  async getPopularGames(options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured');
      return [];
    }

    const {
      limit = 50,
      platforms = [],
      excludeDLC = true,
      excludeExpansions = true
    } = options;

    try {
      const fields = [
        'name',
        'platforms.name',
        'genres.name',
        'first_release_date',
        'summary',
        'cover.image_id',
        'aggregated_rating',
        'follows'
      ];

      const whereConditions: string[] = [
        'aggregated_rating != null',
        'aggregated_rating > 70',
        'follows > 10'
      ];

      // Add platform filtering if specified
      if (platforms.length > 0) {
        const platformIds = platforms.map(platformName => {
          const platformId = this.getPlatformId(platformName as Platform);
          return platformId;
        }).filter(id => id !== undefined);

        if (platformIds.length > 0) {
          whereConditions.push(`platforms = (${platformIds.join(',')})`);
        }
      }

      if (excludeDLC) {
        whereConditions.push('category != 1');
      }
      if (excludeExpansions) {
        whereConditions.push('category != 2');
      }

      const query = `
        fields ${fields.join(', ')};
        where ${whereConditions.join(' & ')};
        sort follows desc;
        limit ${limit};
      `;

      console.log('🔍 IGDB popular games query:', query);
      const games = await this.makeRequest<IGDBGame[]>('games', query);
      console.log(`✅ IGDB popular games: ${games.length} games found`);
      
      return games;
    } catch (error) {
      console.error('❌ IGDB popular games error:', error);
      return [];
    }
  }

  // Get new releases
  async getNewReleases(options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured');
      return [];
    }

    const {
      limit = 50,
      platforms = [],
      excludeDLC = true,
      excludeExpansions = true
    } = options;

    try {
      const fields = [
        'name',
        'platforms.name',
        'genres.name',
        'first_release_date',
        'summary',
        'cover.image_id',
        'aggregated_rating'
      ];

      // Get games released in the last 6 months
      const sixMonthsAgo = Math.floor((Date.now() - (6 * 30 * 24 * 60 * 60 * 1000)) / 1000);
      const whereConditions: string[] = [
        `first_release_date >= ${sixMonthsAgo}`,
        'first_release_date != null'
      ];

      // Add platform filtering if specified
      if (platforms.length > 0) {
        const platformIds = platforms.map(platformName => {
          const platformId = this.getPlatformId(platformName as Platform);
          return platformId;
        }).filter(id => id !== undefined);

        if (platformIds.length > 0) {
          whereConditions.push(`platforms = (${platformIds.join(',')})`);
        }
      }

      if (excludeDLC) {
        whereConditions.push('category != 1');
      }
      if (excludeExpansions) {
        whereConditions.push('category != 2');
      }

      const query = `
        fields ${fields.join(', ')};
        where ${whereConditions.join(' & ')};
        sort first_release_date desc;
        limit ${limit};
      `;

      console.log('🔍 IGDB new releases query:', query);
      const games = await this.makeRequest<IGDBGame[]>('games', query);
      console.log(`✅ IGDB new releases: ${games.length} games found`);
      
      return games;
    } catch (error) {
      console.error('❌ IGDB new releases error:', error);
      return [];
    }
  }

  // Get game details by ID
  async getGameDetails(gameId: number): Promise<IGDBGame | null> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured');
      return null;
    }

    try {
      const query = `
        fields name, platforms.name, genres.name, involved_companies.company.name, involved_companies.developer, involved_companies.publisher, first_release_date, summary, cover.image_id, screenshots.image_id, aggregated_rating, videos.video_id, similar_games.name, category, themes.name, keywords.name, version_parent, version_title;
        where id = ${gameId};
      `;

      console.log(`🔍 IGDB game details for ID ${gameId}:`, query);
      const games = await this.makeRequest<IGDBGame[]>('games', query);
      
      if (games.length === 0) {
        console.warn(`⚠️ No game found with ID ${gameId}`);
        return null;
      }

      console.log(`✅ IGDB game details retrieved for ID ${gameId}`);
      return games[0];
    } catch (error) {
      console.error(`❌ IGDB game details error for ID ${gameId}:`, error);
      return null;
    }
  }

  // Expand game results to include different platform versions
  private async expandGameVersions(games: IGDBGame[]): Promise<IGDBGame[]> {
    const expandedGames: IGDBGame[] = [...games];
    
    // Find games that might have versions (parent games)
    const potentialParents = games.filter(game => 
      game.version_parent === null && // This is a parent game
      !game.version_title // And doesn't have a version title itself
    );

    if (potentialParents.length === 0) {
      return expandedGames;
    }

    try {
      // Look for versions of these parent games
      const parentIds = potentialParents.map(game => game.id);
      const versionQuery = `
        fields name, platforms.name, genres.name, first_release_date, summary, cover.image_id, aggregated_rating, version_parent, version_title;
        where version_parent = (${parentIds.join(',')});
        limit 100;
      `;

      console.log('🔍 Looking for game versions:', versionQuery);
      const versions = await this.makeRequest<IGDBGame[]>('games', versionQuery);
      
      if (versions.length > 0) {
        console.log(`🔍 Found ${versions.length} game versions to add`);
        expandedGames.push(...versions);
      }
    } catch (error) {
      console.warn('⚠️ Failed to expand game versions:', error);
    }

    return expandedGames;
  }

  // Convert IGDB game to standard format
  convertToGame(igdbGame: IGDBGame): Game {
    // Type-safe platform handling with null checks
    const rawPlatforms = igdbGame.platforms?.map(p => p?.name).filter((name): name is string => Boolean(name)) || [];
    
    // Map IGDB platform names to our standardized platform names
    const platforms = rawPlatforms.map(platformName => {
      // First check exact match in our mapping
      const mappedPlatform = this.platformMap[platformName];
      if (mappedPlatform) {
        return mappedPlatform;
      }
      
      // Fallback: try to match common patterns
      if (platformName.toLowerCase().includes('windows') || platformName.toLowerCase().includes('pc')) {
        return 'PC';
      }
      if (platformName.toLowerCase().includes('playstation 5')) {
        return 'PlayStation 5';
      }
      if (platformName.toLowerCase().includes('playstation 4')) {
        return 'PlayStation 4';
      }
      if (platformName.toLowerCase().includes('xbox series')) {
        return 'Xbox Series X/S';
      }
      if (platformName.toLowerCase().includes('nintendo switch')) {
        return 'Nintendo Switch';
      }
      if (platformName.toLowerCase().includes('mobile') || platformName.toLowerCase().includes('phone')) {
        return 'Mobile';
      }
      
      // Log unknown platforms but use PC as fallback to avoid errors
      console.log(`🔍 Unmapped IGDB platform: ${platformName} - using PC as fallback`);
      return 'PC' as Platform;
    }).filter((platform): platform is Platform => Boolean(platform));

    // Type-safe genre handling
    const genres = igdbGame.genres?.map(g => g?.name).filter((name): name is string => Boolean(name)) || [];
    
    // Type-safe company handling for developers and publishers
    const companies = igdbGame.involved_companies || [];
    const developers = companies.filter(c => c?.developer).map(c => c?.company?.name).filter((name): name is string => Boolean(name));
    const publishers = companies.filter(c => c?.publisher).map(c => c?.company?.name).filter((name): name is string => Boolean(name));

    // Type-safe image handling
    const coverImageId = igdbGame.cover?.image_id;
    const coverImage = coverImageId ? `https://images.igdb.com/igdb/image/upload/t_cover_big/${coverImageId}.jpg` : undefined;
    
    const screenshots = igdbGame.screenshots?.map(s => s?.image_id).filter((id): id is string => Boolean(id))
      .map(id => `https://images.igdb.com/igdb/image/upload/t_screenshot_med/${id}.jpg`) || [];

    // Type-safe video handling
    const youtubeLinks = igdbGame.videos?.map(v => v?.video_id).filter((id): id is string => Boolean(id))
      .map(id => `https://www.youtube.com/watch?v=${id}`) || [];

    // Type-safe date handling
    const releaseDate = igdbGame.first_release_date ? new Date(igdbGame.first_release_date * 1000).toISOString().split('T')[0] : undefined;

    return {
      id: `igdb_${igdbGame.id}`,
      title: igdbGame.name || 'Unknown Game',
      platforms: platforms.length > 0 ? platforms : ['PC'], // Fallback to PC if no platforms
      genres,
      developer: developers[0],
      publisher: publishers[0],
      release_date: releaseDate,
      description: igdbGame.summary,
      cover_image: coverImage,
      screenshots,
      youtube_links: youtubeLinks,
      metacritic_score: igdbGame.aggregated_rating ? Math.round(igdbGame.aggregated_rating) : undefined,
      igdb_id: igdbGame.id
    };
  }
}

// Export API service instances
export const igdbAPI = new IGDBAPIService();
export const theGamesDBAPI = new TheGamesDBAPIService();
export const steamGridDBAPI = new SteamGridDBAPIService();
export const gameAPI = igdbAPI; // Keep backward compatibility

// Export types
export type { TheGamesDBGame, TheGamesDBSearchOptions, SteamGridDBGame, SteamGridDBGrid, SteamGridDBSearchOptions, SteamGridDBGridOptions };