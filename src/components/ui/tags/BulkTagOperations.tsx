import { useState } from 'react';
import { Button } from '@/components/ui/base/button';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/base/dialog';
import { Badge } from '@/components/ui/base/badge';
import { Checkbox } from '@/components/ui/base/checkbox';
import { Input } from '@/components/ui/base/input';
import { Label } from '@/components/ui/base/label';
import { Progress } from '@/components/ui/base/progress';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { useUserTags } from '@/hooks/useUserTags';
import { useEnhancedGameTags } from '@/hooks/useEnhancedGameTags';
import { Game } from '@/types';
import { generateThumbnailPlaceholder } from '@/lib/utils/imageUtils';
import { 
  FileStack, 
  Plus, 
  Minus, 
  Search, 
  CheckCircle2, 
  X, 
  Loader2,
  Hash
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface BulkTagOperationsProps {
  games: Game[];
  selectedGameIds: string[];
  onSelectionChange: (gameIds: string[]) => void;
  children?: React.ReactNode;
}

type OperationType = 'add' | 'remove' | 'replace';

export function BulkTagOperations({ 
  games, 
  selectedGameIds, 
  onSelectionChange,
  children 
}: BulkTagOperationsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [operation, setOperation] = useState<OperationType>('add');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [processedCount, setProcessedCount] = useState(0);

  const { tags } = useUserTags();
  const { addTagsToGame, removeTagsFromGame, replaceGameTags } = useEnhancedGameTags();

  const selectedGames = games.filter(game => selectedGameIds.includes(game.id.toString()));
  
  const filteredTags = tags.filter(tag => 
    tag.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleTagToggle = (tagId: string) => {
    setSelectedTags(prev => 
      prev.includes(tagId) 
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  const handleSelectAllTags = () => {
    if (selectedTags.length === filteredTags.length) {
      setSelectedTags([]);
    } else {
      setSelectedTags(filteredTags.map(tag => tag.id));
    }
  };

  const handleSelectAllGames = () => {
    if (selectedGameIds.length === games.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(games.map(game => game.id.toString()));
    }
  };

  const executeBulkOperation = async () => {
    if (selectedGameIds.length === 0 || selectedTags.length === 0) return;

    setIsProcessing(true);
    setProgress(0);
    setProcessedCount(0);

    try {
      const total = selectedGameIds.length;
      
      for (let i = 0; i < selectedGameIds.length; i++) {
        const gameId = selectedGameIds[i];
        
        try {
          switch (operation) {
            case 'add':
              await addTagsToGame(gameId, selectedTags);
              break;
            case 'remove':
              await removeTagsFromGame(gameId, selectedTags);
              break;
            case 'replace':
              await replaceGameTags(gameId, selectedTags);
              break;
          }
        } catch (error) {
          console.error(`Error processing game ${gameId}:`, error);
          // Continue with other games even if one fails
        }

        setProcessedCount(i + 1);
        setProgress(((i + 1) / total) * 100);
        
        // Small delay to prevent overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Reset state after successful operation
      setSelectedTags([]);
      setIsOpen(false);
    } catch (error) {
      console.error('Bulk operation failed:', error);
    } finally {
      setIsProcessing(false);
      setProgress(0);
      setProcessedCount(0);
    }
  };

  const getOperationDescription = () => {
    switch (operation) {
      case 'add':
        return 'Add selected tags to all selected games';
      case 'remove':
        return 'Remove selected tags from all selected games';
      case 'replace':
        return 'Replace all tags on selected games with selected tags';
      default:
        return '';
    }
  };

  const getSelectedTagsDisplay = () => {
    const selectedTagObjects = tags.filter(tag => selectedTags.includes(tag.id));
    return selectedTagObjects;
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button 
            variant="outline" 
            size="sm" 
            disabled={selectedGameIds.length === 0}
            className="gap-2"
          >
            <FileStack className="h-4 w-4" />
            Bulk Tag Operations
            {selectedGameIds.length > 0 && (
              <Badge variant="secondary" className="ml-1">
                {selectedGameIds.length}
              </Badge>
            )}
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileStack className="h-5 w-5" />
            Bulk Tag Operations
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
            {/* Left Column - Games Selection */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">
                  Selected Games ({selectedGameIds.length})
                </Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAllGames}
                >
                  {selectedGameIds.length === games.length ? 'Deselect All' : 'Select All'}
                </Button>
              </div>

              <ScrollArea className="h-64 border rounded-lg p-2">
                {selectedGames.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Hash className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No games selected</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {selectedGames.map(game => (
                      <div
                        key={game.id}
                        className="flex items-center space-x-2 p-2 rounded border hover:bg-muted/50"
                      >
                        <Checkbox
                          checked={selectedGameIds.includes(game.id.toString())}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              onSelectionChange([...selectedGameIds, game.id.toString()]);
                            } else {
                              onSelectionChange(selectedGameIds.filter(id => id !== game.id.toString()));
                            }
                          }}
                        />
                        <img
                          src={game.cover_url || generateThumbnailPlaceholder(game.name)}
                          alt={game.name}
                          className="w-8 h-8 rounded object-cover"
                        />
                        <span className="text-sm truncate flex-1">{game.name}</span>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </div>

            {/* Right Column - Operation & Tags */}
            <div className="space-y-4">
              {/* Operation Type */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Operation Type</Label>
                <div className="flex gap-2">
                  <Button
                    variant={operation === 'add' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setOperation('add')}
                    className="flex-1"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Tags
                  </Button>
                  <Button
                    variant={operation === 'remove' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setOperation('remove')}
                    className="flex-1"
                  >
                    <Minus className="h-4 w-4 mr-1" />
                    Remove Tags
                  </Button>
                  <Button
                    variant={operation === 'replace' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setOperation('replace')}
                    className="flex-1"
                  >
                    <FileStack className="h-4 w-4 mr-1" />
                    Replace
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {getOperationDescription()}
                </p>
              </div>

              {/* Tag Search */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Search Tags</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search tags..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Selected Tags Display */}
              {selectedTags.length > 0 && (
                <div>
                  <Label className="text-sm font-medium mb-2 block">
                    Selected Tags ({selectedTags.length})
                  </Label>
                  <div className="flex flex-wrap gap-1 p-2 border rounded-lg min-h-[3rem] bg-muted/20">
                    {getSelectedTagsDisplay().map(tag => (
                      <Badge
                        key={tag.id}
                        variant="secondary"
                        className="flex items-center gap-1"
                        style={{ backgroundColor: `${tag.color}20`, borderColor: tag.color }}
                      >
                        {tag.name}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 hover:bg-transparent"
                          onClick={() => handleTagToggle(tag.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Tags List */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-sm font-medium">Available Tags</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectAllTags}
                    disabled={filteredTags.length === 0}
                  >
                    {selectedTags.length === filteredTags.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </div>

                <ScrollArea className="h-48 border rounded-lg p-2">
                  {filteredTags.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Hash className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">
                        {searchQuery ? 'No tags found' : 'No tags available'}
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {filteredTags.map(tag => (
                        <div
                          key={tag.id}
                          className={cn(
                            "flex items-center space-x-2 p-2 rounded cursor-pointer transition-colors",
                            selectedTags.includes(tag.id) 
                              ? "bg-primary/10 border border-primary/20" 
                              : "hover:bg-muted/50"
                          )}
                          onClick={() => handleTagToggle(tag.id)}
                        >
                          <Checkbox
                            checked={selectedTags.includes(tag.id)}
                            onCheckedChange={() => handleTagToggle(tag.id)}
                          />
                          <div
                            className="h-3 w-3 rounded-full"
                            style={{ backgroundColor: tag.color }}
                          />
                          <span className="text-sm flex-1">{tag.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {tag.usage_count}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          {isProcessing && (
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Processing games...</span>
                <span>{processedCount} / {selectedGameIds.length}</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 mt-6 pt-4 border-t">
            <Button
              onClick={executeBulkOperation}
              disabled={
                isProcessing || 
                selectedGameIds.length === 0 || 
                selectedTags.length === 0
              }
              className="flex-1"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  Execute Operation
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}