import { userGamesAPI } from './api/collections';

export interface GameInsight {
  gameId: string;
  gameName: string;
  insightType: 'backlog_priority' | 'completion_prediction' | 'genre_recommendation' | 'mood_match';
  score: number; // 0-100 confidence score
  reason: string;
  metadata?: {
    estimatedPlayTime?: number;
    completionPercentage?: number;
    similarGames?: string[];
    recommendedMood?: string;
  };
}

export interface CollectionStats {
  totalGames: number;
  gamesPlayed: number;
  gamesCompleted: number;
  gamesInBacklog: number;
  averageCompletionTime: number;
  topGenres: { genre: string; count: number }[];
  topPlatforms: { platform: string; count: number }[];
  completionRate: number;
  mostPlayedGenre: string;
  leastPlayedGenre: string;
}

export interface BacklogOptimization {
  igdb_id: string;
  gameName: string;
  priority: 'high' | 'medium' | 'low';
  reason: string;
  estimatedPlayTime: number;
  completionLikelihood: number;
  tags: string[];
  cover_image?: string;
}

export interface DiscoveryChallenge {
  id: string;
  title: string;
  description: string;
  type: 'genre' | 'platform' | 'year' | 'completion' | 'discovery';
  targetGames: string[];
  progress: number;
  maxProgress: number;
  reward: string;
  isActive: boolean;
  expiresAt?: string;
}

export interface GamingPersonality {
  type: 'completionist' | 'explorer' | 'casual' | 'hardcore' | 'social' | 'collector';
  score: number;
  traits: string[];
  recommendations: string[];
  description: string;
}

class CollectionInsightsService {
  /**
   * Analyze user's collection and generate comprehensive statistics
   */
  async analyzeCollection(userId: string): Promise<CollectionStats> {
    try {
      const { data: userGames, error } = await userGamesAPI.getUserCollection(userId);
      if (error) throw error;

      const games = userGames || [];
      const totalGames = games.length;
      const gamesPlayed = games.filter(g => g.status === 'playing' || g.status === 'completed').length;
      const gamesCompleted = games.filter(g => g.status === 'completed').length;
      const gamesInBacklog = games.filter(g => g.status === 'backlog').length;

      // Calculate genre distribution
      const genreCount = new Map<string, number>();
      const platformCount = new Map<string, number>();

      games.forEach(() => {
        // Mock genre and platform data - in real implementation, 
        // this would come from the game's metadata
        const mockGenres = ['Action', 'RPG', 'Strategy', 'Adventure', 'Simulation'];
        const mockPlatforms = ['PC', 'PlayStation', 'Xbox', 'Nintendo Switch'];
        
        const genre = mockGenres[Math.floor(Math.random() * mockGenres.length)];
        const platform = mockPlatforms[Math.floor(Math.random() * mockPlatforms.length)];
        
        genreCount.set(genre, (genreCount.get(genre) || 0) + 1);
        platformCount.set(platform, (platformCount.get(platform) || 0) + 1);
      });

      const topGenres = Array.from(genreCount.entries())
        .map(([genre, count]) => ({ genre, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      const topPlatforms = Array.from(platformCount.entries())
        .map(([platform, count]) => ({ platform, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      const completionRate = totalGames > 0 ? (gamesCompleted / totalGames) * 100 : 0;
      const mostPlayedGenre = topGenres[0]?.genre || 'Unknown';
      const leastPlayedGenre = topGenres[topGenres.length - 1]?.genre || 'Unknown';

      return {
        totalGames,
        gamesPlayed,
        gamesCompleted,
        gamesInBacklog,
        averageCompletionTime: 45, // Mock data - hours
        topGenres,
        topPlatforms,
        completionRate,
        mostPlayedGenre,
        leastPlayedGenre
      };
    } catch (error) {
      console.error('Error analyzing collection:', error);
      throw error;
    }
  }

  /**
   * Generate backlog optimization suggestions
   */
  async optimizeBacklog(userId: string): Promise<BacklogOptimization[]> {
    try {
      const { data: userGames, error } = await userGamesAPI.getUserCollection(userId);
      if (error) throw error;

      const backlogGames = userGames?.filter(g => g.status === 'backlog') || [];
      const optimizations: BacklogOptimization[] = [];

      for (const game of backlogGames) {
        // AI-driven analysis (mocked for demonstration)
        const analysis = this.analyzeGameForBacklog(game);
        
        optimizations.push({
          igdb_id: game.game_id,
          gameName: game.game?.name || 'Unknown',
          priority: analysis.priority,
          reason: analysis.reason,
          estimatedPlayTime: analysis.estimatedPlayTime,
          completionLikelihood: analysis.completionLikelihood,
          tags: analysis.tags,
          cover_image: game.game?.cover_image
        });
      }

      // Sort by priority and completion likelihood
      return optimizations.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const aPriority = priorityOrder[a.priority];
        const bPriority = priorityOrder[b.priority];
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority;
        }
        
        return b.completionLikelihood - a.completionLikelihood;
      });
    } catch (error) {
      console.error('Error optimizing backlog:', error);
      return [];
    }
  }

  /**
   * Generate discovery challenges for the user
   */
  async generateDiscoveryChallenges(userId: string): Promise<DiscoveryChallenge[]> {
    try {
      const stats = await this.analyzeCollection(userId);
      const challenges: DiscoveryChallenge[] = [];

      // Genre exploration challenge
      const unexploredGenres = ['Indie', 'Puzzle', 'Horror', 'Fighting', 'Racing']
        .filter(genre => !stats.topGenres.some(g => g.genre === genre));

      if (unexploredGenres.length > 0) {
        challenges.push({
          id: 'genre-explorer',
          title: 'Genre Explorer',
          description: `Try a new genre: ${unexploredGenres[0]}`,
          type: 'genre',
          targetGames: [], // Would be populated with actual games
          progress: 0,
          maxProgress: 3,
          reward: 'Unlock new game recommendations',
          isActive: true,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
        });
      }

      // Completion challenge
      if (stats.completionRate < 50) {
        challenges.push({
          id: 'completion-master',
          title: 'Completion Master',
          description: 'Complete 5 games from your backlog',
          type: 'completion',
          targetGames: [],
          progress: 0,
          maxProgress: 5,
          reward: 'Earn achievement badges',
          isActive: true,
          expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString() // 90 days
        });
      }

      // Platform diversity challenge
      if (stats.topPlatforms.length < 3) {
        challenges.push({
          id: 'platform-diversity',
          title: 'Platform Diversity',
          description: 'Play games on different platforms',
          type: 'platform',
          targetGames: [],
          progress: stats.topPlatforms.length,
          maxProgress: 3,
          reward: 'Discover cross-platform favorites',
          isActive: true
        });
      }

      // Retro gaming challenge
      challenges.push({
        id: 'retro-revival',
        title: 'Retro Revival',
        description: 'Play games from the 1990s or 2000s',
        type: 'year',
        targetGames: [],
        progress: 0,
        maxProgress: 3,
        reward: 'Unlock classic game insights',
        isActive: true,
        expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString() // 60 days
      });

      return challenges;
    } catch (error) {
      console.error('Error generating discovery challenges:', error);
      return [];
    }
  }

  /**
   * Analyze user's gaming personality based on their collection and behavior
   */
  async analyzeGamingPersonality(userId: string): Promise<GamingPersonality> {
    try {
      const stats = await this.analyzeCollection(userId);
      
      // Analyze personality traits based on collection patterns
      const traits: string[] = [];
      let personalityType: GamingPersonality['type'] = 'casual';
      let score = 0;

      // Completionist detection
      if (stats.completionRate > 70) {
        traits.push('High completion rate');
        personalityType = 'completionist';
        score = stats.completionRate;
      }

      // Explorer detection
      if (stats.topGenres.length > 5) {
        traits.push('Diverse genre preferences');
        if (personalityType === 'casual') {
          personalityType = 'explorer';
          score = (stats.topGenres.length / 10) * 100;
        }
      }

      // Collector detection
      if (stats.totalGames > 100) {
        traits.push('Large game collection');
        if (personalityType === 'casual') {
          personalityType = 'collector';
          score = Math.min((stats.totalGames / 200) * 100, 100);
        }
      }

      // Hardcore detection
      if (stats.averageCompletionTime > 100) {
        traits.push('Prefers long, complex games');
        personalityType = 'hardcore';
        score = Math.min((stats.averageCompletionTime / 200) * 100, 100);
      }

      const personalityDescriptions = {
        completionist: 'You love finishing games and achieving 100% completion. You take pride in seeing every game through to the end.',
        explorer: 'You enjoy discovering new genres and game types. Variety is the spice of your gaming life.',
        casual: 'You play games for relaxation and fun. You prefer accessible games that are easy to pick up and play.',
        hardcore: 'You seek challenging, complex games that offer deep gameplay and require significant time investment.',
        social: 'You prefer games that connect you with other players and communities.',
        collector: 'You love building an extensive game library and discovering hidden gems.'
      };

      const recommendations = this.generatePersonalityRecommendations(personalityType, stats);

      return {
        type: personalityType,
        score: Math.round(score),
        traits,
        recommendations,
        description: personalityDescriptions[personalityType]
      };
    } catch (error) {
      console.error('Error analyzing gaming personality:', error);
      return {
        type: 'casual',
        score: 0,
        traits: [],
        recommendations: [],
        description: 'Unable to analyze gaming personality'
      };
    }
  }

  /**
   * Generate personalized insights for the user
   */
  async generateInsights(userId: string): Promise<GameInsight[]> {
    try {
      const stats = await this.analyzeCollection(userId);
      const backlogOptimizations = await this.optimizeBacklog(userId);
      const personality = await this.analyzeGamingPersonality(userId);
      
      const insights: GameInsight[] = [];

      // Backlog priority insights
      backlogOptimizations.slice(0, 3).forEach(opt => {
        insights.push({
          gameId: opt.igdb_id,
          gameName: opt.gameName,
          insightType: 'backlog_priority',
          score: opt.completionLikelihood,
          reason: `High completion likelihood (${opt.completionLikelihood}%). ${opt.reason}`,
          metadata: {
            estimatedPlayTime: opt.estimatedPlayTime,
            completionPercentage: opt.completionLikelihood
          }
        });
      });

      // Genre recommendation insights
      const underrepresentedGenres = this.findUnderrepresentedGenres(stats);
      underrepresentedGenres.forEach(genre => {
        insights.push({
          gameId: 'genre-recommendation',
          gameName: `${genre} Games`,
          insightType: 'genre_recommendation',
          score: 85,
          reason: `Based on your gaming personality (${personality.type}), you might enjoy ${genre} games.`,
          metadata: {
            recommendedMood: genre.toLowerCase()
          }
        });
      });

      // Completion prediction insights
      const { data: playingGames, error } = await userGamesAPI.getUserCollection(userId);
      if (!error && playingGames) {
        const currentlyPlaying = playingGames.filter(g => g.status === 'playing');
        currentlyPlaying.forEach(game => {
          const completionPrediction = this.predictCompletionLikelihood(game, stats);
          insights.push({
            gameId: game.game_id,
            gameName: game.game?.name || 'Unknown',
            insightType: 'completion_prediction',
            score: completionPrediction,
            reason: `Based on your gaming patterns, you have a ${completionPrediction}% chance of completing this game.`,
            metadata: {
              completionPercentage: completionPrediction
            }
          });
        });
      }

      return insights.sort((a, b) => b.score - a.score);
    } catch (error) {
      console.error('Error generating insights:', error);
      return [];
    }
  }

  /**
   * Private helper methods
   */
  private analyzeGameForBacklog(game: unknown): {
    priority: 'high' | 'medium' | 'low';
    reason: string;
    estimatedPlayTime: number;
    completionLikelihood: number;
    tags: string[];
  } {
    // Mock analysis - in real implementation, this would use ML or rule-based analysis
    console.debug('Analyzing game for backlog:', game);
    const priorities = ['high', 'medium', 'low'] as const;
    const reasons = [
      'Short playtime and high user ratings',
      'Matches your favorite genres',
      'Part of a series you enjoy',
      'Highly rated by similar users',
      'Good entry point for new genre'
    ];
    
    const tags = [
      'Quick Complete',
      'Story Rich',
      'Highly Rated',
      'Popular Choice',
      'Hidden Gem'
    ];

    return {
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      reason: reasons[Math.floor(Math.random() * reasons.length)],
      estimatedPlayTime: Math.floor(Math.random() * 100) + 10,
      completionLikelihood: Math.floor(Math.random() * 40) + 60,
      tags: tags.slice(0, Math.floor(Math.random() * 3) + 1)
    };
  }

  private findUnderrepresentedGenres(stats: CollectionStats): string[] {
    const allGenres = ['Action', 'RPG', 'Strategy', 'Adventure', 'Simulation', 'Indie', 'Puzzle', 'Horror', 'Fighting', 'Racing'];
    const userGenres = stats.topGenres.map(g => g.genre);
    
    return allGenres
      .filter(genre => !userGenres.includes(genre))
      .slice(0, 2);
  }

  private predictCompletionLikelihood(_game: unknown, stats: CollectionStats): number {
    // Mock prediction - in real implementation, this would use ML
    const baseScore = stats.completionRate;
    const randomFactor = Math.random() * 20 - 10; // -10 to +10
    
    return Math.max(0, Math.min(100, Math.round(baseScore + randomFactor)));
  }

  private generatePersonalityRecommendations(
    personalityType: GamingPersonality['type'],
    _stats?: CollectionStats
  ): string[] {
    const recommendations = {
      completionist: [
        'Focus on shorter games to maintain completion rate',
        'Try achievement-heavy games for extra completion satisfaction',
        'Consider games with multiple difficulty levels'
      ],
      explorer: [
        'Try indie games for unique experiences',
        'Explore games from different decades',
        'Consider games with experimental mechanics'
      ],
      casual: [
        'Look for games with drop-in/drop-out gameplay',
        'Try mobile-friendly games',
        'Consider puzzle games for short sessions'
      ],
      hardcore: [
        'Try games with high difficulty curves',
        'Look for games with complex mechanics',
        'Consider strategy games and RPGs'
      ],
      social: [
        'Try multiplayer games and MMOs',
        'Look for games with strong communities',
        'Consider co-op games to play with friends'
      ],
      collector: [
        'Explore different gaming eras',
        'Try games from underrepresented genres',
        'Consider bundle deals for variety'
      ]
    };

    return recommendations[personalityType] || [];
  }
}

export const collectionInsightsService = new CollectionInsightsService();