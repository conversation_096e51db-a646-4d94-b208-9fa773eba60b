/**
 * Error handling and logging utilities
 */

export interface ErrorLogEntry {
  id: string;
  timestamp: string;
  level: 'error' | 'warning' | 'info';
  message: string;
  stack?: string;
  context: string;
  userId?: string;
  userAgent?: string;
  url?: string;
  additionalData?: Record<string, unknown>;
}

/**
 * Error Logger class for centralized error handling
 */
export class ErrorLogger {
  private static instance: ErrorLogger;
  private logQueue: ErrorLogEntry[] = [];
  private isOnline = navigator.onLine;
  private maxQueueSize = 100;
  private flushInterval = 30000; // 30 seconds
  private retryAttempts = 3;
  private retryDelay = 1000; // 1 second

  private constructor() {
    this.setupEventListeners();
    this.startPeriodicFlush();
  }

  static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger();
    }
    return ErrorLogger.instance;
  }

  private setupEventListeners() {
    // Monitor online/offline status
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushLogs();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // Capture unhandled errors
    window.addEventListener('error', (event) => {
      this.logError({
        message: event.message,
        stack: event.error?.stack,
        context: 'window.error',
        additionalData: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack,
        context: 'unhandledrejection',
        additionalData: {
          reason: event.reason
        }
      });
    });
  }

  private startPeriodicFlush() {
    setInterval(() => {
      if (this.logQueue.length > 0) {
        this.flushLogs();
      }
    }, this.flushInterval);
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private getUserId(): string {
    try {
      const authData = localStorage.getItem('supabase.auth.token');
      if (authData) {
        const parsed = JSON.parse(authData);
        return parsed.user?.id || 'anonymous';
      }
    } catch {
      // Ignore parsing errors
    }
    return 'anonymous';
  }

  public logError(error: {
    message: string;
    stack?: string;
    context: string;
    additionalData?: Record<string, unknown>;
  }) {
    const entry: ErrorLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level: 'error',
      message: error.message,
      stack: error.stack,
      context: error.context,
      userId: this.getUserId(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      additionalData: error.additionalData
    };

    this.addToQueue(entry);
    
    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.error('🚨 Error logged:', entry);
    }
  }

  public logWarning(warning: {
    message: string;
    context: string;
    additionalData?: Record<string, unknown>;
  }) {
    const entry: ErrorLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level: 'warning',
      message: warning.message,
      context: warning.context,
      userId: this.getUserId(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      additionalData: warning.additionalData
    };

    this.addToQueue(entry);
    
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Warning logged:', entry);
    }
  }

  public logInfo(info: {
    message: string;
    context: string;
    additionalData?: Record<string, unknown>;
  }) {
    const entry: ErrorLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level: 'info',
      message: info.message,
      context: info.context,
      userId: this.getUserId(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      additionalData: info.additionalData
    };

    this.addToQueue(entry);
    
    if (process.env.NODE_ENV === 'development') {
      console.info('ℹ️ Info logged:', entry);
    }
  }

  private addToQueue(entry: ErrorLogEntry) {
    this.logQueue.push(entry);
    
    // Maintain queue size limit
    if (this.logQueue.length > this.maxQueueSize) {
      this.logQueue.shift(); // Remove oldest entry
    }
    
    // Flush immediately for critical errors
    if (entry.level === 'error' && this.isOnline) {
      this.flushLogs();
    }
  }

  private async flushLogs() {
    if (!this.isOnline || this.logQueue.length === 0) {
      return;
    }

    const logsToSend = [...this.logQueue];
    this.logQueue = [];

    for (let attempt = 0; attempt < this.retryAttempts; attempt++) {
      try {
        await this.sendLogs(logsToSend);
        break; // Success, exit retry loop
      } catch (error) {
        console.warn(`Failed to send logs (attempt ${attempt + 1}/${this.retryAttempts}):`, error);
        
        if (attempt === this.retryAttempts - 1) {
          // Final attempt failed, put logs back in queue
          this.logQueue.unshift(...logsToSend);
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, this.retryDelay * (attempt + 1)));
        }
      }
    }
  }

  private async sendLogs(logs: ErrorLogEntry[]): Promise<void> {
    const payload = {
      logs,
      timestamp: new Date().toISOString(),
      source: 'client'
    };

    // Try beacon first (non-blocking)
    if (navigator.sendBeacon) {
      const success = navigator.sendBeacon('/api/log-error', JSON.stringify(payload));
      if (success) {
        return;
      }
    }

    // Fallback to fetch
    const response = await fetch('/api/log-error', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  public getQueueSize(): number {
    return this.logQueue.length;
  }

  public clearQueue(): void {
    this.logQueue = [];
  }

  public forceFlush(): Promise<void> {
    return this.flushLogs();
  }
}

// Export singleton instance
export const errorLogger = ErrorLogger.getInstance();

// Convenience functions
export const logError = (error: {
  message: string;
  stack?: string;
  context: string;
  additionalData?: Record<string, unknown>;
}) => errorLogger.logError(error);

export const logWarning = (warning: {
  message: string;
  context: string;
  additionalData?: Record<string, unknown>;
}) => errorLogger.logWarning(warning);

export const logInfo = (info: {
  message: string;
  context: string;
  additionalData?: Record<string, unknown>;
}) => errorLogger.logInfo(info);

/**
 * Create an error with additional context
 */
export function createError(message: string, context: string, cause?: Error): Error {
  const error = new Error(message);
  error.name = 'ApplicationError';
  // Only set cause if supported by the environment
  if ('cause' in Error.prototype) {
    (error as Error & { cause?: Error }).cause = cause;
  }
  (error as Error & { context?: string }).context = context;
  return error;
}

/**
 * Safely execute a function and log errors
 */
export async function safeExecute<T>(
  fn: () => Promise<T>,
  context: string,
  fallback?: T
): Promise<T | undefined> {
  try {
    return await fn();
  } catch (error) {
    logError({
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      context,
      additionalData: { error }
    });
    return fallback;
  }
}

/**
 * Safely execute a synchronous function and log errors
 */
export function safeExecuteSync<T>(
  fn: () => T,
  context: string,
  fallback?: T
): T | undefined {
  try {
    return fn();
  } catch (error) {
    logError({
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      context,
      additionalData: { error }
    });
    return fallback;
  }
}

/**
 * Retry a function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000,
  context: string = 'retryWithBackoff'
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt === maxAttempts) {
        logError({
          message: `Failed after ${maxAttempts} attempts: ${lastError.message}`,
          stack: lastError.stack,
          context,
          additionalData: { attempts: maxAttempts, error: lastError }
        });
        throw lastError;
      }
      
      const delay = baseDelay * Math.pow(2, attempt - 1);
      logWarning({
        message: `Attempt ${attempt} failed, retrying in ${delay}ms: ${lastError.message}`,
        context,
        additionalData: { attempt, delay, error: lastError }
      });
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

/**
 * Wrap a function to automatically log errors
 */
export function withErrorLogging<T extends (...args: unknown[]) => unknown>(
  fn: T,
  context: string
): T {
  return ((...args: Parameters<T>) => {
    try {
      const result = fn(...args);
      
      // Handle async functions
      if (result instanceof Promise) {
        return result.catch(error => {
          logError({
            message: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            context,
            additionalData: { args, error }
          });
          throw error;
        });
      }
      
      return result;
    } catch (error) {
      logError({
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        context,
        additionalData: { args, error }
      });
      throw error;
    }
  }) as T;
}