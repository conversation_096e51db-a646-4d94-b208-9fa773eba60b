import { supabase } from './supabase-client';

// Types for search analytics
export interface SearchQuery {
  id: string;
  user_id: string | null;
  query_text: string;
  filters_applied: Record<string, unknown>;
  sort_options: Record<string, unknown>;
  search_source: 'manual' | 'voice' | 'ai_assisted' | 'autocomplete';
  session_id: string | null;
  created_at: string;
}

export interface SearchResult {
  id: string;
  query_id: string;
  api_source: 'igdb' | 'rawg' | 'thegamesdb' | 'cache';
  total_results: number;
  results_shown: number;
  response_time_ms: number;
  cache_hit: boolean;
  success: boolean;
  error_message: string | null;
  created_at: string;
}

export interface SearchInteraction {
  id: string;
  user_id: string | null;
  query_id: string;
  game_id: string | null;
  interaction_type: 'click' | 'add_to_library' | 'add_to_wishlist' | 'view_details' | 'copy_link' | 'hover' | 'filter_applied' | 'sort_applied';
  interaction_position: number | null;
  interaction_data: Record<string, unknown>;
  created_at: string;
}

export interface SearchPerformanceMetrics {
  id: string;
  date: string;
  user_id: string | null;
  total_searches: number;
  successful_searches: number;
  failed_searches: number;
  average_response_time_ms: number;
  cache_hit_rate: number;
  most_common_query: string | null;
  most_common_filters: Record<string, unknown>;
  total_interactions: number;
  unique_games_found: number;
  conversion_rate: number;
  created_at: string;
  updated_at: string;
}

export interface PopularSearchTerm {
  id: string;
  query_text: string;
  search_count: number;
  success_rate: number;
  last_searched: string;
  trending_score: number;
  period_type: 'daily' | 'weekly' | 'monthly' | 'all_time';
  created_at: string;
  updated_at: string;
}

export interface SearchRecommendation {
  id: string;
  user_id: string;
  original_query: string;
  recommended_query: string;
  recommendation_type: 'spelling' | 'synonym' | 'related' | 'trending' | 'personalized';
  confidence_score: number;
  metadata: Record<string, unknown>;
  is_accepted: boolean;
  is_dismissed: boolean;
  created_at: string;
  applied_at: string | null;
}

export interface SearchSession {
  id: string;
  user_id: string | null;
  session_id: string;
  start_time: string;
  end_time: string | null;
  total_queries: number;
  total_interactions: number;
  session_goal: string | null;
  session_outcome: 'successful' | 'abandoned' | 'partial' | null;
  device_info: Record<string, unknown>;
  created_at: string;
}

export interface SearchAnalytics {
  total_searches: number;
  successful_searches: number;
  failed_searches: number;
  average_response_time: number;
  cache_hit_rate: number;
  total_interactions: number;
  conversion_rate: number;
  top_queries: string[];
  trending_score: number;
}

/**
 * Search Analytics Database Operations
 */
export const searchAnalytics = {
  // Search Queries
  async recordQuery(
    userId: string | null,
    queryText: string,
    filtersApplied: Record<string, unknown> = {},
    sortOptions: Record<string, unknown> = {},
    searchSource: SearchQuery['search_source'] = 'manual',
    sessionId: string | null = null
  ) {
    const { data, error } = await supabase.rpc('record_search_query', {
      p_user_id: userId,
      p_query_text: queryText,
      p_filters_applied: filtersApplied,
      p_sort_options: sortOptions,
      p_search_source: searchSource,
      p_session_id: sessionId
    });

    if (error) {
      console.error('Error recording search query:', error);
      throw error;
    }

    return { data, error: null };
  },

  async getQueries(userId: string, limit: number = 50) {
    const { data, error } = await supabase
      .from('search_queries')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    return { data: data as SearchQuery[] | null, error };
  },

  // Search Results
  async recordResults(
    queryId: string,
    apiSource: SearchResult['api_source'],
    totalResults: number,
    resultsShown: number,
    responseTimeMs: number,
    cacheHit: boolean = false,
    success: boolean = true,
    errorMessage: string | null = null
  ) {
    const { data, error } = await supabase.rpc('record_search_results', {
      p_query_id: queryId,
      p_api_source: apiSource,
      p_total_results: totalResults,
      p_results_shown: resultsShown,
      p_response_time_ms: responseTimeMs,
      p_cache_hit: cacheHit,
      p_success: success,
      p_error_message: errorMessage
    });

    if (error) {
      console.error('Error recording search results:', error);
      throw error;
    }

    return { data, error: null };
  },

  async getResults(queryId: string) {
    const { data, error } = await supabase
      .from('search_results')
      .select('*')
      .eq('query_id', queryId)
      .order('created_at', { ascending: false });

    return { data: data as SearchResult[] | null, error };
  },

  // Search Interactions
  async recordInteraction(
    userId: string | null,
    queryId: string,
    gameId: string | null,
    interactionType: SearchInteraction['interaction_type'],
    interactionPosition: number | null = null,
    interactionData: Record<string, unknown> = {}
  ) {
    const { data, error } = await supabase.rpc('record_search_interaction', {
      p_user_id: userId,
      p_query_id: queryId,
      p_game_id: gameId,
      p_interaction_type: interactionType,
      p_interaction_position: interactionPosition,
      p_interaction_data: interactionData
    });

    if (error) {
      console.error('Error recording search interaction:', error);
      throw error;
    }

    return { data, error: null };
  },

  async getInteractions(userId: string, limit: number = 100) {
    const { data, error } = await supabase
      .from('search_interactions')
      .select(`
        *,
        search_queries!inner(*),
        games(*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    return { data: data as SearchInteraction[] | null, error };
  },

  // Performance Metrics
  async getPerformanceMetrics(userId: string, days: number = 30) {
    const { data, error } = await supabase
      .from('search_performance_metrics')
      .select('*')
      .eq('user_id', userId)
      .gte('date', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .order('date', { ascending: false });

    return { data: data as SearchPerformanceMetrics[] | null, error };
  },

  async updatePerformanceMetrics(userId: string, metrics: Partial<SearchPerformanceMetrics>) {
    const today = new Date().toISOString().split('T')[0];
    
    const { data, error } = await supabase
      .from('search_performance_metrics')
      .upsert({
        date: today,
        user_id: userId,
        ...metrics,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'date,user_id'
      });

    return { data, error };
  },

  // Popular Search Terms
  async getPopularTerms(periodType: PopularSearchTerm['period_type'] = 'all_time', limit: number = 20) {
    const { data, error } = await supabase
      .from('popular_search_terms')
      .select('*')
      .eq('period_type', periodType)
      .order('search_count', { ascending: false })
      .limit(limit);

    return { data: data as PopularSearchTerm[] | null, error };
  },

  async updatePopularTerms(queryText: string, success: boolean = true) {
    const { data, error } = await supabase.rpc('update_popular_search_terms', {
      p_query_text: queryText,
      p_success: success
    });

    if (error) {
      console.error('Error updating popular search terms:', error);
    }

    return { data, error };
  },

  // Search Recommendations
  async getRecommendations(userId: string, limit: number = 10) {
    const { data, error } = await supabase
      .from('search_recommendations')
      .select('*')
      .eq('user_id', userId)
      .eq('is_dismissed', false)
      .order('confidence_score', { ascending: false })
      .limit(limit);

    return { data: data as SearchRecommendation[] | null, error };
  },

  async createRecommendation(recommendation: Omit<SearchRecommendation, 'id' | 'created_at' | 'applied_at'>) {
    const { data, error } = await supabase
      .from('search_recommendations')
      .insert(recommendation)
      .select()
      .single();

    return { data: data as SearchRecommendation | null, error };
  },

  async updateRecommendation(id: string, updates: Partial<SearchRecommendation>) {
    const { data, error } = await supabase
      .from('search_recommendations')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    return { data: data as SearchRecommendation | null, error };
  },

  // Search Sessions
  async createSession(
    userId: string | null,
    sessionId: string,
    deviceInfo: Record<string, unknown> = {}
  ) {
    const { data, error } = await supabase
      .from('search_sessions')
      .insert({
        user_id: userId,
        session_id: sessionId,
        device_info: deviceInfo,
        start_time: new Date().toISOString()
      })
      .select()
      .single();

    return { data: data as SearchSession | null, error };
  },

  async updateSession(sessionId: string, updates: Partial<SearchSession>) {
    const { data, error } = await supabase
      .from('search_sessions')
      .update(updates)
      .eq('session_id', sessionId)
      .select()
      .single();

    return { data: data as SearchSession | null, error };
  },

  async getSessions(userId: string, limit: number = 50) {
    const { data, error } = await supabase
      .from('search_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('start_time', { ascending: false })
      .limit(limit);

    return { data: data as SearchSession[] | null, error };
  },

  // Analytics
  async getUserAnalytics(userId: string, days: number = 30): Promise<{ data: SearchAnalytics | null, error: unknown }> {
    const { data, error } = await supabase.rpc('get_user_search_analytics', {
      p_user_id: userId,
      p_days: days
    });

    if (error) {
      console.error('Error fetching user search analytics:', error);
      return { data: null, error };
    }

    // The RPC returns an array with a single row
    const analytics = data?.[0] || null;
    return { data: analytics as SearchAnalytics | null, error: null };
  },

  // Utility functions
  generateSessionId(): string {
    return `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  async cleanupOldData(retentionDays: number = 90) {
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000).toISOString();
    
    // Clean up old search queries and related data
    const { error: queriesError } = await supabase
      .from('search_queries')
      .delete()
      .lt('created_at', cutoffDate);

    if (queriesError) {
      console.error('Error cleaning up old search queries:', queriesError);
    }

    // Clean up old performance metrics
    const { error: metricsError } = await supabase
      .from('search_performance_metrics')
      .delete()
      .lt('created_at', cutoffDate);

    if (metricsError) {
      console.error('Error cleaning up old performance metrics:', metricsError);
    }

    return { 
      queriesError, 
      metricsError,
      success: !queriesError && !metricsError 
    };
  }
};