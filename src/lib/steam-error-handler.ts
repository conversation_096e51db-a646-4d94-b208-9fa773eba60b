/**
 * Enhanced Steam API Error Handling System
 * Implements retry logic, circuit breaker patterns, and granular error types
 */

// Enhanced Steam API error types
export enum SteamApiError {
  RATE_LIMITED = 'RATE_LIMITED',
  PRIVATE_PROFILE = 'PRIVATE_PROFILE',
  INVALID_APP_ID = 'INVALID_APP_ID',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  INVALID_STEAM_ID = 'INVALID_STEAM_ID',
  NO_ACHIEVEMENTS = 'NO_ACHIEVEMENTS',
  FRIENDS_LIST_PRIVATE = 'FRIENDS_LIST_PRIVATE',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Steam API context interface
interface SteamApiContext {
  endpoint?: string;
  steamId?: string;
  appId?: string | number;
  attempt?: number;
  [key: string]: unknown;
}

// HTTP Error interface for proper typing
interface HttpError extends Error {
  status?: number;
  statusCode?: number;
  headers?: Record<string, string>;
}

export interface SteamApiErrorDetails {
  type: SteamApiError;
  message: string;
  statusCode?: number;
  retryAfter?: number;
  isRetryable: boolean;
  context?: SteamApiContext;
}

export class SteamApiException extends Error {
  public readonly details: SteamApiErrorDetails;

  constructor(details: SteamApiErrorDetails) {
    super(details.message);
    this.name = 'SteamApiException';
    this.details = details;
  }
}

// Circuit breaker implementation
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private readonly failureThreshold = 5,
    private readonly recoveryTimeout = 60000, // 1 minute
    private readonly monitoringPeriod = 300000 // 5 minutes
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
        console.log('🔄 Circuit breaker transitioning to HALF_OPEN');
      } else {
        throw new SteamApiException({
          type: SteamApiError.SERVICE_UNAVAILABLE,
          message: 'Circuit breaker is OPEN - Steam API temporarily unavailable',
          isRetryable: true,
          retryAfter: Math.ceil((this.recoveryTimeout - (Date.now() - this.lastFailureTime)) / 1000)
        });
      }
    }

    try {
      const result = await operation();
      
      if (this.state === 'HALF_OPEN') {
        this.reset();
        console.log('✅ Circuit breaker reset to CLOSED');
      }
      
      return result;
    } catch (error) {
      this.recordFailure();
      throw error;
    }
  }

  private recordFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
      console.warn(`⚠️ Circuit breaker OPEN after ${this.failures} failures`);
    }
  }

  private reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    this.lastFailureTime = 0;
  }

  getState(): { state: string; failures: number; lastFailureTime: number } {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime
    };
  }
}

// Rate limiter implementation
class RateLimiter {
  private requests: number[] = [];

  constructor(
    private readonly maxRequests = 100,
    private readonly windowMs = 300000 // 5 minutes
  ) {}

  async checkLimit(): Promise<void> {
    const now = Date.now();
    
    // Remove old requests outside the window
    this.requests = this.requests.filter(time => now - time < this.windowMs);

    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);
      
      throw new SteamApiException({
        type: SteamApiError.RATE_LIMITED,
        message: `Rate limit exceeded. ${this.requests.length}/${this.maxRequests} requests in window.`,
        isRetryable: true,
        retryAfter: Math.ceil(waitTime / 1000)
      });
    }

    this.requests.push(now);
  }

  getRemainingRequests(): number {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    return Math.max(0, this.maxRequests - this.requests.length);
  }
}

// Retry configuration
interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  backoffMultiplier: 2,
  jitter: true
};

// Enhanced Steam API client with error handling
export class EnhancedSteamApiClient {
  private circuitBreaker = new CircuitBreaker();
  private rateLimiter = new RateLimiter();

  constructor(private readonly retryConfig: RetryConfig = DEFAULT_RETRY_CONFIG) {}

  async makeRequest<T>(
    operation: () => Promise<T>,
    context: SteamApiContext = {}
  ): Promise<T> {
    return this.circuitBreaker.execute(async () => {
      await this.rateLimiter.checkLimit();
      return this.executeWithRetry(operation, context);
    });
  }

  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: SteamApiContext
  ): Promise<T> {
    let lastError: SteamApiException | null = null;

    for (let attempt = 1; attempt <= this.retryConfig.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = this.handleError(error, context);

        if (!lastError.details.isRetryable || attempt === this.retryConfig.maxAttempts) {
          throw lastError;
        }

        const delay = this.calculateDelay(attempt);
        console.warn(`⚠️ Steam API request failed (attempt ${attempt}/${this.retryConfig.maxAttempts}), retrying in ${delay}ms:`, lastError.message);
        
        await this.sleep(delay);
      }
    }

    throw lastError!;
  }

  private handleError(error: unknown, context: SteamApiContext): SteamApiException {
    // If it's already a SteamApiException, return it
    if (error instanceof SteamApiException) {
      return error;
    }

    // Handle fetch/network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return new SteamApiException({
        type: SteamApiError.NETWORK_ERROR,
        message: 'Network error occurred while contacting Steam API',
        isRetryable: true,
        context
      });
    }

    // Handle timeout errors
    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      return new SteamApiException({
        type: SteamApiError.TIMEOUT,
        message: 'Request to Steam API timed out',
        isRetryable: true,
        context
      });
    }

    // Handle HTTP status codes
    if (error.status || error.statusCode) {
      const statusCode = error.status || error.statusCode;
      
      switch (statusCode) {
        case 401:
          return new SteamApiException({
            type: SteamApiError.PRIVATE_PROFILE,
            message: 'Steam profile or data is private',
            statusCode,
            isRetryable: false,
            context
          });
        
        case 403:
          return new SteamApiException({
            type: SteamApiError.FRIENDS_LIST_PRIVATE,
            message: 'Steam friends list is private',
            statusCode,
            isRetryable: false,
            context
          });
        
        case 404:
          return new SteamApiException({
            type: SteamApiError.INVALID_STEAM_ID,
            message: 'Steam ID or App ID not found',
            statusCode,
            isRetryable: false,
            context
          });
        
        case 429: {
          const httpError = error as HttpError;
          const retryAfter = httpError.headers?.['retry-after'] ? parseInt(httpError.headers['retry-after']) : 60;
          return new SteamApiException({
            type: SteamApiError.RATE_LIMITED,
            message: 'Steam API rate limit exceeded',
            statusCode,
            retryAfter,
            isRetryable: true,
            context
          });
        }
        
        case 500:
        case 502:
        case 503:
        case 504:
          return new SteamApiException({
            type: SteamApiError.SERVICE_UNAVAILABLE,
            message: 'Steam API service temporarily unavailable',
            statusCode,
            isRetryable: true,
            context
          });
        
        default:
          return new SteamApiException({
            type: SteamApiError.UNKNOWN_ERROR,
            message: `Steam API returned status ${statusCode}`,
            statusCode,
            isRetryable: statusCode >= 500,
            context
          });
      }
    }

    // Handle unknown errors
    return new SteamApiException({
      type: SteamApiError.UNKNOWN_ERROR,
      message: error.message || 'Unknown error occurred',
      isRetryable: false,
      context
    });
  }

  private calculateDelay(attempt: number): number {
    let delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt - 1);
    delay = Math.min(delay, this.retryConfig.maxDelay);

    if (this.retryConfig.jitter) {
      // Add jitter to prevent thundering herd
      delay = delay * (0.5 + Math.random() * 0.5);
    }

    return Math.floor(delay);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Utility methods for monitoring
  getCircuitBreakerState() {
    return this.circuitBreaker.getState();
  }

  getRemainingRequests(): number {
    return this.rateLimiter.getRemainingRequests();
  }
}

// Global instance
export const steamApiClient = new EnhancedSteamApiClient();
