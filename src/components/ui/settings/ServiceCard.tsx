/**
 * Service Card Component
 * Individual service management card for artwork API services
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Input } from '@/components/ui/base/input';
import { Label } from '@/components/ui/base/label';
import { Badge } from '@/components/ui/base/badge';
import { 
  Key, 
  Plus, 
  Trash2, 
  Eye, 
  EyeOff, 
  Zap
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { apiKeyStore, ArtworkService, KeyType } from '@/lib/apiKeyStore';
import { artworkAPIRouter } from '@/lib/services/artworkAPIRouter';

interface ServiceKeyEntry {
  id: string;
  value: string;
  label?: string;
  source: 'user' | 'env';
  isVisible: boolean;
  usage?: {
    totalRequests: number;
    successfulRequests: number;
    averageResponseTime: number;
    lastUsed: number;
  };
}

interface ServiceInfo {
  service: ArtworkService;
  displayName: string;
  keyType: KeyType;
  keyLabel: string;
  description: string;
  costPerRequest: number;
  qualityRating: number;
  features: string[];
  isRequired: boolean;
}

interface ServiceCardProps {
  serviceInfo: ServiceInfo;
  keys: ServiceKeyEntry[];
  stats?: {
    totalRequests: number;
    successfulRequests: number;
    averageResponseTime: number;
    availableKeys: number;
  };
  onKeyAdded: () => void;
  onKeyDeleted: () => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ 
  serviceInfo, 
  keys, 
  stats, 
  onKeyAdded, 
  onKeyDeleted 
}) => {
  const [newKeyValue, setNewKeyValue] = useState('');
  const [newKeyLabel, setNewKeyLabel] = useState('');
  const [saving, setSaving] = useState(false);
  const [keyVisibility, setKeyVisibility] = useState<Record<string, boolean>>({});

  const handleAddKey = async () => {
    const keyValue = newKeyValue?.trim();
    const keyLabel = newKeyLabel?.trim();

    if (!keyValue) {
      toast.error('Please enter an API key');
      return;
    }

    setSaving(true);

    try {
      await apiKeyStore.storeKey({
        service: serviceInfo.service,
        keyType: serviceInfo.keyType,
        value: keyValue,
        label: keyLabel || `${serviceInfo.service.toUpperCase()} Key`
      });

      setNewKeyValue('');
      setNewKeyLabel('');
      
      onKeyAdded();
      toast.success('API key added successfully');
    } catch (error) {
      console.error('Failed to add API key:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to add API key');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteKey = async (keyId: string) => {
    if (!confirm('Are you sure you want to delete this API key?')) {
      return;
    }

    try {
      if (!keyId.startsWith('env_')) {
        await apiKeyStore.deleteServiceKey(serviceInfo.service, serviceInfo.keyType, keyId);
      }
      onKeyDeleted();
      toast.success('API key deleted');
    } catch (error) {
      console.error('Failed to delete API key:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete API key');
    }
  };

  const toggleKeyVisibility = (keyId: string) => {
    setKeyVisibility(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  const testAPIKey = async () => {
    try {
      const result = await artworkAPIRouter.routeRequest('search', { 
        gameName: 'Test Game',
        service: serviceInfo.service 
      }, {
        preferredServices: [serviceInfo.service]
      });

      if (result.success) {
        toast.success(`${serviceInfo.service.toUpperCase()} API key is working!`);
      } else {
        toast.error(`${serviceInfo.service.toUpperCase()} API test failed: ${result.error}`);
      }
    } catch (error) {
      toast.error(`API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {serviceInfo.displayName}
              {serviceInfo.isRequired && (
                <Badge variant="destructive" className="text-xs">Required</Badge>
              )}
              <Badge variant="outline" className="text-xs">
                {serviceInfo.costPerRequest > 0 ? `$${serviceInfo.costPerRequest}/req` : 'Free'}
              </Badge>
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">{serviceInfo.description}</p>
          </div>
          <div className="text-right">
            <div className="flex items-center gap-1 mb-1">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className={`w-2 h-2 rounded-full ${
                    i < serviceInfo.qualityRating ? 'bg-yellow-400' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
            <p className="text-xs text-gray-500">Quality Rating</p>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Features */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {serviceInfo.features.map(feature => (
              <Badge key={feature} variant="secondary" className="text-xs">
                {feature}
              </Badge>
            ))}
          </div>
        </div>

        {/* Statistics */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{stats.totalRequests || 0}</div>
              <div className="text-xs text-gray-600">Total Requests</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">{stats.successfulRequests || 0}</div>
              <div className="text-xs text-gray-600">Successful</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-purple-600">{Math.round(stats.averageResponseTime || 0)}ms</div>
              <div className="text-xs text-gray-600">Avg Response</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-orange-600">{stats.availableKeys || 0}</div>
              <div className="text-xs text-gray-600">Available Keys</div>
            </div>
          </div>
        )}

        {/* Existing Keys */}
        {keys.length > 0 && (
          <div className="mb-4">
            <h4 className="font-medium mb-2">API Keys</h4>
            <div className="space-y-2">
              {keys.map(key => (
                <div key={key.id} className="flex items-center gap-2 p-2 border rounded-lg">
                  <Key className="h-4 w-4 text-gray-500" />
                  <div className="flex-1">
                    <div className="font-medium text-sm">
                      {key.label || `${serviceInfo.service.toUpperCase()} Key`}
                      {key.source === 'env' && (
                        <Badge variant="outline" className="ml-2 text-xs">Environment</Badge>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 font-mono">
                      {keyVisibility[key.id] ? key.value : '••••••••••••••••'}
                    </div>
                    {key.usage && (
                      <div className="text-xs text-gray-500 mt-1">
                        {key.usage.totalRequests} requests • {key.usage.successfulRequests} successful
                        {key.usage.lastUsed > 0 && (
                          <> • Last used {new Date(key.usage.lastUsed).toLocaleDateString()}</>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => toggleKeyVisibility(key.id)}
                    >
                      {keyVisibility[key.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={testAPIKey}
                    >
                      <Zap className="h-4 w-4" />
                    </Button>
                    {key.source === 'user' && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDeleteKey(key.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Add New Key */}
        <div className="space-y-3">
          <h4 className="font-medium">Add New {serviceInfo.keyLabel}</h4>
          <div className="space-y-2">
            <div>
              <Label htmlFor={`key-${serviceInfo.service}`}>{serviceInfo.keyLabel}</Label>
              <Input
                id={`key-${serviceInfo.service}`}
                type="password"
                placeholder={`Enter your ${serviceInfo.keyLabel.toLowerCase()}`}
                value={newKeyValue}
                onChange={(e) => setNewKeyValue(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor={`label-${serviceInfo.service}`}>Label (Optional)</Label>
              <Input
                id={`label-${serviceInfo.service}`}
                placeholder={`My ${serviceInfo.displayName} Key`}
                value={newKeyLabel}
                onChange={(e) => setNewKeyLabel(e.target.value)}
              />
            </div>
            <Button
              onClick={handleAddKey}
              disabled={saving || !newKeyValue?.trim()}
              className="w-full"
            >
              {saving ? (
                <>Loading...</>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Add {serviceInfo.keyLabel}
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ServiceCard;