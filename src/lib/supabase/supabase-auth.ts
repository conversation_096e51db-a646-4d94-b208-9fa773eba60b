import { Session, AuthChangeEvent } from '@supabase/supabase-js';
import { supabase } from './supabase-client';
import { logError } from './supabase-utils';
import { ensureUserRecords } from './supabase-database';

export interface AuthResult<T = any> {
  data: T | null;
  error: any;
}

export interface AuthStateChangeCallback {
  (event: AuthChangeEvent, session: Session | null): void;
}

export const auth = {
  signUp: async (email: string, password: string, username?: string): Promise<AuthResult> => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: { username }
      }
    });
    
    if (error) {
      logError('signUp', error, { email: email.substring(0, 3) + '***' });
    } else if (data.user) {
      // Ensure user records are created after successful signup
      await ensureUserRecords(data.user.id);
    }
    
    return { data, error };
  },

  signIn: async (email: string, password: string): Promise<AuthResult> => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) {
      logError('signIn', error, { email: email.substring(0, 3) + '***' });
    } else if (data.user) {
      // Ensure user records exist after successful signin
      await ensureUserRecords(data.user.id);
    }
    
    return { data, error };
  },

  signOut: async (): Promise<{ error: any }> => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      logError('signOut', error);
    }
    return { error };
  },

  resetPassword: async (email: string): Promise<AuthResult> => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    });
    
    if (error) {
      logError('resetPassword', error, { email: email.substring(0, 3) + '***' });
    }
    
    return { data, error };
  },

  getCurrentUser: () => supabase.auth.getUser(),
  
  onAuthStateChange: (callback: AuthStateChangeCallback) => {
    return supabase.auth.onAuthStateChange(callback);
  }
};