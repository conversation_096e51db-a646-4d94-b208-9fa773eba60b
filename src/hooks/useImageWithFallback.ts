import { useState, useCallback, useEffect } from 'react';
import { generateGameCoverPlaceholder, generateThumbnailPlaceholder } from '@/lib/utils/imageUtils';

interface UseImageWithFallbackOptions {
  src?: string | null;
  fallbackText?: string;
  type?: 'cover' | 'thumbnail' | 'screenshot';
  gameName?: string;
}

interface UseImageWithFallbackReturn {
  imageSrc: string;
  isLoading: boolean;
  hasError: boolean;
  handleLoad: () => void;
  handleError: () => void;
  retry: () => void;
}

/**
 * Hook for handling image loading with automatic fallback to local SVG placeholders
 */
export function useImageWithFallback({
  src,
  fallbackText = 'Image',
  type = 'thumbnail',
  gameName
}: UseImageWithFallbackOptions): UseImageWithFallbackReturn {
  const [isLoading, setIsLoading] = useState(!!src);
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const getPlaceholder = useCallback(() => {
    const displayText = gameName || fallbackText;
    
    switch (type) {
      case 'cover':
        return generateGameCoverPlaceholder(displayText, 'box-art');
      case 'thumbnail':
        return generateThumbnailPlaceholder(displayText);
      case 'screenshot':
        return generateThumbnailPlaceholder('Screenshot');
      default:
        return generateThumbnailPlaceholder(displayText);
    }
  }, [gameName, fallbackText, type]);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
  }, []);

  const handleError = useCallback(() => {
    setIsLoading(false);
    setHasError(true);
  }, []);

  const retry = useCallback(() => {
    if (src && retryCount < 2) {
      setRetryCount(prev => prev + 1);
      setIsLoading(true);
      setHasError(false);
    }
  }, [src, retryCount]);

  // Reset state when src changes
  useEffect(() => {
    if (src) {
      setIsLoading(true);
      setHasError(false);
      setRetryCount(0);
    } else {
      setIsLoading(false);
      setHasError(false);
    }
  }, [src]);

  // Determine the image source to use
  const imageSrc = (!src || hasError) ? getPlaceholder() : src;

  return {
    imageSrc,
    isLoading,
    hasError,
    handleLoad,
    handleError,
    retry
  };
}

export default useImageWithFallback;