import { UserGameWithDetails } from "@/types/database";
import { PlatformFamily } from "@/lib/utils/platformFamilyUtils";
import { SortOptions } from "@/types/filters";

export type ViewMode = "status" | "platform" | "platform-family" | "steam";

export interface LibraryStats {
  total: number;
  playing: number;
  completed: number;
  backlog: number;
  wishlist: number;
  completionRate: number;
  averageRating: number;
  totalHours: number;
  averageHoursPerGame: number;
  mostPlayedGame?: string;
}

export interface LibraryFilters {
  status?: string;
  rating?: string;
  platform?: string;
  genre?: string;
  developer?: string;
  publisher?: string;
}

export interface LibrarySettings {
  viewMode: ViewMode;
  sortOptions: SortOptions;
  collapsedPlatforms: Set<string>;
  collapsedFamilies: Set<PlatformFamily>;
}

export interface TabProps {
  libraryGames: UserGameWithDetails[];
  isLoading: boolean;
  error: Error | null;
}

export interface LibraryTabProps extends TabProps {
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  sortOptions: SortOptions;
  setSortOptions: (options: SortOptions) => void;
  collapsedPlatforms: Set<string>;
  onPlatformToggle: (platform: string) => void;
  collapsedFamilies: Set<PlatformFamily>;
  onFamilyToggle: (family: PlatformFamily) => void;
  onGameClick: (game: UserGameWithDetails) => void;
  onStatusUpdate: (userGameId: string, status: string) => void;
  onRemoveGame: (userGameId: string) => void;
}

export interface StatsTabProps extends TabProps {
  stats: LibraryStats;
}

export interface CollectionsTabProps extends TabProps {
  onCollectionCreate: (collection: unknown) => void;
  onCollectionUpdate: (id: string, updates: unknown) => void;
  onCollectionDelete: (id: string) => void;
  onCollectionDuplicate: (id: string) => void;
}

export interface FiltersTabProps extends TabProps {
  filters: LibraryFilters;
  onFiltersApply: (filters: LibraryFilters) => void;
}

export interface AnalyticsTabProps extends TabProps {
  timeframe: "week" | "month" | "quarter" | "year";
  onTimeframeChange: (timeframe: "week" | "month" | "quarter" | "year") => void;
}

export interface ExportTabProps extends TabProps {
  onExport: (format: string, options: Record<string, unknown>) => void;
  onImport: (
    source: string,
    data: unknown,
    options: Record<string, unknown>
  ) => void;
}

export interface ViewModeProps {
  games: UserGameWithDetails[];
  onGameClick: (game: UserGameWithDetails) => void;
  onStatusUpdate: (userGameId: string, status: string) => void;
  onRemoveGame: (userGameId: string) => void;
}

export type StatusViewProps = ViewModeProps;

export interface PlatformViewProps extends ViewModeProps {
  collapsedPlatforms: Set<string>;
  onPlatformToggle: (platform: string) => void;
}

export interface PlatformFamilyViewProps extends ViewModeProps {
  collapsedFamilies: Set<PlatformFamily>;
  onFamilyToggle: (family: PlatformFamily) => void;
  searchQuery?: string;
  filters?: import("@/types/filters").GameFilters;
  sortOptions?: SortOptions;
}

export interface SearchControlsProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  sortOptions: SortOptions;
  onSortChange: (options: SortOptions) => void;
  onClearSearch: () => void;
}
