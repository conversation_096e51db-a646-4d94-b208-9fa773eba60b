/* Main styles entry point */
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap");
@import "./components.css";
@import "./animations.css";
@import "./accessibility.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Gaming-inspired color palette as per PRD */
    --background: 250 20% 98%; /* Light gray-50 equivalent */
    --foreground: 222 84% 5%; /* Dark slate-900 equivalent */
    --card: 0 0% 100%;
    --card-foreground: 222 84% 5%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 5%;
    --primary: 262 83% 58%; /* Gaming purple #8B5CF6 (violet-500) */
    --primary-foreground: 210 40% 98%;
    --secondary: 173 58% 39%; /* Complementary teal #14B8A6 (teal-500) */
    --secondary-foreground: 0 0% 100%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%; /* slate-500 */
    --accent: 43 96% 56%; /* Energetic orange #F59E0B (amber-500) for deals */
    --accent-foreground: 222 84% 5%;
    --destructive: 0 84% 60%; /* red-500 */
    --destructive-foreground: 210 40% 98%;
    --success: 142 76% 36%; /* emerald-500 */
    --success-foreground: 210 40% 98%;
    --warning: 43 96% 56%; /* amber-500 */
    --warning-foreground: 222 84% 5%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 262 83% 58%;
    --radius: 0.75rem;

    /* Theme transition properties */
    --theme-transition-duration: 0.3s;
    --theme-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark {
    /* Dark mode optimized for gaming */
    --background: 222 84% 5%; /* Dark slate-900 */
    --foreground: 210 40% 98%; /* Light slate-50 */
    --card: 217 33% 17%; /* slate-800 */
    --card-foreground: 210 40% 98%;
    --popover: 217 33% 17%;
    --popover-foreground: 210 40% 98%;
    --primary: 262 83% 58%; /* Gaming purple #8B5CF6 */
    --primary-foreground: 210 40% 98%;
    --secondary: 173 58% 39%; /* Complementary teal #14B8A6 */
    --secondary-foreground: 0 0% 100%;
    --muted: 217 33% 17%;
    --muted-foreground: 215 16% 47%;
    --accent: 43 96% 56%; /* Energetic orange for deals */
    --accent-foreground: 222 84% 5%;
    --destructive: 0 62% 30%; /* Darker red for dark mode */
    --destructive-foreground: 210 40% 98%;
    --success: 142 76% 36%; /* emerald-500 */
    --success-foreground: 210 40% 98%;
    --warning: 43 96% 56%; /* amber-500 */
    --warning-foreground: 222 84% 5%;
    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 262 83% 58%;
  }

  /* Smooth theme transitions */
  * {
    transition:
      background-color var(--theme-transition-duration) var(--theme-transition-timing),
      border-color var(--theme-transition-duration) var(--theme-transition-timing),
      color var(--theme-transition-duration) var(--theme-transition-timing),
      fill var(--theme-transition-duration) var(--theme-transition-timing),
      stroke var(--theme-transition-duration) var(--theme-transition-timing),
      box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
  }

  /* Disable transitions during theme change to prevent flash */
  .theme-changing * {
    transition: none !important;
  }

  /* Re-enable transitions after theme change */
  .theme-changing.theme-transition-complete * {
    transition:
      background-color var(--theme-transition-duration) var(--theme-transition-timing),
      border-color var(--theme-transition-duration) var(--theme-transition-timing),
      color var(--theme-transition-duration) var(--theme-transition-timing),
      fill var(--theme-transition-duration) var(--theme-transition-timing),
      stroke var(--theme-transition-duration) var(--theme-transition-timing),
      box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    * {
      transition: none !important;
    }
  }

  /* High contrast mode adjustments */
  @media (prefers-contrast: high) {
    :root {
      --border: 0 0% 20%;
      --ring: 262 100% 70%;
    }

    .dark {
      --border: 0 0% 80%;
      --ring: 262 100% 70%;
    }
  }
}

@layer components {
  /* Enhanced Grid View Styles */
  .enhanced-grid-view {
    @apply relative overflow-hidden;
  }

  .enhanced-grid-view.modern-grid {
    @apply bg-gradient-to-br from-background via-background to-muted/30;
  }

  .enhanced-grid-view.classic-grid {
    @apply bg-background;
  }

  .enhanced-grid-view.minimal-grid {
    @apply bg-background;
  }

  .enhanced-grid-view.vibrant-colors {
    --primary: 262 83% 65%;
    --secondary: 173 58% 45%;
    --accent: 43 96% 62%;
  }

  .enhanced-grid-view.muted-colors {
    --primary: 262 30% 45%;
    --secondary: 173 25% 35%;
    --accent: 43 40% 45%;
  }

  /* Premium Game Card Enhancements */
  .transition-premium {
    @apply transition-all duration-300 ease-out;
  }

  .box-art-glow {
    @apply relative;
  }

  .box-art-glow::before {
    content: "";
    @apply absolute -inset-1 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-lg opacity-0 transition-opacity duration-300 -z-10;
  }

  .box-art-glow:hover::before {
    @apply opacity-100;
  }

  .box-art-shimmer {
    @apply relative overflow-hidden;
  }

  .box-art-shimmer::before {
    content: "";
    @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent;
    animation: shimmer 2s infinite;
  }

  .glass-morphism {
    @apply bg-gradient-to-t from-black/40 via-black/20 to-transparent;
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes glow {
    0%,
    100% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  /* Virtual Scrolling Optimizations */
  .enhanced-grid-view [data-testid="virtual-grid"] {
    @apply will-change-scroll;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  .enhanced-grid-view [data-testid="virtual-grid"]::-webkit-scrollbar {
    @apply w-2;
  }

  .enhanced-grid-view [data-testid="virtual-grid"]::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .enhanced-grid-view [data-testid="virtual-grid"]::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  .enhanced-grid-view
    [data-testid="virtual-grid"]::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Responsive Grid Adjustments */
  @media (max-width: 640px) {
    .enhanced-grid-view {
      @apply px-2;
    }
  }

  @media (max-width: 480px) {
    .enhanced-grid-view {
      @apply px-1;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .enhanced-grid-view {
      --primary: 262 100% 70%;
      --secondary: 173 100% 50%;
      --border: 0 0% 50%;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .enhanced-grid-view * {
      @apply transition-none;
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
    }

    .box-art-shimmer::before {
      @apply hidden;
    }

    .animate-glow {
      @apply animate-none;
    }
  }

  /* Focus management for accessibility */
  .enhanced-grid-view:focus-visible {
    @apply outline-2 outline-primary outline-offset-2;
  }

  /* Loading skeleton styles */
  .grid-skeleton {
    @apply animate-pulse bg-gradient-to-r from-muted via-muted/60 to-muted;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Roboto", system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    min-height: 100vh;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.8);
  }
}

@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-stagger-fade {
    animation: staggerFade 0.8s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Enhanced box art effects */
  .box-art-shimmer {
    position: relative;
    overflow: hidden;
  }

  .box-art-shimmer::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: shimmer 2s infinite;
  }

  .box-art-glow {
    position: relative;
  }

  .box-art-glow::after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(
      45deg,
      hsl(var(--primary) / 0.1),
      hsl(var(--secondary) / 0.1),
      hsl(var(--accent) / 0.1)
    );
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .box-art-glow:hover::after {
    opacity: 1;
  }

  /* Premium glass morphism */
  .glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced transitions */
  .transition-premium {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* Premium card effects */
  .card-hover-lift {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .card-hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
  }

  .card-parallax {
    transform-style: preserve-3d;
    transition: transform 0.1s ease-out;
  }

  .card-glow-border {
    position: relative;
  }

  .card-glow-border::before {
    content: "";
    position: absolute;
    inset: -1px;
    padding: 1px;
    background: linear-gradient(
      45deg,
      hsl(var(--primary) / 0.3),
      hsl(var(--secondary) / 0.3),
      hsl(var(--accent) / 0.3)
    );
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .card-glow-border:hover::before {
    opacity: 1;
  }

  /* Enhanced image effects */
  .image-zoom-effect {
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .image-zoom-effect:hover {
    transform: scale(1.1);
  }

  /* Staggered animation for card grids */
  .animate-stagger-in {
    animation: staggerIn 0.6s ease-out both;
  }

  .animate-stagger-in:nth-child(1) {
    animation-delay: 0ms;
  }
  .animate-stagger-in:nth-child(2) {
    animation-delay: 100ms;
  }
  .animate-stagger-in:nth-child(3) {
    animation-delay: 200ms;
  }
  .animate-stagger-in:nth-child(4) {
    animation-delay: 300ms;
  }
  .animate-stagger-in:nth-child(5) {
    animation-delay: 400ms;
  }
  .animate-stagger-in:nth-child(6) {
    animation-delay: 500ms;
  }

  /* Premium card interaction effects */
  .card-premium-hover {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .card-premium-hover:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px hsl(var(--primary) / 0.1), 0 0 20px hsl(var(--primary) / 0.1);
  }

  /* Advanced parallax effect */
  .card-3d-transform {
    transform-style: preserve-3d;
    transition: transform 0.1s ease-out;
  }

  /* Premium sparkle animation */
  .animate-sparkle {
    animation: sparkle 1.5s ease-in-out infinite;
  }

  /* Enhanced glow effects */
  .premium-glow {
    position: relative;
  }

  .premium-glow::before {
    content: "";
    position: absolute;
    inset: -2px;
    background: linear-gradient(
      45deg,
      hsl(var(--primary) / 0.2),
      hsl(var(--secondary) / 0.2),
      hsl(var(--accent) / 0.2),
      hsl(var(--primary) / 0.2)
    );
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
    animation: premiumGlowRotate 3s linear infinite;
  }

  .premium-glow:hover::before {
    opacity: 1;
  }

  /* Interactive mouse tracking */
  .mouse-track-gradient {
    background: radial-gradient(
      circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      hsl(var(--primary) / 0.1) 0%,
      hsl(var(--secondary) / 0.05) 50%,
      transparent 100%
    );
  }

  /* Enhanced image effects */
  .image-premium-hover {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .image-premium-hover:hover {
    transform: scale(1.1) rotateY(5deg);
    filter: brightness(1.1) saturate(1.2) contrast(1.05);
  }

  /* Premium loading states */
  .premium-shimmer {
    background: linear-gradient(
      90deg,
      hsl(var(--muted)) 0%,
      hsl(var(--muted) / 0.8) 25%,
      hsl(var(--primary) / 0.1) 50%,
      hsl(var(--muted) / 0.8) 75%,
      hsl(var(--muted)) 100%
    );
    background-size: 200% 100%;
    animation: premiumShimmer 2s ease-in-out infinite;
  }

  /* Card press effect */
  .card-press-effect {
    transition: transform 0.1s ease-out;
  }

  .card-press-effect:active {
    transform: scale(0.98) translateY(2px);
  }
}
