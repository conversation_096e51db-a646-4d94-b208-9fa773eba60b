import { Platform } from "../../types";

/**
 * Enhanced platform mappings for IGDB and TheGamesDB
 * This file centralizes all platform mappings to ensure consistency
 */

// IGDB Platform ID mapping for filering
export const igdbPlatformIds: Record<Platform, number> = {
  PC: 6,
  "PlayStation 5": 167,
  "PlayStation 4": 48,
  "PlayStation 3": 9,
  "PlayStation 2": 8,
  PlayStation: 7,
  "PlayStation Vita": 46,
  "Xbox Series X/S": 169,
  "Xbox One": 49,
  "Xbox 360": 12,
  Xbox: 11,
  "Nintendo Switch": 130,
  "Nintendo 3DS": 37,
  "Nintendo DS": 20,
  "Wii U": 41,
  Wii: 5,
  GameCube: 21,
  "Nintendo 64": 4,
  iOS: 39,
  Android: 34,
  "Steam Deck": 170,
  Mac: 14,
  Linux: 3,
  "3DO": 50,
  Arcade: 52,
  "Sega Saturn": 32,
  Mobile: 34,
};

// Enhanced platform mapping for IGDB names to our Platform types
export const igdbPlatformMap: Record<string, Platform> = {
  "PC (Microsoft Windows)": "PC",
  Mac: "Mac",
  Linux: "Linux",
  "PlayStation 5": "PlayStation 5",
  "PlayStation 4": "PlayStation 4",
  "PlayStation 3": "PlayStation 3",
  "PlayStation 2": "PlayStation 2",
  PlayStation: "PlayStation",
  "PlayStation Vita": "PlayStation Vita",
  "Xbox Series X|S": "Xbox Series X/S",
  "Xbox One": "Xbox One",
  "Xbox 360": "Xbox 360",
  Xbox: "Xbox",
  "Nintendo Switch": "Nintendo Switch",
  "Nintendo 3DS": "Nintendo 3DS",
  "Nintendo DS": "Nintendo DS",
  "Wii U": "Wii U",
  Wii: "Wii",
  "Nintendo GameCube": "GameCube",
  "Nintendo 64": "Nintendo 64",
  iOS: "iOS",
  Android: "Android",
  "Steam Deck": "Steam Deck",
  Arcade: "Arcade",
  "Sega Saturn": "Sega Saturn",
  "Web browser": "PC",
  "Legacy Mobile Device": "Mobile",
  "OnLive Game System": "PC",
};

// TheGamesDB platform ID to our Platform type mapping
export const tgdbPlatformMap: Record<number, Platform> = {
  1: "PC", // PC
  4: "PC", // PC
  3: "Nintendo 64", // Nintendo 64
  7: "Nintendo Switch", // Nintendo Switch
  8: "PlayStation 4", // PlayStation 4
  9: "PlayStation 3", // PlayStation 3
  10: "PlayStation 2", // PlayStation 2
  11: "PlayStation", // PlayStation
  12: "Xbox", // Xbox
  13: "PC", // PC
  14: "Xbox 360", // Xbox 360
  15: "Xbox One", // Xbox One
  16: "Steam Deck", // Steam Deck
  18: "Nintendo 3DS", // Nintendo 3DS
  20: "Nintendo DS", // Nintendo DS
  21: "GameCube", // GameCube
  23: "Wii", // Wii
  25: "3DO", // 3DO
  26: "iOS", // iOS
  27: "Android", // Android
  37: "PC", // PC (additional mapping)
  38: "Wii U", // Wii U
  39: "iOS", // iOS
  4916: "PlayStation 5", // PlayStation 5
  4919: "PC", // PC (Steam/Windows)
  4920: "Xbox Series X/S", // Xbox Series X/S
  4938: "PC", // PC (additional mapping)
  4950: "Xbox Series X/S", // Xbox Series X/S (alt)
  46: "PlayStation Vita", // PlayStation Vita
};
