import React, { useState } from 'react';
import { Search, Download, <PERSON>rkles, Plus, Loader2, CheckCircle, Settings, RefreshCw } from 'lucide-react';
import { Input } from '@/components/ui/base/input';
import { Button } from '@/components/ui/base/button';
import { Card } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/base/select';
import { Checkbox } from '@/components/ui/base/checkbox';
import { Label } from '@/components/ui/base/label';
import { useState as useToggle } from 'react';
import { cn } from '@/lib/utils';
import { toast } from 'react-hot-toast';
import { generateThumbnailPlaceholder } from '@/lib/utils/imageUtils';

interface BoxArtResult {
  id: string;
  title: string;
  url: string;
  source: string;
  quality: 'high' | 'medium' | 'low';
  isOriginal: boolean;
}

const getSourceColor = (source: string) => {
  switch (source.toLowerCase()) {
    case 'igdb': return 'bg-blue-500';
    case 'steamgriddb': return 'bg-green-500';
    case 'ai': return 'bg-pink-500';
    case 'web': return 'bg-teal-500';
    default: return 'bg-gray-500';
  }
};

const getSourceLabel = (source: string) => {
  switch (source.toLowerCase()) {
    case 'igdb': return 'IGDB';
    case 'steamgriddb': return 'SteamGrid';
    case 'ai': return 'AI';
    case 'web': return 'Web';
    default: return source.toUpperCase();
  }
};

const getSourceTextColor = (source: string) => {
  switch (source.toLowerCase()) {
    case 'igdb': return 'text-blue-600';
    case 'steamgriddb': return 'text-green-600';
    case 'ai': return 'text-pink-600';
    case 'web': return 'text-teal-600';
    default: return 'text-gray-600';
  }
};

interface BoxArtCardProps {
  result: BoxArtResult;
  onDownload: (url: string, title: string) => void;
  onAddToCollection?: (url: string, title: string) => Promise<void>;
  isUploading?: boolean;
  isUploaded?: boolean;
}

const BoxArtCard: React.FC<BoxArtCardProps> = ({ 
  result, 
  onDownload, 
  onAddToCollection, 
  isUploading = false, 
  isUploaded = false 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [imgSrc, setImgSrc] = useState(result.url);
  const [isUploadingLocal, setIsUploadingLocal] = useState(false);
  
  const handleError = () => {
    setImgSrc(generateThumbnailPlaceholder('Image Not Available'));
    setIsLoading(false);
  };

  const handleAddToCollection = async () => {
    if (onAddToCollection && !isUploadingLocal && !isUploaded) {
      setIsUploadingLocal(true);
      try {
        await onAddToCollection(result.url, result.title);
        toast.success('Added to collection!');
      } catch (error) {
        toast.error('Failed to add to collection');
        console.error('Upload error:', error);
      } finally {
        setIsUploadingLocal(false);
      }
    }
  };

  const getQualityColor = (quality: string) => {
    switch (quality.toLowerCase()) {
      case 'high': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Card className="group h-full w-full overflow-hidden transition-all duration-200 hover:shadow-lg">
      <div className="relative aspect-[2/3] w-full overflow-hidden bg-muted">
        {/* Loading Skeleton */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-muted/25 to-muted/50">
            <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-primary" />
          </div>
        )}

        {/* Image */}
        <img
          src={imgSrc}
          alt={result.title}
          className={cn(
            'h-full w-full object-cover transition-transform duration-300',
            isLoading ? 'opacity-0' : 'opacity-100 group-hover:scale-105'
          )}
          onLoad={() => setIsLoading(false)}
          onError={handleError}
          loading="lazy"
          decoding="async"
        />

        {/* Quality and Source Badges */}
        <div className="absolute left-2 top-2 z-10 flex flex-col gap-1">
          <Badge 
            className={cn(
              'text-xs font-medium text-white shadow-md',
              getQualityColor(result.quality)
            )}
          >
            {result.quality.toUpperCase()}
          </Badge>
          <Badge 
            className={cn(
              'text-xs font-medium text-white shadow-md',
              getSourceColor(result.source)
            )}
          >
            {getSourceLabel(result.source)}
          </Badge>
        </div>

        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 transition-opacity group-hover:opacity-100">
          <div className="absolute bottom-0 left-0 right-0 p-3 space-y-2">
            {/* Add to Collection Button */}
            {onAddToCollection && (
              <Button
                size="sm"
                variant="default"
                className={cn(
                  "w-full font-medium transition-all",
                  isUploaded 
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : "bg-primary hover:bg-primary/90 text-white"
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  handleAddToCollection();
                }}
                disabled={isUploadingLocal || isUploading || isUploaded}
              >
                {isUploadingLocal || isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : isUploaded ? (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Added
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    Add to Collection
                  </>
                )}
              </Button>
            )}
            
            {/* Download Button */}
            <Button
              size="sm"
              variant="outline"
              className="w-full bg-white/90 font-medium text-foreground hover:bg-white border-white/50"
              onClick={(e) => {
                e.stopPropagation();
                onDownload(result.url, result.title);
              }}
            >
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
          </div>
        </div>
      </div>

      {/* Card Footer */}
      <div className="p-3">
        <h3 className="line-clamp-2 h-10 text-sm font-medium leading-tight text-foreground">
          {result.title}
        </h3>
        <div className="mt-2 flex items-center justify-between">
          <span className="text-xs text-muted-foreground">{result.source}</span>
          {result.isOriginal && (
            <Badge variant="secondary" className="text-xs">
              <Sparkles className="mr-1 h-3 w-3" />
              Original
            </Badge>
          )}
        </div>
      </div>
    </Card>
  );
};

export interface SearchFilters {
  sources: ('igdb' | 'steamgriddb' | 'ai')[];
  limit: number;
  searchMode: 'combined' | 'individual';
  qualityFilter: 'high' | 'medium' | 'low' | 'all';
}

interface BoxArtSearchProps {
  onSearch: (query: string, filters?: SearchFilters) => Promise<BoxArtResult[]>;
  onAddToCollection?: (url: string, title: string) => Promise<void>;
  className?: string;
  initialSearchQuery?: string;
  showAdvancedControls?: boolean;
}

export const BoxArtSearch: React.FC<BoxArtSearchProps> = ({ 
  onSearch,
  onAddToCollection,
  className = '',
  initialSearchQuery = '',
  showAdvancedControls = false
}) => {
  const [searchQuery, setSearchQuery] = useState(initialSearchQuery);
  const [results, setResults] = useState<BoxArtResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadingResults, setUploadingResults] = useState<Set<string>>(new Set());
  const [uploadedResults, setUploadedResults] = useState<Set<string>>(new Set());
  
  // Search filter state
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    sources: ['igdb', 'steamgriddb', 'ai'],
    limit: 100,
    searchMode: 'combined',
    qualityFilter: 'all'
  });
  
  const [canLoadMore, setCanLoadMore] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  const handleSearchInternal = React.useCallback(async (query: string) => {
    // Prevent multiple simultaneous searches
    if (isLoading) {
      console.log('🚫 Search already in progress, skipping duplicate request');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResults([]); // Clear previous results

    try {
      console.log('🔍 Starting box art search for:', query, 'with filters:', searchFilters);
      const searchResults = await onSearch(query, searchFilters);
      console.log('✅ Box art search completed, found', searchResults.length, 'results');
      setResults(searchResults);
      
      // Estimate if more results are available (basic heuristic)
      setCanLoadMore(searchResults.length === searchFilters.limit && searchFilters.limit < 200);
    } catch (err) {
      setError('Failed to fetch box art. Please try again.');
      console.error('❌ Search error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [onSearch, isLoading]);

  // Auto-search if initial query is provided - only run once when component mounts
  React.useEffect(() => {
    if (initialSearchQuery.trim()) {
      handleSearchInternal(initialSearchQuery);
    }
  }, [initialSearchQuery]); // Remove handleSearchInternal from dependencies to prevent infinite loop


  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;
    await handleSearchInternal(searchQuery);
  };

  const handleLoadMore = async () => {
    if (!canLoadMore || isLoading) return;
    
    // Increase limit and search again
    const newFilters = { ...searchFilters, limit: searchFilters.limit + 50 };
    setSearchFilters(newFilters);
    
    try {
      setIsLoading(true);
      console.log('📥 Loading more results with limit:', newFilters.limit);
      const searchResults = await onSearch(searchQuery, newFilters);
      setResults(searchResults);
      setCanLoadMore(searchResults.length === newFilters.limit && newFilters.limit < 200);
    } catch (err) {
      toast.error('Failed to load more results');
      console.error('❌ Load more error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddToCollection = async (url: string, title: string) => {
    if (!onAddToCollection) return;
    
    const resultId = `${url}-${title}`;
    setUploadingResults(prev => new Set(prev).add(resultId));
    
    try {
      await onAddToCollection(url, title);
      setUploadedResults(prev => new Set(prev).add(resultId));
    } finally {
      setUploadingResults(prev => {
        const newSet = new Set(prev);
        newSet.delete(resultId);
        return newSet;
      });
    }
  };

  const handleDownload = (url: string, title: string) => {
    try {
      const link = document.createElement('a');
      link.href = url;
      link.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('Download started!');
    } catch (err) {
      toast.error('Failed to download image');
      console.error('Download error:', err);
    }
  };

  return (
    <div className={cn("space-y-6 relative", className)}>
      <div className="sticky top-4 z-40 bg-background p-2 -mx-2 rounded-lg">
        <form onSubmit={handleSearch} className="flex gap-2 max-w-4xl mx-auto">
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search for game box art..."
            disabled={isLoading}
            className="flex-1 border-2 border-primary/20 focus-visible:ring-2 focus-visible:ring-primary/50"
          />
          <Button 
            type="submit" 
            disabled={isLoading || !searchQuery.trim()}
            className="shrink-0 px-6"
          >
            <Search className="h-4 w-4 mr-2" />
            {isLoading ? 'Searching...' : 'Search'}
          </Button>
        </form>

        {/* Advanced Search Controls */}
        {showAdvancedControls && (
          <div className="mt-4">
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
            >
              <Settings className="h-4 w-4 mr-2" />
              Advanced Search Options
            </Button>
            {showAdvancedOptions && (
              <div className="space-y-4 mt-4 p-4 border rounded-lg bg-muted/30">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Source Selection */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Search Sources</Label>
                  <div className="space-y-2">
                    {[
                      { value: 'igdb', label: 'IGDB Official', color: 'text-blue-600' },
                      { value: 'steamgriddb', label: 'SteamGridDB', color: 'text-green-600' },
                      { value: 'ai', label: 'AI Generated', color: 'text-pink-600' }
                    ].map((source) => (
                      <div key={source.value} className="flex items-center space-x-2">
                        <Checkbox 
                          id={source.value}
                          checked={searchFilters.sources.includes(source.value as 'igdb' | 'steamgriddb' | 'ai')}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSearchFilters(prev => ({
                                ...prev,
                                sources: [...prev.sources, source.value as 'igdb' | 'steamgriddb' | 'ai']
                              }));
                            } else {
                              setSearchFilters(prev => ({
                                ...prev,
                                sources: prev.sources.filter(s => s !== source.value)
                              }));
                            }
                          }}
                        />
                        <Label htmlFor={source.value} className={cn("text-sm", source.color)}>
                          {source.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Search Mode */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Search Mode</Label>
                  <Select 
                    value={searchFilters.searchMode} 
                    onValueChange={(value: 'combined' | 'individual') => 
                      setSearchFilters(prev => ({ ...prev, searchMode: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="combined">Combined Search</SelectItem>
                      <SelectItem value="individual">Individual APIs</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Result Limit */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Result Limit</Label>
                  <Select 
                    value={searchFilters.limit.toString()} 
                    onValueChange={(value) => 
                      setSearchFilters(prev => ({ ...prev, limit: parseInt(value) }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="50">50 Results</SelectItem>
                      <SelectItem value="100">100 Results</SelectItem>
                      <SelectItem value="150">150 Results</SelectItem>
                      <SelectItem value="200">200 Results</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Quick Action Buttons */}
              <div className="flex items-center gap-2 pt-2 border-t">
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleSearchInternal(searchQuery)}
                  disabled={isLoading}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Apply Filters
                </Button>
                <Badge variant="secondary" className="text-xs">
                  {searchFilters.sources.length} sources • {searchFilters.limit} limit
                </Badge>
              </div>
              </div>
            )}
          </div>
        )}
      </div>

      {error && (
        <div className="p-4 text-red-600 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800">
          {error}
        </div>
      )}

      {isLoading && (
        <div className="p-4 bg-muted/50 rounded-md border border-muted">
          <div className="flex items-center gap-3">
            <Loader2 className="h-5 w-5 animate-spin text-primary" />
            <div className="flex-1">
              <p className="text-sm font-medium">Searching multiple sources...</p>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className="text-xs">IGDB Official</Badge>
                <Badge variant="outline" className="text-xs">SteamGridDB</Badge>
                <Badge variant="outline" className="text-xs">AI Enhanced</Badge>
              </div>
            </div>
          </div>
        </div>
      )}

      {isLoading ? (
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="group relative overflow-hidden rounded-lg">
              <div className="aspect-[2/3] w-full animate-pulse bg-muted/50" />
              <div className="mt-2 space-y-2 p-2">
                <div className="h-4 w-3/4 rounded bg-muted/50" />
                <div className="flex justify-between">
                  <div className="h-3 w-1/3 rounded bg-muted/30" />
                  <div className="h-3 w-1/4 rounded bg-muted/30" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : results.length > 0 ? (
        <>
          {/* Results Summary */}
          <div className="p-4 bg-muted/30 rounded-md border border-muted">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium">Search Results ({results.length})</h3>
              <div className="flex items-center gap-2">
                {['igdb', 'steamgriddb', 'ai', 'web'].map(source => {
                  const count = results.filter(r => r.source === source).length;
                  if (count === 0) return null;
                  return (
                    <Badge 
                      key={source} 
                      variant="secondary" 
                      className={cn('text-xs', getSourceTextColor(source))}
                    >
                      {getSourceLabel(source)}: {count}
                    </Badge>
                  );
                })}
              </div>
            </div>
            <div className="text-xs text-muted-foreground">
              Results from multiple sources • Sorted by quality and authenticity
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7">
          {results.map((result) => {
            const resultId = `${result.url}-${result.title}`;
            return (
              <BoxArtCard 
                key={result.id}
                result={result} 
                onDownload={handleDownload}
                onAddToCollection={onAddToCollection ? handleAddToCollection : undefined}
                isUploading={uploadingResults.has(resultId)}
                isUploaded={uploadedResults.has(resultId)}
              />
            );
          })}
          </div>

          {/* Load More Button */}
          {canLoadMore && (
            <div className="text-center pt-6">
              <Button 
                onClick={handleLoadMore}
                disabled={isLoading}
                variant="outline"
                size="lg"
                className="min-w-[200px]"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading More...
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    Load More Results
                  </>
                )}
              </Button>
              <p className="text-xs text-muted-foreground mt-2">
                Showing {results.length} results • Load up to {searchFilters.limit + 50} total
              </p>
            </div>
          )}
        </>
      ) : searchQuery ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No results found for "{searchQuery}"</p>
          <p className="text-sm text-muted-foreground mt-2">Try a different search term</p>
        </div>
      ) : (
        <div className="text-center py-12">
          <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium">Search for game box art</h3>
          <p className="text-muted-foreground mt-1">Find high-quality box art for your game collection</p>
        </div>
      )}
    </div>
  );
};

export default BoxArtSearch;
