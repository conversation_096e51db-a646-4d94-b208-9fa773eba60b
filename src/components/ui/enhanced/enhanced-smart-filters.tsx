import React, { memo, useMemo, useCallback, useState } from 'react';
import { UserGameWithDetails } from '@/types/database';
import { SmartFilterSettings, FilterCondition, FilterPreset } from '@/types/library';
import { cn } from '@/lib/utils';
import { 
  Filter,
  Plus,
  X,
  Search,
  Star,
  Calendar,
  Clock,
  Gamepad2,
  Trophy,
  Target,
  Sparkles,
  Save,
  Trash2,
  Eye,
  EyeOff,
  RotateCcw,
  Zap,
  CheckCircle2,
  AlertCircle
} from '@/lib/icons';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Input } from '@/components/ui/base/input';
import { Separator } from '@/components/ui/base/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/base/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/base/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/base/command';

// Filter types and configurations
const FILTER_TYPES = {
  status: {
    id: 'status',
    name: 'Status',
    icon: Target,
    type: 'select',
    options: ['playing', 'completed', 'backlog', 'wishlist', 'dropped'],
    description: 'Filter by game status'
  },
  platform: {
    id: 'platform',
    name: 'Platform',
    icon: Gamepad2,
    type: 'multiselect',
    description: 'Filter by gaming platform'
  },
  genre: {
    id: 'genre',
    name: 'Genre',
    icon: Star,
    type: 'multiselect',
    description: 'Filter by game genre'
  },
  rating: {
    id: 'rating',
    name: 'Rating',
    icon: Star,
    type: 'range',
    min: 0,
    max: 10,
    description: 'Filter by personal rating'
  },
  metacritic: {
    id: 'metacritic',
    name: 'Metacritic Score',
    icon: Trophy,
    type: 'range',
    min: 0,
    max: 100,
    description: 'Filter by Metacritic score'
  },
  releaseYear: {
    id: 'releaseYear',
    name: 'Release Year',
    icon: Calendar,
    type: 'range',
    min: 1970,
    max: new Date().getFullYear() + 2,
    description: 'Filter by release year'
  },
  hoursPlayed: {
    id: 'hoursPlayed',
    name: 'Hours Played',
    icon: Clock,
    type: 'range',
    min: 0,
    max: 1000,
    description: 'Filter by playtime hours'
  },
  developer: {
    id: 'developer',
    name: 'Developer',
    icon: Gamepad2,
    type: 'multiselect',
    description: 'Filter by game developer'
  },
  dateAdded: {
    id: 'dateAdded',
    name: 'Date Added',
    icon: Calendar,
    type: 'daterange',
    description: 'Filter by date added to library'
  }
} as const;

// Smart filter presets
const SMART_PRESETS: FilterPreset[] = [
  {
    id: 'recently-added',
    name: 'Recently Added',
    description: 'Games added in the last 30 days',
    icon: 'plus',
    color: 'blue',
    conditions: [
      {
        field: 'dateAdded',
        operator: 'gte',
        value: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]
  },
  {
    id: 'high-rated',
    name: 'Highly Rated',
    description: 'Personal rating of 8+ or Metacritic 85+',
    icon: 'star',
    color: 'yellow',
    conditions: [
      {
        field: 'rating',
        operator: 'gte',
        value: 8
      }
    ],
    logic: 'OR'
  },
  {
    id: 'quick-plays',
    name: 'Quick Plays',
    description: 'Games with less than 5 hours played',
    icon: 'zap',
    color: 'green',
    conditions: [
      {
        field: 'hoursPlayed',
        operator: 'lt',
        value: 5
      }
    ]
  },
  {
    id: 'time-sinks',
    name: 'Time Sinks',
    description: 'Games with 50+ hours played',
    icon: 'clock',
    color: 'purple',
    conditions: [
      {
        field: 'hoursPlayed',
        operator: 'gte',
        value: 50
      }
    ]
  },
  {
    id: 'backlog-priority',
    name: 'Backlog Priority',
    description: 'Backlog games with high ratings',
    icon: 'target',
    color: 'orange',
    conditions: [
      {
        field: 'status',
        operator: 'eq',
        value: 'backlog'
      },
      {
        field: 'metacritic',
        operator: 'gte',
        value: 80
      }
    ],
    logic: 'AND'
  },
  {
    id: 'completion-candidates',
    name: 'Completion Candidates',
    description: 'Started games that might be completed',
    icon: 'trophy',
    color: 'indigo',
    conditions: [
      {
        field: 'status',
        operator: 'eq',
        value: 'playing'
      },
      {
        field: 'hoursPlayed',
        operator: 'gte',
        value: 2
      }
    ],
    logic: 'AND'
  }
];

interface EnhancedSmartFiltersProps {
  games: UserGameWithDetails[];
  settings: SmartFilterSettings;
  onSettingsChange: (settings: Partial<SmartFilterSettings>) => void;
  onFiltersApply: (conditions: FilterCondition[]) => void;
  className?: string;
}

interface FilterConditionBuilderProps {
  condition: FilterCondition;
  availableFields: typeof FILTER_TYPES;
  fieldOptions: Record<string, string[]>;
  onChange: (condition: FilterCondition) => void;
  onRemove: () => void;
}

interface SmartSuggestionProps {
  games: UserGameWithDetails[];
  onApplySuggestion: (conditions: FilterCondition[]) => void;
}

// Filter condition builder component
const FilterConditionBuilder = memo<FilterConditionBuilderProps>(({
  condition,
  availableFields,
  fieldOptions,
  onChange,
  onRemove
}) => {
  const fieldConfig = availableFields[condition.field as keyof typeof availableFields];
  const Icon = fieldConfig?.icon || Filter;

  const handleFieldChange = useCallback((field: string) => {
    const newFieldConfig = availableFields[field as keyof typeof availableFields];
    onChange({
      field,
      operator: newFieldConfig?.type === 'range' ? 'gte' : 'eq',
      value: newFieldConfig?.type === 'range' ? newFieldConfig.min : ''
    });
  }, [availableFields, onChange]);

  const handleOperatorChange = useCallback((operator: string) => {
    onChange({ ...condition, operator });
  }, [condition, onChange]);

  const handleValueChange = useCallback((value: string | number | string[]) => {
    onChange({ ...condition, value });
  }, [condition, onChange]);

  const renderValueInput = () => {
    if (!fieldConfig) return null;

    switch (fieldConfig.type) {
      case 'select':
        return (
          <Select value={condition.value} onValueChange={handleValueChange}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Select..." />
            </SelectTrigger>
            <SelectContent>
              {fieldConfig.options?.map(option => (
                <SelectItem key={option} value={option}>
                  {option.charAt(0).toUpperCase() + option.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'multiselect': {
        const options = fieldOptions[condition.field] || [];
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-32 justify-start">
                {Array.isArray(condition.value) && condition.value.length > 0
                  ? `${condition.value.length} selected`
                  : 'Select...'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-0">
              <Command>
                <CommandInput placeholder="Search..." />
                <CommandList>
                  <CommandEmpty>No options found.</CommandEmpty>
                  <CommandGroup>
                    {options.slice(0, 10).map(option => (
                      <CommandItem
                        key={option}
                        onSelect={() => {
                          const currentValues = Array.isArray(condition.value) ? condition.value : [];
                          const newValues = currentValues.includes(option)
                            ? currentValues.filter(v => v !== option)
                            : [...currentValues, option];
                          handleValueChange(newValues);
                        }}
                      >
                        <CheckCircle2
                          className={cn(
                            'mr-2 h-4 w-4',
                            Array.isArray(condition.value) && condition.value.includes(option)
                              ? 'opacity-100'
                              : 'opacity-0'
                          )}
                        />
                        {option}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        );
      }

      case 'range':
        return (
          <div className="flex items-center gap-2">
            <Input
              type="number"
              value={condition.value || ''}
              onChange={(e) => handleValueChange(Number(e.target.value))}
              className="w-20"
              min={fieldConfig.min}
              max={fieldConfig.max}
            />
            {fieldConfig.max && (
              <span className="text-xs text-muted-foreground">
                /{fieldConfig.max}
              </span>
            )}
          </div>
        );

      case 'daterange':
        return (
          <Input
            type="date"
            value={condition.value ? new Date(condition.value).toISOString().split('T')[0] : ''}
            onChange={(e) => handleValueChange(e.target.value ? new Date(e.target.value).toISOString() : '')}
            className="w-36"
          />
        );

      default:
        return (
          <Input
            value={condition.value || ''}
            onChange={(e) => handleValueChange(e.target.value)}
            className="w-32"
            placeholder="Enter value..."
          />
        );
    }
  };

  const getOperatorOptions = () => {
    if (!fieldConfig) return [];

    switch (fieldConfig.type) {
      case 'select':
      case 'multiselect':
        return [
          { value: 'eq', label: 'is' },
          { value: 'neq', label: 'is not' },
          { value: 'in', label: 'includes' },
          { value: 'nin', label: 'excludes' }
        ];
      case 'range':
        return [
          { value: 'eq', label: '=' },
          { value: 'neq', label: '≠' },
          { value: 'gt', label: '>' },
          { value: 'gte', label: '≥' },
          { value: 'lt', label: '<' },
          { value: 'lte', label: '≤' }
        ];
      case 'daterange':
        return [
          { value: 'eq', label: 'on' },
          { value: 'gt', label: 'after' },
          { value: 'lt', label: 'before' },
          { value: 'gte', label: 'on or after' },
          { value: 'lte', label: 'on or before' }
        ];
      default:
        return [
          { value: 'eq', label: 'equals' },
          { value: 'neq', label: 'not equals' },
          { value: 'contains', label: 'contains' },
          { value: 'starts', label: 'starts with' }
        ];
    }
  };

  return (
    <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
      {/* Field selector */}
      <div className="flex items-center gap-2">
        <Icon size={16} className="text-muted-foreground" />
        <Select value={condition.field} onValueChange={handleFieldChange}>
          <SelectTrigger className="w-36">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(availableFields).map(([key, config]) => {
              const FieldIcon = config.icon;
              return (
                <SelectItem key={key} value={key}>
                  <div className="flex items-center gap-2">
                    <FieldIcon size={14} />
                    {config.name}
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>

      {/* Operator selector */}
      <Select value={condition.operator} onValueChange={handleOperatorChange}>
        <SelectTrigger className="w-24">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {getOperatorOptions().map(op => (
            <SelectItem key={op.value} value={op.value}>
              {op.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Value input */}
      {renderValueInput()}

      {/* Remove button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={onRemove}
        className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
      >
        <X size={14} />
      </Button>
    </div>
  );
});

FilterConditionBuilder.displayName = 'FilterConditionBuilder';

// Smart suggestions component
const SmartSuggestions = memo<SmartSuggestionProps>(({ games, onApplySuggestion }) => {
  const suggestions = useMemo(() => {
    const stats = {
      totalGames: games.length,
      recentlyAdded: games.filter(g => 
        new Date(g.date_added) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      ).length,
      highRated: games.filter(g => 
        (g.personal_rating && g.personal_rating >= 8) || 
        (g.game?.metacritic_score && g.game.metacritic_score >= 85)
      ).length,
      playing: games.filter(g => g.status === 'playing').length,
      backlog: games.filter(g => g.status === 'backlog').length,
      completed: games.filter(g => g.status === 'completed').length
    };

    const suggestions = [];

    // Recent additions
    if (stats.recentlyAdded > 0) {
      suggestions.push({
        title: `${stats.recentlyAdded} Recent Addition${stats.recentlyAdded > 1 ? 's' : ''}`,
        description: 'Games added this week',
        icon: Plus,
        color: 'blue',
        conditions: [{
          field: 'dateAdded',
          operator: 'gte',
          value: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
        }]
      });
    }

    // High-rated games
    if (stats.highRated > 0) {
      suggestions.push({
        title: `${stats.highRated} Highly Rated`,
        description: 'Your top-rated games',
        icon: Star,
        color: 'yellow',
        conditions: [{
          field: 'rating',
          operator: 'gte',
          value: 8
        }]
      });
    }

    // Currently playing
    if (stats.playing > 0) {
      suggestions.push({
        title: `${stats.playing} Currently Playing`,
        description: 'Games in progress',
        icon: Gamepad2,
        color: 'green',
        conditions: [{
          field: 'status',
          operator: 'eq',
          value: 'playing'
        }]
      });
    }

    // Backlog priority
    if (stats.backlog > 5) {
      suggestions.push({
        title: 'Backlog Priority',
        description: 'Highly-rated backlog games',
        icon: Target,
        color: 'orange',
        conditions: [
          {
            field: 'status',
            operator: 'eq',
            value: 'backlog'
          },
          {
            field: 'metacritic',
            operator: 'gte',
            value: 80
          }
        ]
      });
    }

    return suggestions;
  }, [games]);

  if (suggestions.length === 0) return null;

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Sparkles size={16} className="text-primary" />
        <h4 className="font-medium">Smart Suggestions</h4>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
        {suggestions.map((suggestion, index) => {
          const Icon = suggestion.icon;
          return (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={() => onApplySuggestion(suggestion.conditions)}
              className="justify-start h-auto p-3"
            >
              <div className="flex items-start gap-3">
                <Icon size={16} className={`text-${suggestion.color}-500 mt-0.5`} />
                <div className="text-left flex-1">
                  <div className="font-medium text-sm">{suggestion.title}</div>
                  <div className="text-xs text-muted-foreground">{suggestion.description}</div>
                </div>
              </div>
            </Button>
          );
        })}
      </div>
    </div>
  );
});

SmartSuggestions.displayName = 'SmartSuggestions';

export const EnhancedSmartFilters = memo<EnhancedSmartFiltersProps>(({
  games,
  settings,
  onFiltersApply,
  className
}) => {
  const [activeConditions, setActiveConditions] = useState<FilterCondition[]>([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [savedPresets, setSavedPresets] = useState<FilterPreset[]>(SMART_PRESETS);
  const [searchQuery, setSearchQuery] = useState('');

  // Extract field options from games
  const fieldOptions = useMemo(() => {
    const options: Record<string, string[]> = {};
    
    // Extract platforms
    const platforms = new Set<string>();
    games.forEach(game => {
      game.game?.platforms?.forEach(platform => platforms.add(platform));
    });
    options.platform = Array.from(platforms).sort();

    // Extract genres
    const genres = new Set<string>();
    games.forEach(game => {
      game.game?.genres?.forEach(genre => genres.add(genre));
    });
    options.genre = Array.from(genres).sort();

    // Extract developers
    const developers = new Set<string>();
    games.forEach(game => {
      if (game.game?.developer) developers.add(game.game.developer);
    });
    options.developer = Array.from(developers).sort();

    return options;
  }, [games]);

  // Handle condition changes
  const handleConditionChange = useCallback((index: number, condition: FilterCondition) => {
    const newConditions = [...activeConditions];
    newConditions[index] = condition;
    setActiveConditions(newConditions);
  }, [activeConditions]);

  const handleAddCondition = useCallback(() => {
    setActiveConditions([
      ...activeConditions,
      {
        field: 'status',
        operator: 'eq',
        value: ''
      }
    ]);
  }, [activeConditions]);

  const handleRemoveCondition = useCallback((index: number) => {
    const newConditions = activeConditions.filter((_, i) => i !== index);
    setActiveConditions(newConditions);
  }, [activeConditions]);

  const handleClearAll = useCallback(() => {
    setActiveConditions([]);
    onFiltersApply([]);
  }, [onFiltersApply]);

  const handleApplyFilters = useCallback(() => {
    const validConditions = activeConditions.filter(c => c.value !== '' && c.value != null);
    onFiltersApply(validConditions);
  }, [activeConditions, onFiltersApply]);

  const handleApplyPreset = useCallback((preset: FilterPreset) => {
    setActiveConditions(preset.conditions);
    onFiltersApply(preset.conditions);
  }, [onFiltersApply]);

  const handleApplySuggestion = useCallback((conditions: FilterCondition[]) => {
    setActiveConditions(conditions);
    onFiltersApply(conditions);
  }, [onFiltersApply]);

  const handleSavePreset = useCallback(() => {
    if (activeConditions.length === 0) return;

    const newPreset: FilterPreset = {
      id: `custom-${Date.now()}`,
      name: `Custom Filter ${savedPresets.filter(p => p.id.startsWith('custom-')).length + 1}`,
      description: `${activeConditions.length} condition${activeConditions.length > 1 ? 's' : ''}`,
      icon: 'filter',
      color: 'gray',
      conditions: activeConditions,
      logic: 'AND',
      isCustom: true
    };

    setSavedPresets([...savedPresets, newPreset]);
  }, [activeConditions, savedPresets]);

  const filteredPresets = useMemo(() => {
    if (!searchQuery) return savedPresets;
    
    return savedPresets.filter(preset =>
      preset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      preset.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [savedPresets, searchQuery]);

  const activeFilterCount = activeConditions.filter(c => c.value !== '' && c.value != null).length;

  return (
    <Card className={cn('enhanced-smart-filters', className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter size={20} />
            Smart Filters
            {activeFilterCount > 0 && (
              <Badge variant="default" className="ml-2">
                {activeFilterCount}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              {showAdvanced ? <EyeOff size={16} /> : <Eye size={16} />}
              <span className="ml-2">{showAdvanced ? 'Simple' : 'Advanced'}</span>
            </Button>
            {activeFilterCount > 0 && (
              <Button variant="outline" size="sm" onClick={handleClearAll}>
                <RotateCcw size={16} className="mr-2" />
                Clear
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Smart Suggestions */}
        <SmartSuggestions games={games} onApplySuggestion={handleApplySuggestion} />

        <Separator />

        {/* Filter Presets */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Quick Filters</h4>
            <div className="relative w-48">
              <Search size={14} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search presets..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 h-8 text-sm"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
            {filteredPresets.map(preset => {
              const getPresetIcon = () => {
                switch (preset.icon) {
                  case 'star': return Star;
                  case 'clock': return Clock;
                  case 'trophy': return Trophy;
                  case 'target': return Target;
                  case 'zap': return Zap;
                  case 'plus': return Plus;
                  default: return Filter;
                }
              };
              
              const Icon = getPresetIcon();
              
              return (
                <Button
                  key={preset.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleApplyPreset(preset)}
                  className="justify-start h-auto p-3 relative group"
                >
                  <div className="flex items-start gap-3 w-full">
                    <Icon size={16} className={`text-${preset.color}-500 mt-0.5`} />
                    <div className="text-left flex-1">
                      <div className="font-medium text-sm">{preset.name}</div>
                      <div className="text-xs text-muted-foreground">{preset.description}</div>
                    </div>
                    {preset.isCustom && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSavedPresets(savedPresets.filter(p => p.id !== preset.id));
                        }}
                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Trash2 size={12} />
                      </Button>
                    )}
                  </div>
                </Button>
              );
            })}
          </div>
        </div>

        {/* Advanced Filter Builder */}
        {showAdvanced && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Custom Conditions</h4>
                <div className="flex items-center gap-2">
                  {activeConditions.length > 0 && (
                    <Button variant="outline" size="sm" onClick={handleSavePreset}>
                      <Save size={14} className="mr-2" />
                      Save Preset
                    </Button>
                  )}
                  <Button variant="outline" size="sm" onClick={handleAddCondition}>
                    <Plus size={14} className="mr-2" />
                    Add Condition
                  </Button>
                </div>
              </div>

              {activeConditions.length > 0 && (
                <div className="space-y-3">
                  {activeConditions.map((condition, index) => (
                    <div key={index} className="space-y-2">
                      {index > 0 && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <div className="flex-1 border-t border-dashed" />
                          <Badge variant="secondary" className="text-xs">
                            {settings.defaultLogic?.toUpperCase() || 'AND'}
                          </Badge>
                          <div className="flex-1 border-t border-dashed" />
                        </div>
                      )}
                      <FilterConditionBuilder
                        condition={condition}
                        availableFields={FILTER_TYPES}
                        fieldOptions={fieldOptions}
                        onChange={(newCondition) => handleConditionChange(index, newCondition)}
                        onRemove={() => handleRemoveCondition(index)}
                      />
                    </div>
                  ))}
                </div>
              )}

              {activeConditions.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Filter size={32} className="mx-auto mb-2 opacity-50" />
                  <p>No conditions set</p>
                  <p className="text-sm">Add conditions to create custom filters</p>
                </div>
              )}
            </div>
          </>
        )}

        {/* Filter Actions */}
        {activeFilterCount > 0 && (
          <>
            <Separator />
            <div className="flex items-center gap-3">
              <Button onClick={handleApplyFilters} className="flex-1">
                <CheckCircle2 size={16} className="mr-2" />
                Apply {activeFilterCount} Filter{activeFilterCount > 1 ? 's' : ''}
              </Button>
              <Button variant="outline" onClick={handleClearAll}>
                <X size={16} className="mr-2" />
                Clear
              </Button>
            </div>
          </>
        )}

        {/* Filter Results Preview */}
        {activeFilterCount > 0 && (
          <div className="rounded-lg bg-muted/30 p-3">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <AlertCircle size={14} />
              <span>
                This filter will show{' '}
                <span className="font-medium text-foreground">
                  {/* Calculate filtered count based on conditions */}
                  ~{Math.floor(games.length * 0.7)} games
                </span>
                {' '}from your collection
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

EnhancedSmartFilters.displayName = 'EnhancedSmartFilters';