import { UserGameWithDetails } from '@/types/database';

export interface WishlistStats {
  total: number;
  averageRating: number;
  totalValue: number;
  priceTracked: number;
  averageMetacriticScore: number;
  topGenres: { genre: string; count: number }[];
  topDevelopers: { developer: string; count: number }[];
}

export interface WishlistFilters {
  genre?: string;
  developer?: string;
  publisher?: string;
  minMetacriticScore?: number;
  priceTrackingEnabled?: boolean;
}

export interface WishlistSettings {
  sortOptions: {
    field: string;
    direction: 'asc' | 'desc';
  };
  searchQuery: string;
  showPriceTracking: boolean;
}

export interface WishlistActions {
  addToLibrary: (gameData: UserGameWithDetails) => void;
  moveToLibrary: (userGameId: string, gameData: UserGameWithDetails) => void;
  removeFromWishlist: (userGameId: string) => void;
  togglePriceTracking: (userGameId: string) => void;
}

export interface WishlistData {
  wishlistGames: UserGameWithDetails[];
  stats: WishlistStats;
  isLoading: boolean;
  error: Error | null;
}

export interface WishlistContentProps {
  wishlistGames: UserGameWithDetails[];
  onGameClick: (gameData: UserGameWithDetails) => void;
}

export interface WishlistHeaderProps {
  gameCount: number;
}

export interface WishlistStatsProps {
  gameCount: number;
}