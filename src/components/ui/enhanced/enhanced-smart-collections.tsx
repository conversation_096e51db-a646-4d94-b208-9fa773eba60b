import React, { memo, useMemo, use<PERSON><PERSON>back, useState } from 'react';
import { UserGameWithDetails } from '@/types/database';
import { SmartCollection, CollectionRule } from '@/types/library';
import { cn } from '@/lib/utils';
import { 
  FolderPlus,
  Folder,
  Star,
  Clock,
  Trophy,
  Target,
  Gamepad2,
  Plus,
  Edit3 as Edit,
  Trash2,
  Sparkles,
  Heart,
  Zap,
  RefreshCw,
  Users,
  Share2,
  Eye,
  EyeOff,
  Filter,
  Search,
  MoreHorizontal,
  Copy,
  Download
} from '@/lib/icons';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Input } from '@/components/ui/base/input';
import { Label } from '@/components/ui/base/label';
import { Textarea } from '@/components/ui/base/textarea';
import { Separator } from '@/components/ui/base/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/base/tabs';
import { Switch } from '@/components/ui/base/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/base/select';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from '@/components/ui/base/dropdown-menu';

// Predefined smart collection templates
const COLLECTION_TEMPLATES: Omit<SmartCollection, 'id' | 'createdAt' | 'updatedAt' | 'gameCount'>[] = [
  {
    name: 'Recently Added',
    description: 'Games added in the last 30 days',
    icon: 'plus',
    color: 'blue',
    rules: [
      {
        field: 'dateAdded',
        operator: 'gte',
        value: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        logic: 'AND'
      }
    ],
    isAutoUpdating: true,
    updateFrequency: 'daily',
    isPublic: false,
    isSystem: false
  },
  {
    name: 'Highly Rated Gems',
    description: 'Personal favorites with 8+ rating',
    icon: 'star',
    color: 'yellow',
    rules: [
      {
        field: 'rating',
        operator: 'gte',
        value: 8,
        logic: 'AND'
      }
    ],
    isAutoUpdating: true,
    updateFrequency: 'realtime',
    isPublic: false,
    isSystem: false
  },
  {
    name: 'Quick Sessions',
    description: 'Games perfect for short gaming sessions',
    icon: 'zap',
    color: 'green',
    rules: [
      {
        field: 'hoursPlayed',
        operator: 'lt',
        value: 5,
        logic: 'OR'
      },
      {
        field: 'genre',
        operator: 'in',
        value: ['Puzzle', 'Arcade', 'Casual'],
        logic: 'AND'
      }
    ],
    isAutoUpdating: true,
    updateFrequency: 'weekly',
    isPublic: false,
    isSystem: false
  },
  {
    name: 'Backlog Priorities',
    description: 'High-rated games waiting to be played',
    icon: 'target',
    color: 'orange',
    rules: [
      {
        field: 'status',
        operator: 'eq',
        value: 'backlog',
        logic: 'AND'
      },
      {
        field: 'metacritic',
        operator: 'gte',
        value: 80,
        logic: 'AND'
      }
    ],
    isAutoUpdating: true,
    updateFrequency: 'daily',
    isPublic: false,
    isSystem: false
  },
  {
    name: 'Completion Candidates',
    description: 'Started games ready for completion',
    icon: 'trophy',
    color: 'purple',
    rules: [
      {
        field: 'status',
        operator: 'eq',
        value: 'playing',
        logic: 'AND'
      },
      {
        field: 'hoursPlayed',
        operator: 'gte',
        value: 2,
        logic: 'AND'
      }
    ],
    isAutoUpdating: true,
    updateFrequency: 'daily',
    isPublic: false,
    isSystem: false
  },
  {
    name: 'Modern Classics',
    description: 'Recent releases with exceptional ratings',
    icon: 'sparkles',
    color: 'indigo',
    rules: [
      {
        field: 'releaseYear',
        operator: 'gte',
        value: 2020,
        logic: 'AND'
      },
      {
        field: 'metacritic',
        operator: 'gte',
        value: 85,
        logic: 'AND'
      }
    ],
    isAutoUpdating: true,
    updateFrequency: 'weekly',
    isPublic: false,
    isSystem: false
  }
];

interface EnhancedSmartCollectionsProps {
  games: UserGameWithDetails[];
  collections: SmartCollection[];
  onCollectionCreate: (collection: Omit<SmartCollection, 'id' | 'createdAt' | 'updatedAt' | 'gameCount'>) => void;
  onCollectionUpdate: (id: string, updates: Partial<SmartCollection>) => void;
  onCollectionDelete: (id: string) => void;
  onCollectionDuplicate: (id: string) => void;
  className?: string;
}

interface CollectionCardProps {
  collection: SmartCollection;
  gameCount: number;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onToggleVisibility: () => void;
  onClick: () => void;
}

interface CollectionBuilderProps {
  collection?: SmartCollection;
  games: UserGameWithDetails[];
  onSave: (collection: Omit<SmartCollection, 'id' | 'createdAt' | 'updatedAt' | 'gameCount'>) => void;
  onCancel: () => void;
}

// Utility function to apply collection rules to games
const applyCollectionRules = (games: UserGameWithDetails[], rules: CollectionRule[]): UserGameWithDetails[] => {
  if (rules.length === 0) return games;

  return games.filter(game => {
    let result = true;
    let currentLogic: 'AND' | 'OR' = 'AND';

    for (let i = 0; i < rules.length; i++) {
      const rule = rules[i];
      let ruleResult = false;

      // Apply the rule based on field and operator
      switch (rule.field) {
        case 'status':
          ruleResult = rule.operator === 'eq' ? game.status === rule.value : game.status !== rule.value;
          break;
        
        case 'rating': {
          const rating = game.personal_rating || 0;
          switch (rule.operator) {
            case 'eq': ruleResult = rating === rule.value; break;
            case 'neq': ruleResult = rating !== rule.value; break;
            case 'gt': ruleResult = rating > rule.value; break;
            case 'gte': ruleResult = rating >= rule.value; break;
            case 'lt': ruleResult = rating < rule.value; break;
            case 'lte': ruleResult = rating <= rule.value; break;
          }
          break;
        }

        case 'metacritic': {
          const metacritic = game.game?.metacritic_score || 0;
          switch (rule.operator) {
            case 'eq': ruleResult = metacritic === rule.value; break;
            case 'neq': ruleResult = metacritic !== rule.value; break;
            case 'gt': ruleResult = metacritic > rule.value; break;
            case 'gte': ruleResult = metacritic >= rule.value; break;
            case 'lt': ruleResult = metacritic < rule.value; break;
            case 'lte': ruleResult = metacritic <= rule.value; break;
          }
          break;
        }

        case 'hoursPlayed': {
          const hours = game.hours_played || 0;
          switch (rule.operator) {
            case 'eq': ruleResult = hours === rule.value; break;
            case 'neq': ruleResult = hours !== rule.value; break;
            case 'gt': ruleResult = hours > rule.value; break;
            case 'gte': ruleResult = hours >= rule.value; break;
            case 'lt': ruleResult = hours < rule.value; break;
            case 'lte': ruleResult = hours <= rule.value; break;
          }
          break;
        }

        case 'releaseYear': {
          const releaseYear = game.game?.release_date ? new Date(game.game.release_date).getFullYear() : 0;
          switch (rule.operator) {
            case 'eq': ruleResult = releaseYear === rule.value; break;
            case 'neq': ruleResult = releaseYear !== rule.value; break;
            case 'gt': ruleResult = releaseYear > rule.value; break;
            case 'gte': ruleResult = releaseYear >= rule.value; break;
            case 'lt': ruleResult = releaseYear < rule.value; break;
            case 'lte': ruleResult = releaseYear <= rule.value; break;
          }
          break;
        }

        case 'dateAdded': {
          const dateAdded = new Date(game.date_added);
          const ruleDate = new Date(rule.value);
          switch (rule.operator) {
            case 'eq': ruleResult = dateAdded.toDateString() === ruleDate.toDateString(); break;
            case 'neq': ruleResult = dateAdded.toDateString() !== ruleDate.toDateString(); break;
            case 'gt': ruleResult = dateAdded > ruleDate; break;
            case 'gte': ruleResult = dateAdded >= ruleDate; break;
            case 'lt': ruleResult = dateAdded < ruleDate; break;
            case 'lte': ruleResult = dateAdded <= ruleDate; break;
          }
          break;
        }

        case 'genre': {
          const genres = game.game?.genres || [];
          if (rule.operator === 'in' && Array.isArray(rule.value)) {
            ruleResult = rule.value.some(v => genres.includes(v));
          } else if (rule.operator === 'nin' && Array.isArray(rule.value)) {
            ruleResult = !rule.value.some(v => genres.includes(v));
          }
          break;
        }

        case 'platform': {
          const platforms = game.game?.platforms || [];
          if (rule.operator === 'in' && Array.isArray(rule.value)) {
            ruleResult = rule.value.some(v => platforms.includes(v));
          } else if (rule.operator === 'nin' && Array.isArray(rule.value)) {
            ruleResult = !rule.value.some(v => platforms.includes(v));
          }
          break;
        }
      }

      // Apply logic operators
      if (i === 0) {
        result = ruleResult;
      } else {
        if (currentLogic === 'AND') {
          result = result && ruleResult;
        } else {
          result = result || ruleResult;
        }
      }

      // Set logic for next iteration
      currentLogic = rule.logic || 'AND';
    }

    return result;
  });
};

// Get icon component for collection
const getCollectionIcon = (iconName: string) => {
  switch (iconName) {
    case 'star': return Star;
    case 'clock': return Clock;
    case 'trophy': return Trophy;
    case 'target': return Target;
    case 'zap': return Zap;
    case 'plus': return Plus;
    case 'sparkles': return Sparkles;
    case 'heart': return Heart;
    case 'gamepad': return Gamepad2;
    default: return Folder;
  }
};

// Collection card component
const CollectionCard = memo<CollectionCardProps>(({
  collection,
  gameCount,
  onEdit,
  onDelete,
  onDuplicate,
  onToggleVisibility,
  onClick
}) => {
  const Icon = getCollectionIcon(collection.icon);

  return (
    <Card className="group hover:shadow-md transition-all duration-200 cursor-pointer">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1" onClick={onClick}>
            <div className={cn(
              'p-2 rounded-lg',
              `bg-${collection.color}-100 text-${collection.color}-600 dark:bg-${collection.color}-900/20 dark:text-${collection.color}-400`
            )}>
              <Icon size={20} />
            </div>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-base truncate">{collection.name}</CardTitle>
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {collection.description}
              </p>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreHorizontal size={16} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onEdit}>
                <Edit size={14} className="mr-2" />
                Edit Collection
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onDuplicate}>
                <Copy size={14} className="mr-2" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onToggleVisibility}>
                {collection.isPublic ? <EyeOff size={14} /> : <Eye size={14} />}
                <span className="ml-2">
                  {collection.isPublic ? 'Make Private' : 'Make Public'}
                </span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={onDelete} className="text-destructive">
                <Trash2 size={14} className="mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0" onClick={onClick}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Gamepad2 size={14} />
              <span>{gameCount} games</span>
            </div>
            {collection.isAutoUpdating && (
              <div className="flex items-center gap-1">
                <RefreshCw size={14} />
                <span>Auto-updating</span>
              </div>
            )}
            {collection.isPublic && (
              <div className="flex items-center gap-1">
                <Users size={14} />
                <span>Public</span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-1">
            {collection.rules.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {collection.rules.length} rule{collection.rules.length > 1 ? 's' : ''}
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

CollectionCard.displayName = 'CollectionCard';

// Collection builder component
const CollectionBuilder = memo<CollectionBuilderProps>(({
  collection,
  games,
  onSave,
  onCancel
}) => {
  const [name, setName] = useState(collection?.name || '');
  const [description, setDescription] = useState(collection?.description || '');
  const [icon, setIcon] = useState(collection?.icon || 'folder');
  const [color, setColor] = useState(collection?.color || 'blue');
  const [rules, setRules] = useState<CollectionRule[]>(collection?.rules || []);
  const [isAutoUpdating, setIsAutoUpdating] = useState(collection?.isAutoUpdating || true);
  const [updateFrequency, setUpdateFrequency] = useState(collection?.updateFrequency || 'daily');
  const [isPublic, setIsPublic] = useState(collection?.isPublic || false);

  const previewGames = useMemo(() => {
    return applyCollectionRules(games, rules);
  }, [games, rules]);

  const handleAddRule = useCallback(() => {
    setRules([...rules, {
      field: 'status',
      operator: 'eq',
      value: '',
      logic: 'AND'
    }]);
  }, [rules]);

  const handleUpdateRule = useCallback((index: number, rule: CollectionRule) => {
    const newRules = [...rules];
    newRules[index] = rule;
    setRules(newRules);
  }, [rules]);

  const handleRemoveRule = useCallback((index: number) => {
    setRules(rules.filter((_, i) => i !== index));
  }, [rules]);

  const handleSave = useCallback(() => {
    if (!name.trim()) return;

    onSave({
      name: name.trim(),
      description: description.trim(),
      icon,
      color,
      rules,
      isAutoUpdating,
      updateFrequency,
      isPublic,
      isSystem: false
    });
  }, [name, description, icon, color, rules, isAutoUpdating, updateFrequency, isPublic, onSave]);

  const iconOptions = [
    { value: 'folder', label: 'Folder', icon: Folder },
    { value: 'star', label: 'Star', icon: Star },
    { value: 'trophy', label: 'Trophy', icon: Trophy },
    { value: 'target', label: 'Target', icon: Target },
    { value: 'zap', label: 'Lightning', icon: Zap },
    { value: 'heart', label: 'Heart', icon: Heart },
    { value: 'sparkles', label: 'Sparkles', icon: Sparkles },
    { value: 'gamepad', label: 'Gamepad', icon: Gamepad2 },
  ];

  const colorOptions = [
    'blue', 'green', 'yellow', 'red', 'purple', 'pink', 'indigo', 'orange', 'teal', 'gray'
  ];

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Collection Name</Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter collection name..."
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe what this collection contains..."
            className="mt-1"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>Icon</Label>
            <Select value={icon} onValueChange={setIcon}>
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {iconOptions.map(option => {
                  const IconComponent = option.icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <IconComponent size={16} />
                        {option.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Color</Label>
            <Select value={color} onValueChange={setColor}>
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {colorOptions.map(colorOption => (
                  <SelectItem key={colorOption} value={colorOption}>
                    <div className="flex items-center gap-2">
                      <div className={cn('w-4 h-4 rounded-full', `bg-${colorOption}-500`)} />
                      {colorOption.charAt(0).toUpperCase() + colorOption.slice(1)}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <Separator />

      {/* Collection Rules */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <Label className="text-base">Collection Rules</Label>
            <p className="text-sm text-muted-foreground mt-1">
              Define criteria for automatically including games in this collection
            </p>
          </div>
          <Button variant="outline" size="sm" onClick={handleAddRule}>
            <Plus size={14} className="mr-2" />
            Add Rule
          </Button>
        </div>

        {rules.length > 0 ? (
          <div className="space-y-3">
            {rules.map((rule, index) => (
              <div key={index} className="p-3 border rounded-lg space-y-3">
                <div className="flex items-center gap-3">
                  <Select 
                    value={rule.field} 
                    onValueChange={(field) => handleUpdateRule(index, { ...rule, field })}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="status">Status</SelectItem>
                      <SelectItem value="rating">Rating</SelectItem>
                      <SelectItem value="metacritic">Metacritic</SelectItem>
                      <SelectItem value="hoursPlayed">Hours Played</SelectItem>
                      <SelectItem value="releaseYear">Release Year</SelectItem>
                      <SelectItem value="dateAdded">Date Added</SelectItem>
                      <SelectItem value="genre">Genre</SelectItem>
                      <SelectItem value="platform">Platform</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select 
                    value={rule.operator} 
                    onValueChange={(operator) => handleUpdateRule(index, { ...rule, operator })}
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="eq">equals</SelectItem>
                      <SelectItem value="neq">not equals</SelectItem>
                      <SelectItem value="gt">greater than</SelectItem>
                      <SelectItem value="gte">greater or equal</SelectItem>
                      <SelectItem value="lt">less than</SelectItem>
                      <SelectItem value="lte">less or equal</SelectItem>
                      <SelectItem value="in">includes</SelectItem>
                      <SelectItem value="nin">excludes</SelectItem>
                    </SelectContent>
                  </Select>

                  <Input
                    value={rule.value?.toString() || ''}
                    onChange={(e) => handleUpdateRule(index, { ...rule, value: e.target.value })}
                    placeholder="Value..."
                    className="flex-1"
                  />

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveRule(index)}
                    className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                  >
                    <Trash2 size={14} />
                  </Button>
                </div>

                {index < rules.length - 1 && (
                  <div className="flex items-center gap-2">
                    <div className="flex-1 border-t border-dashed" />
                    <Select 
                      value={rule.logic || 'AND'} 
                      onValueChange={(logic) => handleUpdateRule(index, { ...rule, logic: logic as 'AND' | 'OR' })}
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="AND">AND</SelectItem>
                        <SelectItem value="OR">OR</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="flex-1 border-t border-dashed" />
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground border-2 border-dashed rounded-lg">
            <Filter size={32} className="mx-auto mb-2 opacity-50" />
            <p>No rules defined</p>
            <p className="text-sm">Add rules to automatically filter games</p>
          </div>
        )}
      </div>

      <Separator />

      {/* Collection Settings */}
      <div className="space-y-4">
        <Label className="text-base">Collection Settings</Label>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <Label>Auto-updating</Label>
              <p className="text-sm text-muted-foreground">
                Automatically update when games match the rules
              </p>
            </div>
            <Switch
              checked={isAutoUpdating}
              onCheckedChange={setIsAutoUpdating}
            />
          </div>

          {isAutoUpdating && (
            <div>
              <Label>Update Frequency</Label>
              <Select value={updateFrequency} onValueChange={setUpdateFrequency}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="realtime">Real-time</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="manual">Manual only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div>
              <Label>Public Collection</Label>
              <p className="text-sm text-muted-foreground">
                Allow others to see and use this collection
              </p>
            </div>
            <Switch
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
          </div>
        </div>
      </div>

      {/* Preview */}
      {rules.length > 0 && (
        <>
          <Separator />
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-base">Preview</Label>
              <Badge variant="secondary">
                {previewGames.length} game{previewGames.length !== 1 ? 's' : ''} match
              </Badge>
            </div>
            
            <div className="p-3 bg-muted/30 rounded-lg">
              <p className="text-sm text-muted-foreground">
                This collection will contain {previewGames.length} games based on the current rules.
                {previewGames.length > 0 && ` Preview: ${previewGames.slice(0, 3).map(g => g.game?.title).join(', ')}${previewGames.length > 3 ? '...' : ''}`}
              </p>
            </div>
          </div>
        </>
      )}

      {/* Actions */}
      <div className="flex items-center gap-3 pt-4 border-t">
        <Button onClick={handleSave} disabled={!name.trim()}>
          {collection ? 'Update Collection' : 'Create Collection'}
        </Button>
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </div>
  );
});

CollectionBuilder.displayName = 'CollectionBuilder';

export const EnhancedSmartCollections = memo<EnhancedSmartCollectionsProps>(({
  games,
  collections,
  onCollectionCreate,
  onCollectionUpdate,
  onCollectionDelete,
  onCollectionDuplicate,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showBuilder, setShowBuilder] = useState(false);
  const [editingCollection, setEditingCollection] = useState<SmartCollection | null>(null);
  const [selectedCollection, setSelectedCollection] = useState<SmartCollection | null>(null);

  // Calculate game counts for each collection
  const collectionsWithCounts = useMemo(() => {
    return collections.map(collection => ({
      ...collection,
      gameCount: applyCollectionRules(games, collection.rules).length
    }));
  }, [collections, games]);

  // Filter collections based on search
  const filteredCollections = useMemo(() => {
    if (!searchQuery) return collectionsWithCounts;
    
    const query = searchQuery.toLowerCase();
    return collectionsWithCounts.filter(collection =>
      collection.name.toLowerCase().includes(query) ||
      collection.description.toLowerCase().includes(query)
    );
  }, [collectionsWithCounts, searchQuery]);

  // Group collections
  const groupedCollections = useMemo(() => {
    const groups = {
      system: filteredCollections.filter(c => c.isSystem),
      public: filteredCollections.filter(c => !c.isSystem && c.isPublic),
      private: filteredCollections.filter(c => !c.isSystem && !c.isPublic)
    };
    return groups;
  }, [filteredCollections]);

  const handleCreateFromTemplate = useCallback((template: typeof COLLECTION_TEMPLATES[0]) => {
    onCollectionCreate(template);
  }, [onCollectionCreate]);

  const handleEdit = useCallback((collection: SmartCollection) => {
    setEditingCollection(collection);
    setShowBuilder(true);
  }, []);

  const handleDelete = useCallback((id: string) => {
    onCollectionDelete(id);
  }, [onCollectionDelete]);

  const handleDuplicate = useCallback((id: string) => {
    onCollectionDuplicate(id);
  }, [onCollectionDuplicate]);

  const handleToggleVisibility = useCallback((collection: SmartCollection) => {
    onCollectionUpdate(collection.id, { isPublic: !collection.isPublic });
  }, [onCollectionUpdate]);

  const handleSave = useCallback((collectionData: Omit<SmartCollection, 'id' | 'createdAt' | 'updatedAt' | 'gameCount'>) => {
    if (editingCollection) {
      onCollectionUpdate(editingCollection.id, collectionData);
    } else {
      onCollectionCreate(collectionData);
    }
    setShowBuilder(false);
    setEditingCollection(null);
  }, [editingCollection, onCollectionCreate, onCollectionUpdate]);

  const handleCancel = useCallback(() => {
    setShowBuilder(false);
    setEditingCollection(null);
  }, []);

  if (showBuilder) {
    return (
      <Card className={cn('enhanced-smart-collections', className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderPlus size={20} />
            {editingCollection ? 'Edit Collection' : 'Create Smart Collection'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <CollectionBuilder
            collection={editingCollection || undefined}
            games={games}
            onSave={handleSave}
            onCancel={handleCancel}
          />
        </CardContent>
      </Card>
    );
  }

  if (selectedCollection) {
    const collectionGames = applyCollectionRules(games, selectedCollection.rules);
    
    return (
      <Card className={cn('enhanced-smart-collections', className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedCollection(null)}
              >
                ← Back
              </Button>
              <div className="flex items-center gap-2">
                {React.createElement(getCollectionIcon(selectedCollection.icon), { size: 20 })}
                <CardTitle>{selectedCollection.name}</CardTitle>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => handleEdit(selectedCollection)}>
                <Edit size={16} className="mr-2" />
                Edit
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleDuplicate(selectedCollection.id)}>
                    <Copy size={14} className="mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Share2 size={14} className="mr-2" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download size={14} className="mr-2" />
                    Export
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => handleDelete(selectedCollection.id)}
                    className="text-destructive"
                  >
                    <Trash2 size={14} className="mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          <p className="text-muted-foreground mt-2">{selectedCollection.description}</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm">
                <Badge variant="secondary">
                  {collectionGames.length} game{collectionGames.length !== 1 ? 's' : ''}
                </Badge>
                <Badge variant="outline">
                  {selectedCollection.rules.length} rule{selectedCollection.rules.length !== 1 ? 's' : ''}
                </Badge>
                {selectedCollection.isAutoUpdating && (
                  <Badge variant="outline">
                    <RefreshCw size={12} className="mr-1" />
                    Auto-updating
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="text-center py-8 text-muted-foreground">
              <Gamepad2 size={32} className="mx-auto mb-2 opacity-50" />
              <p>Collection games would be displayed here</p>
              <p className="text-sm">Integration with game list view component needed</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('enhanced-smart-collections', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Folder size={20} />
            Smart Collections
            <Badge variant="secondary" className="ml-2">
              {collections.length}
            </Badge>
          </CardTitle>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search size={14} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search collections..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 w-48"
              />
            </div>
            <Button onClick={() => setShowBuilder(true)}>
              <Plus size={16} className="mr-2" />
              Create Collection
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="collections" className="space-y-4">
          <TabsList>
            <TabsTrigger value="collections">My Collections</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="collections" className="space-y-6">
            {/* System Collections */}
            {groupedCollections.system.length > 0 && (
              <div className="space-y-3">
                <h3 className="font-medium text-sm text-muted-foreground">System Collections</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {groupedCollections.system.map(collection => (
                    <CollectionCard
                      key={collection.id}
                      collection={collection}
                      gameCount={collection.gameCount}
                      onEdit={() => handleEdit(collection)}
                      onDelete={() => handleDelete(collection.id)}
                      onDuplicate={() => handleDuplicate(collection.id)}
                      onToggleVisibility={() => handleToggleVisibility(collection)}
                      onClick={() => setSelectedCollection(collection)}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Public Collections */}
            {groupedCollections.public.length > 0 && (
              <div className="space-y-3">
                <h3 className="font-medium text-sm text-muted-foreground">Public Collections</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {groupedCollections.public.map(collection => (
                    <CollectionCard
                      key={collection.id}
                      collection={collection}
                      gameCount={collection.gameCount}
                      onEdit={() => handleEdit(collection)}
                      onDelete={() => handleDelete(collection.id)}
                      onDuplicate={() => handleDuplicate(collection.id)}
                      onToggleVisibility={() => handleToggleVisibility(collection)}
                      onClick={() => setSelectedCollection(collection)}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Private Collections */}
            {groupedCollections.private.length > 0 && (
              <div className="space-y-3">
                <h3 className="font-medium text-sm text-muted-foreground">Private Collections</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {groupedCollections.private.map(collection => (
                    <CollectionCard
                      key={collection.id}
                      collection={collection}
                      gameCount={collection.gameCount}
                      onEdit={() => handleEdit(collection)}
                      onDelete={() => handleDelete(collection.id)}
                      onDuplicate={() => handleDuplicate(collection.id)}
                      onToggleVisibility={() => handleToggleVisibility(collection)}
                      onClick={() => setSelectedCollection(collection)}
                    />
                  ))}
                </div>
              </div>
            )}

            {filteredCollections.length === 0 && (
              <div className="text-center py-12">
                <Folder size={48} className="mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium text-muted-foreground mb-2">
                  {searchQuery ? 'No collections found' : 'No collections yet'}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery 
                    ? 'Try adjusting your search criteria'
                    : 'Create your first smart collection to organize your games automatically'
                  }
                </p>
                {!searchQuery && (
                  <Button onClick={() => setShowBuilder(true)}>
                    <Plus size={16} className="mr-2" />
                    Create Your First Collection
                  </Button>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <div className="space-y-3">
              <h3 className="font-medium">Quick Start Templates</h3>
              <p className="text-sm text-muted-foreground">
                Choose from pre-built collection templates to get started quickly
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {COLLECTION_TEMPLATES.map((template, index) => {
                const Icon = getCollectionIcon(template.icon);
                const templateGameCount = applyCollectionRules(games, template.rules).length;
                
                return (
                  <Card key={index} className="group hover:shadow-md transition-all duration-200">
                    <CardHeader className="pb-3">
                      <div className="flex items-start gap-3">
                        <div className={cn(
                          'p-2 rounded-lg',
                          `bg-${template.color}-100 text-${template.color}-600 dark:bg-${template.color}-900/20 dark:text-${template.color}-400`
                        )}>
                          <Icon size={20} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-base">{template.name}</CardTitle>
                          <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                            {template.description}
                          </p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                          {templateGameCount} game{templateGameCount !== 1 ? 's' : ''} match
                        </div>
                        <Button 
                          size="sm" 
                          onClick={() => handleCreateFromTemplate(template)}
                        >
                          <Plus size={14} className="mr-2" />
                          Create
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
});

EnhancedSmartCollections.displayName = 'EnhancedSmartCollections';