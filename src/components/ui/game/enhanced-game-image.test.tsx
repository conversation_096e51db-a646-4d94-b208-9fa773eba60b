/**
 * Test file for Enhanced Game Image components
 * Verifies the smart image display functionality
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { EnhancedGameImage, SteamGameImage, FlexibleGameImage } from './enhanced-game-image';

// Mock the hooks
jest.mock('@/hooks/useSmartImageDisplay', () => ({
  useSmartImageDisplay: () => ({
    containerClasses: 'relative overflow-hidden rounded-lg bg-gradient-to-br from-muted via-muted to-muted/80 aspect-[2/3] p-2',
    imageClasses: 'w-full h-full transition-premium object-contain object-center',
    config: {
      aspectRatio: 'aspect-[2/3]',
      objectFit: 'contain',
      containerPadding: 'p-2',
      backgroundStyle: 'bg-gradient-to-br from-muted via-muted to-muted/80'
    },
    isLoading: false,
    hasError: false,
    imageDimensions: { width: 600, height: 800 },
    handleImageLoad: jest.fn(),
    handleImageError: jest.fn(),
    retry: jest.fn()
  })
}));

jest.mock('@/hooks/useImageWithFallback', () => ({
  useImageWithFallback: ({ src }: { src?: string | null }) => ({
    imageSrc: src || 'data:image/svg+xml;base64,placeholder',
    isLoading: false,
    hasError: false,
    handleLoad: jest.fn(),
    handleError: jest.fn(),
    retry: jest.fn()
  })
}));

jest.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' ')
}));

jest.mock('@/lib/icons', () => ({
  ImageIcon: ({ className }: { className?: string }) => (
    <svg className={className} data-testid="image-icon">
      <rect width="100%" height="100%" />
    </svg>
  )
}));

describe('EnhancedGameImage', () => {
  const defaultProps = {
    src: 'https://example.com/game-cover.jpg',
    alt: 'Test Game Cover',
    gameName: 'Test Game'
  };

  it('renders with default props', () => {
    render(<EnhancedGameImage {...defaultProps} />);
    
    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', defaultProps.src);
    expect(image).toHaveAttribute('alt', defaultProps.alt);
  });

  it('applies adaptive mode by default', () => {
    render(<EnhancedGameImage {...defaultProps} />);
    
    // Should use the mocked container classes which include aspect-[2/3]
    const container = screen.getByRole('img').closest('div');
    expect(container).toHaveClass('aspect-[2/3]');
  });

  it('shows fallback when no src provided', () => {
    render(<EnhancedGameImage src={null} alt="No Image" />);
    
    expect(screen.getByTestId('image-icon')).toBeInTheDocument();
    expect(screen.getByText('No Cover Art')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const customClass = 'custom-test-class';
    render(<EnhancedGameImage {...defaultProps} className={customClass} />);
    
    const container = screen.getByRole('img').closest('div');
    expect(container).toHaveClass(customClass);
  });
});

describe('SteamGameImage', () => {
  const defaultProps = {
    src: 'https://example.com/steam-game.jpg',
    alt: 'Steam Game',
    gameName: 'Steam Game'
  };

  it('renders as EnhancedGameImage with steam mode', () => {
    render(<SteamGameImage {...defaultProps} />);
    
    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', defaultProps.src);
  });
});

describe('FlexibleGameImage', () => {
  const defaultProps = {
    src: 'https://example.com/flexible-game.jpg',
    alt: 'Flexible Game',
    gameName: 'Flexible Game'
  };

  it('renders as EnhancedGameImage with flexible mode', () => {
    render(<FlexibleGameImage {...defaultProps} />);
    
    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', defaultProps.src);
  });
});

describe('Image Display Modes', () => {
  const defaultProps = {
    src: 'https://example.com/test-game.jpg',
    alt: 'Test Game',
    gameName: 'Test Game'
  };

  it('supports steam mode', () => {
    render(<EnhancedGameImage {...defaultProps} mode="steam" />);
    expect(screen.getByRole('img')).toBeInTheDocument();
  });

  it('supports flexible mode', () => {
    render(<EnhancedGameImage {...defaultProps} mode="flexible" />);
    expect(screen.getByRole('img')).toBeInTheDocument();
  });

  it('supports traditional mode', () => {
    render(<EnhancedGameImage {...defaultProps} mode="traditional" />);
    expect(screen.getByRole('img')).toBeInTheDocument();
  });

  it('supports adaptive mode', () => {
    render(<EnhancedGameImage {...defaultProps} mode="adaptive" />);
    expect(screen.getByRole('img')).toBeInTheDocument();
  });
});

describe('Loading and Error States', () => {
  const defaultProps = {
    src: 'https://example.com/test-game.jpg',
    alt: 'Test Game',
    gameName: 'Test Game'
  };

  it('handles loading state', () => {
    render(<EnhancedGameImage {...defaultProps} />);
    // The component should render even during loading
    expect(screen.getByRole('img')).toBeInTheDocument();
  });

  it('handles error state with fallback', () => {
    render(<EnhancedGameImage src={null} alt="Error Game" showFallback={true} />);
    expect(screen.getByTestId('image-icon')).toBeInTheDocument();
    expect(screen.getByText('No Cover Art')).toBeInTheDocument();
  });

  it('can disable fallback', () => {
    render(<EnhancedGameImage src={null} alt="No Fallback" showFallback={false} />);
    expect(screen.queryByTestId('image-icon')).not.toBeInTheDocument();
  });
});
