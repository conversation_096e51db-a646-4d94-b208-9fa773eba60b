import { Game, Platform } from '../../types';
import { IGDBGame, TheGamesDBGame } from '../api/games';
import { getCoverImageUrl, getScreenshotUrl } from '../utils/igdbImageUtils';

// Steam game interfaces for proper typing
interface SteamGameData {
  appid?: number;
  id?: number;
  name?: string;
  genres?: Array<{ description: string }>;
  developers?: string[] | string;
  publishers?: string[] | string;
  release_date?: { date?: string };
  short_description?: string;
  detailed_description?: string;
  header_image?: string;
  screenshots?: Array<{ path_full: string }>;
  movies?: Array<{ webm?: { max?: string }; mp4?: { max?: string } }>;
  metacritic?: { score?: number };
}

// Generic game data interface for unknown sources
interface GenericGameData {
  id?: string | number;
  title?: string;
  name?: string;
  platforms?: unknown;
  platform?: unknown;
  genres?: unknown;
  genre?: unknown;
  developer?: string;
  dev?: string;
  publisher?: string;
  pub?: string;
  release_date?: unknown;
  releaseDate?: unknown;
  date?: unknown;
  description?: string;
  summary?: string;
  overview?: string;
  cover_image?: string;
  coverImage?: string;
  image?: string;
  screenshots?: unknown;
  images?: unknown;
  youtube_links?: unknown;
  videos?: unknown;
  metacritic_score?: number;
  score?: number;
  igdb_id?: string;
}

// User collection export data interface
interface UserGameExportData {
  game_id: string;
  game?: { title?: string };
  platform?: string;
  status?: string;
  personal_rating?: number;
  date_added?: string;
  last_played?: string;
  hours_played?: number;
  completion_percentage?: number;
  notes?: string;
  tags?: string[];
  is_favorite?: boolean;
  purchase_price?: number;
  purchase_date?: string;
}

/**
 * Data Transformation Service - Handles data normalization and transformation
 * Provides consistent data formats across different API sources
 */
export class DataTransformationService {
  /**
   * Normalize game data from different sources into a consistent format
   */
  normalizeGameData(gameData: IGDBGame | TheGamesDBGame | SteamGameData | GenericGameData, source: 'igdb' | 'thegamesdb' | 'steam' | 'other' = 'other'): Game {
    try {
      switch (source) {
        case 'igdb':
          return this.normalizeIGDBGame(gameData as IGDBGame);
        case 'thegamesdb':
          return this.normalizeTheGamesDBGame(gameData as TheGamesDBGame);
        case 'steam':
          return this.normalizeSteamGame(gameData);
        default:
          return this.normalizeGenericGame(gameData);
      }
    } catch (error) {
      console.error('Error normalizing game data:', error);
      return this.createFallbackGame(gameData);
    }
  }

  /**
   * Normalize IGDB game data
   */
  private normalizeIGDBGame(igdbGame: IGDBGame): Game {
    return {
      id: `igdb_${igdbGame.id}`,
      title: igdbGame.name || 'Unknown Game',
      platforms: igdbGame.platforms?.map(p => p.name) || ['PC'],
      genres: igdbGame.genres?.map(g => g.name) || [],
      developer: igdbGame.involved_companies?.find(c => c.developer)?.company.name,
      publisher: igdbGame.involved_companies?.find(c => c.publisher)?.company.name,
      release_date: igdbGame.first_release_date ? new Date(igdbGame.first_release_date * 1000).toISOString().split('T')[0] : undefined,
      description: igdbGame.summary,
      cover_image: getCoverImageUrl(igdbGame.cover?.url, 'card'),
      screenshots: igdbGame.screenshots?.map(s => getScreenshotUrl(s.url, 'medium')) || [],
      youtube_links: igdbGame.videos?.map(v => `https://www.youtube.com/watch?v=${v.video_id}`) || [],
      metacritic_score: igdbGame.aggregated_rating ? Math.round(igdbGame.aggregated_rating) : undefined,
      igdb_id: igdbGame.id.toString()
    };
  }

  /**
   * Normalize TheGamesDB game data
   */
  private normalizeTheGamesDBGame(tgdbGame: TheGamesDBGame): Game {
    return {
      id: `tgdb_${tgdbGame.id}`,
      title: tgdbGame.game_title || 'Unknown Game',
      platforms: tgdbGame.platform ? [this.mapTheGamesDBPlatform(tgdbGame.platform)] : ['PC'],
      genres: tgdbGame.genres?.map(id => this.mapTheGamesDBGenre(id)).filter(Boolean) || [],
      developer: tgdbGame.developers?.[0] ? this.mapTheGamesDBDeveloper(tgdbGame.developers[0]) : undefined,
      publisher: tgdbGame.publishers?.[0] ? this.mapTheGamesDBPublisher(tgdbGame.publishers[0]) : undefined,
      release_date: tgdbGame.release_date,
      description: tgdbGame.overview,
      cover_image: undefined, // Would need to be populated from images API
      screenshots: [],
      youtube_links: tgdbGame.youtube ? [`https://www.youtube.com/watch?v=${tgdbGame.youtube}`] : [],
      metacritic_score: undefined,
      igdb_id: undefined
    };
  }

  /**
   * Normalize Steam game data
   */
  private normalizeSteamGame(steamGame: SteamGameData): Game {
    return {
      id: `steam_${steamGame.appid || steamGame.id}`,
      title: steamGame.name || 'Unknown Game',
      platforms: ['PC'], // Steam is PC only
      genres: steamGame.genres?.map(g => g.description) || [],
      developer: Array.isArray(steamGame.developers) ? steamGame.developers[0] : steamGame.developers,
      publisher: Array.isArray(steamGame.publishers) ? steamGame.publishers[0] : steamGame.publishers,
      release_date: steamGame.release_date?.date,
      description: steamGame.short_description || steamGame.detailed_description,
      cover_image: steamGame.header_image,
      screenshots: steamGame.screenshots?.map(s => s.path_full) || [],
      youtube_links: steamGame.movies?.map(m => m.webm?.max || m.mp4?.max).filter(Boolean) || [],
      metacritic_score: steamGame.metacritic?.score,
      igdb_id: undefined
    };
  }

  /**
   * Normalize generic game data
   */
  private normalizeGenericGame(gameData: GenericGameData): Game {
    return {
      id: gameData.id || `generic_${Date.now()}`,
      title: gameData.title || gameData.name || 'Unknown Game',
      platforms: this.normalizePlatforms(gameData.platforms || gameData.platform),
      genres: this.normalizeGenres(gameData.genres || gameData.genre),
      developer: gameData.developer || gameData.dev,
      publisher: gameData.publisher || gameData.pub,
      release_date: this.normalizeDate(gameData.release_date || gameData.releaseDate || gameData.date),
      description: gameData.description || gameData.summary || gameData.overview,
      cover_image: gameData.cover_image || gameData.coverImage || gameData.image,
      screenshots: this.normalizeScreenshots(gameData.screenshots || gameData.images),
      youtube_links: this.normalizeYouTubeLinks(gameData.youtube_links || gameData.videos),
      metacritic_score: gameData.metacritic_score || gameData.score,
      igdb_id: gameData.igdb_id
    };
  }

  /**
   * Create fallback game object when normalization fails
   */
  private createFallbackGame(gameData: GenericGameData): Game {
    return {
      id: gameData.id || `fallback_${Date.now()}`,
      title: gameData.title || gameData.name || 'Unknown Game',
      platforms: ['PC'],
      genres: [],
      developer: undefined,
      publisher: undefined,
      release_date: undefined,
      description: undefined,
      cover_image: undefined,
      screenshots: [],
      youtube_links: [],
      metacritic_score: undefined,
      igdb_id: undefined
    };
  }

  /**
   * Normalize platform data
   */
  private normalizePlatforms(platforms: unknown): Platform[] {
    if (!platforms) return ['PC'];
    
    if (typeof platforms === 'string') {
      return [this.mapPlatformName(platforms)];
    }
    
    if (Array.isArray(platforms)) {
      return platforms.map(p => 
        typeof p === 'string' ? this.mapPlatformName(p) : this.mapPlatformName(p.name || p.platform)
      ).filter(Boolean);
    }
    
    return ['PC'];
  }

  /**
   * Normalize genre data
   */
  private normalizeGenres(genres: unknown): string[] {
    if (!genres) return [];
    
    if (typeof genres === 'string') {
      return [genres];
    }
    
    if (Array.isArray(genres)) {
      return genres.map(g => 
        typeof g === 'string' ? g : g.name || g.genre
      ).filter(Boolean);
    }
    
    return [];
  }

  /**
   * Normalize date strings
   */
  private normalizeDate(date: unknown): string | undefined {
    if (!date) return undefined;
    
    try {
      if (typeof date === 'number') {
        // Unix timestamp
        return new Date(date * 1000).toISOString().split('T')[0];
      }
      
      if (typeof date === 'string') {
        const parsed = new Date(date);
        if (!isNaN(parsed.getTime())) {
          return parsed.toISOString().split('T')[0];
        }
      }
      
      return undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * Normalize screenshot arrays
   */
  private normalizeScreenshots(screenshots: unknown): string[] {
    if (!screenshots) return [];
    
    if (Array.isArray(screenshots)) {
      return screenshots.map(s => 
        typeof s === 'string' ? s : s.url || s.path || s.image
      ).filter(Boolean);
    }
    
    return [];
  }

  /**
   * Normalize YouTube links
   */
  private normalizeYouTubeLinks(videos: unknown): string[] {
    if (!videos) return [];
    
    if (Array.isArray(videos)) {
      return videos.map(v => {
        if (typeof v === 'string') {
          return v.includes('youtube.com') ? v : `https://www.youtube.com/watch?v=${v}`;
        }
        return v.url || v.link;
      }).filter(Boolean);
    }
    
    return [];
  }

  /**
   * Map platform names to standardized Platform enum
   */
  private mapPlatformName(platformName: string): Platform {
    const platformMap: Record<string, Platform> = {
      'pc': 'PC',
      'windows': 'PC',
      'microsoft windows': 'PC',
      'playstation 5': 'PlayStation 5',
      'ps5': 'PlayStation 5',
      'playstation 4': 'PlayStation 4',
      'ps4': 'PlayStation 4',
      'playstation 3': 'PlayStation 3',
      'ps3': 'PlayStation 3',
      'xbox series x/s': 'Xbox Series X/S',
      'xbox series x': 'Xbox Series X/S',
      'xbox series s': 'Xbox Series X/S',
      'xbox one': 'Xbox One',
      'xbox 360': 'Xbox 360',
      'nintendo switch': 'Nintendo Switch',
      'switch': 'Nintendo Switch',
      'steam deck': 'Steam Deck',
      'ios': 'iOS',
      'android': 'Android',
      'mac': 'Mac',
      'macos': 'Mac',
      'linux': 'Linux'
    };

    const normalized = platformName.toLowerCase().trim();
    return platformMap[normalized] || 'PC';
  }

  /**
   * TheGamesDB specific mapping functions
   */
  private mapTheGamesDBPlatform(platformId: number): Platform {
    const platformMap: Record<number, Platform> = {
      1: 'PC',
      4: 'PC',
      8: 'PlayStation 4',
      9: 'PlayStation 3',
      15: 'Xbox One',
      14: 'Xbox 360',
      7: 'Nintendo Switch'
    };
    
    return platformMap[platformId] || 'PC';
  }

  private mapTheGamesDBGenre(genreId: number): string | undefined {
    // This would typically come from a cached mapping
    const genreMap: Record<number, string> = {
      1: 'Action',
      2: 'Adventure',
      3: 'RPG',
      4: 'Strategy',
      5: 'Simulation',
      6: 'Sports',
      7: 'Racing',
      8: 'Puzzle'
    };
    
    return genreMap[genreId];
  }

  private mapTheGamesDBDeveloper(developerId: number): string | undefined {
    // This would typically come from a cached mapping
    return `Developer_${developerId}`;
  }

  private mapTheGamesDBPublisher(publisherId: number): string | undefined {
    // This would typically come from a cached mapping
    return `Publisher_${publisherId}`;
  }

  /**
   * Validate normalized game data
   */
  validateNormalizedGame(game: Game): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!game.id || game.id.trim().length === 0) {
      errors.push('Game ID is required');
    }

    if (!game.title || game.title.trim().length === 0) {
      errors.push('Game title is required');
    }

    if (!game.platforms || game.platforms.length === 0) {
      errors.push('At least one platform is required');
    }

    if (game.release_date && isNaN(Date.parse(game.release_date))) {
      errors.push('Invalid release date format');
    }

    if (game.metacritic_score && (game.metacritic_score < 0 || game.metacritic_score > 100)) {
      errors.push('Metacritic score must be between 0 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Transform user collection data for export
   */
  transformCollectionForExport(userGames: UserGameExportData[], format: 'json' | 'csv' | 'xml' = 'json') {
    const transformedData = userGames.map(userGame => ({
      gameId: userGame.game_id,
      title: userGame.game?.title || 'Unknown',
      platform: userGame.platform,
      status: userGame.status,
      personalRating: userGame.personal_rating,
      dateAdded: userGame.date_added,
      lastPlayed: userGame.last_played,
      hoursPlayed: userGame.hours_played,
      completionPercentage: userGame.completion_percentage,
      notes: userGame.notes,
      tags: userGame.tags,
      isFavorite: userGame.is_favorite,
      purchasePrice: userGame.purchase_price,
      purchaseDate: userGame.purchase_date
    }));

    switch (format) {
      case 'json':
        return {
          format: 'json',
          data: transformedData,
          exportDate: new Date().toISOString(),
          totalGames: transformedData.length
        };

      case 'csv': {
        const headers = [
          'Game ID', 'Title', 'Platform', 'Status', 'Personal Rating',
          'Date Added', 'Last Played', 'Hours Played', 'Completion %',
          'Notes', 'Tags', 'Is Favorite', 'Purchase Price', 'Purchase Date'
        ];

        const rows = transformedData.map(game => [
          game.gameId,
          game.title,
          game.platform || '',
          game.status || '',
          game.personalRating?.toString() || '',
          game.dateAdded || '',
          game.lastPlayed || '',
          game.hoursPlayed?.toString() || '',
          game.completionPercentage?.toString() || '',
          game.notes || '',
          Array.isArray(game.tags) ? game.tags.join(';') : '',
          game.isFavorite ? 'Yes' : 'No',
          game.purchasePrice?.toString() || '',
          game.purchaseDate || ''
        ]);

        return {
          format: 'csv',
          headers,
          rows,
          csv: [headers, ...rows].map(row =>
            row.map(cell => `"${cell?.toString().replace(/"/g, '""') || ''}"`).join(',')
          ).join('\n')
        };
      }

      case 'xml': {
        const xmlData = transformedData.map(game => {
          const gameXml = Object.entries(game)
            .map(([key, value]) => `    <${key}>${this.escapeXml(value?.toString() || '')}</${key}>`)
            .join('\n');
          return `  <game>\n${gameXml}\n  </game>`;
        }).join('\n');

        return {
          format: 'xml',
          xml: `<?xml version="1.0" encoding="UTF-8"?>\n<collection>\n${xmlData}\n</collection>`
        };
      }

      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Escape XML special characters
   */
  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
}

export const dataTransformationService = new DataTransformationService();