-- Function to get all tables
create or replace function get_all_tables()
returns table (
  table_name text,
  table_schema text
) as $$
begin
  return query
  select t.table_name, t.table_schema
  from information_schema.tables t
  where t.table_schema not in ('pg_catalog', 'information_schema')
    and t.table_type = 'BASE TABLE';
end;
$$ language plpgsql security definer;

-- Function to drop a table
create or replace function drop_table(table_name text)
returns void as $$
declare
  full_table_name text;
begin
  full_table_name := quote_ident(table_name);
  execute format('drop table if exists %I cascade', full_table_name);
end;
$$ language plpgsql security definer;