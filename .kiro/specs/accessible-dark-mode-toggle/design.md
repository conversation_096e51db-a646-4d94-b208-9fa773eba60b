# Design Document

## Overview

The accessible dark mode toggle system will provide a comprehensive theming solution for the Codexa gaming library manager. The design leverages the existing CSS custom properties foundation while extending it with proper accessibility features, system preference detection, and persistent user preferences. The implementation will integrate seamlessly with the current shadcn/ui component system and Tailwind CSS configuration.

## Architecture

### Theme Management System
The theme system will be built around a centralized theme provider that manages:
- Theme state (light/dark/system)
- System preference detection via `prefers-color-scheme`
- User preference persistence in localStorage and database
- CSS custom property updates
- Smooth transitions between themes

### Component Integration
The system will integrate with existing components through:
- Enhanced Header component with theme toggle button
- Updated useUserPreferences hook for theme persistence
- CSS variable updates that cascade to all components
- Accessibility enhancements for screen readers

## Components and Interfaces

### 1. Theme Provider (`useTheme` Hook)
```typescript
interface ThemeContextType {
  theme: 'light' | 'dark' | 'system';
  resolvedTheme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  systemTheme: 'light' | 'dark';
}
```

**Responsibilities:**
- Detect system color scheme preference
- Manage theme state and persistence
- Apply theme changes to document root
- Provide theme context to components

### 2. Theme Toggle Component
```typescript
interface ThemeToggleProps {
  variant?: 'icon' | 'button' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}
```

**Features:**
- Multiple display variants (icon-only, button with text, dropdown menu)
- Keyboard navigation support (Tab, Enter, Space)
- Screen reader announcements
- Visual state indicators
- Smooth icon transitions

### 3. Enhanced CSS Variables System
The existing CSS custom properties will be extended with additional semantic tokens:

```css
:root {
  /* Existing variables... */
  
  /* Enhanced theme variables */
  --theme-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --focus-ring: 0 0 0 2px hsl(var(--primary) / 0.5);
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 1px 3px rgba(0, 0, 0, 0.3);
  
  /* Accessibility enhancements */
  --high-contrast-border: 2px solid hsl(var(--foreground));
  --reduced-motion-duration: 0.01ms;
}
```

## Data Models

### Theme Preference Storage
```typescript
interface ThemePreference {
  theme: 'light' | 'dark' | 'system';
  lastUpdated: string;
  autoDetectSystem: boolean;
}
```

**Storage Strategy:**
- **localStorage**: Immediate persistence for fast loading
- **Database**: Long-term persistence via existing user preferences system
- **Fallback**: System preference detection when no stored preference exists

### User Preferences Integration
The theme preference will be integrated into the existing `UserPreferences` interface:

```typescript
interface UserPreferences {
  theme: 'light' | 'dark' | 'system'; // Enhanced from existing
  // ... existing properties
}
```

## Error Handling

### Graceful Degradation
- **System Detection Failure**: Fall back to light theme
- **Storage Failure**: Use in-memory state only
- **CSS Variable Support**: Provide fallback colors for older browsers
- **JavaScript Disabled**: Default to light theme with manual CSS class

### Error Recovery
```typescript
const handleThemeError = (error: Error) => {
  console.warn('Theme system error:', error);
  // Fall back to light theme
  setTheme('light');
  // Clear corrupted localStorage
  localStorage.removeItem('theme-preference');
};
```

## Testing Strategy

### Unit Tests
- Theme provider state management
- System preference detection
- Local storage persistence
- CSS variable application
- Error handling scenarios

### Integration Tests
- Theme toggle component interactions
- Header integration
- User preferences synchronization
- Cross-component theme consistency

### Accessibility Tests
- Screen reader announcements
- Keyboard navigation
- Focus management
- Color contrast validation
- High contrast mode support

### Visual Tests
- Theme transition smoothness
- Component appearance in both themes
- Icon state changes
- Loading states

## Implementation Details

### System Preference Detection
```typescript
const detectSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light';
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches 
    ? 'dark' 
    : 'light';
};
```

### Theme Application
```typescript
const applyTheme = (theme: 'light' | 'dark') => {
  const root = document.documentElement;
  
  // Remove existing theme classes
  root.classList.remove('light', 'dark');
  
  // Add new theme class
  root.classList.add(theme);
  
  // Update meta theme-color for mobile browsers
  const metaThemeColor = document.querySelector('meta[name="theme-color"]');
  if (metaThemeColor) {
    const bgColor = getComputedStyle(root).getPropertyValue('--background');
    metaThemeColor.setAttribute('content', `hsl(${bgColor})`);
  }
};
```

### Smooth Transitions
```css
* {
  transition: var(--theme-transition);
}

/* Disable transitions during theme change to prevent flash */
.theme-changing * {
  transition: none !important;
}

/* Re-enable after theme change */
.theme-changed * {
  transition: var(--theme-transition);
}
```

### Accessibility Enhancements
```typescript
const announceThemeChange = (theme: string) => {
  const announcement = `Theme changed to ${theme} mode`;
  
  // Create live region for screen readers
  const liveRegion = document.createElement('div');
  liveRegion.setAttribute('aria-live', 'polite');
  liveRegion.setAttribute('aria-atomic', 'true');
  liveRegion.className = 'sr-only';
  liveRegion.textContent = announcement;
  
  document.body.appendChild(liveRegion);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(liveRegion);
  }, 1000);
};
```

### Performance Optimizations
- **CSS Variable Caching**: Cache computed values to avoid repeated calculations
- **Debounced Updates**: Prevent rapid theme switching from causing performance issues
- **Lazy Loading**: Only load theme-specific assets when needed
- **Memory Management**: Clean up event listeners and observers

### Browser Compatibility
- **Modern Browsers**: Full CSS custom property support
- **Legacy Support**: Fallback to static CSS classes
- **Mobile Optimization**: Respect system theme changes on mobile devices
- **Reduced Motion**: Honor `prefers-reduced-motion` setting

### Security Considerations
- **XSS Prevention**: Sanitize theme preference values
- **Storage Validation**: Validate localStorage data before use
- **CSP Compliance**: Ensure inline styles comply with Content Security Policy

## Integration Points

### Header Component
- Replace existing basic dark mode toggle with comprehensive theme toggle
- Maintain existing tooltip and accessibility features
- Integrate with user menu for additional theme options

### User Preferences System
- Extend existing `useUserPreferences` hook
- Synchronize theme preference with database
- Handle migration from existing theme settings

### CSS Architecture
- Build upon existing CSS custom properties
- Maintain compatibility with shadcn/ui components
- Extend Tailwind configuration for theme-aware utilities

### Component Library
- Ensure all existing components work with new theme system
- Add theme-aware variants where beneficial
- Maintain design system consistency

This design provides a robust, accessible, and maintainable dark mode toggle system that integrates seamlessly with the existing Codexa architecture while providing enhanced user experience and accessibility features.