import { useState, useEffect, useRef } from 'react';
import { LoadingSpinner } from '@/components/ui/utils';
import { EnhancedGameDetailModal } from '@/components/ui/game/enhanced-game-detail-modal';
import { useUpdateGameStatus, useRemoveFromCollection } from '@/hooks/useGameActions';
import { useViewModeState } from '@/hooks/useViewModeState';
import { UserGameWithDetails } from '@/types/database';
import { LibraryHeader } from './components/LibraryHeader';
import { LibraryStats } from './components/LibraryStats';
import { LibraryTabs, TabContent } from './components/LibraryTabs';
import { LibraryTab } from './components/LibraryTabs/LibraryTab';
import { ArtworkTab } from './components/LibraryTabs/ArtworkTab';
import { useLibraryData } from './hooks/useLibraryData';

export default function Library() {
  const { libraryGames, stats, isLoading, error } = useLibraryData();
  const {
    isLoading: viewModeLoading
  } = useViewModeState();
  const updateGameStatus = useUpdateGameStatus();
  const removeFromCollection = useRemoveFromCollection();
  
  const [selectedGame, setSelectedGame] = useState<UserGameWithDetails | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [sortOptions, setSortOptions] = useState<{ field: string; direction: 'asc' | 'desc' }>({
    field: 'added_at',
    direction: 'desc'
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('library');

  const containerRef = useRef<HTMLDivElement>(null);





  const handleGameClick = (gameData: UserGameWithDetails) => {
    setSelectedGame(gameData);
    setIsModalOpen(true);
  };

  const handleStatusUpdate = (userGameId: string, newStatus: string) => {
    updateGameStatus.mutate({
      userGameId,
      status: newStatus
    });
  };

  const handleRemoveFromCollection = (userGameId: string) => {
    removeFromCollection.mutate(userGameId);
  };





  if (isLoading || viewModeLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="text-center py-8">
          <p className="text-destructive">Failed to load library</p>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className="container mx-auto p-6 space-y-6">
      <LibraryHeader
        gameCount={libraryGames.length}
      />
      
      <LibraryTabs activeTab={activeTab} onTabChange={setActiveTab}>
        <TabContent value="library">
          <LibraryStats stats={stats} isLoading={isLoading} />
          <LibraryTab
            libraryGames={libraryGames}
            isLoading={isLoading}
            error={error}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            sortOptions={sortOptions}
            setSortOptions={setSortOptions}
            onGameClick={handleGameClick}
            onStatusUpdate={handleStatusUpdate}
            onRemoveGame={handleRemoveFromCollection}
          />
        </TabContent>
        
        <TabContent value="artwork">
          <ArtworkTab
            libraryGames={libraryGames}
          />
        </TabContent>
      </LibraryTabs>

      <EnhancedGameDetailModal
        gameData={selectedGame}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onStatusUpdate={handleStatusUpdate}
        onQuickPlay={(gameData) => {
          console.log('Quick play:', gameData.game?.title);
          // Add quick play functionality here
        }}
        onAddToWishlist={(gameData) => {
          handleStatusUpdate(gameData.id, 'wishlist');
        }}
      />
    </div>
  );
}