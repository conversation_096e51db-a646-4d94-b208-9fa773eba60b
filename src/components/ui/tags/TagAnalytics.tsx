import { useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { useUserTags } from '@/hooks/useUserTags';
import { useUserLibrary } from '@/hooks/useUserLibrary';
import { 
  Hash, 
  TrendingUp, 
  BarChart3, 
  PieChart, 
  Target,
  Users,
  Gamepad2
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TagAnalyticsProps {
  className?: string;
}

interface TagUsageStats {
  tagId: string;
  tagName: string;
  tagColor: string;
  usageCount: number;
  percentage: number;
  recentlyUsed: boolean;
}


export function TagAnalytics({ className }: TagAnalyticsProps) {
  const { tags, popularTags } = useUserTags();
  const { games } = useUserLibrary();

  const analytics = useMemo(() => {
    if (!games || !tags) return null;

    // Calculate tag usage statistics
    const tagUsageMap = new Map<string, number>();
    const tagGameMap = new Map<string, string[]>();
    
    games.forEach(game => {
      if (game.tags && game.tags.length > 0) {
        game.tags.forEach(tagId => {
          tagUsageMap.set(tagId, (tagUsageMap.get(tagId) || 0) + 1);
          if (!tagGameMap.has(tagId)) {
            tagGameMap.set(tagId, []);
          }
          tagGameMap.get(tagId)?.push(game.id.toString());
        });
      }
    });

    const totalTaggedGames = games.filter(game => game.tags && game.tags.length > 0).length;
    const untaggedGames = games.length - totalTaggedGames;

    // Create tag usage stats
    const tagStats: TagUsageStats[] = tags.map(tag => {
      const usageCount = tagUsageMap.get(tag.id) || 0;
      const percentage = totalTaggedGames > 0 ? (usageCount / totalTaggedGames) * 100 : 0;
      
      return {
        tagId: tag.id,
        tagName: tag.name,
        tagColor: tag.color,
        usageCount,
        percentage,
        recentlyUsed: tag.updated_at ? 
          new Date(tag.updated_at).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000 : false
      };
    }).sort((a, b) => b.usageCount - a.usageCount);

    // Calculate tag coverage
    const tagCoverage = totalTaggedGames > 0 ? (totalTaggedGames / games.length) * 100 : 0;

    // Find most popular tag combinations
    const combinationMap = new Map<string, number>();
    games.forEach(game => {
      if (game.tags && game.tags.length > 1) {
        const sortedTags = [...game.tags].sort();
        for (let i = 0; i < sortedTags.length; i++) {
          for (let j = i + 1; j < sortedTags.length; j++) {
            const combo = `${sortedTags[i]}|${sortedTags[j]}`;
            combinationMap.set(combo, (combinationMap.get(combo) || 0) + 1);
          }
        }
      }
    });

    const topCombinations = Array.from(combinationMap.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([combo, count]) => {
        const [tag1Id, tag2Id] = combo.split('|');
        const tag1 = tags.find(t => t.id === tag1Id);
        const tag2 = tags.find(t => t.id === tag2Id);
        return {
          tag1: tag1?.name || 'Unknown',
          tag2: tag2?.name || 'Unknown',
          count,
          tag1Color: tag1?.color || '#gray',
          tag2Color: tag2?.color || '#gray'
        };
      });

    return {
      totalTags: tags.length,
      totalTaggedGames,
      untaggedGames,
      tagCoverage,
      tagStats,
      topCombinations,
      averageTagsPerGame: totalTaggedGames > 0 ? 
        Array.from(tagUsageMap.values()).reduce((a, b) => a + b, 0) / totalTaggedGames : 0
    };
  }, [games, tags]);

  if (!analytics) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Hash className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Loading tag analytics...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Hash className="h-4 w-4 text-primary" />
              <div>
                <p className="text-xs text-muted-foreground">Total Tags</p>
                <p className="text-lg font-semibold">{analytics.totalTags}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Gamepad2 className="h-4 w-4 text-primary" />
              <div>
                <p className="text-xs text-muted-foreground">Tagged Games</p>
                <p className="text-lg font-semibold">{analytics.totalTaggedGames}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-primary" />
              <div>
                <p className="text-xs text-muted-foreground">Tag Coverage</p>
                <p className="text-lg font-semibold">{analytics.tagCoverage.toFixed(1)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4 text-primary" />
              <div>
                <p className="text-xs text-muted-foreground">Avg Tags/Game</p>
                <p className="text-lg font-semibold">{analytics.averageTagsPerGame.toFixed(1)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tag Usage Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Tag Usage Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64">
              <div className="space-y-3">
                {analytics.tagStats.slice(0, 10).map((stat, index) => (
                  <div key={stat.tagId} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-muted-foreground">
                          #{index + 1}
                        </span>
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: stat.tagColor }}
                        />
                        <span className="text-sm font-medium">{stat.tagName}</span>
                        {stat.recentlyUsed && (
                          <Badge variant="secondary" className="text-xs">New</Badge>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{stat.usageCount}</p>
                        <p className="text-xs text-muted-foreground">
                          {stat.percentage.toFixed(1)}%
                        </p>
                      </div>
                    </div>
                    <Progress value={stat.percentage} className="h-1" />
                  </div>
                ))}
                
                {analytics.untaggedGames > 0 && (
                  <div className="space-y-2 pt-2 border-t">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-muted-foreground">
                          Untagged
                        </span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{analytics.untaggedGames}</p>
                        <p className="text-xs text-muted-foreground">
                          {((analytics.untaggedGames / (analytics.totalTaggedGames + analytics.untaggedGames)) * 100).toFixed(1)}%
                        </p>
                      </div>
                    </div>
                    <Progress 
                      value={(analytics.untaggedGames / (analytics.totalTaggedGames + analytics.untaggedGames)) * 100} 
                      className="h-1" 
                    />
                  </div>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Popular Tag Combinations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Popular Tag Combinations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.topCombinations.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <PieChart className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No tag combinations found</p>
                  <p className="text-xs">Games need multiple tags to show combinations</p>
                </div>
              ) : (
                analytics.topCombinations.map((combo, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-muted-foreground">
                        #{index + 1}
                      </span>
                      <div className="flex items-center gap-1">
                        <Badge
                          variant="outline"
                          className="text-xs"
                          style={{ borderColor: combo.tag1Color }}
                        >
                          {combo.tag1}
                        </Badge>
                        <span className="text-xs text-muted-foreground">+</span>
                        <Badge
                          variant="outline"
                          className="text-xs"
                          style={{ borderColor: combo.tag2Color }}
                        >
                          {combo.tag2}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{combo.count}</p>
                      <p className="text-xs text-muted-foreground">games</p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Popular Tags from Community */}
      {popularTags.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Community Popular Tags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {popularTags.slice(0, 12).map((tag, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  <span>{tag.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {tag.usage_count}
                  </span>
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.untaggedGames > 0 && (
              <div className="p-3 rounded-lg bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800">
                <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  {analytics.untaggedGames} games need tags
                </p>
                <p className="text-xs text-yellow-600 dark:text-yellow-300">
                  Tag your games to improve organization and discovery
                </p>
              </div>
            )}

            {analytics.tagCoverage < 50 && (
              <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Low tag coverage ({analytics.tagCoverage.toFixed(1)}%)
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-300">
                  Consider using bulk operations to tag multiple games at once
                </p>
              </div>
            )}

            {analytics.averageTagsPerGame < 2 && analytics.totalTaggedGames > 0 && (
              <div className="p-3 rounded-lg bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800">
                <p className="text-sm font-medium text-green-800 dark:text-green-200">
                  Add more tags per game
                </p>
                <p className="text-xs text-green-600 dark:text-green-300">
                  Multiple tags help with better filtering and organization
                </p>
              </div>
            )}

            {analytics.totalTags < 10 && (
              <div className="p-3 rounded-lg bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800">
                <p className="text-sm font-medium text-purple-800 dark:text-purple-200">
                  Create more tag categories
                </p>
                <p className="text-xs text-purple-600 dark:text-purple-300">
                  Try tags like 'favorites', 'multiplayer', 'completed', 'wishlist'
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}