import { describe, it, expect } from 'vitest';
import { Monitor, Gamepad2, Smartphone, Zap, HelpCircle } from 'lucide-react';

import {
  categorizePlatform,
  categorizePlatformsByFamily,
  groupGamesByPlatformFamily,
  calculatePlatformFamilyStats,
  getPlatformFamilyIcon,
  getPlatformFamilyColor,
  getPlatformFamilyConfig,
  getAllPlatformFamilies,
  isPlatformInFamily,
  getPlatformsInFamily,
  filterGamesByPlatformFamily,
  getLibraryPlatformFamilyBreakdown,
  type PlatformFamily,
} from '@/lib/utils/platformFamilyUtils';

import {
  createMockUserGameWithDetails,
  createMockGameCollection,
  TEST_PLATFORMS,
  assertPlatformFamilyStats,
} from '@/__tests__/utils/test-helpers';

describe('platformFamilyUtils', () => {
  describe('categorizePlatform', () => {
    it('should categorize PC platforms correctly', () => {
      TEST_PLATFORMS.PC.forEach(platform => {
        expect(categorizePlatform(platform)).toBe('PC');
      });
    });

    it('should categorize Xbox platforms correctly', () => {
      TEST_PLATFORMS.XBOX.forEach(platform => {
        expect(categorizePlatform(platform)).toBe('Xbox');
      });
    });

    it('should categorize PlayStation platforms correctly', () => {
      TEST_PLATFORMS.PLAYSTATION.forEach(platform => {
        expect(categorizePlatform(platform)).toBe('PlayStation');
      });
    });

    it('should categorize Nintendo platforms correctly', () => {
      TEST_PLATFORMS.NINTENDO.forEach(platform => {
        expect(categorizePlatform(platform)).toBe('Nintendo');
      });
    });

    it('should categorize Mobile platforms correctly', () => {
      TEST_PLATFORMS.MOBILE.forEach(platform => {
        expect(categorizePlatform(platform)).toBe('Mobile');
      });
    });

    it('should handle case-insensitive platform names', () => {
      expect(categorizePlatform('pc')).toBe('PC');
      expect(categorizePlatform('XBOX')).toBe('Xbox');
      expect(categorizePlatform('playstation')).toBe('PlayStation');
      expect(categorizePlatform('nintendo switch')).toBe('Nintendo');
      expect(categorizePlatform('IOS')).toBe('Mobile');
    });

    it('should handle partial matching for common variations', () => {
      expect(categorizePlatform('Xbox Game Pass')).toBe('Xbox');
      expect(categorizePlatform('PlayStation Network')).toBe('PlayStation');
      expect(categorizePlatform('Nintendo eShop')).toBe('Nintendo');
      expect(categorizePlatform('PC Steam')).toBe('PC');
      expect(categorizePlatform('Mobile iOS')).toBe('Mobile');
    });

    it('should categorize unknown platforms as Other', () => {
      TEST_PLATFORMS.OTHER.forEach(platform => {
        expect(categorizePlatform(platform)).toBe('Other');
      });
      expect(categorizePlatform('Unknown Platform')).toBe('Other');
      expect(categorizePlatform('')).toBe('Other');
    });

    it('should handle whitespace in platform names', () => {
      expect(categorizePlatform('  PC  ')).toBe('PC');
      expect(categorizePlatform(' Xbox One ')).toBe('Xbox');
    });
  });

  describe('categorizePlatformsByFamily', () => {
    it('should categorize multiple platforms correctly', () => {
      const platforms = ['PC', 'Xbox One', 'PlayStation 4'];
      const families = categorizePlatformsByFamily(platforms);
      
      expect(families).toContain('PC');
      expect(families).toContain('Xbox');
      expect(families).toContain('PlayStation');
      expect(families).toHaveLength(3);
    });

    it('should deduplicate families from multiple platforms', () => {
      const platforms = ['Xbox One', 'Xbox Series X', 'Xbox 360'];
      const families = categorizePlatformsByFamily(platforms);
      
      expect(families).toEqual(['Xbox']);
    });

    it('should handle empty platform array', () => {
      const families = categorizePlatformsByFamily([]);
      expect(families).toEqual(['Other']);
    });

    it('should handle null/undefined platforms', () => {
      const families = categorizePlatformsByFamily(null as any);
      expect(families).toEqual(['Other']);
    });
  });

  describe('calculatePlatformFamilyStats', () => {
    it('should calculate stats correctly for games with various statuses', () => {
      const games = createMockGameCollection([
        { id: '1', platform: 'PC', status: 'playing', personalRating: 8.5, hoursPlayed: 10.5 },
        { id: '2', platform: 'PC', status: 'completed', personalRating: 9.0, hoursPlayed: 25.0 },
        { id: '3', platform: 'PC', status: 'backlog', personalRating: 7.0 },
        { id: '4', platform: 'PC', status: 'wishlist' },
        { id: '5', platform: 'PC', status: 'owned', personalRating: 6.5, hoursPlayed: 5.0 },
      ]);

      const stats = calculatePlatformFamilyStats(games);

      assertPlatformFamilyStats(stats, {
        total: 5,
        playing: 1,
        completed: 1,
        backlog: 1,
        wishlist: 1,
        owned: 1,
        completionRate: 25, // 1 completed out of 4 non-wishlist games
        averageRating: 7.8, // (8.5 + 9.0 + 7.0 + 6.5) / 4
        totalHours: 40.5, // 10.5 + 25.0 + 5.0
      });
    });

    it('should handle empty games array', () => {
      const stats = calculatePlatformFamilyStats([]);

      assertPlatformFamilyStats(stats, {
        total: 0,
        playing: 0,
        completed: 0,
        backlog: 0,
        wishlist: 0,
        owned: 0,
        completionRate: 0,
      });

      expect(stats.averageRating).toBeUndefined();
      expect(stats.totalHours).toBeUndefined();
    });

    it('should handle games without ratings or hours', () => {
      const games = createMockGameCollection([
        { id: '1', platform: 'PC', status: 'completed' },
        { id: '2', platform: 'PC', status: 'playing' },
      ]);

      const stats = calculatePlatformFamilyStats(games);

      assertPlatformFamilyStats(stats, {
        total: 2,
        completionRate: 50, // 1 completed out of 2 games
      });

      expect(stats.averageRating).toBeUndefined();
      expect(stats.totalHours).toBeUndefined();
    });

    it('should calculate completion rate correctly with wishlist games', () => {
      const games = createMockGameCollection([
        { id: '1', platform: 'PC', status: 'completed' },
        { id: '2', platform: 'PC', status: 'wishlist' },
        { id: '3', platform: 'PC', status: 'wishlist' },
        { id: '4', platform: 'PC', status: 'playing' },
      ]);

      const stats = calculatePlatformFamilyStats(games);

      assertPlatformFamilyStats(stats, {
        total: 4,
        completionRate: 50, // 1 completed out of 2 non-wishlist games
      });
    });
  });

  describe('groupGamesByPlatformFamily', () => {
    it('should group games by platform family correctly', () => {
      const games = createMockGameCollection([
        { id: '1', platform: 'PC', status: 'playing' },
        { id: '2', platform: 'Xbox One', status: 'completed' },
        { id: '3', platform: 'PlayStation 4', status: 'backlog' },
        { id: '4', platform: 'PC', status: 'owned' },
        { id: '5', platform: 'Nintendo Switch', status: 'wishlist' },
      ]);

      const groups = groupGamesByPlatformFamily(games);

      expect(groups).toHaveLength(4); // PC, Xbox, PlayStation, Nintendo
      
      const pcGroup = groups.find(g => g.family === 'PC');
      expect(pcGroup?.games).toHaveLength(2);
      expect(pcGroup?.platforms).toEqual(['PC']);

      const xboxGroup = groups.find(g => g.family === 'Xbox');
      expect(xboxGroup?.games).toHaveLength(1);
      expect(xboxGroup?.platforms).toEqual(['Xbox One']);

      const playstationGroup = groups.find(g => g.family === 'PlayStation');
      expect(playstationGroup?.games).toHaveLength(1);
      expect(playstationGroup?.platforms).toEqual(['PlayStation 4']);

      const nintendoGroup = groups.find(g => g.family === 'Nintendo');
      expect(nintendoGroup?.games).toHaveLength(1);
      expect(nintendoGroup?.platforms).toEqual(['Nintendo Switch']);
    });

    it('should handle games without platform information', () => {
      const gameWithoutPlatform = createMockUserGameWithDetails('1', 'PC', 'playing');
      gameWithoutPlatform.game!.platform = null as any;

      const groups = groupGamesByPlatformFamily([gameWithoutPlatform]);

      expect(groups).toHaveLength(1);
      expect(groups[0].family).toBe('Other');
      expect(groups[0].games).toHaveLength(1);
    });

    it('should handle empty games array', () => {
      const groups = groupGamesByPlatformFamily([]);
      expect(groups).toEqual([]);
    });

    it('should sort groups by family priority', () => {
      const games = createMockGameCollection([
        { id: '1', platform: 'Nintendo Switch', status: 'playing' },
        { id: '2', platform: 'PC', status: 'completed' },
        { id: '3', platform: 'Xbox One', status: 'backlog' },
        { id: '4', platform: 'PlayStation 4', status: 'owned' },
        { id: '5', platform: 'iOS', status: 'wishlist' },
      ]);

      const groups = groupGamesByPlatformFamily(games);

      expect(groups.map(g => g.family)).toEqual(['PC', 'Xbox', 'PlayStation', 'Nintendo', 'Mobile']);
    });

    it('should group multiple platforms within the same family', () => {
      const games = createMockGameCollection([
        { id: '1', platform: 'Xbox One', status: 'playing' },
        { id: '2', platform: 'Xbox Series X', status: 'completed' },
        { id: '3', platform: 'Xbox 360', status: 'backlog' },
      ]);

      const groups = groupGamesByPlatformFamily(games);

      expect(groups).toHaveLength(1);
      expect(groups[0].family).toBe('Xbox');
      expect(groups[0].platforms).toEqual(['Xbox 360', 'Xbox One', 'Xbox Series X']);
      expect(groups[0].games).toHaveLength(3);
    });
  });

  describe('getPlatformFamilyIcon', () => {
    const iconTests: Array<[PlatformFamily, typeof Monitor]> = [
      ['PC', Monitor],
      ['Xbox', Gamepad2],
      ['PlayStation', Gamepad2],
      ['Nintendo', Zap],
      ['Mobile', Smartphone],
      ['Other', HelpCircle],
    ];

    it.each(iconTests)('should return correct icon for %s family', (family, expectedIcon) => {
      expect(getPlatformFamilyIcon(family)).toBe(expectedIcon);
    });
  });

  describe('getPlatformFamilyColor', () => {
    const colorTests: Array<[PlatformFamily, string]> = [
      ['PC', '#0078D4'],
      ['Xbox', '#107C10'],
      ['PlayStation', '#003087'],
      ['Nintendo', '#E60012'],
      ['Mobile', '#8B5CF6'],
      ['Other', '#6B7280'],
    ];

    it.each(colorTests)('should return correct color for %s family', (family, expectedColor) => {
      expect(getPlatformFamilyColor(family)).toBe(expectedColor);
    });
  });

  describe('getPlatformFamilyConfig', () => {
    it('should return complete configuration for each family', () => {
      const pcConfig = getPlatformFamilyConfig('PC');
      
      expect(pcConfig.name).toBe('PC');
      expect(pcConfig.icon).toBe(Monitor);
      expect(pcConfig.color).toBe('#0078D4');
      expect(pcConfig.bgColor).toBe('bg-blue-50');
      expect(pcConfig.borderColor).toBe('border-blue-200');
      expect(pcConfig.textColor).toBe('text-blue-800');
    });
  });

  describe('getAllPlatformFamilies', () => {
    it('should return all platform families in correct order', () => {
      const families = getAllPlatformFamilies();
      expect(families).toEqual(['PC', 'Xbox', 'PlayStation', 'Nintendo', 'Mobile', 'Other']);
    });
  });

  describe('isPlatformInFamily', () => {
    const platformFamilyTests: Array<[string, PlatformFamily, boolean]> = [
      ['PC', 'PC', true],
      ['Xbox One', 'Xbox', true],
      ['PlayStation 4', 'PlayStation', true],
      ['Nintendo Switch', 'Nintendo', true],
      ['iOS', 'Mobile', true],
      ['PC', 'Xbox', false],
      ['Xbox One', 'PlayStation', false],
    ];

    it.each(platformFamilyTests)('should correctly identify if %s belongs to %s family', (platform, family, expected) => {
      expect(isPlatformInFamily(platform, family)).toBe(expected);
    });
  });

  describe('getPlatformsInFamily', () => {
    it('should return all platforms in Xbox family', () => {
      const xboxPlatforms = getPlatformsInFamily('Xbox');
      
      expect(xboxPlatforms).toContain('Xbox');
      expect(xboxPlatforms).toContain('Xbox 360');
      expect(xboxPlatforms).toContain('Xbox One');
      expect(xboxPlatforms).toContain('Xbox Series X/S');
    });

    it('should return all platforms in Mobile family', () => {
      const mobilePlatforms = getPlatformsInFamily('Mobile');
      
      expect(mobilePlatforms).toContain('iOS');
      expect(mobilePlatforms).toContain('Android');
    });

    it('should return sorted platforms', () => {
      const platforms = getPlatformsInFamily('Xbox');
      const sortedPlatforms = [...platforms].sort();
      
      expect(platforms).toEqual(sortedPlatforms);
    });
  });

  describe('filterGamesByPlatformFamily', () => {
    it('should filter games by platform family', () => {
      const games = createMockGameCollection([
        { id: '1', platform: 'PC', status: 'playing' },
        { id: '2', platform: 'Xbox One', status: 'completed' },
        { id: '3', platform: 'PlayStation 4', status: 'backlog' },
        { id: '4', platform: 'PC', status: 'owned' },
      ]);

      const pcGames = filterGamesByPlatformFamily(games, 'PC');
      expect(pcGames).toHaveLength(2);
      expect(pcGames.every(game => game.game?.platform === 'PC')).toBe(true);

      const xboxGames = filterGamesByPlatformFamily(games, 'Xbox');
      expect(xboxGames).toHaveLength(1);
      expect(xboxGames[0].game?.platform).toBe('Xbox One');
    });

    it('should handle games without platform as Other family', () => {
      const gameWithoutPlatform = createMockUserGameWithDetails('1', 'PC', 'playing');
      gameWithoutPlatform.game!.platform = null as any;

      const otherGames = filterGamesByPlatformFamily([gameWithoutPlatform], 'Other');
      expect(otherGames).toHaveLength(1);
    });
  });

  describe('getLibraryPlatformFamilyBreakdown', () => {
    it('should provide breakdown of games by platform family', () => {
      const games = createMockGameCollection([
        { id: '1', platform: 'PC', status: 'playing' },
        { id: '2', platform: 'PC', status: 'completed' },
        { id: '3', platform: 'Xbox One', status: 'backlog' },
        { id: '4', platform: 'PlayStation 4', status: 'owned' },
        { id: '5', platform: 'Nintendo Switch', status: 'wishlist' },
        { id: '6', platform: 'iOS', status: 'playing' },
      ]);

      const breakdown = getLibraryPlatformFamilyBreakdown(games);

      expect(breakdown.PC).toBe(2);
      expect(breakdown.Xbox).toBe(1);
      expect(breakdown.PlayStation).toBe(1);
      expect(breakdown.Nintendo).toBe(1);
      expect(breakdown.Mobile).toBe(1);
      expect(breakdown.Other).toBe(0);
    });

    it('should handle games without platform information', () => {
      const gameWithoutPlatform = createMockUserGameWithDetails('1', 'PC', 'playing');
      gameWithoutPlatform.game!.platform = null as any;

      const breakdown = getLibraryPlatformFamilyBreakdown([gameWithoutPlatform]);

      expect(breakdown.Other).toBe(1);
      expect(breakdown.PC).toBe(0);
    });

    it('should handle empty games array', () => {
      const breakdown = getLibraryPlatformFamilyBreakdown([]);

      Object.values(breakdown).forEach(count => {
        expect(count).toBe(0);
      });
    });
  });
});