-- Additional Performance Indexes Migration
-- Adds advanced performance indexes not included in the base schema

-- Advanced user_games table optimization
-- Partial indexes for frequently filtered data (non-duplicate)
CREATE INDEX IF NOT EXISTS idx_user_games_active_library 
ON user_games(user_id, date_added DESC) 
WHERE status != 'wishlist';

CREATE INDEX IF NOT EXISTS idx_user_games_wishlist_only 
ON user_games(user_id, date_added DESC) 
WHERE status = 'wishlist';

-- Covering index for common user_games queries (includes more columns than base schema)
CREATE INDEX IF NOT EXISTS idx_user_games_covering 
ON user_games(user_id, status) 
INCLUDE (game_id, personal_rating, hours_played, date_added, is_wishlist);

-- Advanced games table search optimization
-- Composite index for game search with filters
CREATE INDEX IF NOT EXISTS idx_games_search_filters 
ON games(platform, release_date DESC, metacritic_score DESC) 
WHERE title IS NOT NULL;

-- Full-text search index for game titles (advanced search capability)
CREATE INDEX IF NOT EXISTS idx_games_title_search 
ON games USING gin(to_tsvector('english', title));

-- Index for popular games sorting
CREATE INDEX IF NOT EXISTS idx_games_popularity_score 
ON games(metacritic_score DESC, release_date DESC) 
WHERE metacritic_score IS NOT NULL;

-- Price tracking performance indexes
-- Covering index for price tracking queries
CREATE INDEX IF NOT EXISTS idx_price_tracking_covering 
ON price_tracking(game_id, store_name, last_updated DESC) 
INCLUDE (price, original_price, is_on_sale, discount_percentage);

-- Index for latest prices per store
CREATE INDEX IF NOT EXISTS idx_price_tracking_latest_per_store 
ON price_tracking(game_id, store_name, last_updated DESC);

-- Index for price tracking by last updated (removed predicate due to immutability requirement)
CREATE INDEX IF NOT EXISTS idx_price_tracking_recent 
ON price_tracking(game_id, last_updated DESC);

-- User recommendations, AI conversations, user preferences, and user collection stats indexes 
-- are already created in previous migrations or tables don't have the required columns

-- Comment explaining the performance improvements
COMMENT ON INDEX idx_user_games_active_library IS 'Optimizes queries for active game library (non-wishlist items)';
COMMENT ON INDEX idx_user_games_wishlist_only IS 'Optimizes wishlist-only queries';
COMMENT ON INDEX idx_games_title_search IS 'Enables fast full-text search on game titles';
COMMENT ON INDEX idx_price_tracking_covering IS 'Covering index for price queries to avoid table lookups';
COMMENT ON INDEX idx_price_tracking_recent IS 'Optimizes queries for recent price updates';