/* Accessibility and Color Contrast Enhancements */

/* Focus management and keyboard navigation */
:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
  border-radius: calc(var(--radius) - 2px);
}

/* Enhanced focus for interactive elements */
button:focus-visible,
[role="button"]:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
  box-shadow: 0 0 0 4px hsl(var(--ring) / 0.2);
}

/* Skip to main content link */
.skip-to-main {
  position: absolute;
  top: -40px;
  left: 6px;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  padding: 8px 16px;
  text-decoration: none;
  border-radius: var(--radius);
  z-index: 1000;
  font-weight: 600;
  transition: top 0.2s ease-in-out;
}

.skip-to-main:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --primary: 262 100% 40%;
    --primary-foreground: 0 0% 100%;
    --secondary: 173 100% 30%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 95%;
    --muted-foreground: 0 0% 20%;
    --accent: 43 100% 40%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 100% 40%;
    --destructive-foreground: 0 0% 100%;
    --success: 142 100% 30%;
    --success-foreground: 0 0% 100%;
    --warning: 43 100% 40%;
    --warning-foreground: 0 0% 100%;
    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 262 100% 50%;
  }
  
  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 5%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 5%;
    --popover-foreground: 0 0% 100%;
    --primary: 262 100% 70%;
    --primary-foreground: 0 0% 0%;
    --secondary: 173 100% 60%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 80%;
    --accent: 43 100% 60%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 100% 60%;
    --destructive-foreground: 0 0% 0%;
    --success: 142 100% 60%;
    --success-foreground: 0 0% 0%;
    --warning: 43 100% 60%;
    --warning-foreground: 0 0% 0%;
    --border: 0 0% 80%;
    --input: 0 0% 80%;
    --ring: 262 100% 70%;
  }

  /* Enhanced borders and outlines in high contrast */
  button,
  [role="button"],
  input,
  select,
  textarea {
    border: 2px solid hsl(var(--border));
  }

  /* Ensure sufficient contrast for interactive elements */
  .hover\:bg-accent\/50:hover {
    background-color: hsl(var(--accent) / 0.8) !important;
  }

  .hover\:bg-muted\/50:hover {
    background-color: hsl(var(--muted) / 0.8) !important;
  }
}

/* Forced colors mode (Windows High Contrast) */
@media (forced-colors: active) {
  :root {
    --background: Canvas;
    --foreground: CanvasText;
    --card: Canvas;
    --card-foreground: CanvasText;
    --popover: Canvas;
    --popover-foreground: CanvasText;
    --primary: Highlight;
    --primary-foreground: HighlightText;
    --secondary: ButtonFace;
    --secondary-foreground: ButtonText;
    --muted: Canvas;
    --muted-foreground: GrayText;
    --accent: Highlight;
    --accent-foreground: HighlightText;
    --destructive: Highlight;
    --destructive-foreground: HighlightText;
    --border: ButtonBorder;
    --input: Field;
    --ring: Highlight;
  }

  /* Ensure borders are visible in forced colors mode */
  button,
  [role="button"],
  input,
  select,
  textarea,
  .border {
    border: 1px solid ButtonBorder !important;
  }

  /* Focus indicators in forced colors mode */
  :focus-visible {
    outline: 2px solid Highlight !important;
    outline-offset: 2px;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Disable theme transitions for reduced motion */
  .theme-changing * {
    transition: none !important;
  }
}

/* Color scheme preference integration */
@media (prefers-color-scheme: dark) {
  :root:not(.light):not(.dark) {
    color-scheme: dark;
  }
}

@media (prefers-color-scheme: light) {
  :root:not(.light):not(.dark) {
    color-scheme: light;
  }
}

/* Enhanced keyboard navigation for theme toggle */
.theme-toggle-dropdown[data-state="open"] {
  animation: theme-dropdown-enter 0.2s ease-out;
}

@keyframes theme-dropdown-enter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Ensure proper contrast for theme toggle states */
.theme-toggle-item[aria-current="true"] {
  background-color: hsl(var(--accent) / 0.2);
  border: 1px solid hsl(var(--accent) / 0.5);
}

.theme-toggle-item:focus-visible {
  background-color: hsl(var(--accent) / 0.1);
  outline: 2px solid hsl(var(--ring));
  outline-offset: -2px;
}

/* Announce theme changes to screen readers */
.theme-announcement {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Color contrast validation helpers */
.contrast-aa {
  /* Ensures WCAG AA compliance (4.5:1 ratio) */
  color: hsl(var(--foreground));
  background-color: hsl(var(--background));
}

.contrast-aaa {
  /* Ensures WCAG AAA compliance (7:1 ratio) */
  color: hsl(var(--foreground));
  background-color: hsl(var(--background));
  font-weight: 600;
}

/* Enhanced focus for theme toggle components */
.theme-toggle:focus-within {
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
  border-radius: var(--radius);
}

/* Ensure theme toggle icons have proper contrast */
.theme-toggle svg {
  color: hsl(var(--foreground));
  transition: color var(--theme-transition-duration) var(--theme-transition-timing);
}

.theme-toggle:hover svg {
  color: hsl(var(--primary));
}

/* System theme indicator styling */
.system-theme-indicator {
  position: relative;
}

.system-theme-indicator::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: hsl(var(--primary));
  border-radius: 50%;
  opacity: 0.7;
}

/* Theme transition loading state */
.theme-loading {
  pointer-events: none;
  opacity: 0.7;
}

.theme-loading * {
  cursor: wait !important;
}
