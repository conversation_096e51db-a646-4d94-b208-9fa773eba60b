import { memo, useMemo } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { Game } from '@/types';

interface VirtualizedGameGridProps {
  games: Game[];
  onGameClick: (game: Game) => void;
  containerWidth: number;
  containerHeight: number;
}

interface GridItemProps {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    games: Game[];
    onGameClick: (game: Game) => void;
    columnsPerRow: number;
  };
}

const GridItem = memo<GridItemProps>(({ columnIndex, rowIndex, style, data }) => {
  const { games, onGameClick, columnsPerRow } = data;
  const gameIndex = rowIndex * columnsPerRow + columnIndex;
  const game = games[gameIndex];

  if (!game) {
    return <div style={style} />;
  }

  return (
    <div style={{ ...style, padding: '8px' }}>
      <div
        className="group cursor-pointer transition-all duration-300 hover:scale-[1.03] hover:shadow-2xl border border-border/50 hover:border-primary/50 overflow-hidden bg-gradient-to-br from-background to-background/80 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
        onClick={() => onGameClick(game)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onGameClick(game);
          }
        }}
        tabIndex={0}
        role="button"
        aria-label={`View details for ${game.title}`}
      >
        {/* Cover Image */}
        <div className="aspect-[2/3] bg-gradient-to-br from-muted/50 to-muted/80 relative overflow-hidden">
          {game.cover_image ? (
            <img
              src={game.cover_image}
              alt={game.title}
              className="w-full h-full object-cover object-center transition-transform duration-300 group-hover:scale-105"
              loading="lazy"
              decoding="async"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-muted/40 to-muted/60">
              <div className="text-center text-muted-foreground">
                <div className="text-xs font-medium">No Cover Art</div>
              </div>
            </div>
          )}

          {/* Rating Badge */}
          {game.metacritic_score && (
            <div className="absolute top-2 right-2">
              <div className={`shadow-lg text-xs font-semibold px-2 py-1 rounded ${
                game.metacritic_score >= 85
                  ? 'bg-emerald-500 text-white' :
                game.metacritic_score >= 70
                  ? 'bg-yellow-500 text-white' :
                game.metacritic_score >= 50
                  ? 'bg-orange-500 text-white' :
                  'bg-red-500 text-white'
              }`}>
                {game.metacritic_score}
              </div>
            </div>
          )}

          {/* Title overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/95 via-black/85 to-transparent text-white p-2 transform translate-y-full group-hover:translate-y-0 transition-all duration-300 ease-out">
            <h3 className="font-bold text-xs leading-tight line-clamp-2 text-white">{game.title}</h3>
            <div className="text-xs text-gray-200 mt-1">
              {game.release_date ? new Date(game.release_date).getFullYear() : 'Unknown'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

GridItem.displayName = 'GridItem';

export const VirtualizedGameGrid = memo<VirtualizedGameGridProps>(({
  games,
  onGameClick,
  containerWidth,
  containerHeight
}) => {
  const { columnsPerRow, rowCount, columnWidth, rowHeight } = useMemo(() => {
    // Calculate responsive columns based on container width
    let cols = 2; // mobile default
    if (containerWidth >= 1536) cols = 7; // 2xl
    else if (containerWidth >= 1280) cols = 6; // xl
    else if (containerWidth >= 1024) cols = 5; // lg
    else if (containerWidth >= 768) cols = 4; // md
    else if (containerWidth >= 640) cols = 3; // sm

    const colWidth = Math.floor(containerWidth / cols);
    const rowHeight = Math.floor(colWidth * 1.8); // Maintain aspect ratio
    const rows = Math.ceil(games.length / cols);

    return {
      columnsPerRow: cols,
      rowCount: rows,
      columnWidth: colWidth,
      rowHeight
    };
  }, [containerWidth, games.length]);

  const itemData = useMemo(() => ({
    games,
    onGameClick,
    columnsPerRow
  }), [games, onGameClick, columnsPerRow]);

  return (
    <Grid
      columnCount={columnsPerRow}
      columnWidth={columnWidth}
      height={containerHeight}
      rowCount={rowCount}
      rowHeight={rowHeight}
      width={containerWidth}
      itemData={itemData}
    >
      {GridItem}
    </Grid>
  );
});

VirtualizedGameGrid.displayName = 'VirtualizedGameGrid';