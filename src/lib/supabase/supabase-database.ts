import { supabase } from "./supabase-client";
import {
  GameRecord,
  GameUpdateData,
  UserGameRecord,
  UserGameUpdateData,
  PriceTrackingRecord,
  PriceTrackingUpdateData,
  PriceAlertRecord,
  PriceAlertUpdateData,
  UserProfileRecord,
  UserProfileUpdateData,
  UserPreferencesRecord,
  UserPreferencesUpdateData,
  UserRecommendationRecord,
  UserRecommendationUpdateData,
} from "../../types/database";

/**
 * Helper function to ensure user records exist
 */
export async function ensureUserRecords(userId: string): Promise<void> {
  try {
    // Check if user profile exists
    const { error: profileError } = await supabase
      .from("user_profiles")
      .select("id")
      .eq("id", userId)
      .single();

    if (profileError && profileError.code === "PGRST116") {
      // Profile doesn't exist, create it
      const { error: createProfileError } = await supabase
        .from("user_profiles")
        .insert({ id: userId });

      if (createProfileError) {
        console.warn("Failed to create user profile:", createProfileError);
      }
    }

    // Check if user preferences exist
    const { error: preferencesError } = await supabase
      .from("user_preferences")
      .select("id")
      .eq("user_id", userId)
      .single();

    if (preferencesError && preferencesError.code === "PGRST116") {
      // Preferences don't exist, create them
      const { error: createPreferencesError } = await supabase
        .from("user_preferences")
        .insert({ user_id: userId });

      if (createPreferencesError) {
        console.warn(
          "Failed to create user preferences:",
          createPreferencesError
        );
      }
    }
  } catch (error) {
    console.warn("Error ensuring user records:", error);
  }
}

// Games database operations
export const games = {
  getAll: () => supabase.from("games").select("*"),

  getById: (id: string) =>
    supabase.from("games").select("*").eq("id", id).single(),

  // Helper function to get game by external ID (tgdb_123, igdb_456, etc.)
  getByExternalId: (externalId: string) => {
    if (externalId.startsWith("tgdb_")) {
      const tgdbId = externalId.replace("tgdb_", "");
      return supabase.from("games").select("*").eq("tgdb_id", tgdbId).single();
    } else if (externalId.startsWith("igdb_")) {
      const igdbId = externalId.replace("igdb_", "");
      return supabase.from("games").select("*").eq("igdb_id", igdbId).single();
    } else if (externalId.startsWith("steam_")) {
      const steamId = externalId.replace("steam_", "");
      return supabase
        .from("games")
        .select("*")
        .eq("steam_app_id", steamId)
        .single();
    } else {
      // Fallback to UUID lookup
      return supabase.from("games").select("*").eq("id", externalId).single();
    }
  },

  getBySteamAppId: (steamAppId: string) =>
    supabase.from("games").select("*").eq("steam_app_id", steamAppId).single(),

  getByTitle: (title: string) =>
    supabase.from("games").select("*").ilike("title", title).single(),

  create: (game: Omit<GameRecord, "id" | "created_at" | "updated_at">) =>
    supabase.from("games").insert(game).select().single(),

  upsert: (game: Omit<GameRecord, "id" | "created_at" | "updated_at">) =>
    supabase
      .from("games")
      .upsert(game, { onConflict: "steam_app_id" })
      .select()
      .single(),

  update: (id: string, updates: GameUpdateData) =>
    supabase.from("games").update(updates).eq("id", id).select().single(),

  delete: (id: string) => supabase.from("games").delete().eq("id", id),
};

// User games database operations
export const userGames = {
  getUserCollection: async (userId: string) => {
    // Ensure user records exist before querying
    await ensureUserRecords(userId);

    return supabase
      .from("user_games")
      .select(
        `
        *,
        game:games(*)
      `
      )
      .eq("user_id", userId)
      .order("date_added", { ascending: false });
  },

  addToCollection: (
    userGame: Omit<UserGameRecord, "id" | "created_at" | "updated_at">
  ) => supabase.from("user_games").insert(userGame).select().single(),

  upsertToCollection: (
    userGame: Omit<UserGameRecord, "id" | "created_at" | "updated_at">
  ) =>
    supabase
      .from("user_games")
      .upsert(userGame, { onConflict: "user_id,game_id" })
      .select()
      .single(),

  getByUserAndGame: (userId: string, gameId: string) =>
    supabase
      .from("user_games")
      .select("*")
      .eq("user_id", userId)
      .eq("game_id", gameId)
      .single(),

  updateStatus: (id: string, updates: UserGameUpdateData) =>
    supabase.from("user_games").update(updates).eq("id", id).select().single(),

  removeFromCollection: (id: string) =>
    supabase.from("user_games").delete().eq("id", id),
};

// Price tracking database operations
export const priceTracking = {
  getGamePrices: (gameId: string) =>
    supabase
      .from("price_tracking")
      .select("*")
      .eq("game_id", gameId)
      .order("last_updated", { ascending: false }),

  getGamePriceHistory: (gameId: string, days: number = 30) => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    return supabase
      .from("price_tracking")
      .select("*")
      .eq("game_id", gameId)
      .gte("last_updated", cutoffDate.toISOString())
      .order("last_updated", { ascending: true });
  },

  getLowestPrices: (gameId: string) =>
    supabase
      .from("price_tracking")
      .select("*")
      .eq("game_id", gameId)
      .order("price", { ascending: true })
      .limit(1)
      .single(),

  getCurrentPrices: (gameId: string) =>
    supabase
      .from("price_tracking")
      .select("*")
      .eq("game_id", gameId)
      .order("last_updated", { ascending: false })
      .limit(10),

  addPriceEntry: (priceData: Omit<PriceTrackingRecord, "id" | "created_at">) =>
    supabase.from("price_tracking").insert(priceData).select().single(),

  updatePriceEntry: (id: string, updates: PriceTrackingUpdateData) =>
    supabase
      .from("price_tracking")
      .update(updates)
      .eq("id", id)
      .select()
      .single(),

  getPriceAlerts: (userId: string) =>
    supabase
      .from("price_alerts")
      .select(
        `
        *,
        game:games(*)
      `
      )
      .eq("user_id", userId)
      .eq("is_active", true),

  createPriceAlert: (
    alertData: Omit<PriceAlertRecord, "id" | "created_at" | "updated_at">
  ) => supabase.from("price_alerts").insert(alertData).select().single(),

  updatePriceAlert: (id: string, updates: PriceAlertUpdateData) =>
    supabase
      .from("price_alerts")
      .update(updates)
      .eq("id", id)
      .select()
      .single(),

  deletePriceAlert: (id: string) =>
    supabase.from("price_alerts").delete().eq("id", id),

  getWishlistWithPrices: async (userId: string) => {
    // Ensure user records exist before querying
    await ensureUserRecords(userId);

    return supabase
      .from("user_games")
      .select(
        `
        *,
        game:games(*)
      `
      )
      .eq("user_id", userId)
      .eq("status", "wishlist")
      .order("date_added", { ascending: false });
  },
};

// User profiles database operations
export const userProfiles = {
  getProfile: async (userId: string) => {
    // Ensure user records exist before querying
    await ensureUserRecords(userId);

    return supabase.from("user_profiles").select("*").eq("id", userId).single();
  },

  updateProfile: (userId: string, updates: UserProfileUpdateData) =>
    supabase
      .from("user_profiles")
      .update(updates)
      .eq("id", userId)
      .select()
      .single(),

  createProfile: (
    profileData: Omit<UserProfileRecord, "created_at" | "updated_at">
  ) => supabase.from("user_profiles").insert(profileData).select().single(),
};

// User preferences database operations
export const userPreferences = {
  getPreferences: async (userId: string) => {
    // Ensure user records exist before querying
    await ensureUserRecords(userId);

    return supabase
      .from("user_preferences")
      .select("*")
      .eq("user_id", userId)
      .single();
  },

  updatePreferences: (userId: string, updates: UserPreferencesUpdateData) =>
    supabase
      .from("user_preferences")
      .update(updates)
      .eq("user_id", userId)
      .select()
      .single(),

  createPreferences: (
    preferencesData: Omit<
      UserPreferencesRecord,
      "id" | "created_at" | "updated_at"
    >
  ) =>
    supabase.from("user_preferences").insert(preferencesData).select().single(),
};

// Stats database operations
export const stats = {
  getUserCollectionStats: async (userId: string) => {
    // Ensure user records exist before querying
    await ensureUserRecords(userId);

    try {
      // Calculate stats dynamically from user_games table
      const { data: userGames, error: gamesError } = await supabase
        .from("user_games")
        .select("status, hours_played, personal_rating")
        .eq("user_id", userId);

      if (gamesError) {
        console.error("Error fetching user games for stats:", gamesError);
        throw gamesError;
      }

      // Calculate wishlist count from user_games table
      const { count: wishlistCount, error: wishlistError } = await supabase
        .from("user_games")
        .select("*", { count: "exact", head: true })
        .eq("user_id", userId)
        .or("is_wishlist.eq.true,status.eq.wishlist");

      if (wishlistError) {
        console.error("Error fetching wishlist count:", wishlistError);
        throw wishlistError;
      }

      // Calculate stats from the data
      const totalGames = userGames?.length || 0;
      const completedGames =
        userGames?.filter((game) => game.status === "completed").length || 0;
      const totalHours =
        userGames?.reduce((sum, game) => sum + (game.hours_played || 0), 0) ||
        0;
      const averageRating =
        userGames?.length > 0
          ? userGames
              .filter((game) => game.personal_rating)
              .reduce((sum, game) => sum + (game.personal_rating || 0), 0) /
            userGames.filter((game) => game.personal_rating).length
          : 0;

      return {
        data: {
          user_id: userId,
          total_games: totalGames,
          completed_games: completedGames,
          wishlist_count: wishlistCount || 0,
          total_hours: totalHours,
          average_rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
          completion_rate:
            totalGames > 0
              ? Math.round((completedGames / totalGames) * 100)
              : 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        error: null,
      };
    } catch (error) {
      console.error("Error calculating user collection stats:", error);
      return {
        data: null,
        error,
      };
    }
  },
};

// Recommendations database operations
export const recommendations = {
  getUserBasedRecommendations: async (userId: string, limit: number = 10) => {
    await ensureUserRecords(userId);

    return supabase
      .from("user_recommendations")
      .select("*")
      .eq("user_id", userId)
      .eq("recommendation_type", "user_based")
      .order("confidence", { ascending: false })
      .order("created_at", { ascending: false })
      .limit(limit);
  },

  getSimilarGameRecommendations: async (
    userId: string,
    sourceGameId: string,
    limit: number = 6
  ) => {
    await ensureUserRecords(userId);

    return supabase
      .from("user_recommendations")
      .select("*")
      .eq("user_id", userId)
      .eq("recommendation_type", "similar_game")
      .eq("source_game_id", sourceGameId)
      .order("confidence", { ascending: false })
      .order("created_at", { ascending: false })
      .limit(limit);
  },

  saveRecommendations: async (
    recommendations: Omit<
      UserRecommendationRecord,
      "id" | "created_at" | "updated_at"
    >[]
  ) => {
    return supabase
      .from("user_recommendations")
      .upsert(recommendations, {
        onConflict: "user_id,igdb_id,recommendation_type,source_game_id",
      })
      .select();
  },

  clearUserRecommendations: async (
    userId: string,
    recommendationType?: "user_based" | "similar_game"
  ) => {
    let query = supabase
      .from("user_recommendations")
      .delete()
      .eq("user_id", userId);

    if (recommendationType) {
      query = query.eq("recommendation_type", recommendationType);
    }

    return query;
  },

  updateRecommendation: (id: string, updates: UserRecommendationUpdateData) =>
    supabase
      .from("user_recommendations")
      .update(updates)
      .eq("id", id)
      .select()
      .single(),

  deleteRecommendation: (id: string) =>
    supabase.from("user_recommendations").delete().eq("id", id),
};
