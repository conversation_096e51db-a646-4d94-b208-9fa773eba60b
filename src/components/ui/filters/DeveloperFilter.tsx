import { useState } from 'react';
import { Checkbox } from '@/components/ui/base/checkbox';
import { Input } from '@/components/ui/base/input';
import { But<PERSON> } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { Switch } from '@/components/ui/base/switch';
import { Label } from '@/components/ui/base/label';
import { Search, X, Plus } from 'lucide-react';
import { DeveloperFilter } from '@/types/filters';

interface DeveloperFilterComponentProps {
  filter: DeveloperFilter;
  onChange: (update: Partial<DeveloperFilter>) => void;
  disabled?: boolean;
}

// Popular game developers - this could be fetched from API in the future
const POPULAR_DEVELOPERS = [
  'Valve Corporation',
  'Rockstar Games',
  'CD Projekt RED',
  'Naughty Dog',
  'Insomniac Games',
  'Santa Monica Studio',
  'FromSoftware',
  'Bethesda Game Studios',
  'id Software',
  'Machine Games',
  'Arkane Studios',
  'Nintendo EPD',
  'Retro Studios',
  'PlatinumGames',
  'Capcom',
  'Square Enix',
  'Konami',
  'Bandai Namco',
  'Ubisoft Montreal',
  'Ubisoft Paris',
  'Electronic Arts',
  'BioWare',
  'Respawn Entertainment',
  'DICE',
  'Epic Games',
  'Bungie',
  '343 Industries',
  'Turn 10 Studios',
  'The Coalition',
  'Playground Games',
  'Mojang Studios',
  'Blizzard Entertainment',
  'Activision',
  'Treyarch',
  'Infinity Ward',
  'Sledgehammer Games',
  'Riot Games',
  'Supercell',
  'Team Cherry',
  'Re-Logic',
  'ConcernedApe',
  'Motion Twin',
  'Klei Entertainment',
  'Devolver Digital',
  'Annapurna Interactive'
];

export function DeveloperFilterComponent({ 
  filter, 
  onChange, 
  disabled = false 
}: DeveloperFilterComponentProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [customDeveloper, setCustomDeveloper] = useState('');

  const filteredDevelopers = POPULAR_DEVELOPERS.filter(developer => 
    developer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleToggleEnabled = (enabled: boolean) => {
    onChange({ enabled });
  };

  const handleDeveloperToggle = (developer: string) => {
    const isSelected = filter.developers.includes(developer);
    const newDevelopers = isSelected
      ? filter.developers.filter(d => d !== developer)
      : [...filter.developers, developer];
    
    onChange({ developers: newDevelopers });
  };

  const handleAddCustomDeveloper = () => {
    if (customDeveloper.trim() && !filter.developers.includes(customDeveloper.trim())) {
      onChange({ 
        developers: [...filter.developers, customDeveloper.trim()]
      });
      setCustomDeveloper('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddCustomDeveloper();
    }
  };

  const removeDeveloper = (developer: string) => {
    onChange({ 
      developers: filter.developers.filter(d => d !== developer)
    });
  };

  const clearAll = () => {
    onChange({ developers: [] });
  };

  const selectPopular = () => {
    const popularToAdd = POPULAR_DEVELOPERS.slice(0, 10);
    const newDevelopers = [...new Set([...filter.developers, ...popularToAdd])];
    onChange({ developers: newDevelopers });
  };

  return (
    <div className="space-y-4">
      {/* Enable/Disable Toggle */}
      <div className="flex items-center justify-between">
        <Label htmlFor="developer-enabled" className="text-sm font-medium">
          Filter by Developer
        </Label>
        <Switch
          id="developer-enabled"
          checked={filter.enabled}
          onCheckedChange={handleToggleEnabled}
          disabled={disabled}
        />
      </div>

      {filter.enabled && (
        <>
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search developers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              disabled={disabled}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery('')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Add Custom Developer */}
          <div className="flex gap-2">
            <Input
              placeholder="Add custom developer..."
              value={customDeveloper}
              onChange={(e) => setCustomDeveloper(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
              disabled={disabled}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddCustomDeveloper}
              disabled={disabled || !customDeveloper.trim()}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {/* Selected Developers */}
          {filter.developers.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">
                  Selected Developers ({filter.developers.length})
                </Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAll}
                  className="text-xs h-6"
                  disabled={disabled}
                >
                  Clear All
                </Button>
              </div>
              <div className="flex flex-wrap gap-1 max-h-24 overflow-y-auto">
                {filter.developers.map(developer => (
                  <Badge
                    key={developer}
                    variant="secondary"
                    className="text-xs flex items-center gap-1"
                  >
                    {developer}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeDeveloper(developer)}
                      className="h-3 w-3 p-0 hover:bg-destructive hover:text-destructive-foreground"
                      disabled={disabled}
                    >
                      <X className="h-2 w-2" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={selectPopular}
              className="text-xs flex-1"
              disabled={disabled}
            >
              Add Popular Developers
            </Button>
          </div>

          {/* Developer Selection */}
          <ScrollArea className="h-48">
            <div className="space-y-2">
              {searchQuery ? (
                // Show filtered results when searching
                <>
                  {filteredDevelopers.map(developer => (
                    <div key={developer} className="flex items-center space-x-2">
                      <Checkbox
                        id={`developer-${developer}`}
                        checked={filter.developers.includes(developer)}
                        onCheckedChange={() => handleDeveloperToggle(developer)}
                        disabled={disabled}
                      />
                      <Label
                        htmlFor={`developer-${developer}`}
                        className="text-sm cursor-pointer flex-1"
                      >
                        {developer}
                      </Label>
                    </div>
                  ))}
                  {filteredDevelopers.length === 0 && (
                    <div className="text-center py-4">
                      <p className="text-sm text-muted-foreground mb-2">
                        No developers found matching "{searchQuery}"
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setCustomDeveloper(searchQuery);
                          setSearchQuery('');
                        }}
                        className="text-xs"
                        disabled={disabled}
                      >
                        Add "{searchQuery}" as custom developer
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                // Show popular developers when not searching
                <>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Popular Developers
                  </Label>
                  {POPULAR_DEVELOPERS.map(developer => (
                    <div key={developer} className="flex items-center space-x-2">
                      <Checkbox
                        id={`developer-${developer}`}
                        checked={filter.developers.includes(developer)}
                        onCheckedChange={() => handleDeveloperToggle(developer)}
                        disabled={disabled}
                      />
                      <Label
                        htmlFor={`developer-${developer}`}
                        className="text-sm cursor-pointer flex-1"
                      >
                        {developer}
                      </Label>
                    </div>
                  ))}
                </>
              )}
            </div>
          </ScrollArea>

          {/* Filter Description */}
          <div className="text-xs text-muted-foreground p-2 bg-muted/50 rounded">
            Show games developed by any of the selected developers. You can search for 
            developers or add custom ones not in the popular list.
          </div>
        </>
      )}
    </div>
  );
}