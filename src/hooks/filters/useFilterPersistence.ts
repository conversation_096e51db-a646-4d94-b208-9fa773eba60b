import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { GameFilters, DEFAULT_FILTERS, SortField } from '@/types/filters';
import { Platform } from '@/types';

interface UseFilterPersistenceOptions {
  persistToURL?: boolean;
}

export function useFilterPersistence(
  filters: GameFilters,
  searchQuery: string,
  options: UseFilterPersistenceOptions = {}
) {
  const { persistToURL = false } = options;
  const [searchParams, setSearchParams] = useSearchParams();

  // Update URL when filters change
  useEffect(() => {
    if (!persistToURL) return;

    const newSearchParams = new URLSearchParams();
    
    // Add search query
    if (searchQuery.trim()) {
      newSearchParams.set('q', searchQuery);
    }
    
    // Add active filters to URL
    if (filters.platforms.enabled && filters.platforms.platforms.length > 0) {
      newSearchParams.set('platforms', filters.platforms.platforms.join(','));
    }
    
    if (filters.genres.enabled && filters.genres.genres.length > 0) {
      newSearchParams.set('genres', filters.genres.genres.join(','));
      newSearchParams.set('genreMode', filters.genres.mode);
    }
    
    if (filters.year.enabled && (filters.year.minYear || filters.year.maxYear)) {
      if (filters.year.minYear) newSearchParams.set('minYear', filters.year.minYear.toString());
      if (filters.year.maxYear) newSearchParams.set('maxYear', filters.year.maxYear.toString());
    }
    
    if (filters.rating.enabled && (filters.rating.minRating || filters.rating.maxRating)) {
      if (filters.rating.minRating) newSearchParams.set('minRating', filters.rating.minRating.toString());
      if (filters.rating.maxRating) newSearchParams.set('maxRating', filters.rating.maxRating.toString());
    }
    
    if (filters.developer.enabled && filters.developer.developers.length > 0) {
      newSearchParams.set('developers', filters.developer.developers.join(','));
    }
    
    if (filters.publisher.enabled && filters.publisher.publishers.length > 0) {
      newSearchParams.set('publishers', filters.publisher.publishers.join(','));
    }
    
    // Add custom tags
    if (filters.customTags.enabled && filters.customTags.tags.length > 0) {
      newSearchParams.set('tags', filters.customTags.tags.join(','));
      newSearchParams.set('tagMode', filters.customTags.mode);
      if (filters.customTags.includeUntagged) {
        newSearchParams.set('includeUntagged', 'true');
      }
    }
    
    // Add status filter
    if (filters.status.enabled && filters.status.statuses.length > 0) {
      newSearchParams.set('statuses', filters.status.statuses.join(','));
      newSearchParams.set('statusMode', filters.status.mode);
    }
    
    // Add collection filter
    if (filters.collection.enabled) {
      const collectionTypes = [];
      if (filters.collection.inLibrary) collectionTypes.push('library');
      if (filters.collection.inWishlist) collectionTypes.push('wishlist');
      if (filters.collection.notInCollection) collectionTypes.push('none');
      if (collectionTypes.length > 0) {
        newSearchParams.set('collection', collectionTypes.join(','));
      }
    }
    
    // Add sort options
    if (filters.sort.field !== 'title' || filters.sort.direction !== 'asc') {
      newSearchParams.set('sort', filters.sort.field);
      newSearchParams.set('sortDir', filters.sort.direction);
    }
    
    setSearchParams(newSearchParams, { replace: true });
  }, [filters, searchQuery, persistToURL, setSearchParams]);

  return {
    parseFiltersFromURL: () => parseFiltersFromURL(searchParams)
  };
}

// Helper function to parse filters from URL search params
export function parseFiltersFromURL(searchParams: URLSearchParams): GameFilters {
  const filters = { ...DEFAULT_FILTERS };
  
  // Parse platforms
  const platforms = searchParams.get('platforms');
  if (platforms) {
    filters.platforms = {
      enabled: true,
      platforms: platforms.split(',') as Platform[]
    };
  }
  
  // Parse genres
  const genres = searchParams.get('genres');
  const genreMode = searchParams.get('genreMode') as 'include' | 'exclude';
  if (genres) {
    filters.genres = {
      enabled: true,
      genres: genres.split(','),
      mode: genreMode || 'include'
    };
  }
  
  // Parse year range
  const minYear = searchParams.get('minYear');
  const maxYear = searchParams.get('maxYear');
  if (minYear || maxYear) {
    filters.year = {
      enabled: true,
      minYear: minYear ? parseInt(minYear) : undefined,
      maxYear: maxYear ? parseInt(maxYear) : undefined
    };
  }
  
  // Parse rating range
  const minRating = searchParams.get('minRating');
  const maxRating = searchParams.get('maxRating');
  if (minRating || maxRating) {
    filters.rating = {
      enabled: true,
      minRating: minRating ? parseFloat(minRating) : undefined,
      maxRating: maxRating ? parseFloat(maxRating) : undefined
    };
  }
  
  // Parse developers
  const developers = searchParams.get('developers');
  if (developers) {
    filters.developer = {
      enabled: true,
      developers: developers.split(',')
    };
  }
  
  // Parse publishers
  const publishers = searchParams.get('publishers');
  if (publishers) {
    filters.publisher = {
      enabled: true,
      publishers: publishers.split(',')
    };
  }
  
  // Parse custom tags
  const tags = searchParams.get('tags');
  const tagMode = searchParams.get('tagMode') as 'include' | 'exclude';
  const includeUntagged = searchParams.get('includeUntagged') === 'true';
  if (tags || includeUntagged) {
    filters.customTags = {
      enabled: true,
      tags: tags ? tags.split(',') : [],
      mode: tagMode || 'include',
      includeUntagged: includeUntagged
    };
  }
  
  // Parse status filter
  const statuses = searchParams.get('statuses');
  const statusMode = searchParams.get('statusMode') as 'include' | 'exclude';
  if (statuses) {
    filters.status = {
      enabled: true,
      statuses: statuses.split(',') as Array<'playing' | 'completed' | 'backlog' | 'wishlist'>,
      mode: statusMode || 'include'
    };
  }
  
  // Parse collection filter
  const collection = searchParams.get('collection');
  if (collection) {
    const collectionTypes = collection.split(',');
    filters.collection = {
      enabled: true,
      inLibrary: collectionTypes.includes('library'),
      inWishlist: collectionTypes.includes('wishlist'),
      notInCollection: collectionTypes.includes('none')
    };
  }
  
  // Parse sort options
  const sort = searchParams.get('sort');
  const sortDir = searchParams.get('sortDir') as 'asc' | 'desc';
  if (sort) {
    filters.sort = {
      field: sort as SortField,
      direction: sortDir || 'asc'
    };
  }
  
  return filters;
}