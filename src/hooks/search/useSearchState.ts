import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { enhancedSearchService, SearchHistory } from '../../lib/enhancedSearchService';

export interface SearchFilters {
  genres?: string[];
  platforms?: string[];
  yearRange?: { from: number; to: number };
  ratingRange?: { min: number; max: number };
}

export interface VoiceSearchState {
  isListening: boolean;
  transcript: string;
  error: string | null;
  isSupported: boolean;
}

export function useSearchState() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([]);
  const [voiceSearch, setVoiceSearch] = useState<VoiceSearchState>({
    isListening: false,
    transcript: '',
    error: null,
    isSupported: typeof window !== 'undefined' && 'webkitSpeechRecognition' in window
  });

  // Load search history on mount
  useEffect(() => {
    if (user?.id) {
      const history = enhancedSearchService.getSearchHistory(user.id);
      setSearchHistory(history);
    }
  }, [user?.id]);

  const updateSearchQuery = (query: string) => {
    setSearchQuery(query);
  };

  const updateSearchFilters = (filters: SearchFilters) => {
    setSearchFilters(filters);
  };

  const updateVoiceSearch = (updates: Partial<VoiceSearchState>) => {
    setVoiceSearch(prev => ({ ...prev, ...updates }));
  };

  const updateSearchHistory = () => {
    if (user?.id) {
      const history = enhancedSearchService.getSearchHistory(user.id);
      setSearchHistory(history);
    }
  };

  return {
    // State
    searchQuery,
    searchFilters,
    searchHistory,
    voiceSearch,
    
    // Actions
    setSearchQuery: updateSearchQuery,
    setSearchFilters: updateSearchFilters,
    setVoiceSearch: updateVoiceSearch,
    updateSearchHistory
  };
}

export type { SearchFilters, VoiceSearchState };