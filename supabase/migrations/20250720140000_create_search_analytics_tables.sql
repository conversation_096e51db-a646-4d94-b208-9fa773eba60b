-- Create Search Analytics Tables for Enhanced Search Analytics Dashboard
-- Phase 3.1: Create search analytics database tables and migrate search history to Supabase

-- Search Queries Table (tracks all search queries and their performance)
CREATE TABLE IF NOT EXISTS search_queries (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  query_text TEXT NOT NULL,
  filters_applied JSONB DEFAULT '{}',
  sort_options JSONB DEFAULT '{}',
  search_source TEXT DEFAULT 'manual' CHECK (search_source IN ('manual', 'voice', 'ai_assisted', 'autocomplete')),
  session_id TEXT, -- Groups related searches in a session
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Search Results Table (tracks results returned for queries)
CREATE TABLE IF NOT EXISTS search_results (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  query_id UUID NOT NULL REFERENCES search_queries(id) ON DELETE CASCADE,
  api_source TEXT NOT NULL CHECK (api_source IN ('igdb', 'rawg', 'thegamesdb', 'cache')),
  total_results INTEGER NOT NULL DEFAULT 0,
  results_shown INTEGER NOT NULL DEFAULT 0,
  response_time_ms INTEGER NOT NULL DEFAULT 0,
  cache_hit BOOLEAN DEFAULT FALSE,
  success BOOLEAN DEFAULT TRUE,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Search Interactions Table (tracks user interactions with search results)
CREATE TABLE IF NOT EXISTS search_interactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  query_id UUID NOT NULL REFERENCES search_queries(id) ON DELETE CASCADE,
  game_id UUID REFERENCES games(id) ON DELETE SET NULL,
  interaction_type TEXT NOT NULL CHECK (interaction_type IN (
    'click', 'add_to_library', 'add_to_wishlist', 'view_details', 
    'copy_link', 'hover', 'filter_applied', 'sort_applied'
  )),
  interaction_position INTEGER, -- Position in search results (1-based)
  interaction_data JSONB DEFAULT '{}', -- Additional metadata about the interaction
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Search Performance Metrics Table (aggregated performance data)
CREATE TABLE IF NOT EXISTS search_performance_metrics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  total_searches INTEGER NOT NULL DEFAULT 0,
  successful_searches INTEGER NOT NULL DEFAULT 0,
  failed_searches INTEGER NOT NULL DEFAULT 0,
  average_response_time_ms DECIMAL(10,2) NOT NULL DEFAULT 0,
  cache_hit_rate DECIMAL(3,2) NOT NULL DEFAULT 0, -- Percentage (0.00-1.00)
  most_common_query TEXT,
  most_common_filters JSONB DEFAULT '{}',
  total_interactions INTEGER NOT NULL DEFAULT 0,
  unique_games_found INTEGER NOT NULL DEFAULT 0,
  conversion_rate DECIMAL(3,2) NOT NULL DEFAULT 0, -- Searches leading to library additions
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT search_performance_metrics_date_user_unique UNIQUE (date, user_id)
);

-- Popular Search Terms Table (trending and popular searches)
CREATE TABLE IF NOT EXISTS popular_search_terms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  query_text TEXT NOT NULL,
  search_count INTEGER NOT NULL DEFAULT 1,
  success_rate DECIMAL(3,2) NOT NULL DEFAULT 0,
  last_searched TIMESTAMPTZ DEFAULT NOW(),
  trending_score DECIMAL(5,2) DEFAULT 0, -- Calculated trending score
  period_type TEXT DEFAULT 'all_time' CHECK (period_type IN ('daily', 'weekly', 'monthly', 'all_time')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT popular_search_terms_query_period_unique UNIQUE (query_text, period_type)
);

-- Search Recommendations Table (AI-generated search suggestions)
CREATE TABLE IF NOT EXISTS search_recommendations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  original_query TEXT NOT NULL,
  recommended_query TEXT NOT NULL,
  recommendation_type TEXT DEFAULT 'spelling' CHECK (recommendation_type IN (
    'spelling', 'synonym', 'related', 'trending', 'personalized'
  )),
  confidence_score DECIMAL(3,2) DEFAULT 0.0,
  metadata JSONB DEFAULT '{}',
  is_accepted BOOLEAN DEFAULT FALSE,
  is_dismissed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  applied_at TIMESTAMPTZ
);

-- Search Sessions Table (groups related searches and tracks user search behavior)
CREATE TABLE IF NOT EXISTS search_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  session_id TEXT NOT NULL,
  start_time TIMESTAMPTZ DEFAULT NOW(),
  end_time TIMESTAMPTZ,
  total_queries INTEGER DEFAULT 0,
  total_interactions INTEGER DEFAULT 0,
  session_goal TEXT, -- What user was trying to achieve
  session_outcome TEXT CHECK (session_outcome IN ('successful', 'abandoned', 'partial')),
  device_info JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT search_sessions_session_id_unique UNIQUE (session_id)
);

-- Add Performance Indexes
CREATE INDEX IF NOT EXISTS idx_search_queries_user_id ON search_queries(user_id);
CREATE INDEX IF NOT EXISTS idx_search_queries_created_at ON search_queries(created_at);
CREATE INDEX IF NOT EXISTS idx_search_queries_session_id ON search_queries(session_id);
CREATE INDEX IF NOT EXISTS idx_search_queries_text_gin ON search_queries USING gin(to_tsvector('english', query_text));

CREATE INDEX IF NOT EXISTS idx_search_results_query_id ON search_results(query_id);
CREATE INDEX IF NOT EXISTS idx_search_results_api_source ON search_results(api_source);
CREATE INDEX IF NOT EXISTS idx_search_results_success ON search_results(success);
CREATE INDEX IF NOT EXISTS idx_search_results_cache_hit ON search_results(cache_hit);

CREATE INDEX IF NOT EXISTS idx_search_interactions_user_id ON search_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_search_interactions_query_id ON search_interactions(query_id);
CREATE INDEX IF NOT EXISTS idx_search_interactions_game_id ON search_interactions(game_id);
CREATE INDEX IF NOT EXISTS idx_search_interactions_type ON search_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_search_interactions_created_at ON search_interactions(created_at);

CREATE INDEX IF NOT EXISTS idx_search_performance_metrics_user_id ON search_performance_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_search_performance_metrics_date ON search_performance_metrics(date);

CREATE INDEX IF NOT EXISTS idx_popular_search_terms_query ON popular_search_terms(query_text);
CREATE INDEX IF NOT EXISTS idx_popular_search_terms_count ON popular_search_terms(search_count DESC);
CREATE INDEX IF NOT EXISTS idx_popular_search_terms_trending ON popular_search_terms(trending_score DESC);
CREATE INDEX IF NOT EXISTS idx_popular_search_terms_period ON popular_search_terms(period_type);

CREATE INDEX IF NOT EXISTS idx_search_recommendations_user_id ON search_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_search_recommendations_type ON search_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_search_recommendations_active ON search_recommendations(user_id, is_accepted, is_dismissed) WHERE is_dismissed = FALSE;

CREATE INDEX IF NOT EXISTS idx_search_sessions_user_id ON search_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_search_sessions_session_id ON search_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_search_sessions_start_time ON search_sessions(start_time);

-- Enable Row Level Security
ALTER TABLE search_queries ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE popular_search_terms ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Search Queries
CREATE POLICY "Users can view their own search queries"
  ON search_queries FOR SELECT
  USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can create search queries"
  ON search_queries FOR INSERT
  WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

-- RLS Policies for Search Results
CREATE POLICY "Users can view search results for their queries"
  ON search_results FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM search_queries sq 
      WHERE sq.id = search_results.query_id 
      AND (sq.user_id = auth.uid() OR sq.user_id IS NULL)
    )
  );

CREATE POLICY "Users can create search results"
  ON search_results FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM search_queries sq 
      WHERE sq.id = search_results.query_id 
      AND (sq.user_id = auth.uid() OR sq.user_id IS NULL)
    )
  );

-- RLS Policies for Search Interactions
CREATE POLICY "Users can view their own search interactions"
  ON search_interactions FOR SELECT
  USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can create their own search interactions"
  ON search_interactions FOR INSERT
  WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

-- RLS Policies for Search Performance Metrics
CREATE POLICY "Users can view their own search performance metrics"
  ON search_performance_metrics FOR SELECT
  USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can create/update their own search performance metrics"
  ON search_performance_metrics FOR ALL
  USING (auth.uid() = user_id OR user_id IS NULL)
  WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

-- RLS Policies for Popular Search Terms (read-only for all users)
CREATE POLICY "All users can view popular search terms"
  ON popular_search_terms FOR SELECT
  TO authenticated
  USING (true);

-- RLS Policies for Search Recommendations
CREATE POLICY "Users can view their own search recommendations"
  ON search_recommendations FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create/update their own search recommendations"
  ON search_recommendations FOR ALL
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for Search Sessions
CREATE POLICY "Users can view their own search sessions"
  ON search_sessions FOR SELECT
  USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can create/update their own search sessions"
  ON search_sessions FOR ALL
  USING (auth.uid() = user_id OR user_id IS NULL)
  WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_search_performance_metrics_updated_at 
  BEFORE UPDATE ON search_performance_metrics 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_popular_search_terms_updated_at 
  BEFORE UPDATE ON popular_search_terms 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Advanced Search Analytics Functions

-- Function to record a search query
CREATE OR REPLACE FUNCTION record_search_query(
  p_user_id UUID,
  p_query_text TEXT,
  p_filters_applied JSONB DEFAULT '{}',
  p_sort_options JSONB DEFAULT '{}',
  p_search_source TEXT DEFAULT 'manual',
  p_session_id TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  query_id UUID;
BEGIN
  INSERT INTO search_queries (
    user_id, query_text, filters_applied, sort_options, search_source, session_id
  ) VALUES (
    p_user_id, p_query_text, p_filters_applied, p_sort_options, p_search_source, p_session_id
  ) RETURNING id INTO query_id;
  
  RETURN query_id;
END;
$$;

-- Function to record search results
CREATE OR REPLACE FUNCTION record_search_results(
  p_query_id UUID,
  p_api_source TEXT,
  p_total_results INTEGER,
  p_results_shown INTEGER,
  p_response_time_ms INTEGER,
  p_cache_hit BOOLEAN DEFAULT FALSE,
  p_success BOOLEAN DEFAULT TRUE,
  p_error_message TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result_id UUID;
BEGIN
  INSERT INTO search_results (
    query_id, api_source, total_results, results_shown, response_time_ms, 
    cache_hit, success, error_message
  ) VALUES (
    p_query_id, p_api_source, p_total_results, p_results_shown, p_response_time_ms,
    p_cache_hit, p_success, p_error_message
  ) RETURNING id INTO result_id;
  
  RETURN result_id;
END;
$$;

-- Function to record search interaction
CREATE OR REPLACE FUNCTION record_search_interaction(
  p_user_id UUID,
  p_query_id UUID,
  p_game_id UUID,
  p_interaction_type TEXT,
  p_interaction_position INTEGER DEFAULT NULL,
  p_interaction_data JSONB DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  interaction_id UUID;
BEGIN
  INSERT INTO search_interactions (
    user_id, query_id, game_id, interaction_type, interaction_position, interaction_data
  ) VALUES (
    p_user_id, p_query_id, p_game_id, p_interaction_type, p_interaction_position, p_interaction_data
  ) RETURNING id INTO interaction_id;
  
  RETURN interaction_id;
END;
$$;

-- Function to update popular search terms
CREATE OR REPLACE FUNCTION update_popular_search_terms(
  p_query_text TEXT,
  p_success BOOLEAN DEFAULT TRUE
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO popular_search_terms (query_text, search_count, success_rate, last_searched)
  VALUES (
    p_query_text, 
    1, 
    CASE WHEN p_success THEN 1.0 ELSE 0.0 END,
    NOW()
  )
  ON CONFLICT (query_text, period_type) 
  DO UPDATE SET
    search_count = popular_search_terms.search_count + 1,
    success_rate = (
      (popular_search_terms.success_rate * popular_search_terms.search_count) + 
      CASE WHEN p_success THEN 1.0 ELSE 0.0 END
    ) / (popular_search_terms.search_count + 1),
    last_searched = NOW(),
    trending_score = (
      popular_search_terms.search_count + 1
    ) * (
      1 + EXTRACT(EPOCH FROM (NOW() - popular_search_terms.last_searched)) / 86400.0
    ),
    updated_at = NOW();
END;
$$;

-- Function to get search analytics for user
CREATE OR REPLACE FUNCTION get_user_search_analytics(
  p_user_id UUID,
  p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
  total_searches BIGINT,
  successful_searches BIGINT,
  failed_searches BIGINT,
  average_response_time DECIMAL,
  cache_hit_rate DECIMAL,
  total_interactions BIGINT,
  conversion_rate DECIMAL,
  top_queries TEXT[],
  trending_score DECIMAL
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH search_stats AS (
    SELECT 
      COUNT(*) as total_searches,
      COUNT(*) FILTER (WHERE sr.success = true) as successful_searches,
      COUNT(*) FILTER (WHERE sr.success = false) as failed_searches,
      AVG(sr.response_time_ms) as avg_response_time,
      AVG(CASE WHEN sr.cache_hit THEN 1.0 ELSE 0.0 END) as cache_hit_rate
    FROM search_queries sq
    LEFT JOIN search_results sr ON sq.id = sr.query_id
    WHERE sq.user_id = p_user_id 
      AND sq.created_at >= NOW() - INTERVAL '1 day' * p_days
  ),
  interaction_stats AS (
    SELECT 
      COUNT(*) as total_interactions,
      COUNT(DISTINCT si.game_id) FILTER (WHERE si.interaction_type IN ('add_to_library', 'add_to_wishlist')) as conversions
    FROM search_interactions si
    JOIN search_queries sq ON si.query_id = sq.id
    WHERE si.user_id = p_user_id 
      AND si.created_at >= NOW() - INTERVAL '1 day' * p_days
  ),
  top_queries AS (
    SELECT array_agg(sq.query_text ORDER BY count DESC) as queries
    FROM (
      SELECT sq.query_text, COUNT(*) as count
      FROM search_queries sq
      WHERE sq.user_id = p_user_id 
        AND sq.created_at >= NOW() - INTERVAL '1 day' * p_days
      GROUP BY sq.query_text
      ORDER BY COUNT(*) DESC
      LIMIT 10
    ) sq
  )
  SELECT 
    ss.total_searches,
    ss.successful_searches,
    ss.failed_searches,
    ss.avg_response_time,
    ss.cache_hit_rate,
    is_stats.total_interactions,
    CASE 
      WHEN ss.total_searches > 0 THEN is_stats.conversions::DECIMAL / ss.total_searches::DECIMAL
      ELSE 0.0
    END as conversion_rate,
    tq.queries as top_queries,
    COALESCE(ss.total_searches::DECIMAL * (ss.cache_hit_rate + 0.1), 0.0) as trending_score
  FROM search_stats ss
  CROSS JOIN interaction_stats is_stats
  CROSS JOIN top_queries tq;
END;
$$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON search_queries TO authenticated;
GRANT ALL ON search_results TO authenticated;
GRANT ALL ON search_interactions TO authenticated;
GRANT ALL ON search_performance_metrics TO authenticated;
GRANT SELECT ON popular_search_terms TO authenticated;
GRANT ALL ON search_recommendations TO authenticated;
GRANT ALL ON search_sessions TO authenticated;

GRANT EXECUTE ON FUNCTION record_search_query(UUID, TEXT, JSONB, JSONB, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION record_search_results(UUID, TEXT, INTEGER, INTEGER, INTEGER, BOOLEAN, BOOLEAN, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION record_search_interaction(UUID, UUID, UUID, TEXT, INTEGER, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION update_popular_search_terms(TEXT, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_search_analytics(UUID, INTEGER) TO authenticated;