import { useState, useMemo, useCallback } from 'react';
import { UserGameWithDetails } from '@/types/database';
import { LibraryFilters } from '../types';

export function useLibraryFilters(games: UserGameWithDetails[]) {
  const [filters, setFilters] = useState<LibraryFilters>({});
  
  // Apply filters to the games list
  const filteredGames = useMemo(() => {
    return games.filter(userGame => {
      const game = userGame.game;
      
      // Status filter
      if (filters.status && userGame.status !== filters.status) {
        return false;
      }
      
      // Rating filter
      if (filters.rating) {
        const rating = userGame.personal_rating;
        if (!rating) return false;
        
        switch (filters.rating) {
          case '5':
            return rating === 5;
          case '4+':
            return rating >= 4;
          case '3+':
            return rating >= 3;
          case '2+':
            return rating >= 2;
          case '1+':
            return rating >= 1;
          default:
            return true;
        }
      }
      
      // Platform filter
      if (filters.platform && game) {
        const platforms = game.platforms || [];
        const hasMatchingPlatform = platforms.some(platform => 
          platform.name?.toLowerCase().includes(filters.platform!.toLowerCase())
        );
        if (!hasMatchingPlatform) return false;
      }
      
      // Genre filter
      if (filters.genre && game) {
        const genres = game.genres || [];
        const hasMatchingGenre = genres.some(genre => 
          genre.name?.toLowerCase().includes(filters.genre!.toLowerCase())
        );
        if (!hasMatchingGenre) return false;
      }
      
      // Developer filter
      if (filters.developer && game) {
        const developers = game.involved_companies || [];
        const hasMatchingDeveloper = developers.some(company => 
          company.company?.name?.toLowerCase().includes(filters.developer!.toLowerCase()) &&
          company.developer === true
        );
        if (!hasMatchingDeveloper) return false;
      }
      
      // Publisher filter
      if (filters.publisher && game) {
        const publishers = game.involved_companies || [];
        const hasMatchingPublisher = publishers.some(company => 
          company.company?.name?.toLowerCase().includes(filters.publisher!.toLowerCase()) &&
          company.publisher === true
        );
        if (!hasMatchingPublisher) return false;
      }
      
      return true;
    });
  }, [games, filters]);
  
  // Update individual filter
  const updateFilter = useCallback((key: keyof LibraryFilters, value: string | undefined) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);
  
  // Apply multiple filters at once
  const applyFilters = useCallback((newFilters: LibraryFilters) => {
    setFilters(newFilters);
  }, []);
  
  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);
  
  // Clear a specific filter
  const clearFilter = useCallback((key: keyof LibraryFilters) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
  }, []);
  
  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return Object.values(filters).some(value => value !== undefined && value !== '');
  }, [filters]);
  
  // Get available filter options from the games
  const availableOptions = useMemo(() => {
    const statuses = Array.from(new Set(games.map(g => g.status))).filter(Boolean);
    
    const platforms = Array.from(new Set(
      games.flatMap(g => g.game?.platforms?.map(p => p.name) || [])
    )).filter(Boolean);
    
    const genres = Array.from(new Set(
      games.flatMap(g => g.game?.genres?.map(genre => genre.name) || [])
    )).filter(Boolean);
    
    const developers = Array.from(new Set(
      games.flatMap(g => 
        g.game?.involved_companies
          ?.filter(c => c.developer)
          ?.map(c => c.company?.name) || []
      )
    )).filter(Boolean);
    
    const publishers = Array.from(new Set(
      games.flatMap(g => 
        g.game?.involved_companies
          ?.filter(c => c.publisher)
          ?.map(c => c.company?.name) || []
      )
    )).filter(Boolean);
    
    return {
      statuses: statuses.sort(),
      platforms: platforms.sort(),
      genres: genres.sort(),
      developers: developers.sort(),
      publishers: publishers.sort(),
      ratings: ['5', '4+', '3+', '2+', '1+']
    };
  }, [games]);
  
  // Get filter count for UI display
  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  }, [filters]);
  
  return {
    filters,
    filteredGames,
    updateFilter,
    applyFilters,
    clearFilters,
    clearFilter,
    hasActiveFilters,
    availableOptions,
    activeFilterCount
  };
}