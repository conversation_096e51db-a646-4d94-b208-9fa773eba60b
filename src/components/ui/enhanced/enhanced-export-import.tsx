import React, { memo, useCallback, useState, useRef } from 'react';
import { UserGameWithDetails } from '@/types/database';
import { ExportFormat, ImportSource, ExportImportSettings } from '@/types/library';
import { cn } from '@/lib/utils';
import { 
  Download,
  Upload,
  FileText,
  Database,
  Archive,
  Cloud,
  Check,
  AlertCircle,
  Info,
  Loader2,
  FileJson,
  FileSpreadsheet,
  Settings,
  Copy,
  RefreshCw,
  ChevronRight,
  ChevronDown
} from '@/lib/icons';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Label } from '@/components/ui/base/label';
import { Separator } from '@/components/ui/base/separator';
import { Switch } from '@/components/ui/base/switch';
import { Progress } from '@/components/ui/base/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/base/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/base/select';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/base/alert';

interface EnhancedExportImportProps {
  games: UserGameWithDetails[];
  settings: ExportImportSettings;
  onSettingsChange: (settings: Partial<ExportImportSettings>) => void;
  onExport: (format: ExportFormat, options: Record<string, unknown>) => Promise<void>;
  onImport: (source: ImportSource, data: unknown, options: Record<string, unknown>) => Promise<void>;
  className?: string;
}

interface ExportOptionProps {
  format: ExportFormat;
  title: string;
  description: string;
  icon: React.ReactNode;
  fileExtension: string;
  estimatedSize: string;
  features: string[];
  onExport: () => void;
  isExporting: boolean;
}

interface ImportOptionProps {
  source: ImportSource;
  title: string;
  description: string;
  icon: React.ReactNode;
  supportedFormats: string[];
  instructions: string[];
  onImport: (files: FileList) => void;
  isImporting: boolean;
}

// Export formats configuration
const EXPORT_FORMATS = {
  json: {
    title: 'JSON Database',
    description: 'Complete library data with full metadata',
    icon: <FileJson size={24} />,
    fileExtension: '.json',
    estimatedSize: '2-5 MB',
    features: [
      'Complete game metadata',
      'Personal ratings and notes',
      'Play statistics',
      'Collection organization',
      'Import/export compatibility'
    ]
  },
  csv: {
    title: 'CSV Spreadsheet',
    description: 'Tabular data for spreadsheet applications',
    icon: <FileSpreadsheet size={24} />,
    fileExtension: '.csv',
    estimatedSize: '500 KB - 2 MB',
    features: [
      'Basic game information',
      'Compatible with Excel/Sheets',
      'Easy data analysis',
      'Lightweight format'
    ]
  },
  backup: {
    title: 'Complete Backup',
    description: 'Full system backup with all data',
    icon: <Archive size={24} />,
    fileExtension: '.zip',
    estimatedSize: '5-20 MB',
    features: [
      'Complete library backup',
      'User preferences',
      'Collection settings',
      'Import history',
      'Cached images'
    ]
  },
  html: {
    title: 'HTML Report',
    description: 'Shareable web page of your collection',
    icon: <FileText size={24} />,
    fileExtension: '.html',
    estimatedSize: '1-3 MB',
    features: [
      'Visual collection display',
      'Shareable format',
      'Statistics overview',
      'No import needed to view'
    ]
  }
} as const;

// Import sources configuration
const IMPORT_SOURCES = {
  steam: {
    title: 'Steam Library',
    description: 'Import games from your Steam account',
    icon: <Cloud size={24} />,
    supportedFormats: ['Steam Web API', 'Steam Library Export'],
    instructions: [
      'Ensure your Steam profile is public',
      'Get your Steam ID or profile URL',
      'API will fetch your complete library'
    ]
  },
  csv: {
    title: 'CSV File',
    description: 'Import from spreadsheet or CSV export',
    icon: <FileSpreadsheet size={24} />,
    supportedFormats: ['.csv', '.tsv', '.txt'],
    instructions: [
      'Prepare CSV with headers: Title, Platform, Status, Rating',
      'Use comma-separated values',
      'Ensure UTF-8 encoding for special characters'
    ]
  },
  json: {
    title: 'JSON Database',
    description: 'Import from JSON backup or export',
    icon: <FileJson size={24} />,
    supportedFormats: ['.json'],
    instructions: [
      'Use JSON format from this app or compatible sources',
      'File should contain games array with metadata',
      'Validate JSON format before importing'
    ]
  },
  backup: {
    title: 'Backup Restore',
    description: 'Restore from complete backup file',
    icon: <Archive size={24} />,
    supportedFormats: ['.zip', '.backup'],
    instructions: [
      'Use backup file created by this application',
      'Will restore all data including preferences',
      'This will replace current library data'
    ]
  }
} as const;

// Export option component
const ExportOption = memo<ExportOptionProps>(({
  title,
  description,
  icon,
  fileExtension,
  estimatedSize,
  features,
  onExport,
  isExporting
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="group hover:shadow-md transition-all duration-200">
      <CardHeader className="pb-4">
        <div className="flex items-start gap-4">
          <div className="p-3 rounded-lg bg-primary/10 text-primary">
            {icon}
          </div>
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg">{title}</CardTitle>
            <p className="text-muted-foreground mt-1">{description}</p>
            <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
              <span>Format: {fileExtension}</span>
              <span>Size: {estimatedSize}</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              Details
            </Button>
            <Button 
              onClick={onExport}
              disabled={isExporting}
              className="min-w-24"
            >
              {isExporting ? (
                <Loader2 size={16} className="animate-spin" />
              ) : (
                <Download size={16} className="mr-2" />
              )}
              {isExporting ? 'Exporting...' : 'Export'}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          <Separator className="mb-4" />
          <div>
            <h4 className="font-medium mb-2">Features & Compatibility</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <Check size={14} className="text-green-500" />
                  <span>{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
});

ExportOption.displayName = 'ExportOption';

// Import option component
const ImportOption = memo<ImportOptionProps>(({
  source,
  title,
  description,
  icon,
  supportedFormats,
  instructions,
  onImport,
  isImporting
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      onImport(files);
    }
  }, [onImport]);

  const getAcceptedTypes = () => {
    return supportedFormats.filter(format => format.startsWith('.')).join(',');
  };

  return (
    <Card className="group hover:shadow-md transition-all duration-200">
      <CardHeader className="pb-4">
        <div className="flex items-start gap-4">
          <div className="p-3 rounded-lg bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400">
            {icon}
          </div>
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg">{title}</CardTitle>
            <p className="text-muted-foreground mt-1">{description}</p>
            <div className="flex flex-wrap gap-1 mt-2">
              {supportedFormats.map((format, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {format}
                </Badge>
              ))}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              Guide
            </Button>
            <Button 
              onClick={handleFileSelect}
              disabled={isImporting}
              className="min-w-24"
            >
              {isImporting ? (
                <Loader2 size={16} className="animate-spin" />
              ) : (
                <Upload size={16} className="mr-2" />
              )}
              {isImporting ? 'Importing...' : 'Import'}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          <Separator className="mb-4" />
          <div>
            <h4 className="font-medium mb-2">Import Instructions</h4>
            <ol className="space-y-2">
              {instructions.map((instruction, index) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <span className="w-5 h-5 rounded-full bg-primary/10 text-primary text-xs flex items-center justify-center mt-0.5 shrink-0">
                    {index + 1}
                  </span>
                  <span>{instruction}</span>
                </li>
              ))}
            </ol>
          </div>
        </CardContent>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept={getAcceptedTypes()}
        onChange={handleFileChange}
        className="hidden"
        multiple={source === 'csv' || source === 'json'}
      />
    </Card>
  );
});

ImportOption.displayName = 'ImportOption';

export const EnhancedExportImport = memo<EnhancedExportImportProps>(({
  games,
  settings,
  onSettingsChange,
  onExport,
  onImport,
  className
}) => {
  const [activeTab, setActiveTab] = useState('export');
  const [exportingFormat, setExportingFormat] = useState<ExportFormat | null>(null);
  const [importingSource, setImportingSource] = useState<ImportSource | null>(null);
  const [exportProgress, setExportProgress] = useState(0);
  const [importProgress, setImportProgress] = useState(0);

  // Export handlers
  const handleExport = useCallback(async (format: ExportFormat) => {
    setExportingFormat(format);
    setExportProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setExportProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      await onExport(format, {
        includeImages: settings.includeImages,
        includeMetadata: settings.includeMetadata,
        includePersonalData: settings.includePersonalData,
        compression: settings.compression
      });

      clearInterval(progressInterval);
      setExportProgress(100);
      
      // Reset after showing completion
      setTimeout(() => {
        setExportingFormat(null);
        setExportProgress(0);
      }, 2000);
    } catch (error) {
      console.error('Export failed:', error);
      setExportingFormat(null);
      setExportProgress(0);
    }
  }, [onExport, settings]);

  // Import handlers
  const handleImport = useCallback(async (source: ImportSource, files: FileList) => {
    setImportingSource(source);
    setImportProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setImportProgress(prev => Math.min(prev + 8, 85));
      }, 150);

      await onImport(source, files, {
        overwriteExisting: settings.overwriteExisting,
        skipDuplicates: settings.skipDuplicates,
        preserveMetadata: settings.preserveMetadata
      });

      clearInterval(progressInterval);
      setImportProgress(100);
      
      // Reset after showing completion
      setTimeout(() => {
        setImportingSource(null);
        setImportProgress(0);
      }, 2000);
    } catch (error) {
      console.error('Import failed:', error);
      setImportingSource(null);
      setImportProgress(0);
    }
  }, [onImport, settings]);

  const totalGames = games.length;
  const totalSizeEstimate = totalGames * 0.5; // Rough estimate in KB per game

  return (
    <div className={cn('enhanced-export-import space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Database size={24} />
            Export & Import
          </h2>
          <p className="text-muted-foreground">
            Backup your library or import games from external sources
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="secondary">
            {totalGames} games
          </Badge>
          <Badge variant="outline">
            ~{(totalSizeEstimate / 1024).toFixed(1)} MB
          </Badge>
        </div>
      </div>

      {/* Progress indicators */}
      {(exportingFormat || importingSource) && (
        <Alert>
          <Loader2 className="h-4 w-4 animate-spin" />
          <AlertTitle>
            {exportingFormat ? 'Exporting' : 'Importing'} in progress...
          </AlertTitle>
          <AlertDescription>
            <div className="mt-2">
              <Progress 
                value={exportingFormat ? exportProgress : importProgress} 
                className="w-full"
              />
              <p className="text-sm mt-1">
                {exportingFormat ? `Exporting to ${exportingFormat}` : `Importing from ${importingSource}`} - 
                {exportingFormat ? exportProgress : importProgress}% complete
              </p>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 w-full max-w-md">
          <TabsTrigger value="export">Export</TabsTrigger>
          <TabsTrigger value="import">Import</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="export" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Export Your Library</h3>
            <p className="text-muted-foreground mb-4">
              Choose a format to export your game library. All exports include your personal data like ratings and play status.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {Object.entries(EXPORT_FORMATS).map(([format, config]) => (
              <ExportOption
                key={format}
                format={format as ExportFormat}
                title={config.title}
                description={config.description}
                icon={config.icon}
                fileExtension={config.fileExtension}
                estimatedSize={config.estimatedSize}
                features={config.features}
                onExport={() => handleExport(format as ExportFormat)}
                isExporting={exportingFormat === format}
              />
            ))}
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Export Information</AlertTitle>
            <AlertDescription>
              All exports are processed locally in your browser. Your data is never sent to external servers during the export process.
            </AlertDescription>
          </Alert>
        </TabsContent>

        <TabsContent value="import" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Import Games</h3>
            <p className="text-muted-foreground mb-4">
              Import games from various sources to quickly build your library. Choose the source that matches your data.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {Object.entries(IMPORT_SOURCES).map(([source, config]) => (
              <ImportOption
                key={source}
                source={source as ImportSource}
                title={config.title}
                description={config.description}
                icon={config.icon}
                supportedFormats={config.supportedFormats}
                instructions={config.instructions}
                onImport={(files) => handleImport(source as ImportSource, files)}
                isImporting={importingSource === source}
              />
            ))}
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Import Safety</AlertTitle>
            <AlertDescription>
              Always backup your current library before importing. Large imports may take several minutes to process.
              Check the settings tab to configure import behavior.
            </AlertDescription>
          </Alert>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Export & Import Settings</h3>
            <p className="text-muted-foreground mb-4">
              Configure how exports and imports are handled.
            </p>
          </div>

          <div className="space-y-6">
            {/* Export Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download size={18} />
                  Export Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Include Images</Label>
                    <p className="text-sm text-muted-foreground">
                      Include game cover images in exports (increases file size)
                    </p>
                  </div>
                  <Switch
                    checked={settings.includeImages}
                    onCheckedChange={(checked) => 
                      onSettingsChange({ includeImages: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Include Metadata</Label>
                    <p className="text-sm text-muted-foreground">
                      Include detailed game metadata (genres, developers, etc.)
                    </p>
                  </div>
                  <Switch
                    checked={settings.includeMetadata}
                    onCheckedChange={(checked) => 
                      onSettingsChange({ includeMetadata: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Include Personal Data</Label>
                    <p className="text-sm text-muted-foreground">
                      Include ratings, notes, and personal play data
                    </p>
                  </div>
                  <Switch
                    checked={settings.includePersonalData}
                    onCheckedChange={(checked) => 
                      onSettingsChange({ includePersonalData: checked })
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label>Compression Level</Label>
                  <Select 
                    value={settings.compression} 
                    onValueChange={(value) => 
                      onSettingsChange({ compression: value as 'none' | 'standard' | 'high' })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No compression (faster)</SelectItem>
                      <SelectItem value="standard">Standard compression</SelectItem>
                      <SelectItem value="high">High compression (slower)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Import Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload size={18} />
                  Import Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Overwrite Existing</Label>
                    <p className="text-sm text-muted-foreground">
                      Replace existing games with imported data
                    </p>
                  </div>
                  <Switch
                    checked={settings.overwriteExisting}
                    onCheckedChange={(checked) => 
                      onSettingsChange({ overwriteExisting: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Skip Duplicates</Label>
                    <p className="text-sm text-muted-foreground">
                      Skip games that already exist in your library
                    </p>
                  </div>
                  <Switch
                    checked={settings.skipDuplicates}
                    onCheckedChange={(checked) => 
                      onSettingsChange({ skipDuplicates: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Preserve Metadata</Label>
                    <p className="text-sm text-muted-foreground">
                      Keep existing metadata when importing duplicates
                    </p>
                  </div>
                  <Switch
                    checked={settings.preserveMetadata}
                    onCheckedChange={(checked) => 
                      onSettingsChange({ preserveMetadata: checked })
                    }
                  />
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings size={18} />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  <Button variant="outline" size="sm">
                    <RefreshCw size={14} className="mr-2" />
                    Reset Settings
                  </Button>
                  <Button variant="outline" size="sm">
                    <Copy size={14} className="mr-2" />
                    Export Settings
                  </Button>
                  <Button variant="outline" size="sm">
                    <Upload size={14} className="mr-2" />
                    Import Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
});

EnhancedExportImport.displayName = 'EnhancedExportImport';