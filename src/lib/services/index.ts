// Export all services
export { GameService, gameService } from './gameService';
export { UserService, userService } from './userService';
export { CollectionService, collectionService } from './collectionService';
export { OrchestrationService, orchestrationService } from './orchestrationService';
export { DataTransformationService, dataTransformationService } from './dataTransformationService';

// Re-export existing services for backward compatibility
export { aiRecommendationService } from '../aiRecommendationService';
export { collectionInsightsService } from '../collectionInsightsService';
export { enhancedAIService } from '../enhancedAIService';

// Import services for aggregation
import { gameService } from './gameService';
import { userService } from './userService';
import { collectionService } from './collectionService';
import { orchestrationService } from './orchestrationService';
import { dataTransformationService } from './dataTransformationService';
import { aiRecommendationService } from '../aiRecommendationService';
import { collectionInsightsService } from '../collectionInsightsService';
import { enhancedAIService } from '../enhancedAIService';

// Service aggregator for easy access
export const services = {
  game: gameService,
  user: userService,
  collection: collectionService,
  orchestration: orchestrationService,
  dataTransformation: dataTransformationService,
  aiRecommendation: aiRecommendationService,
  collectionInsights: collectionInsightsService,
  enhancedAI: enhancedAIService
};

// Business logic services namespace
export const businessLogic = {
  games: gameService,
  users: userService,
  collections: collectionService,
  orchestration: orchestrationService,
  dataTransformation: dataTransformationService
};

// AI services namespace
export const aiServices = {
  recommendations: aiRecommendationService,
  insights: collectionInsightsService,
  chat: enhancedAIService
};