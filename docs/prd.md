# Personal Game Library - Product Requirements Document

## Vision

A personal web application to discover, organize, and track your video game collection with integrated AI assistance, Steam library import, and advanced gaming features.

**Last Updated**: July 2025
**Status**: Living document - Updated to reflect current implementation

## Core Features

### 1. Game Discovery & Management

- **Search**: Find games using IGDB and RAWG APIs
- **Game Details**: View descriptions, platforms, genres, release dates, ratings, screenshots, and videos
- **Collection Management**: Add/remove games from personal library
- **Status Tracking**: Mark games as "Playing", "Completed", "Backlog", "Dropped", or "Wishlist"
- **Platform Filtering**: Filter by gaming platforms you own

### 2. AI-Powered Features (Implemented)

- **AI Gaming Assistant**: Conversational AI with multiple providers (OpenAI, DeepSeek, Gemini)
- **Smart Recommendations**: AI-powered game suggestions based on user collection
- **Box Art Discovery**: AI-powered web search for high-quality game artwork
- **Collection Insights**: AI analysis of gaming habits and preferences

### 3. Steam Integration (Implemented)

- **Steam Library Import**: Complete Steam library synchronization with playtime data
- **Duplicate Detection**: Intelligent game matching using Steam App IDs
- **Import History**: Track and manage library imports with detailed statistics
- **Status Assignment**: Intelligent game status based on playtime patterns

### 4. Price Monitoring (Planned)

- **Price Tracking**: Monitor game prices across multiple platforms
- **Wishlist Alerts**: Get notified when wishlist games go on sale
- **Price History**: Track price trends for better purchase decisions

### 5. Personal Dashboard (Implemented)

- **Collection Stats**: Total games, completion rates, platform breakdown
- **Current Activity**: Currently playing games and recent additions
- **AI Intelligence**: Smart collection insights and recommendations
- **Quick Actions**: Fast access to add games and manage library

### 6. User Authentication & Data (Implemented)

- **Secure Login**: Supabase Auth for account management
- **Personal Collection**: Persistent game library storage
- **User Preferences**: Default platforms, notification settings

## Technical Requirements

### Tech Stack

- **Frontend Framework**: React 18+ with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **Styling**: Tailwind CSS v3+ with custom configuration
- **UI Components**: shadcn/ui component library
- **Icons**: Lucide React for consistent iconography
- **Backend**: Supabase (Auth, PostgreSQL, Storage, Edge Functions)
- **APIs**: IGDB, RAWG, YouTube Data API, Steam Web API
- **AI Integration**: OpenAI GPT-4, DeepSeek, Google Gemini for conversational AI
- **Price Monitoring**: Custom scrapers/APIs for price tracking (planned)
- **State Management**: React Query (TanStack Query) for server state
- **Routing**: React Router v7
- **Forms**: React Hook Form with Zod validation
- **Testing**: Vitest with React Testing Library

### UI/UX Design System

#### Color Palette

- **Primary**: Gaming-inspired purple (`#8B5CF6` - violet-500)
- **Secondary**: Complementary teal (`#14B8A6` - teal-500)
- **Accent**: Energetic orange (`#F59E0B` - amber-500) for deals/promotions
- **Background**:
  - Light mode: `#FAFAFA` (gray-50)
  - Dark mode: `#0F172A` (slate-900)
- **Surface**:
  - Light mode: `#FFFFFF` (white)
  - Dark mode: `#1E293B` (slate-800)
- **Text**:
  - Primary: `#0F172A` (slate-900) / `#F8FAFC` (slate-50)
  - Secondary: `#64748B` (slate-500)
- **Success**: `#10B981` (emerald-500)
- **Warning**: `#F59E0B` (amber-500)
- **Error**: `#EF4444` (red-500)

#### Typography

- **Font Family**: Inter (Google Fonts) - clean, modern, excellent readability
- **Font Weights**:
  - Regular (400) for body text
  - Medium (500) for emphasis
  - Semibold (600) for headings
  - Bold (700) for titles
- **Scale**: Tailwind's default typographic scale
  - `text-xs` to `text-6xl`
  - Consistent line heights and spacing

#### Layout & Spacing

- **Container**: Fixed-width desktop containers (min-width: 1024px)
- **Grid**: CSS Grid and Flexbox for layouts
- **Spacing**: Tailwind's 4px base unit system
- **Target Resolution**: Optimized for 1920x1080 and 1440p displays
- **Minimum Width**: 1024px (desktop only)

#### Components Design Principles

- **Game Cards**: Hover effects, smooth transitions, clear CTAs
- **Modals**: Backdrop blur, smooth animations, escape key support
- **Buttons**: Consistent sizing, loading states, disabled states
- **Forms**: Clear validation, helpful error messages
- **Navigation**: Sticky sidebar, breadcrumbs, active states
- **Feedback**: Toast notifications, loading skeletons, empty states

### Key Considerations

- **Performance**: Fast search, optimized images, debounced inputs, lazy loading
- **Security**: Protected API keys, secure user data, input validation
- **Accessibility**: WCAG 2.1 AA compliance, keyboard navigation, screen reader support
- **Desktop-First**: Optimized for desktop gaming setups (1920x1080+)
- **Dark Mode**: System preference detection, manual toggle
- **Reliability**: Robust error handling, offline capabilities, graceful degradation
- **Maintainability**: Clean architecture, component reusability, consistent patterns

## User Interface Design

### Layout Structure

- **Sidebar Navigation**: Persistent left sidebar (280px) with:
  - Logo/branding
  - Main navigation (Dashboard, Search, Library, Wishlist, Deals)
  - User profile section
  - Settings access
- **Main Content Area**: Responsive content area with:
  - Top navigation bar with search and user menu
  - Breadcrumb navigation
  - Content cards with consistent spacing
- **Footer**: Simple footer with API attributions

### Visual Design Elements

- **Game Cards**:
  - Cover art thumbnails (16:9 aspect ratio)
  - Game title, platform icons, rating
  - Quick action buttons (Add to Library, Wishlist)
  - Hover effects with subtle shadows
- **Status Indicators**:
  - Color-coded badges for game status
  - Progress bars for completion tracking
  - Deal alerts with pulsing animations
- **Interactive Elements**:
  - Smooth hover transitions (300ms)
  - Button states with appropriate feedback
  - Loading skeletons for better perceived performance

### Theming

- **Default Theme**: Dark mode (gaming-focused audience)
- **Light Mode**: Available via toggle
- **System Preference**: Auto-detection and sync
- **Theme Persistence**: Saved in localStorage

## User Flow

1. **Discovery**: Search and explore games
2. **Collection**: Add interesting games to library with status
3. **Wishlist**: Mark desired games for purchase
4. **Monitoring**: Receive alerts when wishlist games are discounted
5. **Management**: Track progress and update game statuses

## Success Metrics (Personal Use)

- Time saved finding game deals
- Reduced duplicate purchases
- Better organization of game backlog
- Improved gaming decision-making

## Implemented Features (July 2025)

- ✅ **AI Game Recommendations**: Multi-provider AI system for personalized suggestions
- ✅ **Advanced Search**: Enhanced search with voice recognition and NLP
- ✅ **Steam Library Import**: Complete Steam integration with playtime tracking
- ✅ **Data Import/Export**: CSV import/export capabilities for game collections
- ✅ **Enhanced Analytics**: Collection insights and gaming statistics
- ✅ **Security**: Encrypted API key storage and comprehensive authentication
- ✅ **Performance**: Virtualization, caching, and optimization systems
- ✅ **Error Handling**: Comprehensive error boundaries and logging
- ✅ **Testing**: Vitest testing framework with React Testing Library

## Future Enhancements (Planned)

- Enhanced price tracking features across multiple platforms
- Social sharing and community features
- Advanced filtering and sorting improvements
- Mobile app development
- Additional platform integrations (Epic Games, PlayStation, Xbox)
- Public API development
