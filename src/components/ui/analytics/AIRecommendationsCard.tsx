import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/base/tabs';
import { Skeleton } from '@/components/ui/base/skeleton';
import { 
  Brain, 
  Sparkles, 
  RefreshCw, 
  Star, 
  Clock, 
  Tag,
  TrendingUp,
  Info,
  GamepadIcon
} from 'lucide-react';
import { 
  useUserBasedRecommendations, 
  useSimilarGames,
  getRecommendationBadgeColor, 
  getRecommendationIcon 
} from '@/hooks/useAIRecommendations';
import { GameRecommendation } from '@/lib/aiRecommendationService';
import { cn } from '@/lib/utils';

interface AIRecommendationsCardProps {
  className?: string;
  gameId?: string;
  gameName?: string;
  showSimilarGames?: boolean;
}

export const AIRecommendationsCard: React.FC<AIRecommendationsCardProps> = ({ 
  className = '',
  gameId,
  gameName,
  showSimilarGames = false
}) => {
  const [activeTab, setActiveTab] = useState(showSimilarGames && gameId ? 'similar' : 'recommendations');
  
  const {
    recommendations,
    recommendationStats,
    isLoading,
    isGenerating,
    generateAllRecommendations
  } = useUserBasedRecommendations();

  const {
    similarGames,
    isLoading: isLoadingSimilar,
    generateSimilar
  } = useSimilarGames(gameId, gameName);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <div className="grid gap-3">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-24 w-full" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const RecommendationCard = ({ rec }: { rec: GameRecommendation }) => (
    <Link to={`/game/${rec.igdb_id}`} className="block">
      <div className="p-3 border rounded-lg hover:shadow-lg transition-all duration-200 bg-gradient-to-r from-background to-muted/20 flex items-start gap-4">
        {rec.cover_image ? (
          <img
            src={rec.cover_image}
            alt={rec.gameName}
            className="w-20 h-auto rounded-md object-cover"
          />
        ) : (
          <div className="w-20 h-[106px] bg-muted rounded-md flex items-center justify-center">
            <GamepadIcon className="w-6 h-6 text-muted-foreground" />
          </div>
        )}
        <div className="flex-1">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="font-semibold text-sm line-clamp-1">{rec.gameName}</h3>
              <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                {rec.reason}
              </p>
            </div>
            <Badge className={cn("text-xs ml-2 flex-shrink-0", getRecommendationBadgeColor(rec.confidence))}>
              {rec.confidence}%
            </Badge>
          </div>
          
          <div className="flex flex-wrap gap-1 mb-2">
            {rec.categories.slice(0, 2).map((category, idx) => (
              <Badge key={idx} variant="outline" className="text-xs">
                {getRecommendationIcon()} {category}
              </Badge>
            ))}
          </div>

          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{rec.estimatedPlayTime}h</span>
            </div>
            <div className="flex items-center gap-1">
              <Tag className="h-3 w-3" />
              <span className="line-clamp-1">{rec.genres.slice(0, 2).join(', ')}</span>
            </div>
          </div>

          {rec.similarTo && rec.similarTo.length > 0 && (
            <div className="mt-1 text-xs text-blue-600 dark:text-blue-400">
              Similar to: {rec.similarTo.slice(0, 2).join(', ')}
            </div>
          )}
        </div>
      </div>
    </Link>
  );

  const hasRecommendations = recommendations.length > 0;
  const hasSimilarGames = similarGames.length > 0;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Recommendations
            <Sparkles className="h-4 w-4 text-yellow-500" />
          </div>
          <div className="flex items-center gap-2">
            {hasRecommendations && (
              <Badge variant="outline" className="text-xs">
                {recommendationStats.totalRecommendations} games
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={generateAllRecommendations}
              disabled={isGenerating}
            >
              <RefreshCw className={`h-4 w-4 ${isGenerating ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className={cn("grid w-full", showSimilarGames && gameId ? "grid-cols-2" : "grid-cols-1")}>
            <TabsTrigger value="recommendations" className="text-xs">
              <Star className="h-3 w-3 mr-1" />
              Your Games
            </TabsTrigger>
            {showSimilarGames && gameId && (
              <TabsTrigger value="similar" className="text-xs">
                <TrendingUp className="h-3 w-3 mr-1" />
                Similar
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="recommendations" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Based on Your Collection</h3>
              {hasRecommendations && (
                <Badge variant="secondary" className="text-xs">
                  Avg. {Math.round(recommendationStats.averageConfidence)}% confidence
                </Badge>
              )}
            </div>
            
            {!hasRecommendations ? (
              <div className="text-center py-8">
                <Brain className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground mb-2">
                  No recommendations yet
                </p>
                <p className="text-xs text-muted-foreground">
                  Add games to your collection to get personalized suggestions
                </p>
              </div>
            ) : (
              <div className="grid gap-3">
                {recommendations.slice(0, 8).map((rec, index) => (
                  <RecommendationCard key={index} rec={rec} />
                ))}
              </div>
            )}
          </TabsContent>

          {showSimilarGames && gameId && (
            <TabsContent value="similar" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Similar to {gameName}</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => generateSimilar(gameId, gameName || '')}
                  disabled={isLoadingSimilar}
                >
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Find More
                </Button>
              </div>
              
              {isLoadingSimilar ? (
                <div className="grid gap-3">
                  {[...Array(3)].map((_, i) => (
                    <Skeleton key={i} className="h-24 w-full" />
                  ))}
                </div>
              ) : !hasSimilarGames ? (
                <div className="text-center py-6">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">
                    No similar games found yet
                  </p>
                </div>
              ) : (
                <div className="grid gap-3">
                  {similarGames.map((rec, index) => (
                    <RecommendationCard key={index} rec={rec} />
                  ))}
                </div>
              )}
            </TabsContent>
          )}
        </Tabs>

        {/* Recommendation Stats */}
        {hasRecommendations && (
          <div className="mt-6 pt-4 border-t">
            <div className="flex items-center gap-2 mb-3">
              <Info className="h-4 w-4 text-muted-foreground" />
              <p className="text-sm font-medium">Recommendation Insights</p>
            </div>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-lg font-bold text-primary">
                  {recommendationStats.totalRecommendations}
                </p>
                <p className="text-xs text-muted-foreground">
                  Total Games
                </p>
              </div>
              <div>
                <p className="text-lg font-bold text-green-600">
                  {Math.round(recommendationStats.averageConfidence)}%
                </p>
                <p className="text-xs text-muted-foreground">
                  Avg. Confidence
                </p>
              </div>
              <div>
                <p className="text-lg font-bold text-blue-600">
                  {recommendationStats.topCategories.length}
                </p>
                <p className="text-xs text-muted-foreground">
                  Categories
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};