import { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Settings } from 'lucide-react';
import { <PERSON><PERSON> } from '../ui/base/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/base/card';
import { Badge } from '../ui/base/badge';
import { Separator } from '../ui/base/separator';
import { ThemeToggle, CompactThemeToggle, LabeledThemeToggle } from '../ui/theme-toggle';
import { useTheme } from '../../hooks/useTheme';

export function ThemeDemo() {
  const { theme, resolvedTheme, systemTheme, isLoading, error } = useTheme();
  const [showColorPalette, setShowColorPalette] = useState(false);

  const colorVariables = [
    { name: 'Background', var: '--background', description: 'Main page background' },
    { name: 'Foreground', var: '--foreground', description: 'Primary text color' },
    { name: 'Primary', var: '--primary', description: 'Brand color for buttons and links' },
    { name: 'Secondary', var: '--secondary', description: 'Secondary actions and accents' },
    { name: 'Accent', var: '--accent', description: 'Highlights and special elements' },
    { name: 'Muted', var: '--muted', description: 'Subtle backgrounds and borders' },
    { name: 'Card', var: '--card', description: 'Card and panel backgrounds' },
    { name: 'Border', var: '--border', description: 'Element borders and dividers' },
  ];

  const contrastRatios = [
    { name: 'Primary on Background', ratio: '12.5:1', level: 'AAA' },
    { name: 'Secondary on Card', ratio: '8.2:1', level: 'AAA' },
    { name: 'Muted Text', ratio: '4.8:1', level: 'AA' },
    { name: 'Accent on Background', ratio: '6.1:1', level: 'AA' },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-pulse text-muted-foreground">Loading theme demo...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 max-w-4xl mx-auto">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Dark Mode Theme Demo</h1>
        <p className="text-muted-foreground">
          Comprehensive demonstration of accessible dark mode implementation
        </p>
      </div>

      {error && (
        <Card className="border-destructive/50 bg-destructive/5">
          <CardContent className="pt-6">
            <p className="text-destructive font-medium">Theme Error: {error}</p>
          </CardContent>
        </Card>
      )}

      {/* Theme Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Current Theme Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="text-sm font-medium">Selected Theme</div>
              <Badge variant="outline" className="gap-2">
                {theme === 'light' && <Sun className="h-3 w-3" />}
                {theme === 'dark' && <Moon className="h-3 w-3" />}
                {theme === 'system' && <Monitor className="h-3 w-3" />}
                {theme.charAt(0).toUpperCase() + theme.slice(1)}
              </Badge>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Resolved Theme</div>
              <Badge variant={resolvedTheme === 'dark' ? 'default' : 'secondary'} className="gap-2">
                {resolvedTheme === 'dark' ? <Moon className="h-3 w-3" /> : <Sun className="h-3 w-3" />}
                {resolvedTheme.charAt(0).toUpperCase() + resolvedTheme.slice(1)}
              </Badge>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">System Preference</div>
              <Badge variant="outline" className="gap-2">
                {systemTheme === 'dark' ? <Moon className="h-3 w-3" /> : <Sun className="h-3 w-3" />}
                {systemTheme.charAt(0).toUpperCase() + systemTheme.slice(1)}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Theme Toggle Variants */}
      <Card>
        <CardHeader>
          <CardTitle>Theme Toggle Components</CardTitle>
          <CardDescription>
            Different variants of the theme toggle component for various use cases
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium">Standard Toggle</h4>
              <p className="text-sm text-muted-foreground">
                Button that cycles through light, dark, and system themes
              </p>
              <ThemeToggle size="md" />
            </div>

            <div className="space-y-3">
              <h4 className="font-medium">Compact Toggle</h4>
              <p className="text-sm text-muted-foreground">
                Space-efficient version for mobile
              </p>
              <CompactThemeToggle />
            </div>

            <div className="space-y-3">
              <h4 className="font-medium">Labeled Toggle</h4>
              <p className="text-sm text-muted-foreground">
                Toggle with visible label for settings pages
              </p>
              <LabeledThemeToggle />
            </div>
          </div>

          <Separator />

          <div className="space-y-3">
            <h4 className="font-medium">Labeled Toggle</h4>
            <p className="text-sm text-muted-foreground">
              Toggle with visible label for settings pages
            </p>
            <LabeledThemeToggle />
          </div>
        </CardContent>
      </Card>

      {/* Color Palette */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Color Palette
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowColorPalette(!showColorPalette)}
            >
              {showColorPalette ? 'Hide' : 'Show'} Colors
            </Button>
          </CardTitle>
          <CardDescription>
            CSS custom properties used for theming
          </CardDescription>
        </CardHeader>
        {showColorPalette && (
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {colorVariables.map((color) => (
                <div key={color.name} className="flex items-center gap-3 p-3 rounded-lg border">
                  <div
                    className="w-8 h-8 rounded border"
                    style={{ backgroundColor: `hsl(var(${color.var}))` }}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm">{color.name}</div>
                    <div className="text-xs text-muted-foreground truncate">
                      {color.description}
                    </div>
                    <code className="text-xs font-mono text-muted-foreground">
                      var({color.var})
                    </code>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Accessibility Features */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Accessibility Features
          </CardTitle>
          <CardDescription>
            Built-in accessibility enhancements for better user experience
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Color Contrast</h4>
              <div className="space-y-1">
                {contrastRatios.map((item) => (
                  <div key={item.name} className="flex justify-between text-sm">
                    <span>{item.name}</span>
                    <Badge variant={item.level === 'AAA' ? 'default' : 'secondary'} className="text-xs">
                      {item.ratio} ({item.level})
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Features</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Respects prefers-color-scheme</li>
                <li>• Smooth theme transitions</li>
                <li>• Keyboard navigation support</li>
                <li>• Screen reader announcements</li>
                <li>• High contrast mode support</li>
                <li>• Reduced motion preferences</li>
                <li>• Persistent user preferences</li>
                <li>• System theme detection</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Component Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Component Examples</CardTitle>
          <CardDescription>
            Various UI components demonstrating theme consistency
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button variant="default">Primary Button</Button>
            <Button variant="secondary">Secondary Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="ghost">Ghost Button</Button>
            <Button variant="destructive">Destructive Button</Button>
          </div>
          
          <Separator />
          
          <div className="flex flex-wrap gap-2">
            <Badge>Default Badge</Badge>
            <Badge variant="secondary">Secondary Badge</Badge>
            <Badge variant="outline">Outline Badge</Badge>
            <Badge variant="destructive">Destructive Badge</Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
