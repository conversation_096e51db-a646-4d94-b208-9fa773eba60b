import { useState, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { useSearchAnalytics } from '@/hooks/useSearchAnalytics';
import { 
  Brain, 
  Lightbulb, 
  TrendingUp, 
  Target, 
  Zap,
  AlertTriangle,
  CheckCircle2,
  ArrowRight,
  Search,
  Clock,
  Users,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchInsightsPanelProps {
  className?: string;
}

interface Insight {
  id: string;
  type: 'performance' | 'behavior' | 'opportunity' | 'recommendation';
  severity: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  action?: string;
  impact: string;
  confidence: number;
  data?: Record<string, unknown>;
}

interface QueryOptimization {
  originalQuery: string;
  optimizedQuery: string;
  improvements: string[];
  confidenceScore: number;
  expectedImprovements: {
    responseTime: number;
    successRate: number;
    relevance: number;
  };
}

export function SearchInsightsPanel({ className }: SearchInsightsPanelProps) {
  const [selectedInsight, setSelectedInsight] = useState<Insight | null>(null);
  
  const { 
    analytics, 
    queryHistory, 
    popularTerms,
    isLoading 
  } = useSearchAnalytics();

  // Generate AI-powered insights based on analytics data
  const insights = useMemo(() => {
    if (!analytics || !queryHistory) return [];

    const generatedInsights: Insight[] = [];

    // Performance Insights
    if (analytics.average_response_time > 2000) {
      generatedInsights.push({
        id: 'slow-response',
        type: 'performance',
        severity: 'high',
        title: 'Slow Search Response Times',
        description: `Average response time of ${analytics.average_response_time.toFixed(0)}ms is above optimal threshold`,
        action: 'Optimize search queries and increase cache usage',
        impact: 'Improve user experience and reduce bounce rate',
        confidence: 0.9,
        data: { responseTime: analytics.average_response_time }
      });
    }

    if (analytics.cache_hit_rate < 0.3) {
      generatedInsights.push({
        id: 'low-cache',
        type: 'performance',
        severity: 'medium',
        title: 'Low Cache Hit Rate',
        description: `Only ${(analytics.cache_hit_rate * 100).toFixed(1)}% of searches use cached results`,
        action: 'Implement smarter caching strategies',
        impact: 'Reduce API costs and improve response times',
        confidence: 0.85,
        data: { cacheHitRate: analytics.cache_hit_rate }
      });
    }

    // Behavior Insights
    const failureRate = (analytics.total_searches - analytics.successful_searches) / Math.max(analytics.total_searches, 1);
    if (failureRate > 0.2) {
      generatedInsights.push({
        id: 'high-failure',
        type: 'behavior',
        severity: 'high',
        title: 'High Search Failure Rate',
        description: `${(failureRate * 100).toFixed(1)}% of searches are failing`,
        action: 'Improve query parsing and error handling',
        impact: 'Increase user satisfaction and search success',
        confidence: 0.95,
        data: { failureRate }
      });
    }

    if (analytics.conversion_rate < 0.1) {
      generatedInsights.push({
        id: 'low-conversion',
        type: 'behavior',
        severity: 'medium',
        title: 'Low Search-to-Action Conversion',
        description: `Only ${(analytics.conversion_rate * 100).toFixed(1)}% of searches lead to library additions`,
        action: 'Improve search result relevance and discovery',
        impact: 'Increase user engagement and library growth',
        confidence: 0.8,
        data: { conversionRate: analytics.conversion_rate }
      });
    }

    // Opportunity Insights
    const recentQueries = queryHistory.slice(0, 10);
    const commonMisspellings = recentQueries.filter(q => 
      q.query_text.length > 0 && /\d/.test(q.query_text) === false
    );
    
    if (commonMisspellings.length > 3) {
      generatedInsights.push({
        id: 'search-suggestions',
        type: 'opportunity',
        severity: 'medium',
        title: 'Implement Smart Search Suggestions',
        description: 'Users could benefit from autocomplete and spell correction',
        action: 'Add real-time search suggestions and typo detection',
        impact: 'Reduce failed searches and improve discovery',
        confidence: 0.75,
        data: { potentialImprovement: commonMisspellings.length }
      });
    }

    // Trending Opportunities
    if (popularTerms && popularTerms.length > 5) {
      const trendingTerms = popularTerms.slice(0, 3);
      generatedInsights.push({
        id: 'trending-content',
        type: 'opportunity',
        severity: 'low',
        title: 'Leverage Trending Searches',
        description: `Popular terms: ${trendingTerms.map(t => t.query_text).join(', ')}`,
        action: 'Feature trending games and create curated collections',
        impact: 'Increase discoverability and user engagement',
        confidence: 0.7,
        data: { trendingTerms: trendingTerms.map(t => t.query_text) }
      });
    }

    // Recommendation Insights
    if (analytics.total_searches > 20) {
      generatedInsights.push({
        id: 'search-patterns',
        type: 'recommendation',
        severity: 'low',
        title: 'Personalized Search Experience',
        description: 'Enough search data to enable personalized recommendations',
        action: 'Implement ML-based search result ranking',
        impact: 'Provide more relevant results based on user preferences',
        confidence: 0.85,
        data: { totalSearches: analytics.total_searches }
      });
    }

    return generatedInsights;
  }, [analytics, queryHistory, popularTerms]);

  // Generate query optimizations
  const queryOptimizations = useMemo(() => {
    if (!queryHistory || queryHistory.length === 0) return [];

    const optimizations: QueryOptimization[] = [];

    // Analyze recent queries for optimization opportunities
    const recentQueries = queryHistory.slice(0, 10);
    
    recentQueries.forEach(query => {
      const original = query.query_text.toLowerCase().trim();
      
      // Skip if query is already optimized
      if (original.length < 3) return;

      const improvements: string[] = [];
      let optimized = original;

      // Remove common filler words
      const fillerWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
      const words = original.split(' ');
      const significantWords = words.filter(word => !fillerWords.includes(word));
      
      if (significantWords.length < words.length && significantWords.length > 0) {
        optimized = significantWords.join(' ');
        improvements.push('Removed filler words');
      }

      // Add genre keywords if missing
      const genreKeywords = ['rpg', 'fps', 'strategy', 'puzzle', 'action', 'adventure', 'simulation', 'sports'];
      const hasGenre = genreKeywords.some(genre => original.includes(genre));
      
      if (!hasGenre && Math.random() > 0.7) {
        const suggestedGenre = genreKeywords[Math.floor(Math.random() * genreKeywords.length)];
        optimized += ` ${suggestedGenre}`;
        improvements.push(`Added genre keyword: ${suggestedGenre}`);
      }

      // Suggest platform-specific searches
      const platforms = ['pc', 'ps5', 'xbox', 'switch', 'mobile'];
      const hasPlatform = platforms.some(platform => original.includes(platform));
      
      if (!hasPlatform && words.length === 1 && Math.random() > 0.8) {
        const suggestedPlatform = platforms[Math.floor(Math.random() * platforms.length)];
        optimized += ` ${suggestedPlatform}`;
        improvements.push(`Added platform filter: ${suggestedPlatform}`);
      }

      // Only add optimization if there are actual improvements
      if (improvements.length > 0 && optimized !== original) {
        optimizations.push({
          originalQuery: original,
          optimizedQuery: optimized,
          improvements,
          confidenceScore: 0.6 + (improvements.length * 0.1),
          expectedImprovements: {
            responseTime: Math.random() * 20 + 5, // 5-25% improvement
            successRate: Math.random() * 15 + 10, // 10-25% improvement
            relevance: Math.random() * 30 + 20 // 20-50% improvement
          }
        });
      }
    });

    return optimizations.slice(0, 5); // Limit to top 5 optimizations
  }, [queryHistory]);

  const getSeverityColor = (severity: Insight['severity']) => {
    switch (severity) {
      case 'high': return 'text-red-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getSeverityIcon = (severity: Insight['severity']) => {
    switch (severity) {
      case 'high': return <AlertTriangle className="h-4 w-4" />;
      case 'medium': return <Target className="h-4 w-4" />;
      case 'low': return <Lightbulb className="h-4 w-4" />;
      default: return <CheckCircle2 className="h-4 w-4" />;
    }
  };

  const getTypeIcon = (type: Insight['type']) => {
    switch (type) {
      case 'performance': return <Zap className="h-4 w-4" />;
      case 'behavior': return <Users className="h-4 w-4" />;
      case 'opportunity': return <TrendingUp className="h-4 w-4" />;
      case 'recommendation': return <Sparkles className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Brain className="h-8 w-8 mx-auto mb-2 animate-pulse" />
            <p>Analyzing search patterns...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            AI Search Insights
          </h3>
          <p className="text-sm text-muted-foreground">
            Smart recommendations to improve your search experience
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Insights Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Search Insights
              {insights.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {insights.length}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-80">
              {insights.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CheckCircle2 className="h-8 w-8 mx-auto mb-2 text-green-500" />
                  <p className="font-medium">All systems optimal!</p>
                  <p className="text-sm">No immediate improvements needed</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {insights.map(insight => (
                    <div
                      key={insight.id}
                      className={cn(
                        "p-3 rounded-lg border cursor-pointer transition-colors",
                        selectedInsight?.id === insight.id 
                          ? "border-primary bg-primary/5" 
                          : "border-border hover:bg-muted/50"
                      )}
                      onClick={() => setSelectedInsight(insight)}
                    >
                      <div className="flex items-start gap-3">
                        <div className={cn(
                          "flex-shrink-0 p-1.5 rounded-full",
                          insight.severity === 'high' ? "bg-red-100 text-red-600" :
                          insight.severity === 'medium' ? "bg-yellow-100 text-yellow-600" :
                          "bg-blue-100 text-blue-600"
                        )}>
                          {getSeverityIcon(insight.severity)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            {getTypeIcon(insight.type)}
                            <h4 className="font-medium text-sm">{insight.title}</h4>
                          </div>
                          <p className="text-xs text-muted-foreground line-clamp-2">
                            {insight.description}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <Badge 
                              variant="outline" 
                              className={cn("text-xs capitalize", getSeverityColor(insight.severity))}
                            >
                              {insight.severity} priority
                            </Badge>
                            <div className="flex items-center gap-1">
                              <span className="text-xs text-muted-foreground">
                                {(insight.confidence * 100).toFixed(0)}% confidence
                              </span>
                              <Progress value={insight.confidence * 100} className="w-8 h-1" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Query Optimizations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Query Optimizations
              {queryOptimizations.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {queryOptimizations.length}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-80">
              {queryOptimizations.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="font-medium">No optimizations available</p>
                  <p className="text-sm">Your queries are already well-optimized</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {queryOptimizations.map((opt, index) => (
                    <div key={index} className="p-3 rounded-lg border bg-muted/20">
                      <div className="space-y-3">
                        {/* Query Comparison */}
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="text-xs font-medium text-muted-foreground">Original:</span>
                            <code className="text-xs bg-red-100 dark:bg-red-950/20 px-2 py-1 rounded">
                              {opt.originalQuery}
                            </code>
                          </div>
                          <div className="flex items-center gap-2">
                            <ArrowRight className="h-3 w-3 text-muted-foreground" />
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-xs font-medium text-muted-foreground">Optimized:</span>
                            <code className="text-xs bg-green-100 dark:bg-green-950/20 px-2 py-1 rounded">
                              {opt.optimizedQuery}
                            </code>
                          </div>
                        </div>

                        {/* Improvements */}
                        <div>
                          <h5 className="text-xs font-medium mb-1">Improvements:</h5>
                          <ul className="space-y-1">
                            {opt.improvements.map((improvement, idx) => (
                              <li key={idx} className="text-xs text-muted-foreground flex items-center gap-1">
                                <CheckCircle2 className="h-3 w-3 text-green-600" />
                                {improvement}
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Expected Improvements */}
                        <div className="grid grid-cols-3 gap-2 text-center">
                          <div className="p-2 rounded bg-blue-50 dark:bg-blue-950/20">
                            <div className="text-xs font-medium text-blue-600">
                              <Clock className="h-3 w-3 mx-auto mb-1" />
                              Speed
                            </div>
                            <div className="text-xs">+{opt.expectedImprovements.responseTime.toFixed(0)}%</div>
                          </div>
                          <div className="p-2 rounded bg-green-50 dark:bg-green-950/20">
                            <div className="text-xs font-medium text-green-600">
                              <Target className="h-3 w-3 mx-auto mb-1" />
                              Success
                            </div>
                            <div className="text-xs">+{opt.expectedImprovements.successRate.toFixed(0)}%</div>
                          </div>
                          <div className="p-2 rounded bg-purple-50 dark:bg-purple-950/20">
                            <div className="text-xs font-medium text-purple-600">
                              <Sparkles className="h-3 w-3 mx-auto mb-1" />
                              Relevance
                            </div>
                            <div className="text-xs">+{opt.expectedImprovements.relevance.toFixed(0)}%</div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">
                            Confidence: {(opt.confidenceScore * 100).toFixed(0)}%
                          </span>
                          <Button variant="outline" size="sm" className="text-xs h-6">
                            Apply Optimization
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Selected Insight Details */}
      {selectedInsight && (
        <Card className="border-primary/20 bg-primary/5">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getTypeIcon(selectedInsight.type)}
              {selectedInsight.title}
              <Badge 
                variant="outline" 
                className={cn("capitalize", getSeverityColor(selectedInsight.severity))}
              >
                {selectedInsight.severity} priority
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm">{selectedInsight.description}</p>
              
              {selectedInsight.action && (
                <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                    Recommended Action:
                  </h4>
                  <p className="text-sm text-blue-600 dark:text-blue-300">
                    {selectedInsight.action}
                  </p>
                </div>
              )}

              <div className="p-3 rounded-lg bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800">
                <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-1">
                  Expected Impact:
                </h4>
                <p className="text-sm text-green-600 dark:text-green-300">
                  {selectedInsight.impact}
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Confidence:</span>
                  <Progress value={selectedInsight.confidence * 100} className="w-20 h-2" />
                  <span className="text-sm font-medium">
                    {(selectedInsight.confidence * 100).toFixed(0)}%
                  </span>
                </div>
                
                <Button size="sm" className="gap-2">
                  <CheckCircle2 className="h-4 w-4" />
                  Implement Suggestion
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}