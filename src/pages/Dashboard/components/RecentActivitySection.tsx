import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Badge } from '@/components/ui/base';
import { LoadingSpinner } from '@/components/ui/utils';
import { useRecentActivity } from '@/hooks/useUserStats';
import { Calendar } from 'lucide-react';
import { RecentActivityItem } from '../types';

export function RecentActivitySection() {
  const { data: recentActivity, isLoading: activityLoading } = useRecentActivity();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          Recent Activity
        </CardTitle>
        <CardDescription>Your latest gaming activity</CardDescription>
      </CardHeader>
      <CardContent>
        {activityLoading ? (
          <div className="flex items-center justify-center py-4">
            <LoadingSpinner />
          </div>
        ) : recentActivity && recentActivity.length > 0 ? (
          (() => {
            const validGames = recentActivity.filter((game: RecentActivityItem) => game.game?.name);
            return validGames.length > 0 ? (
              <div className="space-y-3">
                {validGames.map((game: RecentActivityItem) => (
                  <div key={game.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50">
                    <div className="flex-1">
                      <p className="font-medium">{game.game?.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Added to {game.status === 'wishlist' ? 'wishlist' : 'library'}
                      </p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {game.status}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No recent activity with valid games</p>
              </div>
            );
          })()
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No recent activity</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}