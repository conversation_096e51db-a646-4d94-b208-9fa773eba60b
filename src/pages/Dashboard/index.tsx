import { LoadingSpinner } from '@/components/ui/utils';
import { AlertCircle } from 'lucide-react';
import { useUserStats } from '@/hooks/useUserStats';
import { DashboardHeader } from './components/DashboardHeader';
import { StatsOverview } from './components/StatsOverview';
import { AIIntelligenceSection } from './components/AIIntelligenceSection';
import { HotDealsSection } from './components/HotDealsSection';
import { RecentActivitySection } from './components/RecentActivitySection';


export default function Dashboard() {
  const { data: stats, isLoading: statsLoading, error: statsError } = useUserStats();

  if (statsLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (statsError) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
            <p className="text-destructive">Failed to load dashboard data</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <DashboardHeader stats={stats} />
      <StatsOverview stats={stats} />
      <AIIntelligenceSection />
      <HotDealsSection />
      <RecentActivitySection />
    </div>
  );
}