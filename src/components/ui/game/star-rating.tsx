import { useState } from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StarRatingProps {
  value: number;
  onChange: (value: number) => void;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  readOnly?: boolean;
  className?: string;
}

export function StarRating({ 
  value, 
  onChange, 
  max = 5, 
  size = 'md',
  readOnly = false,
  className 
}: StarRatingProps) {
  const [hoveredValue, setHoveredValue] = useState(0);

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const handleClick = (rating: number) => {
    if (!readOnly) {
      onChange(rating);
    }
  };

  const handleMouseEnter = (rating: number) => {
    if (!readOnly) {
      setHoveredValue(rating);
    }
  };

  const handleMouseLeave = () => {
    if (!readOnly) {
      setHoveredValue(0);
    }
  };

  return (
    <div className={cn('flex items-center gap-1', className)}>
      {Array.from({ length: max }, (_, i) => {
        const rating = i + 1;
        const isActive = rating <= (hoveredValue || value);
        
        return (
          <Star
            key={i}
            className={cn(
              sizeClasses[size],
              'transition-colors',
              isActive 
                ? 'fill-yellow-400 text-yellow-400' 
                : 'fill-none text-muted-foreground',
              !readOnly && 'cursor-pointer hover:text-yellow-400'
            )}
            onClick={() => handleClick(rating)}
            onMouseEnter={() => handleMouseEnter(rating)}
            onMouseLeave={handleMouseLeave}
          />
        );
      })}
    </div>
  );
}