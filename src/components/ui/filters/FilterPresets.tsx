import { useState } from 'react';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/base/dialog';
import { Input } from '@/components/ui/base/input';
import { Label } from '@/components/ui/base/label';
import { Textarea } from '@/components/ui/base/textarea';
import { Switch } from '@/components/ui/base/switch';
import { useFilterPresets, usePublicFilterPresets } from '@/hooks/useFilterPresets';
import { GameFilters } from '@/types/filters';
import { Bookmark, Save, Trash2, Users, Lock, Clock, Filter } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FilterPresetsProps {
  currentFilters: GameFilters;
  onLoadPreset: (filters: GameFilters) => void;
  className?: string;
}

export function FilterPresets({ currentFilters, onLoadPreset, className }: FilterPresetsProps) {
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [isPresetsDialogOpen, setIsPresetsDialogOpen] = useState(false);
  const [saveForm, setSaveForm] = useState({
    name: '',
    description: '',
    isPublic: false
  });

  const { 
    presets, 
    createPreset, 
    deletePreset, 
    incrementUsage,
    isCreating 
  } = useFilterPresets();
  
  const { publicPresets } = usePublicFilterPresets();

  // Check if current filters have any active filters
  const hasActiveFilters = Object.values(currentFilters).some(filter => {
    if (typeof filter === 'object' && 'enabled' in filter) {
      return filter.enabled;
    }
    return false;
  });

  // Handle saving current filters as preset
  const handleSavePreset = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!saveForm.name.trim()) return;

    try {
      await createPreset({
        name: saveForm.name.trim(),
        description: saveForm.description.trim() || undefined,
        filters: currentFilters,
        isPublic: saveForm.isPublic
      });
      
      setSaveForm({ name: '', description: '', isPublic: false });
      setIsSaveDialogOpen(false);
    } catch (error) {
      console.error('Error saving preset:', error);
    }
  };

  // Handle loading a preset
  const handleLoadPreset = (filters: GameFilters, presetId: string) => {
    onLoadPreset(filters);
    incrementUsage(presetId);
  };

  // Handle deleting a preset
  const handleDeletePreset = async (presetId: string) => {
    if (window.confirm('Are you sure you want to delete this preset?')) {
      try {
        await deletePreset(presetId);
      } catch (error) {
        console.error('Error deleting preset:', error);
      }
    }
  };

  // Get filter summary for display
  const getFilterSummary = (filters: GameFilters) => {
    const summary = [];
    
    if (filters.platforms.enabled && filters.platforms.platforms.length > 0) {
      summary.push(`${filters.platforms.platforms.length} platform${filters.platforms.platforms.length === 1 ? '' : 's'}`);
    }
    if (filters.genres.enabled && filters.genres.genres.length > 0) {
      summary.push(`${filters.genres.genres.length} genre${filters.genres.genres.length === 1 ? '' : 's'}`);
    }
    if (filters.customTags.enabled && filters.customTags.tags.length > 0) {
      summary.push(`${filters.customTags.tags.length} tag${filters.customTags.tags.length === 1 ? '' : 's'}`);
    }
    if (filters.year.enabled) {
      summary.push('year range');
    }
    if (filters.rating.enabled) {
      summary.push('rating range');
    }
    if (filters.status.enabled && filters.status.statuses.length > 0) {
      summary.push(`${filters.status.statuses.length} status${filters.status.statuses.length === 1 ? '' : 'es'}`);
    }
    
    return summary.length > 0 ? summary.join(', ') : 'No filters';
  };

  return (
    <div className={cn('flex gap-2', className)}>
      {/* Save Current Filters */}
      <Dialog open={isSaveDialogOpen} onOpenChange={setIsSaveDialogOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            disabled={!hasActiveFilters}
            className="h-8"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Preset
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Save Filter Preset</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSavePreset} className="space-y-4">
            <div>
              <Label htmlFor="preset-name">Name</Label>
              <Input
                id="preset-name"
                value={saveForm.name}
                onChange={(e) => setSaveForm({ ...saveForm, name: e.target.value })}
                placeholder="Enter preset name"
                maxLength={100}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="preset-description">Description (optional)</Label>
              <Textarea
                id="preset-description"
                value={saveForm.description}
                onChange={(e) => setSaveForm({ ...saveForm, description: e.target.value })}
                placeholder="Describe this filter preset"
                rows={3}
                maxLength={500}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                checked={saveForm.isPublic}
                onCheckedChange={(checked) => setSaveForm({ ...saveForm, isPublic: checked })}
              />
              <Label className="text-sm">Make public (share with other users)</Label>
            </div>

            <div className="text-sm text-muted-foreground">
              <p>Current filters: {getFilterSummary(currentFilters)}</p>
            </div>
            
            <div className="flex gap-2">
              <Button type="submit" disabled={isCreating || !saveForm.name.trim()} className="flex-1">
                {isCreating ? 'Saving...' : 'Save Preset'}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsSaveDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Browse Presets */}
      <Dialog open={isPresetsDialogOpen} onOpenChange={setIsPresetsDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="h-8">
            <Bookmark className="h-4 w-4 mr-2" />
            Presets
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Filter Presets</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-6">
            {/* Your Presets */}
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <Lock className="h-4 w-4" />
                Your Presets ({presets.length})
              </h3>
              
              {presets.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-8">
                  No saved presets yet. Save your current filters to create your first preset.
                </p>
              ) : (
                <div className="grid gap-3">
                  {presets.map(preset => (
                    <Card key={preset.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="space-y-1">
                            <CardTitle className="text-base">{preset.name}</CardTitle>
                            {preset.description && (
                              <CardDescription className="text-sm">
                                {preset.description}
                              </CardDescription>
                            )}
                          </div>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleLoadPreset(preset.filters, preset.id)}
                            >
                              <Filter className="h-4 w-4 mr-1" />
                              Load
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeletePreset(preset.id)}
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">
                            {getFilterSummary(preset.filters)}
                          </span>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            Used {preset.usage_count} times
                            {preset.is_public && (
                              <Badge variant="secondary" className="text-xs">
                                <Users className="h-3 w-3 mr-1" />
                                Public
                              </Badge>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Public Presets */}
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <Users className="h-4 w-4" />
                Public Presets ({publicPresets.length})
              </h3>
              
              {publicPresets.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-8">
                  No public presets available yet.
                </p>
              ) : (
                <div className="grid gap-3">
                  {publicPresets.map(preset => (
                    <Card key={preset.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="space-y-1">
                            <CardTitle className="text-base">{preset.name}</CardTitle>
                            {preset.description && (
                              <CardDescription className="text-sm">
                                {preset.description}
                              </CardDescription>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleLoadPreset(preset.filters, preset.id)}
                          >
                            <Filter className="h-4 w-4 mr-1" />
                            Load
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">
                            {getFilterSummary(preset.filters)}
                          </span>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            Used {preset.usage_count} times
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}