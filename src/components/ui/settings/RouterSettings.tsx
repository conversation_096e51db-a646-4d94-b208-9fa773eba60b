/**
 * Router Settings Component
 * Manages artwork API router configuration settings
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Label } from '@/components/ui/base/label';
import { Switch } from '@/components/ui/base/switch';
import { Settings } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { rateLimitManager } from '@/lib/services/rateLimitManager';
import { useArtworkRouterSettings } from '@/hooks/useArtworkRouterSettings';

const RouterSettings: React.FC = () => {
  const { settings, isLoading, updateSetting } = useArtworkRouterSettings();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Router Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-48"></div>
                  <div className="h-3 bg-gray-200 rounded w-64"></div>
                </div>
                <div className="w-10 h-6 bg-gray-200 rounded-full"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Router Settings
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <Label>Use Environment Keys as Fallback</Label>
            <p className="text-sm text-gray-600">
              Allow using environment variables when user keys are not available
            </p>
          </div>
          <Switch 
            checked={settings.useEnvironmentKeys}
            onCheckedChange={(checked) => updateSetting('useEnvironmentKeys', checked)}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <Label>Enable High Quality Mode</Label>
            <p className="text-sm text-gray-600">
              Prefer services with higher quality ratings (may increase cost)
            </p>
          </div>
          <Switch 
            checked={settings.enableHighQuality}
            onCheckedChange={(checked) => updateSetting('enableHighQuality', checked)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <Label>Enable Cost Optimization</Label>
            <p className="text-sm text-gray-600">
              Prefer free or low-cost services when possible
            </p>
          </div>
          <Switch 
            checked={settings.enableCostOptimization}
            onCheckedChange={(checked) => updateSetting('enableCostOptimization', checked)}
          />
        </div>

        <div className="pt-4 border-t">
          <Button 
            variant="outline" 
            onClick={() => {
              rateLimitManager.cleanup();
              toast.success('Rate limit cache cleared');
            }}
          >
            Clear Rate Limit Cache
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default RouterSettings;