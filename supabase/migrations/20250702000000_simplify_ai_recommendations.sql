/*
  Simplify AI Recommendations Schema
  
  This migration removes mood and discovery tables and creates a simple 
  user_recommendations table for caching AI recommendations based on user's games.
*/

-- Create simplified user_recommendations table
-- Handle potential type mismatch with games.id column
CREATE TABLE IF NOT EXISTS user_recommendations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  game_id text, -- Changed to text to match existing games table
  igdb_id text,
  game_name text NOT NULL,
  reason text NOT NULL,
  confidence integer NOT NULL CHECK (confidence >= 0 AND confidence <= 100),
  categories text[] DEFAULT '{}',
  similar_to text[] DEFAULT '{}',
  genres text[] DEFAULT '{}',
  platforms text[] DEFAULT '{}',
  description text,
  tags text[] DEFAULT '{}',
  estimated_play_time integer,
  cover_image text,
  recommendation_type text NOT NULL DEFAULT 'user_based' CHECK (recommendation_type IN ('user_based', 'similar_game')),
  source_game_id text, -- Changed to text to match existing games table
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, igdb_id, recommendation_type, source_game_id)
);

-- Add foreign key constraints after table creation to handle existing data
DO $$ 
BEGIN
    -- Add foreign key constraint for game_id if games table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'games') THEN
        BEGIN
            ALTER TABLE user_recommendations ADD CONSTRAINT user_recommendations_game_id_fkey 
            FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE;
        EXCEPTION WHEN OTHERS THEN
            -- If constraint fails due to type mismatch, skip it
            RAISE NOTICE 'Could not add game_id foreign key constraint due to type mismatch';
        END;
        
        BEGIN
            ALTER TABLE user_recommendations ADD CONSTRAINT user_recommendations_source_game_id_fkey 
            FOREIGN KEY (source_game_id) REFERENCES games(id) ON DELETE CASCADE;
        EXCEPTION WHEN OTHERS THEN
            -- If constraint fails due to type mismatch, skip it
            RAISE NOTICE 'Could not add source_game_id foreign key constraint due to type mismatch';
        END;
    END IF;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_recommendations_user_id ON user_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_game_id ON user_recommendations(game_id);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_igdb_id ON user_recommendations(igdb_id);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_type ON user_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_confidence ON user_recommendations(confidence DESC);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_created_at ON user_recommendations(created_at DESC);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_user_recommendations_user_type ON user_recommendations(user_id, recommendation_type);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_user_confidence ON user_recommendations(user_id, confidence DESC);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_source_game ON user_recommendations(source_game_id, user_id);

-- Enable RLS
ALTER TABLE user_recommendations ENABLE ROW LEVEL SECURITY;

-- RLS Policies
DROP POLICY IF EXISTS "Users can view their own recommendations" ON user_recommendations;
CREATE POLICY "Users can view their own recommendations" ON user_recommendations
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own recommendations" ON user_recommendations;
CREATE POLICY "Users can insert their own recommendations" ON user_recommendations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own recommendations" ON user_recommendations;
CREATE POLICY "Users can update their own recommendations" ON user_recommendations
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own recommendations" ON user_recommendations;
CREATE POLICY "Users can delete their own recommendations" ON user_recommendations
  FOR DELETE USING (auth.uid() = user_id);

-- Updated_at trigger
DROP TRIGGER IF EXISTS update_user_recommendations_updated_at ON user_recommendations;
CREATE TRIGGER update_user_recommendations_updated_at
  BEFORE UPDATE ON user_recommendations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create view for user recommendations with game details
DROP VIEW IF EXISTS user_recommendations_with_details;
CREATE OR REPLACE VIEW user_recommendations_with_details AS
SELECT 
  ur.*,
  g.title as actual_game_title,
  g.cover_image as actual_cover_image,
  g.platform as actual_platform,
  g.genres as actual_genres,
  g.developer,
  g.publisher,
  g.release_date,
  g.metacritic_score,
  sg.title as source_game_title
FROM user_recommendations ur
LEFT JOIN games g ON ur.game_id = g.id
LEFT JOIN games sg ON ur.source_game_id = sg.id;

-- Grant permissions on view
GRANT SELECT ON user_recommendations_with_details TO authenticated;

-- Function to clean old recommendations (optional, for maintenance)
CREATE OR REPLACE FUNCTION clean_old_recommendations(days_old integer DEFAULT 30)
RETURNS void AS $$
BEGIN
  DELETE FROM user_recommendations
  WHERE created_at < NOW() - INTERVAL '1 day' * days_old;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user-based recommendations
CREATE OR REPLACE FUNCTION get_user_based_recommendations(
  p_user_id uuid,
  p_limit integer DEFAULT 10
) RETURNS TABLE (
  id uuid,
  game_name text,
  reason text,
  confidence integer,
  categories text[],
  similar_to text[],
  genres text[],
  platforms text[],
  description text,
  tags text[],
  estimated_play_time integer,
  cover_image text,
  created_at timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ur.id,
    ur.game_name,
    ur.reason,
    ur.confidence,
    ur.categories,
    ur.similar_to,
    ur.genres,
    ur.platforms,
    ur.description,
    ur.tags,
    ur.estimated_play_time,
    ur.cover_image,
    ur.created_at
  FROM user_recommendations ur
  WHERE ur.user_id = p_user_id
    AND ur.recommendation_type = 'user_based'
  ORDER BY ur.confidence DESC, ur.created_at DESC
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get similar game recommendations
CREATE OR REPLACE FUNCTION get_similar_game_recommendations(
  p_user_id uuid,
  p_source_game_id uuid,
  p_limit integer DEFAULT 6
) RETURNS TABLE (
  id uuid,
  game_name text,
  reason text,
  confidence integer,
  categories text[],
  similar_to text[],
  genres text[],
  platforms text[],
  description text,
  tags text[],
  estimated_play_time integer,
  cover_image text,
  created_at timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ur.id,
    ur.game_name,
    ur.reason,
    ur.confidence,
    ur.categories,
    ur.similar_to,
    ur.genres,
    ur.platforms,
    ur.description,
    ur.tags,
    ur.estimated_play_time,
    ur.cover_image,
    ur.created_at
  FROM user_recommendations ur
  WHERE ur.user_id = p_user_id
    AND ur.recommendation_type = 'similar_game'
    AND ur.source_game_id = p_source_game_id
  ORDER BY ur.confidence DESC, ur.created_at DESC
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_user_based_recommendations TO authenticated;
GRANT EXECUTE ON FUNCTION get_similar_game_recommendations TO authenticated;
GRANT EXECUTE ON FUNCTION clean_old_recommendations TO authenticated;

-- Comment explaining the new structure
COMMENT ON TABLE user_recommendations IS 'Simplified AI recommendations table that caches recommendations based on user games collection, removing mood and discovery logic';
COMMENT ON COLUMN user_recommendations.recommendation_type IS 'Type of recommendation: user_based (from user collection) or similar_game (similar to specific game)';
COMMENT ON COLUMN user_recommendations.source_game_id IS 'Source game ID for similar_game recommendations, null for user_based recommendations';