import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { XboxGameTile } from '@/components/ui/game/XboxGameTile';
import { UserGameWithDetails } from '@/types/database';

// Mock the custom hooks
vi.mock('@/hooks/useCustomArtwork', () => ({
  useCustomArtwork: () => ({
    getBestCoverImage: () => null,
    hasCustomArtwork: false
  })
}));

// Mock the game status utils
vi.mock('@/lib/gameStatusUtils', () => ({
  getStatusIconElement: (status: string) => <span data-testid="status-icon">{status}</span>,
  getStatusColor: (status: string) => `status-${status}`
}));

const mockGameData: UserGameWithDetails = {
  id: '1',
  user_id: 'user1',
  game_id: 'game1',
  status: 'playing',
  date_added: '2024-01-01',
  game: {
    id: 'game1',
    title: 'Test Game',
    platform: 'PC',
    genres: ['Action', 'Adventure'],
    developer: 'Test Developer',
    publisher: 'Test Publisher',
    release_date: '2024-01-01',
    description: 'A test game',
    cover_image: 'https://example.com/cover.jpg',
    metacritic_score: 85
  }
};

describe('XboxGameTile', () => {
  it('renders game title correctly', () => {
    render(<XboxGameTile gameData={mockGameData} />);
    
    expect(screen.getByText('Test Game')).toBeInTheDocument();
  });

  it('displays game artwork when available', () => {
    render(<XboxGameTile gameData={mockGameData} />);
    
    const image = screen.getByAltText('Test Game');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'https://example.com/cover.jpg');
  });

  it('shows fallback when no artwork is available', () => {
    const gameDataWithoutImage = {
      ...mockGameData,
      game: {
        ...mockGameData.game,
        cover_image: undefined
      }
    };

    render(<XboxGameTile gameData={gameDataWithoutImage} />);
    
    expect(screen.getByText('No Artwork')).toBeInTheDocument();
  });

  it('calls onSelect when clicked', () => {
    const onSelect = vi.fn();
    render(<XboxGameTile gameData={mockGameData} onSelect={onSelect} />);
    
    const tile = screen.getByRole('button');
    fireEvent.click(tile);
    
    expect(onSelect).toHaveBeenCalledWith(mockGameData);
  });

  it('applies selected styling when isSelected is true', () => {
    render(<XboxGameTile gameData={mockGameData} isSelected={true} />);
    
    const tile = screen.getByRole('button');
    expect(tile).toHaveClass('ring-2', 'ring-primary');
  });

  it('renders different sizes correctly', () => {
    const { rerender } = render(<XboxGameTile gameData={mockGameData} size="small" />);
    let tile = screen.getByRole('button');
    expect(tile).toHaveClass('w-40', 'h-[90px]');

    rerender(<XboxGameTile gameData={mockGameData} size="medium" />);
    tile = screen.getByRole('button');
    expect(tile).toHaveClass('w-60', 'h-[135px]');

    rerender(<XboxGameTile gameData={mockGameData} size="large" />);
    tile = screen.getByRole('button');
    expect(tile).toHaveClass('w-80', 'h-[180px]');
  });

  it('displays metacritic score when available', () => {
    render(<XboxGameTile gameData={mockGameData} />);
    
    // The score should be visible on hover, but we can check it exists in the DOM
    expect(screen.getByText('85')).toBeInTheDocument();
  });

  it('displays release year when available', () => {
    render(<XboxGameTile gameData={mockGameData} />);
    
    expect(screen.getByText('2024')).toBeInTheDocument();
  });

  it('displays developer info for large tiles', () => {
    render(<XboxGameTile gameData={mockGameData} size="large" />);
    
    expect(screen.getByText('Test Developer')).toBeInTheDocument();
  });

  it('does not display developer info for small tiles', () => {
    render(<XboxGameTile gameData={mockGameData} size="small" />);
    
    expect(screen.queryByText('Test Developer')).not.toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<XboxGameTile gameData={mockGameData} />);
    
    const tile = screen.getByRole('button');
    expect(tile).toHaveAttribute('aria-label', 'Test Game - playing');
    expect(tile).toHaveAttribute('tabIndex', '0');
  });
});