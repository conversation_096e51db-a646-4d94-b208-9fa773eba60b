import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/base/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/base/dialog';
import { Input } from '@/components/ui/base/input';
import { Label } from '@/components/ui/base/label';
import { Textarea } from '@/components/ui/base/textarea';
import { Badge } from '@/components/ui/base/badge';
import { useUserTags } from '@/hooks/useUserTags';
import { UserTag } from '@/types/filters';
import { Settings, Plus, Edit2, Trash2, Hash } from 'lucide-react';
import { cn } from '@/lib/utils';

const DEFAULT_COLORS = [
  '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',
  '#22c55e', '#10b981', '#06b6d4', '#0ea5e9', '#3b82f6',
  '#6366f1', '#8b5cf6', '#a855f7', '#d946ef', '#ec4899',
  '#f43f5e', '#64748b', '#6b7280', '#374151', '#1f2937'
];

interface TagManagerProps {
  children?: React.ReactNode;
}

export function TagManager({ children }: TagManagerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<UserTag | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#3b82f6'
  });

  const { tags, createTag, updateTag, deleteTag, isCreating: isCreatingTag, isUpdating, isDeleting } = useUserTags();

  const handleCreate = () => {
    setIsCreating(true);
    setEditingTag(null);
    setFormData({ name: '', description: '', color: '#3b82f6' });
  };

  const handleEdit = (tag: UserTag) => {
    setEditingTag(tag);
    setIsCreating(false);
    setFormData({
      name: tag.name,
      description: tag.description || '',
      color: tag.color
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) return;

    try {
      if (isCreating) {
        await createTag({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          color: formData.color
        });
      } else if (editingTag) {
        await updateTag({
          tagId: editingTag.id,
          updates: {
            name: formData.name.trim(),
            description: formData.description.trim() || undefined,
            color: formData.color
          }
        });
      }
      
      setIsCreating(false);
      setEditingTag(null);
      setFormData({ name: '', description: '', color: '#3b82f6' });
    } catch (error) {
      console.error('Error saving tag:', error);
    }
  };

  const handleDelete = async (tagId: string) => {
    if (window.confirm('Are you sure you want to delete this tag?')) {
      try {
        await deleteTag(tagId);
      } catch (error) {
        console.error('Error deleting tag:', error);
      }
    }
  };

  const handleCancel = () => {
    setIsCreating(false);
    setEditingTag(null);
    setFormData({ name: '', description: '', color: '#3b82f6' });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Manage Tags
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Hash className="h-5 w-5" />
            Tag Manager
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Create New Tag Button */}
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Manage your custom tags for better organization
            </p>
            <Button onClick={handleCreate} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              New Tag
            </Button>
          </div>

          {/* Tag Creation/Edit Form */}
          {(isCreating || editingTag) && (
            <form onSubmit={handleSubmit} className="space-y-4 p-4 border rounded-lg bg-muted/50">
              <div className="flex items-center gap-2">
                <Hash className="h-4 w-4" />
                <h3 className="font-medium">
                  {isCreating ? 'Create New Tag' : 'Edit Tag'}
                </h3>
              </div>
              
              <div className="space-y-3">
                <div>
                  <Label htmlFor="tag-name">Name</Label>
                  <Input
                    id="tag-name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Enter tag name"
                    maxLength={50}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="tag-description">Description (optional)</Label>
                  <Textarea
                    id="tag-description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Enter tag description"
                    rows={2}
                    maxLength={200}
                  />
                </div>
                
                <div>
                  <Label>Color</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {DEFAULT_COLORS.map(color => (
                      <button
                        key={color}
                        type="button"
                        className={cn(
                          'w-8 h-8 rounded-full border-2 transition-all',
                          formData.color === color ? 'border-foreground scale-110' : 'border-transparent'
                        )}
                        style={{ backgroundColor: color }}
                        onClick={() => setFormData({ ...formData, color })}
                      />
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button 
                  type="submit" 
                  size="sm"
                  disabled={isCreatingTag || isUpdating || !formData.name.trim()}
                >
                  {isCreating ? 'Create' : 'Save'}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
              </div>
            </form>
          )}

          {/* Tags List */}
          <div className="space-y-3">
            <h3 className="font-medium">Your Tags ({tags.length})</h3>
            
            {tags.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Hash className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No tags created yet</p>
                <p className="text-sm">Create your first tag to get started</p>
              </div>
            ) : (
              <div className="grid gap-2">
                {tags.map(tag => (
                  <div
                    key={tag.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="h-4 w-4 rounded-full"
                        style={{ backgroundColor: tag.color }}
                      />
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{tag.name}</span>
                          {tag.is_system && (
                            <Badge variant="secondary" className="text-xs">System</Badge>
                          )}
                        </div>
                        {tag.description && (
                          <p className="text-sm text-muted-foreground">{tag.description}</p>
                        )}
                        <p className="text-xs text-muted-foreground">
                          Used {tag.usage_count} time{tag.usage_count === 1 ? '' : 's'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(tag)}
                        disabled={isUpdating}
                      >
                        <Edit2 className="h-4 w-4" />
                      </Button>
                      {!tag.is_system && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(tag.id)}
                          disabled={isDeleting}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}