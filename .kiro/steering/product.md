# Product Overview

## Codexa - AI-Powered Gaming Library Manager

Codexa is a sophisticated web application for discovering, tracking, and managing video game collections. It combines traditional library management with AI-powered features to create an intelligent gaming companion.

### Core Purpose
- **Game Discovery**: Search and explore games using comprehensive databases (IGDB, TheGamesDB)
- **Collection Management**: Organize personal game libraries with status tracking and metadata
- **AI Integration**: Leverage multiple AI providers for recommendations, insights, and conversational assistance
- **Platform Integration**: Import libraries from Steam, Epic Games, PlayStation, Xbox, and other platforms
- **Price Monitoring**: Track game prices and receive deal notifications

### Target Audience
- Gaming enthusiasts who want to organize large game collections
- Users seeking AI-powered game recommendations and insights
- Gamers interested in price tracking and deal discovery
- Desktop-focused users (optimized for 1920x1080+ displays)

### Key Differentiators
- **Multi-AI Provider Support**: OpenAI, DeepSeek, Google Gemini integration
- **Comprehensive Import System**: Steam library synchronization with playtime data
- **Security-First**: Encrypted API key storage with AES-256 encryption
- **Performance Optimized**: Virtualized lists, lazy loading, and intelligent caching
- **Desktop-First Design**: Optimized for gaming setups and large displays

### Business Model
Personal use application with focus on user privacy and data ownership. No data selling or tracking.