import { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button, Input, Label, Textarea, Badge } from '@/components/ui/base';
import { LoadingSpinner } from '@/components/ui/utils';
import { useAuth } from '@/contexts/AuthContext';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { db } from '@/lib/supabase';
import { storage } from '@/lib/supabase';
import { 
  User, 
  Camera, 
  Save,
  AlertCircle,
  Upload,
  Trash2,
  Calendar,
  Mail
} from 'lucide-react';

interface UserProfile {
  username: string;
  display_name: string;
  bio: string;
  avatar_url: string;
}

export default function Profile() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState<UserProfile>({
    username: '',
    display_name: '',
    bio: '',
    avatar_url: '',
  });
  const [hasChanges, setHasChanges] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string>('');

  // Fetch user profile
  const { data: profile, isLoading, error } = useQuery({
    queryKey: ['user-profile', user?.id],
    queryFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const { data, error } = await db.userProfiles.getProfile(user.id);
      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }
      
      return data;
    },
    enabled: !!user?.id,
  });

  // Get user stats for profile display
  const { data: stats, error: statsError, isLoading: statsLoading } = useQuery({
    queryKey: ['user-collection-stats', user?.id],
    queryFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const { data, error } = await db.stats.getUserCollectionStats(user.id);
      if (error) throw error;
      
      return data;
    },
    enabled: !!user?.id,
    retry: 3,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update profile mutation
  const updateProfile = useMutation({
    mutationFn: async (newProfile: UserProfile) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      let finalAvatarUrl = newProfile.avatar_url;
      
      // Upload new avatar if provided
      if (avatarFile) {
        const { data: uploadData, error: uploadError } = await storage.uploadUserAvatar(avatarFile, user.id);
        if (uploadError) throw uploadError;
        finalAvatarUrl = uploadData || '';
      }
      
      const profileData = {
        ...newProfile,
        avatar_url: finalAvatarUrl,
      };
      
      if (profile) {
        // Update existing profile
        const { data, error } = await db.userProfiles.updateProfile(user.id, profileData);
        if (error) throw error;
        return data;
      } else {
        // Create new profile
        const { data, error } = await db.userProfiles.createProfile({
          id: user.id,
          ...profileData
        });
        if (error) throw error;
        return data;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-profile', user?.id] });
      setHasChanges(false);
      setAvatarFile(null);
      setAvatarPreview('');
      
      // Show success toast
      toast.success('Profile updated successfully');
    },
    onError: () => {
      toast.error('Failed to update profile. Please try again');
    },
  });

  // Delete avatar mutation
  const deleteAvatar = useMutation({
    mutationFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const { error } = await storage.deleteUserAvatar(user.id);
      if (error) throw error;
      
      // Update profile to remove avatar URL
      if (profile) {
        const { data, error: updateError } = await db.userProfiles.updateProfile(user.id, {
          ...formData,
          avatar_url: ''
        });
        if (updateError) throw updateError;
        return data;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-profile', user?.id] });
      setFormData(prev => ({ ...prev, avatar_url: '' }));
      setAvatarPreview('');
      
      // Show success toast
      toast.success('Avatar deleted successfully');
    },
    onError: () => {
      toast.error('Failed to delete avatar. Please try again');
    },
  });

  // Initialize form data when profile loads
  useEffect(() => {
    if (profile) {
      setFormData({
        username: profile.username || '',
        display_name: profile.display_name || '',
        bio: profile.bio || '',
        avatar_url: profile.avatar_url || '',
      });
    } else if (user) {
      // Initialize with user metadata if no profile exists
      setFormData(prev => ({
        ...prev,
        username: user.user_metadata?.username || '',
        display_name: user.user_metadata?.display_name || user.user_metadata?.username || '',
      }));
    }
  }, [profile, user]);

  const handleInputChange = (field: keyof UserProfile, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      setHasChanges(true);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = () => {
    updateProfile.mutate(formData);
  };

  const handleDeleteAvatar = () => {
    deleteAvatar.mutate();
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
            <p className="text-destructive">Failed to load profile</p>
          </div>
        </div>
      </div>
    );
  }

  const currentAvatar = avatarPreview || formData.avatar_url;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Profile</h1>
          <p className="text-muted-foreground">Manage your profile information</p>
        </div>
        <Badge variant="outline" className="bg-primary/10">
          <User className="h-3 w-3 mr-1" />
          My Profile
        </Badge>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Profile Picture & Stats */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              Profile Picture
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col items-center space-y-4">
              <div className="relative">
                <div className="w-32 h-32 rounded-full bg-muted flex items-center justify-center overflow-hidden">
                  {currentAvatar ? (
                    <img
                      src={currentAvatar}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="h-16 w-16 text-muted-foreground" />
                  )}
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="relative">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarChange}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                </Button>
                
                {(currentAvatar || avatarFile) && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleDeleteAvatar}
                    disabled={deleteAvatar.isPending}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            {/* User Stats */}
            <div className="pt-4 border-t space-y-2">
              <h4 className="font-medium text-sm">Gaming Stats</h4>
              {statsLoading ? (
                <div className="flex items-center justify-center py-4">
                  <LoadingSpinner size="sm" />
                </div>
              ) : statsError ? (
                <div className="text-xs text-destructive">
                  Failed to load stats
                </div>
              ) : (
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total Games:</span>
                    <span className="font-medium">{stats?.total_games || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Completed:</span>
                    <span className="font-medium">{stats?.completed_games || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Wishlist:</span>
                    <span className="font-medium">{stats?.wishlist_count || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Hours Played:</span>
                    <span className="font-medium">{Math.round(stats?.total_hours || 0)}h</span>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Profile Information */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profile Information
            </CardTitle>
            <CardDescription>Update your personal information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  placeholder="Enter your username"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="display_name">Display Name</Label>
                <Input
                  id="display_name"
                  value={formData.display_name}
                  onChange={(e) => handleInputChange('display_name', e.target.value)}
                  placeholder="Enter your display name"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="bio">Bio</Label>
              <Textarea
                id="bio"
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                placeholder="Tell us about yourself..."
                rows={4}
              />
            </div>

            {/* Account Information */}
            <div className="pt-4 border-t space-y-4">
              <h4 className="font-medium">Account Information</h4>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-sm text-muted-foreground">{user?.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Member Since</p>
                    <p className="text-sm text-muted-foreground">
                      {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Button */}
      {hasChanges && (
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-primary" />
                <span className="text-sm text-primary">You have unsaved changes</span>
              </div>
              <Button 
                onClick={handleSave}
                disabled={updateProfile.isPending}
                className="min-w-[100px]"
              >
                {updateProfile.isPending ? (
                  <LoadingSpinner className="h-4 w-4" />
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

    </div>
  );
}