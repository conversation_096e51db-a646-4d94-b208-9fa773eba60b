import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  migrateLegacyViewPreferences,
  needsViewPreferenceMigration,
  migrateLocalStorageViewPreferences,
  getDefaultViewModeFromLegacy,
  validateMigratedViewMode
} from '@/lib/utils/viewModePreferenceMigration';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('viewModePreferenceMigration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('migrateLegacyViewPreferences', () => {
    it('should migrate legacy default_platform values', () => {
      const legacyPrefs = { default_platform: 'platform' };
      const result = migrateLegacyViewPreferences(legacyPrefs);
      
      expect(result.default_view_mode).toBe('platform');
      expect(result.default_platform).toBe('platform');
    });

    it('should migrate platform-family preference', () => {
      const legacyPrefs = { default_platform: 'platform-family' };
      const result = migrateLegacyViewPreferences(legacyPrefs);
      
      expect(result.default_view_mode).toBe('platform-family');
      expect(result.default_platform).toBe('platform-family');
    });

    it('should default to status for unknown values', () => {
      const legacyPrefs = { default_platform: 'unknown' };
      const result = migrateLegacyViewPreferences(legacyPrefs);
      
      expect(result.default_view_mode).toBe('status');
      expect(result.default_platform).toBe('status');
    });

    it('should migrate legacy default_sort values', () => {
      const legacyPrefs = { default_sort: 'platform-family' };
      const result = migrateLegacyViewPreferences(legacyPrefs);
      
      expect(result.default_view_mode).toBe('platform-family');
      expect(result.default_platform).toBe('platform-family');
    });

    it('should handle empty preferences', () => {
      const result = migrateLegacyViewPreferences({});
      
      expect(result).toEqual({});
    });
  });

  describe('needsViewPreferenceMigration', () => {
    it('should return true when legacy fields exist but modern fields do not', () => {
      const prefs = { default_platform: 'platform' };
      expect(needsViewPreferenceMigration(prefs)).toBe(true);
    });

    it('should return true when display.default_sort exists but modern fields do not', () => {
      const prefs = { display: { default_sort: 'platform' } };
      expect(needsViewPreferenceMigration(prefs)).toBe(true);
    });

    it('should return false when modern fields exist', () => {
      const prefs = { 
        default_platform: 'platform',
        default_view_mode: 'platform-family'
      };
      expect(needsViewPreferenceMigration(prefs)).toBe(false);
    });

    it('should return false when display.default_view_mode exists', () => {
      const prefs = { 
        default_platform: 'platform',
        display: { default_view_mode: 'status' }
      };
      expect(needsViewPreferenceMigration(prefs)).toBe(false);
    });

    it('should return false when no legacy fields exist', () => {
      const prefs = {};
      expect(needsViewPreferenceMigration(prefs)).toBe(false);
    });
  });

  describe('migrateLocalStorageViewPreferences', () => {
    it('should not migrate when modern view mode exists', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'library-view-mode') return 'platform';
        return null;
      });

      migrateLocalStorageViewPreferences();

      expect(localStorageMock.setItem).not.toHaveBeenCalled();
      expect(localStorageMock.removeItem).not.toHaveBeenCalled();
    });

    it('should migrate from legacy platform view setting', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'library-platform-view') return 'platform-family';
        return null;
      });

      migrateLocalStorageViewPreferences();

      expect(localStorageMock.setItem).toHaveBeenCalledWith('library-view-mode', 'platform-family');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('library-platform-view');
    });

    it('should migrate JSON-formatted legacy values', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'library-platform-view') return JSON.stringify('platform');
        return null;
      });

      migrateLocalStorageViewPreferences();

      expect(localStorageMock.setItem).toHaveBeenCalledWith('library-view-mode', 'platform');
    });

    it('should migrate collapsed platform states', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'collapsed-platforms') return JSON.stringify(['PC', 'Xbox']);
        if (key === 'library-collapsed-platforms') return null;
        return null;
      });

      migrateLocalStorageViewPreferences();

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'library-collapsed-platforms',
        JSON.stringify(['PC', 'Xbox'])
      );
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('collapsed-platforms');
    });

    it('should migrate collapsed family states', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'collapsed-families') return JSON.stringify(['PlayStation']);
        if (key === 'library-collapsed-families') return null;
        return null;
      });

      migrateLocalStorageViewPreferences();

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'library-collapsed-families',
        JSON.stringify(['PlayStation'])
      );
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('collapsed-families');
    });

    it('should handle JSON parsing errors gracefully', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'library-platform-view') return 'invalid-json{';
        return null;
      });

      expect(() => migrateLocalStorageViewPreferences()).not.toThrow();
    });
  });

  describe('getDefaultViewModeFromLegacy', () => {
    it('should prioritize modern default_view_mode field', () => {
      const prefs = {
        display: { default_view_mode: 'platform-family' },
        default_platform: 'platform'
      };
      
      expect(getDefaultViewModeFromLegacy(prefs)).toBe('platform-family');
    });

    it('should use legacy default_sort as fallback', () => {
      const prefs = {
        display: { default_sort: 'platform' },
        default_platform: 'status'
      };
      
      expect(getDefaultViewModeFromLegacy(prefs)).toBe('platform');
    });

    it('should use legacy default_platform as final fallback', () => {
      const prefs = {
        default_platform: 'platform-family'
      };
      
      expect(getDefaultViewModeFromLegacy(prefs)).toBe('platform-family');
    });

    it('should default to status when no valid legacy fields exist', () => {
      const prefs = {
        default_platform: 'invalid',
        display: { default_sort: 'invalid' }
      };
      
      expect(getDefaultViewModeFromLegacy(prefs)).toBe('status');
    });

    it('should handle empty preferences', () => {
      expect(getDefaultViewModeFromLegacy({})).toBe('status');
    });
  });

  describe('validateMigratedViewMode', () => {
    it('should return valid view modes unchanged', () => {
      expect(validateMigratedViewMode('status')).toBe('status');
      expect(validateMigratedViewMode('platform')).toBe('platform');
      expect(validateMigratedViewMode('platform-family')).toBe('platform-family');
    });

    it('should return status for invalid values', () => {
      expect(validateMigratedViewMode('invalid')).toBe('status');
      expect(validateMigratedViewMode(null)).toBe('status');
      expect(validateMigratedViewMode(undefined)).toBe('status');
      expect(validateMigratedViewMode(123)).toBe('status');
    });
  });
});