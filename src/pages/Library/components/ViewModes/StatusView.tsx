import { Card, CardContent } from '@/components/ui/base';
import { GameCard } from '@/components/ui/game';
import { StatusViewProps } from '../../types';
import { UserGameWithDetails } from '@/types/database';

export function StatusView({ games, onGameClick, onStatusUpdate, onRemoveGame }: StatusViewProps) {
  return (
    <Card>
      <CardContent className="pt-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
          {games.map((userGame: unknown) => {
            const gameData = userGame as UserGameWithDetails;
            return (
              <GameCard
                key={gameData.id}
                gameData={gameData}
                onGameClick={onGameClick}
                onUpdateStatus={onStatusUpdate}
                onRemoveGame={onRemoveGame}
              />
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}