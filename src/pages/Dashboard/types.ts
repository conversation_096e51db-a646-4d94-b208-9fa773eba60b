export interface DashboardStats {
  totalGames: number;
  currentlyPlaying: number;
  completed: number;
  wishlistCount: number;
}

export interface DashboardHeaderProps {
  stats?: DashboardStats;
}

export interface StatsOverviewProps {
  stats?: DashboardStats;
}

export interface RecentActivityItem {
  id: string;
  status: string;
  game?: {
    name?: string;
  };
}

export interface DealItem {
  gameName: string;
  discountPercent: number;
  currentPrice: number;
  originalPrice: number;
  shopName: string;
}

export interface DealStats {
  biggestDiscount: number;
}

export interface HotDealsSectionProps {
  deals?: DealItem[];
  dealStats?: DealStats;
  isLoading?: boolean;
}

export interface RecentActivitySectionProps {
  activities?: RecentActivityItem[];
  isLoading?: boolean;
}

export interface AIFeature {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  bgColor: string;
  iconColor: string;
}