import { useState } from 'react';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/base/dialog';
import { Progress } from '@/components/ui/base/progress';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { useUserTags } from '@/hooks/useUserTags';
import { useEnhancedGameTags } from '@/hooks/useEnhancedGameTags';
import { Game } from '@/types';
import { generateThumbnailPlaceholder } from '@/lib/utils/imageUtils';
import { 
  Sparkles, 
  Brain, 
  Wand2, 
  Check, 
  X, 
  Loader2, 
  Target
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SmartTagSuggestionsProps {
  games: Game[];
  children?: React.ReactNode;
}

interface TagSuggestion {
  tagName: string;
  confidence: number;
  reason: string;
  gameIds: string[];
  isExistingTag: boolean;
  existingTagId?: string;
  color?: string;
}

interface AutoTagSession {
  total: number;
  processed: number;
  suggestions: Map<string, TagSuggestion[]>;
  isProcessing: boolean;
}

export function SmartTagSuggestions({ games, children }: SmartTagSuggestionsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [session, setSession] = useState<AutoTagSession>({
    total: 0,
    processed: 0,
    suggestions: new Map(),
    isProcessing: false
  });
  const [appliedTags, setAppliedTags] = useState<Set<string>>(new Set());

  const { tags, createTag } = useUserTags();
  const { addTagsToGame } = useEnhancedGameTags();

  // Smart tag suggestion algorithm
  const generateSmartSuggestions = async (game: Game): Promise<TagSuggestion[]> => {
    const suggestions: TagSuggestion[] = [];

    // Genre-based suggestions
    if (game.genres && game.genres.length > 0) {
      game.genres.forEach(genre => {
        const existingTag = tags.find(t => 
          t.name.toLowerCase() === genre.toLowerCase()
        );
        
        suggestions.push({
          tagName: existingTag ? existingTag.name : genre,
          confidence: 0.9,
          reason: `Game belongs to ${genre} genre`,
          gameIds: [game.id.toString()],
          isExistingTag: !!existingTag,
          existingTagId: existingTag?.id,
          color: existingTag?.color
        });
      });
    }

    // Platform-based suggestions
    if (game.platforms && game.platforms.length > 0) {
      game.platforms.forEach(platform => {
        const platformCategory = categorizePlatform(platform);
        if (platformCategory) {
          const existingTag = tags.find(t => 
            t.name.toLowerCase() === platformCategory.toLowerCase()
          );
          
          suggestions.push({
            tagName: existingTag ? existingTag.name : platformCategory,
            confidence: 0.8,
            reason: `Available on ${platformCategory} platform`,
            gameIds: [game.id.toString()],
            isExistingTag: !!existingTag,
            existingTagId: existingTag?.id,
            color: existingTag?.color
          });
        }
      });
    }

    // Rating-based suggestions
    if (game.metacritic_score || game.user_score) {
      const score = game.metacritic_score || game.user_score;
      if (score >= 90) {
        const existingTag = tags.find(t => 
          t.name.toLowerCase() === 'highly rated' || 
          t.name.toLowerCase() === 'masterpiece' ||
          t.name.toLowerCase() === 'excellent'
        );
        
        suggestions.push({
          tagName: existingTag ? existingTag.name : 'Highly Rated',
          confidence: 0.85,
          reason: `High rating score (${score})`,
          gameIds: [game.id.toString()],
          isExistingTag: !!existingTag,
          existingTagId: existingTag?.id,
          color: existingTag?.color || '#22c55e'
        });
      } else if (score >= 80) {
        const existingTag = tags.find(t => 
          t.name.toLowerCase() === 'well rated' || 
          t.name.toLowerCase() === 'good'
        );
        
        suggestions.push({
          tagName: existingTag ? existingTag.name : 'Well Rated',
          confidence: 0.7,
          reason: `Good rating score (${score})`,
          gameIds: [game.id.toString()],
          isExistingTag: !!existingTag,
          existingTagId: existingTag?.id,
          color: existingTag?.color || '#3b82f6'
        });
      }
    }

    // Year-based suggestions
    if (game.release_date) {
      const year = new Date(game.release_date).getFullYear();
      const currentYear = new Date().getFullYear();
      
      if (year <= 2000) {
        const existingTag = tags.find(t => 
          t.name.toLowerCase() === 'retro' || 
          t.name.toLowerCase() === 'classic'
        );
        
        suggestions.push({
          tagName: existingTag ? existingTag.name : 'Retro',
          confidence: 0.9,
          reason: `Released in ${year} (retro game)`,
          gameIds: [game.id.toString()],
          isExistingTag: !!existingTag,
          existingTagId: existingTag?.id,
          color: existingTag?.color || '#f59e0b'
        });
      } else if (currentYear - year <= 2) {
        const existingTag = tags.find(t => 
          t.name.toLowerCase() === 'new' || 
          t.name.toLowerCase() === 'recent'
        );
        
        suggestions.push({
          tagName: existingTag ? existingTag.name : 'New Release',
          confidence: 0.8,
          reason: `Recently released (${year})`,
          gameIds: [game.id.toString()],
          isExistingTag: !!existingTag,
          existingTagId: existingTag?.id,
          color: existingTag?.color || '#10b981'
        });
      }
    }

    // Title-based suggestions
    const titleWords = game.name.toLowerCase().split(/\s+/);
    const commonGameWords = ['edition', 'deluxe', 'ultimate', 'remastered', 'remake', 'hd'];
    
    titleWords.forEach(word => {
      if (commonGameWords.includes(word)) {
        const existingTag = tags.find(t => 
          t.name.toLowerCase() === word
        );
        
        suggestions.push({
          tagName: existingTag ? existingTag.name : word.charAt(0).toUpperCase() + word.slice(1),
          confidence: 0.6,
          reason: `Special edition (${word})`,
          gameIds: [game.id.toString()],
          isExistingTag: !!existingTag,
          existingTagId: existingTag?.id,
          color: existingTag?.color || '#8b5cf6'
        });
      }
    });

    // Developer-based suggestions for indie games
    if (game.developers && game.developers.length > 0) {
      const indieKeywords = ['indie', 'independent', 'small', 'studio'];
      const hasIndieKeyword = game.developers.some(dev => 
        indieKeywords.some(keyword => dev.toLowerCase().includes(keyword))
      );
      
      if (hasIndieKeyword) {
        const existingTag = tags.find(t => 
          t.name.toLowerCase() === 'indie'
        );
        
        suggestions.push({
          tagName: existingTag ? existingTag.name : 'Indie',
          confidence: 0.75,
          reason: 'Independent developer',
          gameIds: [game.id.toString()],
          isExistingTag: !!existingTag,
          existingTagId: existingTag?.id,
          color: existingTag?.color || '#ec4899'
        });
      }
    }

    return suggestions.filter(s => s.confidence >= 0.6);
  };

  const categorizePlatform = (platform: string): string | null => {
    const platformLower = platform.toLowerCase();
    
    if (platformLower.includes('pc') || platformLower.includes('windows') || 
        platformLower.includes('mac') || platformLower.includes('linux')) {
      return 'PC';
    }
    
    if (platformLower.includes('playstation') || platformLower.includes('xbox') || 
        platformLower.includes('nintendo switch') || platformLower.includes('wii')) {
      return 'Console';
    }
    
    if (platformLower.includes('ios') || platformLower.includes('android')) {
      return 'Mobile';
    }
    
    if (platformLower.includes('3ds') || platformLower.includes('vita') || 
        platformLower.includes('psp')) {
      return 'Handheld';
    }
    
    return null;
  };

  const runAutoTagging = async () => {
    setSession(prev => ({ ...prev, isProcessing: true, total: games.length, processed: 0 }));
    
    const newSuggestions = new Map<string, TagSuggestion[]>();
    
    for (let i = 0; i < games.length; i++) {
      const game = games[i];
      try {
        const suggestions = await generateSmartSuggestions(game);
        if (suggestions.length > 0) {
          newSuggestions.set(game.id.toString(), suggestions);
        }
        
        setSession(prev => ({ ...prev, processed: i + 1 }));
        
        // Small delay to prevent overwhelming the UI
        await new Promise(resolve => setTimeout(resolve, 50));
      } catch (error) {
        console.error(`Error generating suggestions for game ${game.name}:`, error);
      }
    }
    
    setSession(prev => ({ 
      ...prev, 
      suggestions: newSuggestions, 
      isProcessing: false 
    }));
  };

  const applyTagSuggestion = async (gameId: string, suggestion: TagSuggestion) => {
    try {
      let tagId = suggestion.existingTagId;
      
      // Create tag if it doesn't exist
      if (!suggestion.isExistingTag) {
        const newTag = await createTag({
          name: suggestion.tagName,
          color: suggestion.color || '#3b82f6'
        });
        tagId = newTag.id;
      }
      
      if (tagId) {
        await addTagsToGame(gameId, [tagId]);
        setAppliedTags(prev => new Set([...prev, `${gameId}-${suggestion.tagName}`]));
      }
    } catch (error) {
      console.error('Error applying tag suggestion:', error);
    }
  };

  const dismissSuggestion = (gameId: string, suggestionIndex: number) => {
    setSession(prev => {
      const newSuggestions = new Map(prev.suggestions);
      const gameSuggestions = newSuggestions.get(gameId);
      if (gameSuggestions) {
        gameSuggestions.splice(suggestionIndex, 1);
        if (gameSuggestions.length === 0) {
          newSuggestions.delete(gameId);
        } else {
          newSuggestions.set(gameId, gameSuggestions);
        }
      }
      return { ...prev, suggestions: newSuggestions };
    });
  };

  const getAllSuggestions = () => {
    const allSuggestions: Array<{ gameId: string; game: Game; suggestions: TagSuggestion[] }> = [];
    
    session.suggestions.forEach((suggestions, gameId) => {
      const game = games.find(g => g.id.toString() === gameId);
      if (game) {
        allSuggestions.push({ gameId, game, suggestions });
      }
    });
    
    return allSuggestions.sort((a, b) => 
      b.suggestions.reduce((sum, s) => sum + s.confidence, 0) - 
      a.suggestions.reduce((sum, s) => sum + s.confidence, 0)
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" className="gap-2">
            <Sparkles className="h-4 w-4" />
            Smart Tag Suggestions
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Smart Tag Suggestions
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {/* Control Panel */}
          <div className="space-y-4 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  AI-powered tag suggestions based on game metadata
                </p>
                {session.suggestions.size > 0 && (
                  <p className="text-xs text-muted-foreground">
                    Found suggestions for {session.suggestions.size} games
                  </p>
                )}
              </div>
              <Button
                onClick={runAutoTagging}
                disabled={session.isProcessing || games.length === 0}
                className="gap-2"
              >
                {session.isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4" />
                    Generate Suggestions
                  </>
                )}
              </Button>
            </div>

            {/* Progress Bar */}
            {session.isProcessing && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Processing games...</span>
                  <span>{session.processed} / {session.total}</span>
                </div>
                <Progress value={(session.processed / session.total) * 100} className="h-2" />
              </div>
            )}
          </div>

          {/* Suggestions List */}
          <ScrollArea className="flex-1">
            {session.suggestions.size === 0 && !session.isProcessing ? (
              <div className="text-center py-12 text-muted-foreground">
                <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">No suggestions yet</p>
                <p className="text-sm">Click "Generate Suggestions" to analyze your games</p>
              </div>
            ) : (
              <div className="space-y-4">
                {getAllSuggestions().map(({ gameId, game, suggestions }) => (
                  <Card key={gameId}>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-3 text-base">
                        <img
                          src={game.cover_url || generateThumbnailPlaceholder(game.name)}
                          alt={game.name}
                          className="w-10 h-10 rounded object-cover"
                        />
                        <div className="flex-1">
                          <p className="font-medium">{game.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {suggestions.length} suggestion{suggestions.length !== 1 ? 's' : ''}
                          </p>
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        {suggestions.map((suggestion, index) => {
                          const isApplied = appliedTags.has(`${gameId}-${suggestion.tagName}`);
                          
                          return (
                            <div
                              key={index}
                              className={cn(
                                "flex items-center justify-between p-3 rounded-lg border transition-colors",
                                isApplied 
                                  ? "bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800" 
                                  : "bg-muted/20 hover:bg-muted/40"
                              )}
                            >
                              <div className="flex items-center gap-3 flex-1">
                                <div className="flex items-center gap-2">
                                  <Target 
                                    className="h-4 w-4" 
                                    style={{ 
                                      color: suggestion.confidence >= 0.8 ? '#22c55e' : 
                                             suggestion.confidence >= 0.7 ? '#f59e0b' : '#6b7280' 
                                    }} 
                                  />
                                  <Badge
                                    variant={suggestion.isExistingTag ? "default" : "secondary"}
                                    className="text-xs"
                                    style={suggestion.color ? { 
                                      backgroundColor: `${suggestion.color}20`, 
                                      borderColor: suggestion.color 
                                    } : {}}
                                  >
                                    {suggestion.tagName}
                                  </Badge>
                                </div>
                                
                                <div className="flex-1">
                                  <p className="text-sm">{suggestion.reason}</p>
                                  <div className="flex items-center gap-2 mt-1">
                                    <span className="text-xs text-muted-foreground">
                                      Confidence: {(suggestion.confidence * 100).toFixed(0)}%
                                    </span>
                                    {!suggestion.isExistingTag && (
                                      <Badge variant="outline" className="text-xs">
                                        New Tag
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>

                              <div className="flex gap-1">
                                {isApplied ? (
                                  <Badge variant="secondary" className="text-xs gap-1">
                                    <Check className="h-3 w-3" />
                                    Applied
                                  </Badge>
                                ) : (
                                  <>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => applyTagSuggestion(gameId, suggestion)}
                                      className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                                    >
                                      <Check className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => dismissSuggestion(gameId, index)}
                                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
}